<script setup lang="tsx">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { NButton, NTag } from 'naive-ui';
import { fetchDeleteDictionary, fetchGetDictionaryList, fetchGetDictionaryType } from '@/service/api/dictionary';
import { useTable } from '@/hooks/common/table';
import ButtonIcon from '@/components/custom/button-icon.vue';
import AddOrUpdateDictionary from './modules/addOrUpdate.vue';

const [showDrawer, toggleShowDrawer] = useToggle(false);
const drawerType = ref<'add' | 'edit'>('add');
const editingData = ref<Api.Dictionary.Dictionary | null>(null);

const { data, pagination, columns, getData, updateSearchParams } = useTable({
  apiFn: fetchGetDictionaryList,
  apiParams: {
    current: 1,
    size: 10
  },
  columns() {
    return [
      {
        title: 'Type',
        key: 'type',
        width: 250
      },
      {
        title: 'Name',
        key: 'name',
        ellipsis: {
          tooltip: true
        },
        align: 'center'
      },
      {
        title: 'Code',
        key: 'code',
        align: 'center'
      },
      {
        title: 'Description',
        key: 'description',
        ellipsis: {
          tooltip: true
        },
        align: 'center'
      },
      {
        title: 'Enabled',
        key: 'isEnabled',
        align: 'center',
        render(row) {
          return (
            <NTag size="small" bordered={false} type={row.isEnabled === 1 ? 'success' : 'error'}>
              {row.isEnabled === 1 ? 'Enabled' : 'Disabled'}
            </NTag>
          );
        }
      },
      {
        title: 'Sort',
        key: 'sort',
        align: 'center',
        width: 60
      },
      {
        key: 'operate',
        width: 100,
        render(row) {
          return (
            <div class="flex justify-end gap-4">
              <ButtonIcon text quaternary={false} icon="solar:pen-2-linear" onClick={() => handleEdit(row)} />
              <ButtonIcon
                text
                quaternary={false}
                icon="solar:trash-bin-2-linear"
                type="error"
                onClick={() => handleDelete(row.id)}
              />
            </div>
          );
        }
      }
    ];
  }
});

const typeOptions = ref<{ label: string; value: string }[]>([]);
const enabledOptions = ref<{ label: string; value: number }[]>([
  { label: 'Enabled', value: 1 },
  { label: 'Disabled', value: 0 }
]);

async function initTypeOptions() {
  const { data: typeOpts } = await fetchGetDictionaryType();
  if (typeOpts) {
    typeOptions.value = typeOpts.map(item => ({
      label: item.type,
      value: item.type
    }));
  }
}

function handleEdit(row: Api.Dictionary.Dictionary) {
  drawerType.value = 'edit';
  editingData.value = { ...row };
  toggleShowDrawer(true);
}

function handleAdd() {
  drawerType.value = 'add';
  toggleShowDrawer(true);
}

async function handleDelete(id: number) {
  const { error: deleteError } = await fetchDeleteDictionary(id);
  if (!deleteError) {
    window.$message?.success('Delete dictionary successfully.');
    getData();
  }
}

function handleSearch(value: string, key: keyof Api.Dictionary.DictionaryListParams) {
  updateSearchParams({ current: 1, size: 10, [key]: value });
  getData();
}

function handleSubmit() {
  getData();
  initTypeOptions();
}

function init() {
  initTypeOptions();
}

init();
</script>

<template>
  <NFlex vertical gap="16px">
    <NCard class="card-wrapper" :bordered="false" title="Dictionary Management">
      <NGrid cols="4" x-gap="16" y-gap="16">
        <NGi span="1">
          <NSelect :options="typeOptions" clearable @update:value="handleSearch($event, 'type')" />
        </NGi>
        <NGi span="1">
          <NSelect :options="enabledOptions" clearable @update:value="handleSearch($event, 'isEnabled')" />
        </NGi>
      </NGrid>
    </NCard>
    <NCard class="min-h-[400px] flex-1 card-wrapper" :bordered="false" title="Dictionary List">
      <template #header-extra>
        <NButton strong secondary type="primary" @click="handleAdd">Add</NButton>
      </template>
      <NDataTable class="h-full" :bordered="false" remote :columns="columns" :data="data" :pagination="pagination" />
    </NCard>

    <AddOrUpdateDictionary
      v-model:show="showDrawer"
      :drawer-type="drawerType"
      :editing-data="editingData"
      :type-options="typeOptions"
      @submit="handleSubmit"
    />
  </NFlex>
</template>

<style scoped></style>
