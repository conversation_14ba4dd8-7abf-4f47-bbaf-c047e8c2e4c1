<script setup lang="ts">
import { computed } from 'vue';
import { getFallbackImage } from '@/utils/fake-image';

const { VITE_SHOP_LEADER_AVATAR_URL } = import.meta.env;

interface Props {
  type: 'video' | 'live';
  data: Api.CategoryLeaders.LeaderShopContent;
  isExport?: boolean;
}

const props = defineProps<Props>();

const imageUrl = computed(() => {
  return `${VITE_SHOP_LEADER_AVATAR_URL}${props.data.coverLocal}`;
});

const avatarUrl = computed(() => {
  return `${VITE_SHOP_LEADER_AVATAR_URL}${props.data.accountAvatarLocal}`;
});

const truncatedTitle = computed(() => {
  const text = props.data.title || '--';
  if (text.length <= 23) return text;
  return `${text.slice(0, 23)}...`;
});

const indicators = computed(() => {
  return [
    {
      title: props.type === 'live' ? 'Viewers' : 'Views',
      prefix: '',
      value: props.data?.viewers || '0',
      showPercent: false,
      description: '',
      average: true,
      decimals: 2,
      suffix: ''
    },
    {
      title: 'GMV',
      prefix: '$',
      value: props.data?.gmv || '$0',
      showPercent: false,
      description: '',
      average: true,
      decimals: 2,
      suffix: ''
    },
    {
      title: 'Units Sold',
      prefix: '',
      value: props.data?.sold || '0',
      showPercent: false,
      description: '',
      average: true,
      decimals: 2,
      suffix: ''
    }
  ];
});

function handleLinkToAccount(accountId: string) {
  if (accountId) {
    window.open(`https://www.tiktok.com/@${accountId}`, '_blank');
  }
}

function handleLinkToVideo(homePage: string) {
  if (homePage) {
    window.open(homePage, '_blank');
  }
}
</script>

<template>
  <NCard class="card-wrapper" content-class="flex-col gap-8px" size="small">
    <template #cover>
      <div
        class="h-180px w-full flex-center overflow-hidden border-1 rounded-md hover:cursor-pointer"
        @click="handleLinkToVideo(props.data.homePage)"
      >
        <NImage object-fit="cover" preview-disabled :src="imageUrl" :fallback-src="getFallbackImage(260, 180)" />
      </div>
    </template>
    <template #header>
      <div class="h-26px overflow-hidden">
        <NEllipsis
          v-if="!isExport"
          style="max-width: 100%"
          class="font-bold"
          :tooltip="{ contentStyle: 'max-width:400px' }"
          :line-clamp="1"
        >
          {{ props.data.title || '--' }}
        </NEllipsis>
        <div v-else class="font-bold">
          {{ truncatedTitle }}
        </div>
      </div>
    </template>
    <div class="flex items-center gap-8px hover:cursor-pointer" @click="handleLinkToAccount(props.data.accountId)">
      <div class="h-32px w-32px overflow-hidden border-1 rounded-full">
        <NImage preview-disabled :src="avatarUrl" :fallback-src="getFallbackImage(32, 32)" />
      </div>
      <NEllipsis style="max-width: 100%" :tooltip="{ contentStyle: 'max-width:400px' }" :line-clamp="1">
        {{ props.data.accountName }}
      </NEllipsis>
    </div>
    <span class="text-xs text-coolgray">{{ props.data.postTime }}</span>
    <template #action>
      <NGrid class="" x-gap="0" :cols="3">
        <NGi v-for="indicator in indicators" :key="indicator.title">
          <NFlex vertical justify="center" align="center" :size="0">
            <NFlex align="center" :size="16" :wrap="false">
              <span class="font-bold">{{ indicator.value }}</span>
            </NFlex>
            <NFlex>
              <NText class="text-gray">{{ indicator.title }}</NText>
              <Tip v-if="indicator.description !== ''" :description="indicator.description" />
            </NFlex>
          </NFlex>
        </NGi>
      </NGrid>
    </template>
  </NCard>
</template>

<style scoped></style>
