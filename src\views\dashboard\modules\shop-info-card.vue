<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-11-01 14:46:30
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-01 14:49:00
 * @FilePath: \tiksage-frontend\src\views\dashboard\modules\shop-info-card.vue
 * @Description: shop-info-card
-->
<script setup lang="ts">
import { ref, watch } from 'vue';
import { isNil } from 'lodash-es';
import { fetchGetShopDataById } from '@/service/api';
import { getFallbackImage } from '@/utils/fake-image';

interface Props {
  id?: number | null;
}

const props = defineProps<Props>();

const shopInfo = ref<Api.Dashboard.ShopData>();

async function initData(id: number) {
  const { data, error } = await fetchGetShopDataById(id);
  if (!error) {
    shopInfo.value = {
      ...data,
      avatar: data.avatar ? `${import.meta.env.VITE_SHOP_AVATAR_URL}${data.avatar}` : ''
    };
  }
}

watch(
  () => props.id,
  newVal => {
    if (!isNil(newVal) && newVal !== 0) {
      initData(newVal);
    }
  },
  {
    immediate: true
  }
);
</script>

<template>
  <div class="flex-center gap-8px">
    <NAvatar size="small" :src="shopInfo?.avatar" :fallback-src="getFallbackImage(50, 50)"></NAvatar>
    <NText class="text-xl">{{ shopInfo?.shopName }}</NText>
  </div>
</template>

<style scoped></style>
