<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-31 14:37:07
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-11 17:13:14
 * @FilePath: \tiksage-frontend\src\views\video-manage\product-list\index.vue
 * @Description: product-list
-->
<script setup lang="tsx">
import { reactive, ref, watch } from 'vue';
import { NAvatar, NEllipsis, NFlex, NSwitch } from 'naive-ui';
import { fetchGetProductList, fetchGetShopOptions, fetchUpdateShopProductStatus } from '@/service/api';
import { useTable } from '@/hooks/common/table';

const shopOptions = ref<Api.VideoManage.ShopOption[]>();

const statusOptions = reactive<CommonType.Option<number>[]>([
  {
    label: 'Non-designated',
    value: 0
  },
  {
    label: 'Designated',
    value: 1
  }
]);

type SeachParams = Pick<Api.VideoManage.ShopProductListSearchParams, 'shopId' | 'status' | 'product'>;

const selectParams = ref<SeachParams>({
  shopId: null,
  status: null,
  product: null
});

const { loading, data, getData, columns, pagination, updateSearchParams } = useTable({
  apiFn: fetchGetProductList,
  apiParams: {
    current: 1,
    size: 10
  },
  columns() {
    return [
      {
        key: 'productId',
        title: 'ID',
        align: 'center',
        width: 200
      },
      {
        key: 'shopName',
        title: 'Shop',
        align: 'center',
        width: 200
      },
      {
        key: 'productName',
        title: 'Product',
        align: 'center',
        render(rowData) {
          return (
            <NFlex class="w-full" align="center" wrap={false}>
              <NAvatar
                class="flex-shrink-0"
                src={`${import.meta.env.VITE_PRODUCT_AVATAR_URL}${rowData.productAvatarLocal}`}
              />
              <NEllipsis>{rowData.productName}</NEllipsis>
            </NFlex>
          );
        }
      },
      {
        key: 'operate',
        title: 'Designated Status',
        align: 'center',
        width: 150,
        render(rowData) {
          return (
            <NFlex justify="center" align="center">
              <NSwitch
                value={rowData.status}
                checkedValue={1}
                uncheckedValue={0}
                onUpdate:value={v => handleUpdateStatus(rowData.id, v)}
              >
                {/* {{
                  checked: () => ''
                }} */}
              </NSwitch>
            </NFlex>
          );
        }
      }
    ];
  }
});

function handleInputChange(value: string) {
  selectParams.value.product = value;
}

async function handleUpdateStatus(id: number, status: number) {
  const { error } = await fetchUpdateShopProductStatus({ id, status });
  if (!error) {
    getData();
  }
}

async function initData() {
  const { data: shopOptionsData, error } = await fetchGetShopOptions();
  if (error) return;
  shopOptions.value = shopOptionsData;
}

watch(
  () => selectParams.value,
  newVal => {
    updateSearchParams({ current: 1, ...newVal });
    getData();
  },
  {
    deep: true
  }
);

initData();
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="card-wrapper" :bordered="false" title="Operationally Designated Products"></NCard>
    <NCard class="h-full card-wrapper" content-class="flex-col gap-16px" :bordered="false">
      <NFlex justify="end" align="center" :wrap="false">
        <NSelect
          v-model:value="selectParams.shopId"
          style="width: 200px"
          :options="shopOptions"
          value-field="shopId"
          label-field="shopName"
          consistent-menu-width
          placeholder="Shop"
          clearable
        />
        <NSelect
          v-model:value="selectParams.status"
          style="width: 200px"
          :options="statusOptions"
          placeholder="Designated Status"
          clearable
        />
        <NInput style="width: 200px" placeholder="Product name or ID" clearable @change="handleInputChange"></NInput>
      </NFlex>
      <NDataTable
        class="h-full"
        size="small"
        remote
        :loading="loading"
        :data="data"
        :columns="columns"
        :pagination="pagination"
        :bordered="false"
        flex-height
      ></NDataTable>
    </NCard>
  </NFlex>
</template>

<style scoped></style>
