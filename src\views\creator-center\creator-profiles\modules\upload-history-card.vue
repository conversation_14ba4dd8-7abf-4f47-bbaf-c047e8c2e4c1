<script setup lang="tsx">
import { onUnmounted } from 'vue';
import { useEventBus } from '@vueuse/core';
import dayjs from 'dayjs';
import { isNil } from 'lodash-es';
import { fetchDownloadTaskFailedData, fetchGetCreatorCenterTaskList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { downloadFile } from '@/utils/download';
import ButtonIcon from '@/components/custom/button-icon.vue';
import { TimeFormat } from '@/enum';
import TaskProgress from '@/views/analysis/list/modules/task-progress.vue';

const bus = useEventBus<string>('refresh-task');

const { data, loading, pagination, columns, getData } = useTable({
  immediate: false,
  apiFn: fetchGetCreatorCenterTaskList,
  apiParams: {
    current: 1,
    size: 10
  },
  columns() {
    return [
      {
        key: 'fileName',
        title: 'File Name',
        width: 200,
        ellipsis: {
          tooltip: true
        }
      },
      {
        key: 'createTime',
        title: 'Create Time',
        align: 'center',
        width: 300,
        render(rowData) {
          const time = dayjs(rowData.createTime).tz('Etc/GMT+8').format(TimeFormat.US_TIME_24);
          return time || '-';
        }
      },
      {
        key: 'total',
        title: 'Total ',
        align: 'center'
      },
      {
        key: 'success',
        title: 'Success',
        align: 'center'
      },
      {
        key: 'fail',
        title: 'Failed',
        align: 'center'
      },
      {
        key: 'grabTaskStatus',
        title: 'Status',
        width: 150,
        align: 'center',
        render(rowData) {
          return <TaskProgress taskStatus={rowData.grabTaskStatus} />;
        }
      },
      {
        key: 'operate',
        width: 50,
        render(rowData) {
          const disabled = isNil(rowData.fail) || rowData.fail < 1;
          return (
            <div class="flex-y-center justify-end gap-16px">
              <ButtonIcon
                text
                quaternary={false}
                disabled={disabled}
                tooltipContent="Download Failed"
                tooltipPlacement="top-end"
                icon="solar:download-linear"
                onClick={() => handleDownloadFailedData(rowData.taskId)}
              ></ButtonIcon>
            </div>
          );
        }
      }
    ];
  }
});

const unsubscribe = bus.on(() => {
  getData();
});

async function handleDownloadFailedData(taskId: number) {
  const { data: failedXlsxData, error } = await fetchDownloadTaskFailedData(taskId);
  if (!error) {
    downloadFile(failedXlsxData, 'xlsx', 'Creator Import Failed Data');
  }
}

onUnmounted(() => {
  unsubscribe();
});
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" title="Import History">
    <template #header-extra>
      <ButtonIcon text :quaternary="false" :rotate="loading" icon="tabler:refresh" @click="getData" />
    </template>
    <NDataTable
      class="h-full"
      :bordered="false"
      remote
      :columns="columns"
      :data="data"
      :pagination="pagination"
      :loading="loading"
    ></NDataTable>
  </NCard>
</template>

<style scoped></style>
