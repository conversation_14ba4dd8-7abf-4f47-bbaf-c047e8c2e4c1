<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-16 21:49:47
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-07-24 15:52:54
 * @FilePath: \tiksage-frontend\src\views\home\modules\shop-button.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { computed, ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { useAuthStore } from '@/store/modules/auth';
import { useAppStore } from '@/store/modules/app';

interface Props {
  shopShow?: boolean;
}

type Model = CommonType.RecordNullable<{
  brandList: string[];
  marketList: string[];
  shopIdsArr: number[];
  [prototype: string]: any;
}>;

const props = withDefaults(defineProps<Props>(), {
  shopShow: true
});

const appStore = useAppStore();

const model = defineModel<Model>('value', { required: true });

const authStore = useAuthStore();

const displayShop = ref(model.value.shopIdsArr);

const displayMarket = ref(model.value.marketList);

const cascaderRef = ref();

const [showOption, toggleShowOption] = useToggle(false);

const selectedBrand = ref<string | null>(null);
const selectedMarket = ref<string | null>(null);
const selectedShop = ref<string | null>(null);

const handleUpdate = (value: (number | string)[], _option: any, pathValue: any) => {
  if (props.shopShow) {
    displayShop.value = value as number[];
  } else {
    displayMarket.value = value as string[];
  }
  let brand = [];
  let market = [];
  let shop = [];
  for (const options of pathValue) {
    brand.push(options[0].label);
    market.push(options[1].label);
    if (props.shopShow) {
      shop.push(options[2].label);
    }
  }

  brand = Array.from(new Set(brand));
  market = Array.from(new Set(market));
  shop = Array.from(new Set(shop));

  if (brand.length === 1) {
    selectedBrand.value = brand[0];
  } else {
    selectedBrand.value = brand.length ? `${brand.length} brands` : null;
  }
  if (market.length === 1) {
    selectedMarket.value = market[0];
  } else {
    selectedMarket.value = market.length ? `${market.length} markets` : null;
  }
  if (shop.length === 1) {
    selectedShop.value = shop[0];
  } else {
    selectedShop.value = shop.length ? `${shop.length} shops` : null;
  }
};

const handleBlur = () => {
  toggleShowOption();
  if (props.shopShow) {
    model.value.shopIdsArr = displayShop.value;
  } else {
    // this need change
    model.value.marketList = [];
  }
};

const handleClickBtn = () => {
  cascaderRef.value?.focus();
};

const options = computed(() => {
  if (props.shopShow) {
    return authStore.userInfo.selectOptions;
  }
  const opt = authStore.userInfo.selectOptions.map(v => {
    return {
      ...v,
      children: v.children?.map(m => {
        return {
          label: m.label,
          value: m.value
        };
      })
    };
  });
  return opt;
});
</script>

<template>
  <div class="relative">
    <NCascader
      ref="cascaderRef"
      :class="appStore.isMobile ? 'block opacity-100' : 'absolute left-0 top-0 z-0 opacity-0'"
      :value="props.shopShow ? displayShop : displayMarket"
      :show="showOption"
      :show-path="false"
      multiple
      :clearable="false"
      expand-trigger="hover"
      :max-tag-count="1"
      check-strategy="child"
      :cascade="true"
      :options="options"
      :on-focus="() => toggleShowOption()"
      :on-update:value="handleUpdate"
      :on-blur="handleBlur"
    />

    <!--
 <NButton v-if="appStore.isMobile" @click="handleClickBtn">
      <template #icon>
        <SvgIcon icon="tabler:building-store"></SvgIcon>
      </template>
      Select Shop
    </NButton> 
-->
    <NButtonGroup v-show="!appStore.isMobile" class="z-1">
      <NButton secondary class="flex justify-around" @click="handleClickBtn">
        <template #icon>
          <SvgIcon icon="tabler:world-pin"></SvgIcon>
        </template>
        {{ selectedBrand || 'All brands' }}
        <SvgIcon icon="solar:alt-arrow-down-linear" />
      </NButton>
      <NButton secondary class="flex justify-around" @click="handleClickBtn">
        <template #icon>
          <SvgIcon icon="tabler:shopping-cart-pin"></SvgIcon>
        </template>
        {{ selectedMarket || 'All markets' }}
        <SvgIcon icon="solar:alt-arrow-down-linear" />
      </NButton>
      <NButton v-if="props.shopShow" secondary class="flex justify-around" @click="handleClickBtn">
        <template #icon>
          <SvgIcon icon="tabler:building-store"></SvgIcon>
        </template>
        {{ selectedShop || 'All shops' }}
        <SvgIcon icon="solar:alt-arrow-down-linear" />
      </NButton>
    </NButtonGroup>
  </div>
</template>

<style scoped>
.n-button .n-button__content {
  flex: 1;
}
</style>
