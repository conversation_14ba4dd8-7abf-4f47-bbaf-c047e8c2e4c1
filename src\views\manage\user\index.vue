<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-05 11:03:34
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-10-22 16:07:42
 * @FilePath: \tiksage-frontend\src\views\manage\user\index.vue
 * @Description: user managment
-->
<script setup lang="tsx">
import { NAvatar, NButton, NFlex, NPopconfirm, NTag, NText } from 'naive-ui';
import { enableStatusRecord } from '@/constants/business';
import {
  fetchBatchDeleteUserByIds,
  fetchDeleteUserById,
  fetchGetUserList,
  fetchResetPasswordById
} from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { dateFormat } from '@/utils/date';
import callbackImg from '@/assets/svg-icon/avatar.svg';
import { $t } from '@/locales';
import UserOperateDrawer from './modules/user-operate-drawer.vue';
import UserSearch from './modules/user-search.vue';

const appStore = useAppStore();

const { columns, columnChecks, data, getData, loading, mobilePagination, searchParams, resetSearchParams } = useTable({
  apiFn: fetchGetUserList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    userName: null,
    tel: null,
    email: null,
    status: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'userName',
      title: $t('page.manage.user.userName'),
      align: 'center',
      width: 150,
      render(row) {
        return (
          <NFlex justify="center" align="center">
            <NAvatar round src={row.avatar} fallbackSrc={callbackImg} />
            <NText class="flex-1">{row.userName}</NText>
          </NFlex>
        );
      }
    },
    {
      key: 'email',
      title: $t('page.manage.user.userEmail'),
      align: 'center',
      minWidth: 200
    },
    {
      key: 'lastLoginDate',
      title: 'Last Login Date',
      align: 'center',
      minWidth: 200,
      render(rowData) {
        return <NText>{rowData.lastLoginDate && dateFormat(rowData.lastLoginDate)}</NText>;
      }
    },
    {
      key: 'lastLoginIp',
      title: 'Last Login Ip',
      align: 'center',
      minWidth: 200
    },
    {
      key: 'status',
      title: $t('page.manage.user.userStatus'),
      align: 'center',
      width: 100,
      render: row => {
        if (row.status === null) {
          return null;
        }

        const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
          0: 'success',
          1: 'warning'
        };

        const label = $t(enableStatusRecord[row.status]);

        return <NTag type={tagMap[row.status]}>{label}</NTag>;
      }
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 205,
      render: row => {
        const { status } = row;
        return (
          <div class="flex-center gap-8px">
            <NButton type="primary" ghost size="small" onClick={() => edit(row.id)}>
              {$t('common.edit')}
            </NButton>
            <NPopconfirm onPositiveClick={() => handleResetPwd(row.id)}>
              {{
                default: () => {
                  return status ? 'Reset Password & Resolve Issues' : 'Reset Password';
                },
                trigger: () => (
                  <NButton type="warning" ghost size="small">
                    Reset
                  </NButton>
                )
              }}
            </NPopconfirm>
            <NPopconfirm onPositiveClick={() => handleDelete(row.id)}>
              {{
                default: () => $t('common.confirmDelete'),
                trigger: () => (
                  <NButton type="error" ghost size="small">
                    {$t('common.delete')}
                  </NButton>
                )
              }}
            </NPopconfirm>
          </div>
        );
      }
    }
  ]
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  onBatchDeleted,
  onDeleted
  // closeDrawer
} = useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  const ids = checkedRowKeys.value.map(v => Number(v));
  const { error } = await fetchBatchDeleteUserByIds(ids);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: number) {
  // request
  const { error } = await fetchDeleteUserById(id);
  if (error) return;
  onDeleted();
}

async function handleResetPwd(id: number) {
  // request
  const { data: resetPwdData, error } = await fetchResetPasswordById(id);
  if (error) return;
  window.$message?.success(
    `After successfully resetting your password, the system has prompted that your new password is the default one: ${resetPwdData.password}.`
  );
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <UserSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getData" />
    <NCard :title="$t('page.manage.user.title')" :bordered="false" size="small" class="card-wrapper sm:flex-1-hidden">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <UserOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
