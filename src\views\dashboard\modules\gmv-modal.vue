<script setup lang="ts">
import { watch } from 'vue';
import { useToggle } from '@vueuse/core';
import dayjs from 'dayjs';
import { fetchGetProductGMVTrendData } from '@/service/api';
import { useVChart } from '@/hooks/common/vchart';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat, TimeFormat } from '@/enum';

interface Props {
  productId?: string;
  shopId?: number;
}

const props = defineProps<Props>();

const modelShow = defineModel('show', {
  type: Boolean,
  default: false
});

const [loading, toggleLoading] = useToggle(false);

const { numberFormat } = useNumberFormat();

const defaultSpec: Visactor.VChart.IAreaChartSpec = {
  type: 'area',
  padding: 0,
  data: [],
  seriesField: 'type',
  xField: 'x',
  yField: 'y',
  stack: false,
  // color: ['#a855f7'],
  point: {
    visible: false
  },
  line: {
    style: {
      curveType: 'monotone'
    }
  },
  tooltip: {
    dimension: {
      title: {
        value: datum => {
          return dayjs(datum?.x).format(TimeFormat.US_DATE);
        }
      },
      content: {
        key: v => {
          return v ? (v?.type as string) : '';
        },
        value: datum => {
          return numberFormat(datum?.y, NumeralFormat.Dollar);
        }
      }
    }
  },
  axes: [
    {
      orient: 'bottom',
      visible: true,
      label: {
        formatMethod(text: any) {
          return dayjs(text).format(TimeFormat.US_DATE);
        }
      }
    },
    {
      id: 'left',
      orient: 'left',
      visible: true,
      // title: {
      //   visible: true,
      //   text: 'GMV'
      // },
      label: {
        formatMethod: v => {
          return numberFormat(v, NumeralFormat.Dollar);
        }
      }
    }
  ],
  area: {
    style: {
      fill: {
        gradient: 'linear',
        x0: 0.5,
        y0: 0,
        x1: 0.5,
        y1: 1,
        stops: [
          {
            offset: 0,
            opacity: 1
          },
          {
            offset: 1,
            opacity: 0
          }
        ]
      }
    }
  },
  legends: {
    orient: 'top',
    visible: true
  }
};

const { domRef: historyAreaRef, updateSpec: updateHistorySpec } = useVChart(() => defaultSpec);

function formatData(data: Api.Dashboard.ProductTrend[]) {
  const values: any[] = data.map(i => ({ x: i.belongDate, type: 'GMV', y: i.gmv }));
  return [
    {
      id: '1',
      values
    }
  ];
}

async function initData() {
  if (!props.productId || !props.shopId) return;
  toggleLoading(true);
  const { data, error } = await fetchGetProductGMVTrendData(props.productId, props.shopId);

  if (!error) {
    setTimeout(() => {
      updateHistorySpec(oldOpts => {
        return { ...oldOpts, data: formatData(data) };
      });
    });
  }
  toggleLoading(false);
}

watch(
  () => modelShow.value,
  newVal => {
    if (newVal) {
      initData();
    } else {
      updateHistorySpec(oldOpts => {
        return { ...oldOpts, data: [] };
      });
    }
  }
);
</script>

<template>
  <NModal v-model:show="modelShow" class="w-848px" preset="card" closable>
    <template #header>Product Performance</template>
    <NSpin :show="loading">
      <div class="flex-col gap-4">
        <div ref="historyAreaRef" class="h-400px w-800px"></div>
      </div>
    </NSpin>
  </NModal>
</template>

<style scoped></style>
