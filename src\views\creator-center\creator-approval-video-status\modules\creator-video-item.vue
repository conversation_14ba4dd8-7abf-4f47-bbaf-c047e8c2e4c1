<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-12-03 13:30:33
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-12-06 10:06:04
 * @FilePath: \tiksage-frontend\src\views\creator-manage\video-status\modules\creator-video-item.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="tsx">
import { computed } from 'vue';
import { NFlex, NSwitch } from 'naive-ui';
import dayjs from 'dayjs';
import { fetchUpdateAdCode, fetchUpdateClientVisible, fetchUpdateNote } from '@/service/api';
import { useAuth } from '@/hooks/business/auth';
import { getFallbackImage } from '@/utils/fake-image';
import ButtonCopy from '@/components/custom/button-copy.vue';
import ButtonIcon from '@/components/custom/button-icon.vue';
import { TimeFormat } from '@/enum';
import ShowOrEdit from './showOrEdit.vue';
import type { VideoStatus } from './share';

const { VITE_CREATOR_AVATAR_URL, VITE_PRODUCT_AVATAR_URL } = import.meta.env;

const { hasAuth } = useAuth();

type ProductStatus = {
  type: NaiveUI.ThemeColor;
  label: string;
};

const productStatus: { [key in VideoStatus]?: ProductStatus } & { [key: number]: ProductStatus | undefined } = {
  10: {
    type: 'default',
    label: 'Invite for Sample'
  },
  20: {
    type: 'warning',
    label: 'Shipping samples'
  },
  40: {
    type: 'warning',
    label: 'Video Pending'
  },
  51: {
    type: 'error',
    label: 'Rejected'
  },
  52: {
    type: 'error',
    label: 'Expired'
  },
  53: {
    type: 'error',
    label: 'Unfulfilled'
  },
  57: {
    type: 'error',
    label: 'Canceled'
  },
  100: {
    type: 'success',
    label: 'Video Post'
  }
};

interface Emits {
  (e: 'update'): void;
}

const emit = defineEmits<Emits>();

interface Props {
  data: Api.CreatorManage.CreatorProductVideo;
}

const props = defineProps<Props>();

function handleLinkTo(link: string) {
  window.open(link, '_blank');
}

const baseColumns: NaiveUI.DataTableColumns<Api.CreatorManage.CreatorSampleVideo> = [
  {
    key: 'contentType',
    title: '',
    width: 50,
    render(rowData) {
      return (
        <div class="flex-center">
          {rowData.contentType === 1 ? (
            <ButtonIcon
              type="primary"
              text
              quaternary={false}
              tooltipPlacement="top"
              tooltipContent="Live"
              icon="solar:play-stream-bold-duotone"
            />
          ) : (
            <ButtonIcon
              type="primary"
              text
              quaternary={false}
              tooltipPlacement="top"
              tooltipContent="Video"
              icon="solar:videocamera-record-bold-duotone"
            />
          )}
        </div>
      );
    }
  },
  {
    key: 'videoDesc',
    title: 'Title',
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width:400px'
      }
    }
  },
  {
    key: 'releaseTimestamp',
    align: 'center',
    title: 'Time',
    width: 260,
    render(rowData) {
      if (rowData.contentType === 1) {
        return (
          <div class="flex-col-center text-sm">
            <span>{dayjs.tz(rowData.releaseTimestamp, 'Etc/GMT+8').format(TimeFormat.US_TIME_24)} ~</span>
            <span>{dayjs.tz(rowData.finishTimestamp, 'Etc/GMT+8').format(TimeFormat.US_TIME_24)}</span>
          </div>
        );
      }
      return dayjs.tz(rowData.releaseTimestamp, 'Etc/GMT+8').format(TimeFormat.US_TIME_24);
    }
  },
  {
    key: 'contentId',
    align: 'center',
    title: 'TikTok Link',
    render(rowData) {
      if (rowData.contentType === 1) {
        return '--';
      }
      const tiktokLink = `https://www.tiktok.com/@${props.data.creatorName}/video/${rowData.contentId}`;
      return (
        <NFlex justify="center" size={16} wrap={false}>
          <ButtonIcon
            text
            quaternary={false}
            icon="logos:tiktok-icon"
            tooltipContent="TikTok"
            tooltipPlacement="top"
            onClick={() => handleLinkTo(tiktokLink)}
          />
          <ButtonCopy copy={tiktokLink} />
        </NFlex>
      );
    }
  }
];

const columsByOperator = computed(() => {
  return baseColumns.concat([
    {
      key: 'adCode',
      align: 'center',
      title: 'Ad Code',
      render(rowData) {
        if (rowData.contentType === 1) {
          return '--';
        }
        return <ShowOrEdit value={rowData.adCode} onUpdate={v => handleUpdateAdCode(rowData.contentId, v)} />;
      }
    },
    {
      key: 'note',
      align: 'center',
      title: 'Note',
      render(rowData) {
        return <ShowOrEdit value={rowData.note} onUpdate={v => handleUpdateNote(rowData.contentId, v)} />;
      }
    },
    {
      key: 'clientVisible',
      align: 'center',
      title: 'Client Visible',
      render(rowData) {
        return (
          <NSwitch
            value={rowData.clientVisible}
            checkedValue={1}
            uncheckedValue={0}
            onUpdateValue={v => handleUpdateClientVisible(rowData.contentId, v)}
          ></NSwitch>
        );
      }
    }
  ]);
});

const columnsByApprover = computed(() => {
  return baseColumns;
});

async function handleUpdateAdCode(contentId: string, adCode: string) {
  const { error } = await fetchUpdateAdCode({ contentId, adCode: adCode.trim() });
  if (!error) {
    window.$message?.success('AdCode updated successfully!');
    emit('update');
  }
}

async function handleUpdateNote(contentId: string, note: string) {
  const { error } = await fetchUpdateNote({ contentId, note: note.trim() });
  if (!error) {
    window.$message?.success('Note updated successfully!');
    emit('update');
  }
}

async function handleUpdateClientVisible(contentId: string, status: boolean) {
  const { error } = await fetchUpdateClientVisible({ contentId, status: Number(status) });
  if (!error) {
    window.$message?.success('Client Visible updated successfully!');
    emit('update');
  }
}
</script>

<template>
  <NFlex vertical :wrap="false" :size="16">
    <div class="flex items-center gap-16px rounded-xl bg-gray-1 p-16px">
      <icon-tabler:chevron-down />
      <NAvatar
        round
        :src="`${VITE_CREATOR_AVATAR_URL}${data.creatorAvatarLocal}`"
        :fallback-src="getFallbackImage(50, 50)"
      />
      <span>{{ data.creatorName }}</span>
    </div>
    <!-- <NCollapseTransition :show="true"> -->
    <div
      v-for="productInfo in data.productList"
      :key="productInfo.productName"
      class="m-b-16px flex-col gap-16px p-l-48px"
    >
      <NFlex class="rounded-xl bg-gray-1 p-8px" align="center" :wrap="false">
        <NImage
          width="50px"
          height="50px"
          preview-disabled
          :src="`${VITE_PRODUCT_AVATAR_URL}${productInfo.skuAvatar}`"
          :fallback-src="getFallbackImage(50, 50)"
        />
        <NEllipsis
          style="max-width: 600px"
          :tooltip="{
            contentStyle: 'max-width:400px'
          }"
        >
          {{ productInfo.productName }}
        </NEllipsis>
        <div class="flex items-center gap-8px p-l-16px">
          <NTag round :type="productStatus[productInfo.productStatus]?.type">
            {{ productStatus[productInfo.productStatus]?.label }}
          </NTag>
        </div>
      </NFlex>
      <NDataTable
        v-if="productInfo.videoList.length"
        size="small"
        :bordered="false"
        :columns="hasAuth('video-status:operator') ? columsByOperator : columnsByApprover"
        :data="productInfo.videoList"
      ></NDataTable>
      <span v-else class="text-coolgray">No videos or live streams available.</span>
    </div>
    <!-- </NCollapseTransition> -->
  </NFlex>
</template>

<style scoped></style>
