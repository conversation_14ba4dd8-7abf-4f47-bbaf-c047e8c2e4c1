<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-29 15:03:53
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-07-29 15:12:42
 * @FilePath: \tiksage-frontend\src\components\custom\card-title.vue
 * @Description: card-title
-->
<script setup lang="ts">
interface Props {
  title: string;
}
defineProps<Props>();

defineSlots();
</script>

<template>
  <NFlex justify="space-between">
    <NFlex>
      <NText class="text-size-18px font-500">{{ title }}</NText>
    </NFlex>
    <NFlex :size="16">
      <slot name="actions"></slot>
    </NFlex>
  </NFlex>
</template>

<style scoped></style>
