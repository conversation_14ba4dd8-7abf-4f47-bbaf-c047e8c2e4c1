<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-26 14:17:14
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-17 14:58:22
 * @FilePath: \tiksage-frontend\src\views\category-intelligence\modules\indicator-card.vue
 * @Description: indicator-card
-->
<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { toNumber } from 'lodash-es';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import type { NumeralFormat } from '@/enum';

interface Props {
  metric: {
    key: string;
    title: string;
    description: string;
    value: number;
    unit: NumeralFormat;
    icon: string;
    decimals: number;
  };
}
const props = defineProps<Props>();

const { numberFormat, extractParts } = useNumberFormat();

const prefix = ref('');
const showNum = ref(0);
const suffix = ref('');
watchEffect(() => {
  const num = numberFormat(props.metric.value, props.metric.unit);
  const parts = extractParts(num);
  prefix.value = parts?.prefix || '';
  showNum.value = toNumber(parts?.number) || 0;
  suffix.value = parts?.suffix || '';
});
</script>

<template>
  <NFlex class="flex-1 p-20px" vertical align="center" :wrap="false">
    <NIconWrapper :size="58" class="bg-white/50 text-dark">
      <SvgIcon class="h-30px w-30px text-primary" :icon="props.metric.icon" />
    </NIconWrapper>
    <NFlex vertical>
      <NFlex justify="center" align="center" :wrap="false">
        <NText class="text-sm text-gray">{{ props.metric.title }}</NText>
        <Tip v-if="metric.description.length" :description="props.metric.description" />
      </NFlex>
      <CountTo
        class="text-center text-2xl font-semibold"
        :prefix="prefix"
        :start-value="0"
        :end-value="showNum"
        :suffix="suffix"
        :decimals="props.metric.decimals"
      />
    </NFlex>
  </NFlex>
</template>

<style scoped lang="scss">
#indicator-container {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  background-image: url(/src/assets/svg-icon/metric-bg.svg);
}
</style>
