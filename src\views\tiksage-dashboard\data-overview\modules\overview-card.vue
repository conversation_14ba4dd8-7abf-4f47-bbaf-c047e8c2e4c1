<script setup lang="ts">
import { computed, watch } from 'vue';
import GmvPie<PERSON>hart from './gmv-pie-chart.vue';
import { useOverviewData } from './shared';

interface Props {
  dateRange: string[] | undefined;
}
const props = defineProps<Props>();

type IndicatorsType = CommonType.Component.Metric<string>[];

const { realData, fetchData } = useOverviewData();

// eslint-disable-next-line complexity
const coreDataIndicators = computed<IndicatorsType>(() => {
  return [
    {
      key: '',
      title: realData.value?.sellerGmvCount
        ? `Your sellers (${realData.value?.sellerGmvCount} with GMV)`
        : 'Your sellers',
      value: realData.value?.sellerCount || 0,
      description: '',
      unit: '',
      icon: 'solar:shop-2-bold-duotone',
      decimals: 0,
      isConversion: false
    },
    {
      key: '',
      title: 'Current est. commission',
      value: realData.value?.currentData.estCommission || 0,
      description: '',
      unit: '$0.[00]a',
      icon: 'solar:hand-money-bold-duotone',
      decimals: 2,
      isConversion: false
    },
    {
      key: '',
      title: 'Total GMV',
      value: realData.value?.currentData.gmvTotal || 0,
      prevValue: realData.value?.previousData.gmvTotal || 0,
      description: '',
      unit: '$0.[00]a',
      icon: 'solar:dollar-minimalistic-bold-duotone',
      decimals: 2,
      isConversion: false,
      conversionDescription: 'Vs.previous period'
    },
    {
      key: '',
      title: 'Total units sold',
      value: realData.value?.currentData.unitSales || 0,
      prevValue: realData.value?.previousData.unitSales || 0,
      description: '',
      unit: '',
      icon: 'solar:box-bold-duotone',
      decimals: 0,
      isConversion: false,
      conversionDescription: 'Vs.previous period'
    },
    {
      key: '',
      title: 'Total video GMV',
      value: realData.value?.currentData.gmvVideo || 0,
      prevValue: realData.value?.previousData.gmvVideo || 0,
      description: '',
      unit: '$0.[00]a',
      icon: 'solar:play-bold-duotone',
      decimals: 2,
      isConversion: false,
      conversionDescription: 'Vs.previous period'
    },
    {
      key: '',
      title: 'Total LIVE GMV',
      value: realData.value?.currentData.gmvLive || 0,
      prevValue: realData.value?.previousData.gmvLive || 0,
      description: '',
      unit: '$0.[00]a',
      icon: 'solar:play-stream-bold-duotone',
      decimals: 2,
      isConversion: false,
      conversionDescription: 'Vs.previous period'
    },
    {
      key: '',
      title: 'Total product card GMV',
      value: realData.value?.currentData.gmvProductCard || 0,
      prevValue: realData.value?.previousData.gmvProductCard || 0,
      description: '',
      unit: '$0.[00]a',
      icon: 'solar:card-bold-duotone',
      decimals: 2,
      isConversion: false,
      conversionDescription: 'Vs.previous period'
    },
    {
      key: '',
      title: 'Affiliate GMV',
      value: realData.value?.currentData.gmvAffiliate || 0,
      prevValue: realData.value?.previousData.gmvAffiliate || 0,
      description: '',
      unit: '$0.[00]a',
      icon: 'solar:users-group-rounded-bold-duotone',
      decimals: 2,
      isConversion: false,
      conversionDescription: 'Vs.previous period'
    }
  ];
});

async function initData(params: Api.TikSageDashboard.DateParams) {
  await fetchData(params);
}

watch(
  () => props.dateRange,
  newVal => {
    if (!newVal) return;
    const [startDateStr, endDateStr, perviousStartDateStr, perviousEndDateStr] = newVal;
    initData({
      startDateStr,
      endDateStr,
      perviousStartDateStr,
      perviousEndDateStr
    });
  },
  {
    immediate: true
  }
);
</script>

<template>
  <div class="flex-col gap-4">
    <NGrid :cols="4" :x-gap="16" :y-gap="16">
      <NGi v-for="item in coreDataIndicators" :key="item.title">
        <IndicatorCard class="h-full" :metric="item" />
      </NGi>
    </NGrid>
    <GmvPieChart :real-data="realData" />
  </div>
</template>

<style scoped></style>
