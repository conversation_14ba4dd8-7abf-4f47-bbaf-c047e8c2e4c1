<script setup lang="ts">
import { computed } from 'vue';
import { useVChart } from '@/hooks/common/vchart';

interface Props {
  score: number;
}

const props = defineProps<Props>();

const gaugeSpec = computed<Visactor.VChart.IChartSpec>(() => {
  return {
    type: 'common',
    padding: 0,
    data: [
      {
        id: 'pointer',
        values: [
          {
            type: 'A',
            value: props.score
          }
        ]
      },
      {
        id: 'segment',
        values: [
          {
            type: 'Level 1',
            value: 60
          },
          {
            type: 'Level 2',
            value: 80
          },
          {
            type: 'Level 3',
            value: 100
          }
        ]
      }
    ],
    series: [
      {
        type: 'gauge',
        dataIndex: 1,
        radiusField: 'type',
        angleField: 'value',
        seriesField: 'type',
        outerRadius: 0.9,
        innerRadius: 0.65,
        roundCap: true,
        tooltip: {
          visible: false
        },
        segment: {
          style: {
            cornerRadius: 500,
            innerPadding: 10,
            outerPadding: 10,
            fill: {
              type: 'threshold',
              field: 'value',
              domain: [60.1, 80.1],
              range: ['#FFD666', '#C680FF', '#95DC64']
            }
          }
        },
        track: {
          visible: true,
          style: {
            cornerRadius: 500,
            roundCap: true,
            fill: 'rgba(0, 0, 0, 0.1)'
          }
        }
      },
      {
        type: 'gaugePointer',
        dataIndex: 0,
        categoryField: 'type',
        valueField: 'value',
        innerRadius: 0.45,
        pin: {
          visible: true,
          width: 0.04,
          height: 0.04,
          isOnCenter: false,
          style: {
            fill: {
              type: 'threshold',
              field: 'value',
              domain: [60, 80],
              range: ['#FFD666', '#C680FF', '#95DC64']
            }
          }
        },
        pinBackground: {
          visible: false
        },
        pointer: {
          width: 0.2,
          height: 0.1,
          isOnCenter: false,
          style: {
            fill: {
              type: 'threshold',
              field: 'value',
              domain: [60, 80],
              range: ['#FFD666', '#C680FF', '#95DC64']
            }
          }
        },
        animation: false
      }
    ],
    startAngle: -200,
    endAngle: 20,
    axes: [
      {
        type: 'linear',
        orient: 'angle',
        inside: true,
        outerRadius: 0.9,
        innerRadius: 0.6,
        min: 0,
        max: 100,
        grid: { visible: false },
        tick: { visible: true, tickSize: 0, style: { lineWidth: 4, lineCap: 'round' } },
        subTick: { visible: true, tickSize: 0, style: { lineWidth: 4, lineCap: 'round' } },
        label: { visible: false }
      },
      {
        type: 'linear',
        orient: 'radius',
        outerRadius: 0.6,
        grid: { visible: false },
        label: { visible: false }
      }
    ],
    indicator: [
      {
        visible: true,
        offsetY: '15%',
        title: {
          style: {
            text: String(props.score || 0),
            fontSize: 70,
            fontWeight: 800
          }
        },
        content: [
          {
            style: {
              dy: 10,
              text: 'Score',
              fontSize: 24
            }
          }
        ]
      },
      {
        visible: true,
        offsetX: '-70%',
        offsetY: '45%',
        title: {
          style: {
            text: '0',
            fontSize: 18
          }
        }
      },
      {
        visible: true,
        offsetX: '70%',
        offsetY: '45%',
        title: {
          style: {
            text: '100',
            fontSize: 18
          }
        }
      }
    ]
  };
});

const { domRef: gaugeRef } = useVChart(() => gaugeSpec.value);
</script>

<template>
  <div ref="gaugeRef" class="h-300px w-full"></div>
</template>

<style scoped></style>
