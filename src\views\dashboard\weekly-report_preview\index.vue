<script setup lang="ts">
import { ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { fetchExportWeeklyReport, fetchGetWeeklyReportDataByViewer, fetchGetWeeklyReportDateList } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useUserStore } from '@/store/modules/user';
import { downloadFile } from '@/utils/download';
import OverallPerformanceEdit from '../weekly-report_generate/modules/overall-performance-edit.vue';
import AffiliateEdit from '../weekly-report_generate/modules/affiliate-edit.vue';
import AdsEdit from '../weekly-report_generate/modules/ads-edit.vue';
import CampaignEdit from '../weekly-report_generate/modules/campaign-edit.vue';
import ShopInfoCard from '../modules/shop-info-card.vue';
const shopOptions = ref<CommonType.Option<number>[]>([]);

const authStore = useAuthStore();
const userStore = useUserStore();

const shopId = ref<number>(0);
const reportId = ref<number | null>(null);
const weeklyReportOptions = ref<CommonType.Option<number>[]>([]);

const [loading, toggleLoading] = useToggle(true);
const [empty, toggleEmpty] = useToggle(false);
const reportData = ref<Api.WeeklyReport.WeeklyReportData>();

async function handleExportPDF() {
  if (!reportId.value) return;

  const weekNumber = reportData.value?.week.split('-')[1].replace(/\D/g, '');
  const { data: exportData, error: exportErr } = await fetchExportWeeklyReport(reportId.value);
  if (!exportErr) {
    window.$message?.success('Export Successfully.');
    downloadFile(exportData, 'pdf', `Weekly Report For ${reportData.value?.shopName} - Week ${weekNumber}`, false);
  }
}

function handleChangeShop(value: number) {
  shopId.value = value;
  initShopReport();
}

async function initShopOptions() {
  const userShops = await userStore.getUserShop();

  shopOptions.value = userShops.map(item => ({ label: item.shopName, value: item.shopId }));

  shopId.value = userShops[0].shopId;
}

async function initWeeklyReportList() {
  const { data: reportList, error: listErr } = await fetchGetWeeklyReportDateList(shopId.value);
  if (!listErr) {
    weeklyReportOptions.value = reportList.map(item => ({ label: item.week, value: item.id }));

    if (!reportList.length) {
      toggleEmpty(true);
      reportId.value = null;
      return;
    }
    toggleEmpty(false);

    reportId.value = reportList[0]?.id;
  }
}

async function initReportData() {
  if (!reportId.value) return;
  toggleLoading(true);

  const { data: originalData, error: originalDataErr } = await fetchGetWeeklyReportDataByViewer(reportId.value);
  if (!originalDataErr) {
    reportData.value = { ...originalData };
  }
  toggleLoading(false);
}

async function initShopReport() {
  toggleLoading(true);

  await initWeeklyReportList();
  toggleLoading(false);
}

async function initData() {
  toggleLoading(true);
  await initShopOptions();

  await initShopReport();
  toggleLoading(false);
}

initData();

watch(
  () => reportId.value,
  () => {
    if (!reportId.value) return;
    initReportData();
  },
  {
    immediate: true
  }
);
</script>

<template>
  <div class="flex-col gap-4">
    <NCard class="card-wrapper" :bordered="false" title="Weekly Report">
      <template #header-extra>
        <div class="flex-col gap-4">
          <NPopselect
            :value="shopId"
            :disabled="authStore.userInfo.userShopIds.length <= 1"
            :options="shopOptions"
            @update:value="handleChangeShop"
          >
            <NButton quaternary>
              <div class="flex items-center justify-end">
                <ShopInfoCard :id="shopId" />
                <icon-solar:alt-arrow-down-line-duotone v-if="authStore.userInfo.userShopIds.length > 1" />
              </div>
            </NButton>
          </NPopselect>
        </div>
      </template>
    </NCard>
    <NCard class="h-full min-h-400px card-wrapper" content-class="flex-col" segmented :bordered="false" title=" ">
      <template #header-extra>
        <div class="flex justify-end gap-4">
          <NButtonGroup>
            <NButton type="primary" secondary>Week</NButton>
            <NSelect
              v-model:value="reportId"
              class="custom-select min-w-120px"
              placeholder="Select Week"
              :consistent-menu-width="false"
              :options="weeklyReportOptions"
            ></NSelect>
          </NButtonGroup>

          <NButton secondary :disabled="!reportId" @click="handleExportPDF">
            <template #icon>
              <icon-solar:export-linear />
            </template>
            Export
          </NButton>
        </div>
      </template>
      <NSpin class="h-full" content-class="flex-col min-h-400px h-full" :show="loading">
        <NEmpty v-if="empty" class="m-auto">
          <template #icon>
            <icon-solar:documents-linear />
          </template>
          <div class="flex-col-center gap-2">
            <span>No weekly reports available for this shop.</span>
            <span class="text-sm text-gray-500">Weekly reports will appear here once they are published.</span>
          </div>
        </NEmpty>
        <NTabs v-else type="segment" animated>
          <NTabPane name="Overall Performance">
            <OverallPerformanceEdit is-show :report-data="reportData" />
          </NTabPane>
          <NTabPane name="Affiliates">
            <AffiliateEdit is-show :report-data="reportData" />
          </NTabPane>
          <NTabPane name="Ads">
            <AdsEdit is-show :report-data="reportData" />
          </NTabPane>
          <NTabPane name="Campaigns">
            <CampaignEdit is-show :report-data="reportData" />
          </NTabPane>
        </NTabs>
      </NSpin>
    </NCard>
  </div>
</template>

<style scoped lang="scss">
:deep(.custom-select .n-base-selection) {
  border-radius: 0 6px 6px 0 !important;
}

:deep(.n-data-table .n-data-table-th) {
  background-color: #eaf4ff !important;
  color: #5a5a5a !important;
}

:deep(.n-data-table .n-data-table-td.n-data-table-td--summary) {
  background-color: #ffffff !important;
}
</style>
