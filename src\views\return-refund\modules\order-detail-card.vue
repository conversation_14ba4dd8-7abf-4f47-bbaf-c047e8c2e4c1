<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-11-04 16:35:09
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-19 10:57:07
 * @FilePath: \tiksage-frontend\src\views\return-refund\modules\order-detail-card.vue
 * @Description: order-detail-card
-->
<script setup lang="tsx">
import { computed, ref } from 'vue';
import { NFlex, NGi, NGrid, NTag } from 'naive-ui';
import { fetchExportRRDetailExcel, fetchGetReturnOrderDetailByPage } from '@/service/api';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { useTable } from '@/hooks/common/table';
import { dateFormat } from '@/utils/date';
import { downloadFile } from '@/utils/download';
import Tip from '@/components/custom/tip.vue';
import ProductDetailLine from './product-detail-line.vue';

const Type: any = {
  2: 'Refund only',
  3: 'Return and refund',
  5: 'Exchange only'
};

interface Props {
  params: Api.ReturnRefund.OverviewSearchParams;
}

const props = defineProps<Props>();

const { getDictionaryByCodeType } = useDictionaryStore();

const returnStatusOptions = ref<Api.Dictionary.DictionaryItem<string>[]>([]);

const orderStatusOptions = ref<Api.Dictionary.DictionaryItem<string>[]>([]);

const { data, getData, loading, columns, pagination, updateSearchParams } = useTable({
  apiFn: fetchGetReturnOrderDetailByPage,
  apiParams: {
    current: 1,
    size: 10,
    startTimeStamp: props.params.startTimeStamp,
    endTimeStamp: props.params.endTimeStamp,
    shopId: props.params.shopId
  },
  columns() {
    return [
      {
        type: 'expand',
        expandable: () => true,
        renderExpand(rowData) {
          return (
            <NGrid xGap={16} yGap={16} class="divide-x" cols={3} itemResponsive={true}>
              {rowData?.productList?.map(p => (
                <>
                  <NGi>
                    <ProductDetailLine product={p} />
                  </NGi>
                </>
              ))}
            </NGrid>
          );
        }
      },
      // {
      //   key: 'reverseId',
      //   align: 'center',
      //   title: 'Return Order ID'
      // },
      {
        key: 'orderId',
        align: 'center',
        title: 'Order ID',
        width: 170
      },
      {
        key: 'reverseType',
        align: 'center',
        title: 'Type',
        render(rowData) {
          return rowData.reverseType ? (
            <NTag size="small" bordered={false}>
              {Type[rowData.reverseType]}
            </NTag>
          ) : (
            ''
          );
        }
      },
      // {
      //   key: 'requestStatus',
      //   align: 'center',
      //   title: 'Status',
      //   render(rowData) {
      //     const { requestStatus } = rowData;
      //     return requestStatus ? (
      //       <NTag type={requestStatus.includes('complete') ? 'success' : 'primary'}>{requestStatus}</NTag>
      //     ) : (
      //       ''
      //     );
      //   }
      // },
      {
        key: 'orderStatus',
        align: 'center',
        title() {
          return (
            <NFlex justify="center" align="center" wrap={false}>
              <span>Order Status</span>
              <Tip description="Fulfillment status for this order." />
            </NFlex>
          );
        },
        render(rowData) {
          const dic = orderStatusOptions.value.find(v => v.code === rowData.orderStatus);

          return (
            <NTag size="small" bordered={false}>
              <div class="flex-center gap-2">
                <span>{dic?.name || '-'}</span>
                {dic?.description ? <Tip description={dic?.description} /> : ''}
              </div>
            </NTag>
          );
        }
      },
      {
        key: 'reverseStatus',
        align: 'center',
        title() {
          return (
            <NFlex justify="center" align="center" wrap={false}>
              <span>Return Status</span>
              <Tip description="Status of return request for this order." />
            </NFlex>
          );
        },
        render(rowData) {
          const dic = returnStatusOptions.value.find(v => v.code === rowData.reverseStatus);

          return (
            <NTag size="small" bordered={false}>
              <div class="flex-center gap-2">
                <span>{dic?.name || '-'}</span>
                {dic?.description ? <Tip description={dic?.description} /> : ''}
              </div>
            </NTag>
          );
        }
      },
      {
        key: 'reverseTime',
        title: 'Order Time',
        align: 'center',
        width: 280,
        render(rowData) {
          return dateFormat(rowData.reverseTime);
        }
      }
    ];
  }
});

const expandRowKeys = computed(() => {
  return data.value.map(v => v.reverseId);
});

function handleSearch(value: string) {
  updateSearchParams({ current: 1, orderId: value });
  getData();
}

async function handleDownload() {
  const { data: fileData, error } = await fetchExportRRDetailExcel(props.params);
  if (error) return;
  downloadFile(fileData, 'xlsx', 'Return & Refund Orders');
}

async function getReturnStatusDic() {
  const returnStatusRes = await getDictionaryByCodeType<string>('return_status');
  if (returnStatusRes) {
    returnStatusOptions.value = returnStatusRes;
  }
}

async function getOrderStatusDic() {
  const orderStatusRes = await getDictionaryByCodeType<string>('order_status');
  if (orderStatusRes) {
    orderStatusOptions.value = orderStatusRes;
  }
}

function init() {
  getReturnStatusDic();
  getOrderStatusDic();
}

init();
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" title="Returns/Refunds Order Detail">
    <template #header-extra>
      <NFlex align="center" :size="16" :wrap="false">
        <NInput placeholder="Order ID" clearable @change="handleSearch">
          <template #prefix>
            <icon-tabler:search class="text-gray" />
          </template>
        </NInput>
        <ButtonIcon
          icon="solar:download-linear"
          tooltip-content="Export"
          tooltip-placement="top"
          @click="handleDownload"
        />
      </NFlex>
    </template>
    <NDataTable
      remote
      :expanded-row-keys="expandRowKeys"
      :loading="loading"
      :min-height="400"
      :bordered="false"
      size="small"
      :data="data"
      :columns="columns"
      :pagination="pagination"
      :row-key="rowData => rowData.reverseId"
    ></NDataTable>
  </NCard>
</template>

<style scoped></style>
