<script setup lang="ts">
import type { RouteKey } from '@elegant-router/types';
import { useRouterPush } from '@/hooks/common/router';

const { routerPushByKey } = useRouterPush();

const buttons: {
  icon: string;
  text: string;
  route: RouteKey;
}[] = [
  {
    icon: 'mingcute:mail-ai-fill',
    text: 'Email',
    route: 'creator-center_creator-outreach_history_email'
  },
  {
    icon: 'mage:tiktok-circle',
    text: 'TikTok',
    route: 'creator-center_creator-outreach_find-creator'
  }
];

const handleClick = (route: RouteKey) => {
  routerPushByKey(route);
};
</script>

<template>
  <div class="flex-col gap-16px">
    <div class="flex-center py-30 text-4xl font-bold">Contact Your Creators</div>
    <div class="flex-center gap-4">
      <div
        v-for="button in buttons"
        :key="button.route"
        class="inline-flex items-center gap-16px border-1px border-dark rounded-full bg-dark px-6 py-2 text-white hover:(cursor-pointer bg-transparent bg-white text-dark)"
        @click="handleClick(button.route)"
      >
        <SvgIcon class="text-2xl" :icon="button.icon" />
        <div class="flex-col">
          <span class="mb-1 text-xs">BY</span>
          <span class="text-base font-bold">{{ button.text }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
