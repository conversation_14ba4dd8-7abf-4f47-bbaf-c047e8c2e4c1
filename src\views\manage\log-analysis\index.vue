<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-28 13:36:23
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-22 16:08:24
 * @FilePath: \tiksage-frontend\src\views\manage\log-analysis\index.vue
 * @Description: log-analysis
-->
<script setup lang="ts">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import dayjs from 'dayjs';
import { delay } from 'lodash-es';
import { fetchUserLog } from '@/service/api';
import { NumeralFormat } from '@/enum';
import PageDistribution from './modules/page-distribution.vue';
import OverviewCard from './modules/overview-card.vue';
import UserRankCard from './modules/user-rank-card.vue';
import LoginRecordCard from './modules/login-record-card.vue';
import UserPageLocationCard from './modules/user-page-location.card.vue';

interface Model {
  id?: number;
  startDateStr: string;
  endDateStr: string;
}

type MetricKey = Extract<keyof Api.SystemManage.UserLogResponse, 'uniqueVisitor' | 'pageView'>;

const metrics = ref<CommonType.Component.Metric<MetricKey>[]>([
  {
    key: 'uniqueVisitor',
    title: 'Unique Vistor',
    description: '',
    value: 0,
    unit: NumeralFormat.Number,
    icon: 'tabler:database',
    decimals: 0,
    isConversion: false
  },
  {
    key: 'pageView',
    title: 'Page View',
    description: '',
    value: 0,
    unit: NumeralFormat.Number,
    icon: 'tabler:database',
    decimals: 0,
    isConversion: false
  }
]);

const responseData = ref<Api.SystemManage.UserLogResponse>();

const [loading, toggleLoading] = useToggle(false);

const initData = async (newData: Model) => {
  toggleLoading(true);
  const query: Api.SystemManage.UserLogSearchParams = {
    id: newData.id,
    startTime: newData.startDateStr,
    endTime: newData.endDateStr
  };

  const { data, error } = await fetchUserLog(query);

  if (!error) {
    if (data)
      metrics.value = metrics.value.map(m => {
        return {
          ...m,
          value: data[m.key] || 0
        };
      });
    responseData.value = data;
  }
  delay(() => {
    toggleLoading(false);
  }, 500);
};

const dateRange = ref<string[] | null>(null);

function handleDateUpdate(range: string[] | null) {
  if (range) {
    const query: Model = {
      id: undefined,
      startDateStr: range[0],
      endDateStr: range[1]
    };
    initData(query);
    dateRange.value = range;
  }
}
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="card-wrapper" :bordered="false" title="Login Analytics">
      <template #header-extra>
        <ButtonDate
          :default-value="-1"
          start-time="0"
          :end-time="dayjs().toISOString()"
          @update:value="handleDateUpdate"
        />
      </template>
    </NCard>
    <NSpin v-if="loading" class="h-full w-full"></NSpin>
    <NFlex v-else vertical :size="16">
      <NGrid x-gap="16" y-gap="16" item-responsive responsive="screen">
        <NGi span="14">
          <OverviewCard :metrics="metrics" :chart-data="responseData?.userLogByDays || []" />
        </NGi>
        <NGi class="h-full" span="10">
          <PageDistribution :data="responseData?.frequencies || []" />
        </NGi>
      </NGrid>
      <UserRankCard :data="responseData?.userFrequencies || []" />
      <LoginRecordCard :data="responseData?.loginRecords || []" />
      <UserPageLocationCard :date-range="dateRange" />
    </NFlex>
  </NFlex>
</template>

<style scoped></style>
