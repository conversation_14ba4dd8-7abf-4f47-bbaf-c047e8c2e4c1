/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-11 15:10:09
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-31 13:18:41
 * @FilePath: \tiksage-frontend\src\service\api\dashboard.ts
 * @Description: dashboard data api
 */
import { request } from '../request';

export function fetchGetDashboardData(data: Api.Dashboard.DashboardSearchParams) {
  return request<Api.Dashboard.DashboardData>({
    url: '/shop/listShopsDailyData',
    method: 'post',
    data
  });
}

export function fetchGetCreatorsTopList(data: Api.Dashboard.DashboardTopSearchParams) {
  return request<Api.Dashboard.CreatorsTopResponse>({
    url: '/shop/listCreatorsTopData',
    method: 'post',
    data
  });
}

export function fetchGetProductTopList(data: Api.Dashboard.DashboardTopSearchParams) {
  return request<Api.Dashboard.ProductTopResponse>({
    url: '/shop/listProductsTopData',
    method: 'post',
    data
  });
}

export function fetchGetDashboardShopList(data: Api.Dashboard.DashboardShopSearchParams) {
  return request<Api.Dashboard.DashboardShopResponse>({
    url: '/shop/listShopsData',
    method: 'post',
    data
  });
}

export function fetchGetTodayPerformance(shopId: number) {
  return request<Api.Dashboard.TodayPerformanceResponse>({
    url: '/sellerAnalytics/dashboardRealTimeData',
    method: 'get',
    params: { shopId }
  });
}

export function fetchTodayCreatorList(params: Api.Dashboard.TodaySearchParams) {
  return request<Api.Dashboard.TodayCreatorResponse>({
    url: '/sellerAnalytics/todayAffiliatePerformance',
    method: 'get',
    params
  });
}

export function fetchTodayVideoList(params: Api.Dashboard.TodaySearchParams) {
  return request<Api.Dashboard.TodayVideoResponse>({
    url: '/sellerAnalytics/todayAffiliateVideo',
    method: 'get',
    params
  });
}

export function fetchDownloadTrendPerformance(data: Api.Dashboard.DashboardSearchParams) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/shop/exportTrendDailyData',
    method: 'post',
    data
  });
}

export function fetchDownoloadProductTopData(data: Api.Dashboard.DashboardTopSearchParams) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/shop/exportProductsTopData',
    method: 'post',
    data
  });
}

export function fetchDownoloadCreatorsTopData(data: Api.Dashboard.DashboardTopSearchParams) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/shop/exportCreatorsTopData',
    method: 'post',
    data
  });
}

export function fetchDownoloadShopsData(data: Api.Dashboard.DashboardSearchParams) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/shop/exportShopsData',
    method: 'post',
    data
  });
}

export function fetchDownoloadSubBrandShopsData(data: Api.DashboardJazwares.BaseSearchParams) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/shop/exportSubBrandShopsData',
    method: 'post',
    data
  });
}

export function fetchDownoloadProductsTopDataByBrand(data: Api.Dashboard.DashboardTopSearchParams) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/shop/exportProductsTopDataByBrand',
    method: 'post',
    data
  });
}

export function fetchGetShopDataById(shopId: number) {
  return request<Api.Dashboard.ShopData>({
    url: '/shop/getShopData',
    method: 'get',
    params: { shopId }
  });
}

export function fetchGetProductCreators(params: Api.Dashboard.ProductCreatorSearchParams) {
  return request<Api.Dashboard.ProductCreatorResponse>({
    url: '/sellerAnalytics/pullProductCreator',
    method: 'get',
    params
  });
}

export function fetchGetLiveContentTop(params: Api.Dashboard.ContentTopSearchParams) {
  return request<Api.Dashboard.LiveContentTopResponse>({
    url: '/sellerAnalytics/pullLiveTop',
    method: 'get',
    params
  });
}

export function fetchGetVideoContentTop(params: Api.Dashboard.ContentTopSearchParams) {
  return request<Api.Dashboard.VideoContentTopResponse>({
    url: '/sellerAnalytics/pullVideoTop',
    method: 'get',
    params
  });
}

export function fetchGetMonthlyData(shopId: number) {
  return request<Api.Dashboard.MonthlyData[]>({
    url: '/monthlyAnalytics/list',
    method: 'get',
    params: { shopId }
  });
}

export function fetchExportMonthlyDataByExcel(shopId: number) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/shop/exportSubBrandShopsData',
    method: 'post',
    data: { shopId }
  });
}

export function fetchGetConversionAnalysisData(params: Api.Dashboard.ConversionAnalysisSearchParams) {
  return request<Api.Dashboard.ConversionAnalysisResponse>({
    url: '/shop/getShopConversionData',
    method: 'get',
    params
  });
}

export function fetchGetProductGMVTrendData(productId: string, shopId: number) {
  return request<Api.Dashboard.ProductTrend[]>({
    url: '/shop/productDailyData',
    method: 'get',
    params: { productId, shopId }
  });
}
