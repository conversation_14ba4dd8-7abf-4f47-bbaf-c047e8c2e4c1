declare namespace Visactor {
  namespace VChart {
    type IData = import('@visactor/vchart').IData;
    type IDataValues = import('@visactor/vchart').IDataValues;
    type IChartSpec = import('@visactor/vchart').IChartSpec;
    type ILineChartSpec = import('@visactor/vchart').ILineChartSpec;
    type IAreaChartSpec = import('@visactor/vchart').IAreaChartSpec;
    type IPieChartSpec = import('@visactor/vchart').IPieChartSpec;
    type IBarChartSpec = import('@visactor/vchart').IBarChartSpec;
    type ITreemapChartSpec = import('@visactor/vchart').ITreemapChartSpec;
    type ICommonChartSpec = import('@visactor/vchart').ICommonChartSpec;
    type ISCatterChartSpec = import('@visactor/vchart').IScatterChartSpec;
    type IVChart = import('@visactor/vchart').IVChart;
    type VChart = import('@visactor/vchart').VChart;
    type ICartesianLinearAxisSpec = import('@visactor/vchart').ICartesianLinearAxisSpec;
    type ISeriesSpec = import('@visactor/vchart').ISeriesSpec;
  }
}
