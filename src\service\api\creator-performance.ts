import { request } from '../request';

export function fetchGetCreatorPerformanceData(data: Api.CreatorPerformance.CreatorPerformanceSearchParams) {
  return request<Api.CreatorPerformance.CreatorPerformanceResponse>({
    url: '/creatorPerformance/shopsData',
    method: 'post',
    data
  });
}

export function fetchGetMonthlyDataByCreatorPerformance(shopIdsArr: number[], brandList?: string[] | null) {
  return request<Api.CreatorPerformance.CreatorPerformanceMonthlyResponse>({
    url: '/creatorPerformance/monthData',
    method: 'post',
    data: { shopIdsArr, brandList }
  });
}

export function fetchGetVideoData(data: Api.CreatorPerformance.CreatorPerformanceSearchParams) {
  return request<Api.CreatorPerformance.VideoData[]>({
    url: '/creatorPerformance/videoData',
    method: 'post',
    data
  });
}

export function fetchGetVideoDailyData(videoId: string, shopId: number) {
  return request<Api.CreatorPerformance.VideoHistory[]>({
    url: '/creatorPerformance/videoDailyData',
    method: 'get',
    params: { videoId, shopId }
  });
}

export function fetchGetLastDayByCreatorPerformance(data: Api.CreatorPerformance.CreatorPerformanceSearchParams) {
  return request<Api.CreatorPerformance.LastDayResponse>({
    url: '/creatorPerformance/lastDay',
    method: 'post',
    data
  });
}

export function fetchGetProductsAnalysisByCreatorPerformance(
  data: Api.CreatorPerformance.ProductsAnalysisSearchParams
) {
  return request<Api.CreatorPerformance.ProductsAnalysisResponse>({
    url: '/creatorPerformance/getProductSampleInfo',
    method: 'post',
    data
  });
}

export function fetchGetProductTrackingData(data: Api.CreatorPerformance.ProductTrackingSearchParams) {
  return request<Api.CreatorPerformance.ProductTrackingResponse>({
    url: '/creatorPerformance/getSampleTrackData',
    method: 'post',
    data
  });
}

export function fetchExportProdcuctTrackingReport(data: Api.CreatorPerformance.CreatorPerformanceSearchParams) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/creatorPerformance/exportSampleTrackData',
    method: 'post',
    data
  });
}

export function fetchUpdateProductGoal(data: Api.CreatorPerformance.ProductGoalParams) {
  return request({
    url: '/creatorPerformance/createOrUpdateGoals',
    method: 'post',
    data
  });
}

export function fetchUpdateProductShortName(data: Api.CreatorPerformance.ChangeShortNameParams) {
  return request({
    url: '/creatorPerformance/setProductShortName',
    method: 'put',
    data
  });
}
