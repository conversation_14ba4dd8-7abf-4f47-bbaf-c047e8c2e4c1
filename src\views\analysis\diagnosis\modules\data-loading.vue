<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-07 10:56:59
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-08-07 13:46:38
 * @FilePath: \tiksage-frontend\src\views\analysis\modules\data-loading.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts"></script>

<template>
  <NResult
    title="Being diagnosed"
    description="Your comprehensive shop analysis is underway, revealing valuable insights into creator collaborations and market
      positioning. Please wait, as we prepare to enhance your strategic vision"
  >
    <template #icon>
      <icon-local-loading class="text-300px text-primary" />
    </template>
  </NResult>
</template>

<style scoped></style>
