<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-21 11:10:32
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-22 14:57:09
 * @FilePath: \tiksage-frontend\src\views\home\modules\data-chart-card.vue
 * @Description: data-chart-card
-->
<script setup lang="ts">
import { reactive, watchEffect } from 'vue';
import { createReusableTemplate } from '@vueuse/core';
import { random } from 'lodash-es';
import { useRouterPush } from '@/hooks/common/router';
import { nanoid } from '~/packages/utils/src';
interface Props {
  data: any;
}
const { data } = defineProps<Props>();

const [DefineIndexLabel, IndexLabel] = createReusableTemplate();

const options = reactive({
  chart: {
    id: nanoid(),
    toolbar: false,
    group: 'sparklines',
    type: 'area',
    sparkline: {
      enabled: true
    }
  },
  stroke: {
    curve: 'straight'
  },
  colors: ['#FF7C04']
  // xaxis: {
  //   categories: [1991, 1992, 1993, 1994, 1995, 1996, 1997, 1998]
  // }
});
const series = reactive([
  {
    name: 'value',
    data: [30, 40, 45, 50, 49, 60, 70, 91]
  }
]);

watchEffect(() => {
  options.chart.id = data.title;
  options.colors = [data.color];
  series[0].data = Array.from({ length: 7 }, () => random(0, 100));
});

const { routerPushByKey } = useRouterPush();
const onSeeMore = () => {
  routerPushByKey('sales-analytics', { params: { shopId: '1' } });
};
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <NFlex vertical>
      <NFlex justify="space-between" align="center">
        <NFlex align="center">
          <NAvatar :color="data.color" :size="48">
            <SvgIcon class="h-32px w-32px" :icon="data.icon" />
          </NAvatar>
          <NText class="text-size-18px">{{ data.title }}</NText>
        </NFlex>
        <NButton quaternary @click="onSeeMore">See more</NButton>
      </NFlex>
      <NFlex class="h-3" justify="space-around" align="center">
        <NText>{{ data.mainData.title }}</NText>
        <NText :type="data.mainData.upOrDown === 'up' ? 'success' : 'error'">
          {{ `${data.mainData.upOrDown === 'up' ? '↑' : '↓'} ${data.mainData.percent}` }}
        </NText>
        <NText>{{ data.mainData.dataUnit + ' ' + data.mainData.data }}</NText>
      </NFlex>
      <Apexchart type="area" :options="options" :series="series"></Apexchart>
      <NGrid :x-gap="16" y-gap="16" item-responsive :cols="2">
        <NGi v-for="item in data.otherData" :key="item.title">
          <DefineIndexLabel v-slot="{ data }">
            <NFlex class="border-l-1 border-l-4 p-l-3 p-r-3">
              <NText>{{ data.title }}</NText>
              <NFlex justify="space-between" class="w-full">
                <NText :type="data.upOrDown === 'up' ? 'success' : 'error'">
                  {{ `${data.upOrDown === 'up' ? '↑' : '↓'} ${data.percent}` }}
                </NText>
                <NText>{{ data.data }}</NText>
              </NFlex>
            </NFlex>
          </DefineIndexLabel>
          <IndexLabel :data="item"></IndexLabel>
        </NGi>
      </NGrid>
    </NFlex>
  </NCard>
</template>

<style scoped></style>
