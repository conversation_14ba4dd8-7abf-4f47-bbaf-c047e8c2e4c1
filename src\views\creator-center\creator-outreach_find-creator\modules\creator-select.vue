<script setup lang="ts">
import { ref } from 'vue';

interface Emits {
  (e: 'reset'): void;
}

const emit = defineEmits<Emits>();

const MAX_COUNT_CREATOR = 2000;

// Add new state variable
const creatorRange = defineModel<[string, string]>('value', {
  default: ['1', '']
});
const isValidRange = ref(true);

// Verify that the input is a number and is within range
function validateCreatorRange(value: string, index: number) {
  // Only numbers are allowed
  if (value && !/^\d+$/.test(value)) {
    creatorRange.value[index] = '';
    return false;
  }

  const numValue = Number(value);
  // Verify minimum value
  if (index === 0 && numValue < 1) {
    creatorRange.value[index] = '1';
    return false;
  }
  // Verify maximum value
  if (index === 1 && numValue > MAX_COUNT_CREATOR) {
    creatorRange.value[index] = `${MAX_COUNT_CREATOR}`;
    return false;
  }

  return true;
}

// Process input changes
function handleRangeChange(value: [string, string]) {
  creatorRange.value = value;

  // Verify input
  const isMinValid = validateCreatorRange(value[0], 0);
  const isMaxValid = validateCreatorRange(value[1], 1);
  isValidRange.value = isMinValid && isMaxValid;

  if (isValidRange.value && value[0] && value[1]) {
    // Make sure the minimum value is not greater than the maximum value
    const min = Number(value[0]);
    const max = Number(value[1]);

    if (min > max) {
      creatorRange.value = [String(max), String(min)];
    }
  }
}

function handleReset() {
  creatorRange.value = ['1', ''];
  emit('reset');
}
</script>

<template>
  <div class="flex-y-center gap-2">
    <NInput
      v-model:value="creatorRange"
      class="max-w-350px border-b-1"
      pair
      :bordered="false"
      :placeholder="['Min 1', `Max ${MAX_COUNT_CREATOR}`]"
      separator="-"
      :status="isValidRange ? undefined : 'error'"
      @update:value="handleRangeChange"
    >
      <template #prefix>Select</template>
      <template #suffix>Creators</template>
    </NInput>
    <NButton quaternary @click="handleReset">Reset</NButton>
  </div>
</template>

<style scoped></style>
