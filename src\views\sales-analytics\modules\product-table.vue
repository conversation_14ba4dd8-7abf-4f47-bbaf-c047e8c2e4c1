<script setup lang="tsx">
import { onMounted, reactive } from 'vue';
import { NAvatar, NFlex, NText } from 'naive-ui';
import { random } from 'lodash-es';
import { getFallbackImage } from '@/utils/fake-image';

const columns = reactive([
  {
    key: 'rank',
    title: 'Rank'
  },
  {
    key: 'id',
    title: 'Product Info',
    render: (row: any) => (
      <NFlex align="center">
        <NAvatar src={row.avatar}></NAvatar>
        <NText>{row.name}</NText>
      </NFlex>
    )
  },
  {
    key: 'revenue',
    title: 'Revenue'
  },
  {
    key: 'unitsSold',
    title: 'UnitsSold'
  },
  {
    key: 'unitsSold',
    title: 'UnitsSold'
  },
  {
    key: 'unitsSold',
    title: 'UnitsSold'
  },
  {
    key: 'unitsSold',
    title: 'UnitsSold'
  },
  {
    key: 'unitsSold',
    title: 'UnitsSold'
  },
  {
    key: 'unitsSold',
    title: 'UnitsSold'
  }
]);

const data = reactive<any[]>([]);

onMounted(() => {
  for (let i = 0; i < 10; i += 1) {
    const element = {
      id: i + 1,
      rank: `No.${i + 1}`,
      name: `Product${i + 1}`,
      avatar: getFallbackImage(100, 100),
      revenue: random(0, 5000),
      unitsSold: random(0, 1000)
    };
    data.push(element);
  }
});
</script>

<template>
  <NCard :bordered="false" class="card-wrapper" title="Product Performace">
    <NDataTable remote :columns="columns" :data="data"></NDataTable>
  </NCard>
</template>

<style scoped></style>
