<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-11-18 16:17:03
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-19 10:08:52
 * @FilePath: \tiksage-frontend\src\views\return-refund\modules\product-detail-line.vue
 * @Description: product-detail-line
-->
<script setup lang="ts">
import { computed } from 'vue';
import { getFallbackImage } from '@/utils/fake-image';

interface Props {
  product: Api.ReturnRefund.Product;
}

const props = defineProps<Props>();
const productAvatarUrl = computed(() => {
  return `${import.meta.env.VITE_PRODUCT_AVATAR_URL}${props.product.productAvatarLocal}`;
});
</script>

<template>
  <NThing class="w-full p-8px">
    <template #avatar>
      <div class="w-50px">
        <NImage
          :preview-disabled="true"
          width="50px"
          height="50px"
          :src="productAvatarUrl"
          :fallback-src="getFallbackImage(50, 50)"
        ></NImage>
      </div>
    </template>
    <template #header>
      <NEllipsis style="max-width: 250px">{{ product.productName }}</NEllipsis>
    </template>
    <template #header-extra>
      <span>x {{ product.quantity }}</span>
    </template>
    <template #description>
      <NFlex class="text-gray" vertical :size="0">
        <span>{{ product.skuName }}</span>
        <div class="flex gap-8px align-mid">
          <span>Seller SKU:</span>
          <span>{{ product.sellerSku }}</span>
          <ButtonCopy :copy="product.sellerSku" />
        </div>
        <!--
 <div class="flex gap-8px align-mid">
          <span>Total amount paid:</span>
          <span>{{ product.paidUnitPrice }}</span>
          <Tip description="Total amount the customer paid for the order." />
        </div>
-->
        <div class="flex gap-8px align-mid">
          <span>Total refund:</span>
          <span>{{ product.returnUnitPrice }}</span>
          <Tip description="Refund amount requested by the customer." />
        </div>
      </NFlex>
    </template>
  </NThing>
</template>

<style scoped></style>
