<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { useToggle } from '@vueuse/core';
import type { PaginationProps } from 'naive-ui';
import dayjs from 'dayjs';
import { delay, isNumber } from 'lodash-es';
import { fetchGetCreatorApprovalListByPage } from '@/service/api';
import { useAuth } from '@/hooks/business/auth';
import { TimeFormat } from '@/enum';
import CreatorCard from './creator-card.vue';

interface Props {
  clientOptions: Api.VideoManage.ApprovalUserOption[];
}

const props = defineProps<Props>();

const { hasAuth } = useAuth();

enum Status {
  PENDING,
  APPROVALED,
  REJECTED
}
const tabs = [
  { name: Status.PENDING, label: 'Pending Review' },
  { name: Status.APPROVALED, label: 'Approved' },
  { name: Status.REJECTED, label: 'Rejected' }
];

const currentTab = ref<Status>(Status.APPROVALED);

type SearchParams = Api.CreatorManage.CreatorApprovalListSearchParams;

function createDefaultPage() {
  return {
    size: 12,
    current: 1
  };
}

function createDefaultSearchParams(client: number | null): SearchParams {
  return {
    ...createDefaultPage(),
    client,
    // shopId: props.clientOptions[0].shopId,
    status: currentTab.value
  };
}

const searchParams = ref(createDefaultSearchParams(props.clientOptions?.[0].userId));

const pagination: PaginationProps = reactive({
  page: 1,
  pageSize: 12,
  showSizePicker: false,
  onUpdatePage: async (page: number) => {
    pagination.page = page;

    searchParams.value.current = page;
    searchParams.value.size = pagination.pageSize!;

    getData(searchParams.value);
  },
  onUpdatePageSize: async (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;

    searchParams.value.current = pagination.page;
    searchParams.value.size = pageSize;

    getData(searchParams.value);
  }
});

function updatePagination(update: Partial<PaginationProps>) {
  Object.assign(pagination, update);
}

const creatorList = ref<Api.CreatorManage.CreatorApprovalInfo[]>([]);

const [isInvite, toggleIsInvite] = useToggle(false);
const inviteList = ref<string[]>([]);

const emptyContent = computed(() => {
  const description: any = {
    0: 'No Creator awaiting review.',
    1: 'No Creator have been marked as approved.',
    2: 'No Creator have been marked as rejected.'
  };
  return description[searchParams.value.status || Status.PENDING];
});

function handleChangeInvite() {
  toggleIsInvite(true);
}

function handleSubmitInvite() {
  window.$dialog?.create({
    title: 'Notice',
    showIcon: false,
    content: `You have selected ${inviteList.value.length} creator to invite. Please confirm.`,
    positiveText: 'Invite',
    onPositiveClick() {
      // invite api

      inviteList.value = [];
      toggleIsInvite(false);
    },
    negativeText: 'Cancel'
  });
}

function handleCheckCreator(checkValue: boolean, creatorId: string) {
  if (checkValue) {
    inviteList.value.push(creatorId);
  } else {
    inviteList.value = inviteList.value.filter(v => v !== creatorId);
  }
}

function handleCancelInvite() {
  inviteList.value = [];
  toggleIsInvite(false);
}

async function handleUpdateCreator() {
  const { data, error } = await fetchGetCreatorApprovalListByPage(searchParams.value);
  if (!error) {
    creatorList.value = data.records;
    updatePagination({
      page: data.current,
      pageSize: data.size,
      itemCount: data.total
    });
  }
}

const [loading, toggleLoading] = useToggle(false);

// Tab Switch
function handleTabChange(newVal: Status) {
  if (loading.value) return;

  currentTab.value = newVal;

  const newParams: SearchParams = {
    ...searchParams.value,
    ...createDefaultPage(),
    status: newVal
  };
  searchParams.value = newParams;

  getData(newParams);
}

// Client selection
function handleClientChange(value: number) {
  const newParams: SearchParams = {
    ...searchParams.value,
    ...createDefaultPage(),
    client: value
  };
  searchParams.value = newParams;

  getData(newParams);
}

// Date change
function handleDateChange(time: [number, number] | null) {
  const newParams: SearchParams = {
    ...searchParams.value,
    ...createDefaultPage(),
    startTime: time ? dayjs.tz(time[0], 'Etc/GMT+8').format(TimeFormat.CN_TIME_24_NO_TIMEZONE) : undefined,
    endTime: time ? dayjs.tz(time[1], 'Etc/GMT+8').format(TimeFormat.CN_TIME_24_NO_TIMEZONE) : undefined
  };

  searchParams.value = newParams;

  getData(newParams);
}

function handleSearch(type: 'creatorName' | 'tags', value: string) {
  const newParams: SearchParams = {
    ...searchParams.value,
    ...createDefaultPage(),
    [type]: value
  };
  searchParams.value = newParams;

  getData(newParams);
}

async function getData(params: SearchParams) {
  toggleLoading(true);
  searchParams.value = params;

  if (isNumber(params.status)) {
    currentTab.value = params.status;
  }

  const { data, error } = await fetchGetCreatorApprovalListByPage(searchParams.value);
  if (!error) {
    creatorList.value = data.records;
    updatePagination({
      page: data.current,
      pageSize: data.size,
      itemCount: data.total
    });
  }
  delay(() => toggleLoading(false), 500);
}

defineExpose({
  getData
});
</script>

<template>
  <NCard class="h-full card-wrapper" content-class="flex-col min-h-400px h-full" :bordered="false">
    <NTabs :value="currentTab" @update:value="handleTabChange">
      <NTab v-for="item in tabs" :key="item.name" :label="item.label" :name="item.name" />
      <template #suffix>
        <NFlex align="center" :size="16" :wrap="false">
          <SearchInput placeholder="Creator Name" @change="(v: string) => handleSearch('creatorName', v)" />
          <template v-if="hasAuth('creator-approval:operator')">
            <SearchInput placeholder="Creator Tags" @change="(v: string) => handleSearch('tags', v)" />
            <NSelect
              :value="searchParams.client"
              :options="clientOptions"
              value-field="userId"
              label-field="shopName"
              placeholder="Client"
              @update:value="handleClientChange"
            />
          </template>
          <ButtonDate :default-value="-2" :show-quick-button="false" @update:timestamp="handleDateChange" />
        </NFlex>
      </template>
    </NTabs>
    <NSpin class="flex-1 p-t-16px" content-class="h-full flex-col" :show="loading">
      <!-- invite -->
      <template v-if="!loading">
        <NFlex v-if="false" justify="end" align="center">
          <NButton v-if="!isInvite" size="small" tertiary @click="handleChangeInvite">Invite</NButton>
          <template v-else>
            <NButton size="small" type="primary" tertiary @click="handleSubmitInvite">
              <template #icon>
                <icon-mingcute:invite-line class="text-gray" />
              </template>
              Invite({{ inviteList.length }})
            </NButton>
            <NButton size="small" tertiary @click="handleCancelInvite">
              <template #icon>
                <icon-tabler:arrow-right-to-arc class="text-gray" />
              </template>
              Cancel
            </NButton>
          </template>
        </NFlex>
        <NEmpty v-if="!creatorList.length" class="m-auto">{{ emptyContent }}</NEmpty>
        <template v-else>
          <NGrid cols="4" x-gap="16" y-gap="16">
            <NGi v-for="creator in creatorList" :key="creator.id">
              <CreatorCard
                :data="creator"
                :can-invite="isInvite"
                :current-tab="currentTab"
                @check="handleCheckCreator"
                @submit="handleUpdateCreator"
              />
            </NGi>
          </NGrid>
          <NFlex class="m-t-24px" justify="flex-end">
            <NPagination v-bind="pagination" />
          </NFlex>
        </template>
      </template>
    </NSpin>
  </NCard>
</template>

<style scoped></style>
