<script setup lang="ts">
import { computed } from 'vue';
import { max } from 'lodash-es';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { getFallbackImage } from '@/utils/fake-image';
import { NumeralFormat } from '@/enum';

interface Props {
  data: Api.Analysis.CreatorInfo;
}

type CreatorInfo = Api.Analysis.CreatorInfo & {
  category: string[];
  followerAge: string[];
};

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const creatorInfo = computed<CreatorInfo>(() => {
  const result = {
    ...props.data,
    category: JSON.parse(props.data.categoryJson),
    followerAge: JSON.parse(props.data.followerAgeJson)
  };
  return result;
});

const indicators = computed(() => {
  return [
    {
      title: 'GMV',
      prefix: '$',
      value: props.data.gmvNum,
      description: ''
    },
    {
      title: 'Units Sold',
      prefix: '',
      value: props.data.unitsSoldNum,
      description: ''
    },
    {
      title: 'Avg.views',
      prefix: '',
      value: props.data.avgViewsNum,
      description: ''
    }
  ];
});

const handleOpen = () => {
  window.open(props.data.homepage, '_blank');
};
</script>

<template>
  <NCard class="cursor-pointer hover:shadow-xl" content-style="padding:8px" @click="handleOpen">
    <NFlex class="relative min-h-300px" :size="16" justify="space-between" align="center" vertical>
      <NImage
        class="rounded-full"
        width="80px"
        height="80px"
        :src="creatorInfo.avatar"
        :fallback-src="getFallbackImage(80, 80)"
      />
      <NFlex vertical align="center" :size="0">
        <NText class="font-bold">{{ creatorInfo.creatorId }}</NText>
        <NText class="text-sm text-neutral">{{ creatorInfo.nickname }}</NText>
      </NFlex>
      <NFlex justify="center">
        <TagPopover
          v-if="creatorInfo.category.length > 1"
          prefix="+"
          show-first
          :tags="creatorInfo.category"
        ></TagPopover>
      </NFlex>
      <NFlex align="center" :wrap="false">
        <SvgIcon icon="tabler:users" />
        <NText>{{ numberFormat(creatorInfo.followerNum, NumeralFormat.Number) }}</NText>
        <SvgIcon
          :icon="
            creatorInfo.followerMaleRatio > creatorInfo.followerFemaleRatio
              ? 'twemoji:male-sign'
              : 'twemoji:female-sign'
          "
        />
        <NText>{{ max([creatorInfo.followerMaleRatio, creatorInfo.followerFemaleRatio]) }}%</NText>
        <TagPopover
          v-if="creatorInfo.followerAge.length > 0"
          prefix="+"
          suffix=""
          show-first
          :tags="creatorInfo.followerAge"
        ></TagPopover>
      </NFlex>
      <NGrid class="w-full rounded bg-coolgray-1 pb-8px pt-8px" :cols="3">
        <NGi v-for="indicator in indicators" :key="indicator.title">
          <NFlex vertical justify="center" align="center">
            <AverageCountTo
              class="font-bold"
              average
              :prefix="indicator.prefix"
              :start-value="0"
              :end-value="indicator.value"
            />
            <NFlex>
              <NText class="text-coolGray">{{ indicator.title }}</NText>
              <Tip v-if="indicator.description !== ''" :description="indicator.description" />
            </NFlex>
          </NFlex>
        </NGi>
      </NGrid>
      <div class="absolute right-0 top-0 h-40px w-40px flex-center rounded bg-amber/10 text-amber">
        <span class="font-bold">{{ creatorInfo.score }}</span>
      </div>
      <div v-if="props.data.isRepeat" class="absolute left-0 top-0 h-40px w-40px flex-center">
        <NTooltip>
          <template #trigger>
            <icon-tabler:refresh-alert class="text-2xl color-warning" />
          </template>
          This creator is currently working with your target shop
        </NTooltip>
      </div>
    </NFlex>
  </NCard>
</template>

<style scoped></style>
