<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-09-02 10:30:34
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-21 15:56:29
 * @FilePath: \tiksage-frontend\src\views\tools\index\index.vue
 * @Description: tools-index
-->
<script setup lang="ts">
import { computed } from 'vue';
import type { RouteMap } from '@elegant-router/types';
import { useRouterPush } from '@/hooks/common/router';
import { useAuth } from '@/hooks/business/auth';

const { hasAuth } = useAuth();

const { routerPushByKey } = useRouterPush();

interface ToolInfo {
  title: string;
  icon: string;
  desc: string;
  href: keyof RouteMap;
}

interface ToolMap {
  title: string;
  list: ToolInfo[];
}

const toolMap: ToolMap[] = [
  {
    title: 'Creative Tools for Content',
    list: [
      {
        title: 'Video Downloader',
        icon: 'solar:video-library-bold-duotone',
        desc: 'Watermark-Free Short Video Download.',
        href: 'tools_video-download'
      },
      {
        title: 'Creator Data Fetch',
        icon: 'solar:star-fall-minimalistic-2-bold-duotone',
        desc: 'Export designated Creator data.',
        href: 'tools_creator-export'
      },
      {
        title: 'Timezone Converter',
        icon: 'solar:calendar-minimalistic-bold-duotone',
        desc: 'Convert time to a specified time zone',
        href: 'tools_time-convert'
      },
      {
        title: 'Product Video Scraper',
        icon: 'solar:calendar-minimalistic-bold-duotone',
        desc: 'Automatically scrapes TikTok video links',
        href: 'tools_product-video-scraper'
      }
    ]
  }
];

const hasToolAuth = (href: keyof RouteMap) => {
  return hasAuth(`operator:${href}`);
};

const toolsMapFilter = computed(() => {
  return toolMap.map(item => {
    item.list = item.list.filter(tool => {
      return hasToolAuth(tool.href);
    });
    return item;
  });
});

const handleLinkTo = (href: keyof RouteMap) => {
  routerPushByKey(href);
};
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="card-wrapper" :border="false" title="Tool Kits"></NCard>
    <div v-for="tools in toolsMapFilter" :key="tools.title" class="flex-col gap-4">
      <template v-if="tools.list.length">
        <span class="text-xl font-bold">{{ tools.title }}</span>
        <NGrid cols="4" :x-gap="16" :y-gap="16">
          <NGi v-for="tool in tools.list" :key="tool.href" class="" span="1">
            <div
              class="h-140px flex cursor-pointer items-center gap-4 border border-white rounded-xl bg-white px-4 py-8 hover:(border shadow)"
              @click="handleLinkTo(tool.href)"
            >
              <div class="h-70px w-70px flex-center flex-shrink-0 rounded-full bg-primary-100">
                <SvgIcon class="text-3xl text-primary" :icon="tool.icon" />
              </div>
              <div class="h-full flex-col">
                <span class="flex-1 text-base font-bold">{{ tool.title }}</span>
                <span class="flex-1 text-xs text-gray">{{ tool.desc }}</span>
              </div>
            </div>
          </NGi>
        </NGrid>
      </template>
    </div>
  </NFlex>
</template>

<style scoped></style>
