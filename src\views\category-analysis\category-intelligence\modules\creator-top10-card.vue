<script setup lang="tsx">
import { ref } from 'vue';
import { NAvatar, NEllipsis, NFlex, NTag } from 'naive-ui';
import { divide, isEmpty, isNil, isNumber } from 'lodash-es';
import { fetchCreatorSyncDate, fetchCreatorTop10 } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { dateFormat } from '@/utils/date';
import { getFallbackImage } from '@/utils/fake-image';
import SvgIcon from '@/components/custom/svg-icon.vue';
import TagPopover from '@/components/custom/tag-popover.vue';
import { NumeralFormat } from '@/enum';
import Tip from '@/components/custom/tip.vue';
import CategoryShopSelect from './category-shop-select.vue';

interface Props {
  shopType: number;
  month: string;
}

const props = defineProps<Props>();

const { unFormat, numberFormat } = useNumberFormat();

const { data, columns, getData, mobilePagination, loading, updateSearchParams } = useTable({
  apiFn: fetchCreatorTop10,
  immediate: false,
  apiParams: {
    current: 1,
    size: 10,
    shopType: props.shopType,
    month: props.month
  },
  columns() {
    return [
      {
        key: 'index',
        align: 'center',
        fixed: 'left',
        width: 50,
        render(rowData) {
          if (rowData.index === 1) {
            return (
              <NFlex class="w-100%" justify="center">
                <SvgIcon style="width:25px;height:25px" icon="twemoji:1st-place-medal" />
              </NFlex>
            );
          } else if (rowData.index === 2) {
            return (
              <NFlex class="w-100%" justify="center">
                <SvgIcon style="width:25px;height:25px" icon="twemoji:2nd-place-medal" />
              </NFlex>
            );
          } else if (rowData.index === 3) {
            return (
              <NFlex class="w-100%" justify="center">
                <SvgIcon style="width:25px;height:25px" icon="twemoji:3rd-place-medal" />
              </NFlex>
            );
          }
          return <span>{rowData.index}</span>;
        }
      },
      {
        key: 'creatorId',
        title: 'Creator',
        fixed: 'left',
        width: 250,
        render: rowData => {
          return (
            <NFlex align="center" wrap={false}>
              <NAvatar
                class="flex-shrink-0"
                src={`${import.meta.env.VITE_CREATOR_AVATAR_URL}${rowData.avatar}`}
                fallbackSrc={getFallbackImage(50, 50)}
              />
              <NEllipsis lineClamp={1}>{rowData.nickName}</NEllipsis>
            </NFlex>
          );
        }
      },
      {
        key: 'category',
        title: 'Categories',
        align: 'center',
        width: 160,
        render(rowData) {
          if (isNil(rowData.category) || isEmpty(rowData.category)) return '-';
          const categorys = JSON.parse(rowData.category);
          if (categorys.length === 0) return '-';
          return (
            <NFlex>
              {categorys.map((c: string) => {
                return (
                  <NTag size="small" bordered={false}>
                    {c}
                  </NTag>
                );
              })}
            </NFlex>
          );
        }
      },
      {
        key: 'gmv',
        title() {
          return (
            <NFlex justify="center" align="center">
              <span>Gross Sales</span>
              <Tip description="This metric reflects the total revenue generated by creators from LIVE streams, shoppable videos, or showcases within the last 30 days, across all categories and not limited to the GMV from shops partnered within this timeframe." />
            </NFlex>
          );
        },
        align: 'center',
        width: 160,
        sorter(row1, row2) {
          return unFormat(row1.gmv) - unFormat(row2.gmv);
        },
        sortOrder: 'descend'
      },
      {
        key: 'followers',
        title: 'No. of Followers',
        align: 'center',
        width: 140
      },
      {
        key: 'videos',
        title: 'No. of Videos',
        align: 'center',
        width: 140
      },
      {
        key: 'lives',
        title: 'No. of Live Sessions',
        align: 'center',
        width: 140
      },
      {
        key: 'gmvPerBuyer',
        title: 'GMV per buyer',
        align: 'center',
        width: 140
      },
      {
        key: 'gpm',
        title() {
          return (
            <NFlex justify="center" align="center">
              <span>GPM</span>
              <Tip description="Total revenue generated per 1000 video views or 1000 LIVE video viewers." />
            </NFlex>
          );
        },
        align: 'center',
        width: 140
      },
      {
        key: 'affiliateBrandsJson',
        title: 'Brands',
        align: 'center',
        width: 140,
        render(rowData) {
          const brands = JSON.parse(rowData.affiliateBrandsJson);
          return (
            <NFlex justify="center">
              <TagPopover tags={brands} prefix="" suffix="brands" />
            </NFlex>
          );
        }
      }
    ];
  }
});

const dollarKeys: string[] = [];
const percentKeys: string[] = [];

const renderCell = (value: any, _rowData: any, column: any) => {
  if (!isNumber(value)) return value;
  if (dollarKeys.length && dollarKeys.includes(column.key)) {
    return numberFormat(value, NumeralFormat.Dollar);
  }
  if (percentKeys.length && percentKeys.includes(column.key)) {
    return numberFormat(divide(Number(value), 100), NumeralFormat.Percent);
  } else if (typeof value === 'number') {
    return numberFormat(value, NumeralFormat.Number);
  }
  return value || 0;
};

const handleGetData = (model: { category: string; shopIds: number[] }) => {
  updateSearchParams({
    current: 1,
    ...model
  });
  getData();
};

const syncDate = ref('');

async function getCreatorSyncDate() {
  const { data: res, error } = await fetchCreatorSyncDate(props.month);
  if (error) return;
  if (!isNil(res)) {
    syncDate.value = res.syncDate;
  }
}

getCreatorSyncDate();
</script>

<template>
  <NCard :bordered="false" class="card-wrapper" title="">
    <template #header>
      <NFlex vertical>
        <span>Top 50 Creators by Sales</span>
        <span class="text-sm text-coolgray">
          * Display only creator objects from TikTok Affiliate.(Update time:
          {{ dateFormat(syncDate) }})
        </span>
      </NFlex>
    </template>
    <template #header-extra>
      <CategoryShopSelect :month="month" :shop-type="shopType" show-shop @update:value="handleGetData" />
    </template>
    <NDataTable
      :bordered="false"
      size="small"
      :loading="loading"
      :data="data"
      :columns="columns"
      :pagination="mobilePagination"
      :render-cell="renderCell"
      remote
      scroll-x="1460"
    ></NDataTable>
  </NCard>
</template>

<style scoped></style>
