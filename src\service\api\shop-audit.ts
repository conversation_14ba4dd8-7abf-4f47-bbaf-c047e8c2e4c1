import { request } from '../request';

export function fetchGetShopAuditBaseDataByTaskId(taskId: number) {
  return request<Api.ShopAudit.ShopAuditInfo>({
    url: '/scoreShop/queryShopData',
    method: 'get',
    params: { taskId }
  });
}

export function fetchGetShopAuditScoreDataByTaskId(taskId: number) {
  return request<Api.ShopAudit.ShopAuditScore>({
    url: '/scoreShop/queryShopScoreData',
    method: 'get',
    params: { taskId }
  });
}

export function fetchGetShopAuditReportHistory(params: Api.Common.CommonSearchParams) {
  return request<Api.ShopAudit.ShopAuditReportResponse>({
    url: '/scoreShop/getHistoryPullRecord',
    method: 'get',
    params
  });
}

export function fetchSubmitShopAuditReport(shopName: string) {
  return request<number>({
    url: '/scoreShop/getShopAndScore',
    method: 'post',
    data: { shopName }
  });
}
