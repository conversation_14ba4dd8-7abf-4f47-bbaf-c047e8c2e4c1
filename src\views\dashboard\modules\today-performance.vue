<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-12 16:57:27
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-12-06 10:57:09
 * @FilePath: \tiksage-frontend\src\views\dashboard\modules\today-performance.vue
 * @Description: today-performance
-->
<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { useIntervalFn, useToggle } from '@vueuse/core';
import { fetchGetTodayPerformance } from '@/service/api';
import { dateFormat } from '@/utils/date';
import { NumeralFormat } from '@/enum';
// import TodayContributingCreatorsCard from './today-contributing-creators-card.vue';
// import TodayContributingVideosCard from './today-contributing-videos-card.vue';

interface Props {
  shopId: number;
}

const props = defineProps<Props>();

const updateTime = ref<number>();

const updateStr = computed(() => {
  if (!updateTime.value) return '';
  return `Last updated: ${dateFormat(updateTime.value)}`;
});

const topCreators = ref<Api.Dashboard.TodayCreator[]>([]);

const topVideos = ref<Api.Dashboard.TodayVideo[]>([]);

const [loading, toggleLoading] = useToggle(true);

const metrics = ref<CommonType.Component.Metric<MetricKeys>[]>([
  {
    key: 'gmv',
    title: 'GMV',
    description: '',
    value: 0,
    unit: NumeralFormat.Dollar,
    decimals: 2,
    icon: 'solar:dollar-minimalistic-bold-duotone',
    isConversion: false,
    prevValue: 0,
    conversionDescription: 'Vs.previous period'
  },
  {
    key: 'affiliateGMV',
    title: 'Affiliate GMV',
    description: '',
    value: 0,
    unit: NumeralFormat.Dollar,
    decimals: 2,
    icon: 'solar:hand-money-bold-duotone',
    isConversion: false,
    prevValue: 0,
    conversionDescription: 'Vs.previous period'
  },
  {
    key: 'liveGmv',
    title: 'LIVE GMV',
    description: '',
    value: 0,
    unit: NumeralFormat.Dollar,
    decimals: 2,
    icon: 'solar:play-stream-bold-duotone',
    isConversion: false,
    prevValue: 0,
    conversionDescription: 'Vs.previous period'
  },
  {
    key: 'videoGmv',
    title: 'Video GMV',
    description: '',
    value: 0,
    unit: NumeralFormat.Dollar,
    decimals: 2,
    icon: 'solar:videocamera-record-bold-duotone',
    isConversion: false,
    prevValue: 0,
    conversionDescription: 'Vs.previous period'
  },
  {
    key: 'productCardGmv',
    title: 'Product card GMV',
    description: '',
    value: 0,
    unit: NumeralFormat.Dollar,
    decimals: 2,
    icon: 'solar:card-bold-duotone',
    isConversion: false,
    prevValue: 0,
    conversionDescription: 'Vs.previous period'
  },
  {
    key: 'orderCnt',
    title: 'Ordered SKUs',
    description: '',
    value: 0,
    unit: NumeralFormat.Number,
    decimals: 2,
    icon: 'solar:bag-check-bold-duotone',
    isConversion: false,
    prevValue: 0,
    conversionDescription: 'Vs.previous period'
  },
  {
    key: 'visitorCnt',
    title: 'Visitors',
    description: '',
    value: 0,
    unit: NumeralFormat.Number,
    decimals: 2,
    icon: 'solar:users-group-rounded-bold-duotone',
    isConversion: false,
    prevValue: 0,
    conversionDescription: 'Vs.previous period'
  },
  {
    key: 'conversionRate',
    title: 'Conversion rate',
    description: '',
    value: 0,
    unit: NumeralFormat.Percent,
    decimals: 2,
    icon: 'solar:chart-2-bold-duotone',
    isConversion: false,
    prevValue: 0,
    conversionDescription: 'Vs.previous period'
  }
]);

async function initData(shopId: number) {
  toggleLoading(true);
  const { data, error } = await fetchGetTodayPerformance(shopId);
  if (error) return;
  topCreators.value = data.todayAffiliateSales?.records || [];
  topVideos.value = data.todayAffiliateVideo?.records || [];
  updateTime.value = data.todayPerformance?.updatedTimestamp;
  if (data.todayPerformance) {
    metrics.value = metrics.value.map(m => {
      m.value = data.todayPerformance[m.key] as number;
      return m;
    });
  }
  toggleLoading(false);
}

type MetricKeys = keyof Api.Dashboard.TodayPerformance;

function handleRefresh() {
  initData(props.shopId);
}

const { pause, resume } = useIntervalFn(
  () => {
    initData(props.shopId);
  },
  1000 * 60 * 5
);

watch(
  () => props.shopId,
  newVal => {
    initData(newVal);
  },
  {
    immediate: true
  }
);

onMounted(() => {
  resume();
});

onUnmounted(() => {
  pause();
});
</script>

<template>
  <NFlex vertical :size="[16, 16]">
    <NCard class="card-wrapper" :bordered="false">
      <template #header>
        <NFlex align="center">
          <span>Today Performance</span>
          <NText class="text-base text-gray">{{ updateStr }}</NText>
        </NFlex>
      </template>
      <template #header-extra>
        <ButtonRefresh :callback="handleRefresh" />
      </template>
    </NCard>
    <NSpin class="h-full" content-class="h-full" :show="loading">
      <NFlex class="h-full" vertical :size="16">
        <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
          <NGi v-for="metric in metrics" :key="metric.key" span="6">
            <IndicatorCard :metric="metric" />
          </NGi>
        </NGrid>
        <!--
 <NGrid class="flex-1" :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
          <NGi span="12">
            <TodayContributingCreatorsCard
              class="h-full"
              :shop-id="props.shopId"
              :top-data="topCreators"
              :update-time="updateTime"
            />
          </NGi>
          <NGi span="12">
            <TodayContributingVideosCard
              class="h-full"
              :shop-id="props.shopId"
              :top-data="topVideos"
              :update-time="updateTime"
            />
          </NGi>
        </NGrid> 
-->
      </NFlex>
    </NSpin>
  </NFlex>
</template>

<style scoped></style>
