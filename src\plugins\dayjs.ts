import { extend } from 'dayjs';
import localeData from 'dayjs/plugin/localeData';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import { setDayjsLocale } from '../locales/dayjs';

export function setupDayjs() {
  extend(localeData);
  extend(utc);
  extend(timezone);
  extend(advancedFormat);
  extend(weekOfYear);
  extend(customParseFormat);

  setDayjsLocale();
}
