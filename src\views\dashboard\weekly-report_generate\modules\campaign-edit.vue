<script setup lang="tsx">
import { nextTick, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { NInput } from 'naive-ui';
import SvgIcon from '@/components/custom/svg-icon.vue';
import CampaignTemplateSelect from './campaign-template-select.vue';

interface Emits {
  (e: 'update', key: string, value: any): void;
}

interface Props {
  isShow?: boolean;
  reportData?: Api.WeeklyReport.WeeklyReportData;
}

type TableData = Api.WeeklyReport.CampaignData;

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const toolbarConfig = ref({
  excludeKeys: ['uploadImage']
});

const data = ref<TableData[]>([]);

const canEdit = ref(false);

const columns = ref<NaiveUI.DataTableColumns<TableData>>([
  {
    key: 'month',
    title: 'Month',
    titleAlign: 'center',
    width: 150,
    render: editRender('month')
  },
  {
    key: 'campaigns',
    title: 'TT campaigns',
    titleAlign: 'center',
    width: 150,
    render: editRender('campaigns')
  },
  {
    key: 'date',
    title: 'Date',
    titleAlign: 'center',
    width: 150,
    render: editRender('date')
  },
  {
    key: 'type',
    title: 'Type',
    width: 100,
    titleAlign: 'center',
    render: editRender('type')
  },
  {
    key: 'campaignDetails',
    title: 'Campaign details',
    titleAlign: 'center',
    width: 300,
    render: editRender('campaignDetails', 'textarea')
  },
  {
    key: 'sellerDiscount',
    title: 'Seller additional discount',
    titleAlign: 'center',
    width: 150,
    render: editRender('sellerDiscount')
  },
  {
    key: 'tiktokSellerDiscount',
    title: 'TikTok funded discount',
    titleAlign: 'center',
    width: 150,
    render: editRender('tiktokSellerDiscount')
  },
  {
    key: 'totalDiscount',
    title: 'Total discount',
    titleAlign: 'center',
    width: 150,
    render: editRender('totalDiscount')
  },
  {
    key: 'status',
    title: 'Status',
    titleAlign: 'center',
    width: 150,
    render: editRender('status')
  }
]);

const selectedRowIdx = ref<number>();
const [showDropdown, toggleShowDropdown] = useToggle(false);
const position = ref({ x: 0, y: 0 });
const dropdownOptions = ref([
  {
    label: () => (
      <div class="flex-y-center gap-2">
        <SvgIcon icon="solar:arrow-to-top-right-linear" />
        Insert Above
      </div>
    ),
    key: 'insertAbove'
  },
  {
    label: () => (
      <div class="flex-y-center gap-2">
        <SvgIcon icon="solar:arrow-to-down-left-linear" />
        Insert Below
      </div>
    ),
    key: 'insertBelow'
  },
  {
    label: () => (
      <div class="flex-y-center gap-2 text-error">
        <SvgIcon icon="solar:trash-bin-minimalistic-linear" />
        Delete
      </div>
    ),
    key: 'delete'
  }
]);

function createEmptyRows(): TableData[] {
  return [
    {
      month: '',
      campaigns: '',
      date: '',
      type: '',
      campaignDetails: '',
      sellerDiscount: '',
      tiktokSellerDiscount: '',
      totalDiscount: '',
      status: ''
    }
  ];
}

function handleSelect(key: string) {
  switch (key) {
    case 'insertAbove':
      data.value.splice(selectedRowIdx.value!, 0, ...createEmptyRows());
      break;
    case 'insertBelow':
      data.value.splice(selectedRowIdx.value! + 1, 0, ...createEmptyRows());
      break;
    case 'delete':
      data.value.splice(selectedRowIdx.value!, 1);
      break;
    default:
      break;
  }
  toggleShowDropdown(false);
}

function rowProps(_rowData: any, index: number) {
  return {
    onContextmenu: async (e: MouseEvent) => {
      selectedRowIdx.value = index;
      e.preventDefault();
      toggleShowDropdown(false);
      await nextTick();
      toggleShowDropdown(true);
      position.value = { x: e.clientX, y: e.clientY };
    }
  };
}

function editRender(key: keyof TableData, type: 'text' | 'textarea' = 'text') {
  return (rowData: TableData, rowIndex: number) => {
    return canEdit.value ? (
      <div class="w-full">
        <NInput
          placeholder=""
          type={type}
          value={rowData[key]}
          onUpdateValue={v => {
            // Update the value of the current row
            data.value[rowIndex][key] = v;
          }}
        ></NInput>
      </div>
    ) : (
      <div class="whitespace-pre-wrap" innerHTML={rowData[key]}></div>
    );
  };
}

function handleEdit() {
  if (data.value.length === 0) {
    data.value = createEmptyRows();
  }
  canEdit.value = !canEdit.value;
}

function handleUpdateHtml(v: string, key: string) {
  emit('update', key, v);
}

watch(
  () => props.reportData,
  () => {
    data.value = props.reportData?.campaignList || [];
  },
  {
    immediate: true,
    deep: true
  }
);

watch(
  () => data.value,
  newVal => {
    emit('update', 'campaignList', newVal);
  },
  {
    deep: true
  }
);
</script>

<template>
  <div class="flex-col gap-4">
    <NFormItem label-style="font-weight: 500;font-size: 16px;" label="4. Campaign">
      <div v-if="isShow" class="html-content">
        <div class="html-reset" v-html="reportData?.campaignHtml || ''"></div>
      </div>
      <WangEditor
        v-else
        :value-html="reportData?.campaignHtml || ''"
        :toolbar-config="toolbarConfig"
        @update:value-html="v => handleUpdateHtml(v, 'campaignHtml')"
      ></WangEditor>
    </NFormItem>
    <div v-if="!isShow" class="flex-y-center justify-end gap-2">
      <span class="text-coolgray">Right-click in the table to operate on the table</span>
      <CampaignTemplateSelect v-model:data="data" />
      <NButton secondary type="primary" @click="handleEdit">Edit</NButton>
    </div>
    <NDataTable :single-line="false" :columns="columns" :data="data" :row-props="rowProps"></NDataTable>
    <NDropdown
      v-if="!isShow"
      placement="bottom-start"
      trigger="manual"
      :options="dropdownOptions"
      :x="position.x"
      :y="position.y"
      :show="showDropdown"
      @select="handleSelect"
      @clickoutside="toggleShowDropdown(false)"
    />

    <div v-if="isShow" class="html-content">
      <div class="html-reset" v-html="reportData?.campaignHtmlBottom || ''"></div>
    </div>
    <WangEditor
      v-else
      :value-html="reportData?.campaignHtmlBottom || ''"
      :toolbar-config="toolbarConfig"
      @update:value-html="v => handleUpdateHtml(v, 'campaignHtmlBottom')"
    ></WangEditor>
  </div>
</template>

<style scoped></style>
