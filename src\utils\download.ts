/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-15 17:45:03
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-25 11:43:46
 * @FilePath: \tiksage-frontend\src\utils\download.ts
 * @Description: dowload Excel in end
 */
import { nextTick } from 'vue';
import dayjs from 'dayjs';
import html2canvas from 'html2canvas-pro';
import jsPDF from 'jspdf';
import { TimeFormat } from '@/enum';

/**
 * Download files according to URL
 *
 * @param url - Download link
 */
export function downloadByUrl(url: string) {
  const a = document.createElement('a');
  a.href = url;
  a.download = url.split('/').pop() || 'download';
  a.target = '_blank';
  a.rel = 'noopener noreferrer';
  document.body.appendChild(a);
  a.click();
  a.remove();
}

// eslint-disable-next-line max-params
export function downloadFile(data: Blob, type: string, filename: string, needExportTime = true) {
  const url = window.URL.createObjectURL(data as Blob);
  const a = document.createElement('a');
  a.href = url;
  let name = '';
  if (filename) {
    name = filename;
  }
  if (needExportTime) {
    name = `${name}-${dayjs().tz('Etc/GMT+8').format(TimeFormat.US_TIME_24)}`;
  }
  a.download = `${name}.${type}`;
  document.body.appendChild(a);
  a.click();

  // Cleaning operation
  a.remove();
  window.URL.revokeObjectURL(url);
}

/**
 * Download report
 *
 * use data-html2canvas-ignore to ignore the element
 *
 * @param type - pdf or png
 * @param elementId - the id of the element to be downloaded
 * @returns void
 */
export async function downloadReport(type: 'pdf' | 'png', elementId: string) {
  window.$loadingBar?.start();
  await nextTick();
  const element = document.getElementById(elementId);

  if (!element) {
    window.$loadingBar?.error();
    return;
  }

  try {
    const canvas = await html2canvas(element as HTMLElement, {
      // Reduce the scale value to reduce the image size
      scale: 2,
      allowTaint: true,
      useCORS: true,
      logging: true,
      backgroundColor: '#F7FAFC',
      ignoreElements(ele) {
        return ele.id === 'download-button';
      },
      width: element.scrollWidth + 50,
      height: element.scrollHeight + (type === 'pdf' ? 50 : 75),
      x: -25,
      y: -25
    });

    await new Promise(resolve => {
      setTimeout(resolve, 0);
    });

    // 降低图片质量
    const imgUrl = canvas.toDataURL('image/png', 0.7); // 添加质量参数0.7

    if (type === 'png') {
      const link = document.createElement('a');
      link.download = `report-${dayjs().format(TimeFormat.US_TIME_24)}.png`;
      link.href = imgUrl;
      link.click();
    } else {
      // eslint-disable-next-line
      const PDF = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
        compress: true
      });

      // 添加背景色
      PDF.setFillColor(247, 250, 252); // #F7FAFC 转换为 RGB
      PDF.rect(0, 0, 210, 297, 'F'); // 填充整个 A4 页面

      const imgProps = PDF.getImageProperties(imgUrl);
      const pdfWidth = 210;
      const pdfHeight = 297;

      // 计算缩放比例，确保图片完整显示在一页内
      const scale = Math.min(pdfWidth / imgProps.width, pdfHeight / imgProps.height);

      // 计算缩放后的尺寸
      const scaledWidth = imgProps.width * scale;
      const scaledHeight = imgProps.height * scale;

      // 居中显示
      const x = (pdfWidth - scaledWidth) / 2;
      const y = (pdfHeight - scaledHeight) / 2;

      // 添加缩放后的图片
      PDF.addImage(imgUrl, 'PNG', x, y, scaledWidth, scaledHeight);

      PDF.save(`report-${dayjs().format(TimeFormat.US_TIME_24)}.pdf`);
    }
    window.$loadingBar?.finish();
  } catch (error) {
    window.$loadingBar?.error();
    window.$message?.error('Failed to generate report');
    console.error('Failed to generate report:', error);
  }
}
