<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-11-04 16:33:45
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-21 17:18:40
 * @FilePath: \tiksage-frontend\src\views\return-refund\modules\product-distribution-card.vue
 * @Description: product-distribution-card
-->
<script setup lang="tsx">
import { computed } from 'vue';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';

interface Props {
  data: Api.ReturnRefund.ReturnProduct[];
}

const props = defineProps<Props>();

const top10Products = computed<Api.ReturnRefund.ReturnProduct[]>(() => {
  if (!props.data || props.data.length === 0) {
    return [];
  }

  return [...props.data]
    .sort((a, b) => {
      // Main sort: return quantity
      if (b.productReturnNum !== a.productReturnNum) {
        return b.productReturnNum - a.productReturnNum;
      }
      // Sub-sort: Return rate
      return b.productReturnToTotalOrdersRatio - a.productReturnToTotalOrdersRatio;
    })
    .slice(0, 10);
});

const { numberFormat } = useNumberFormat();

const chartOptions = computed<Visactor.VChart.IBarChartSpec>(() => {
  const res: Visactor.VChart.IBarChartSpec = {
    type: 'bar',
    data: [
      {
        id: 'xxx',
        values: top10Products.value
      }
    ],
    xField: 'productName',
    yField: 'productReturnNum',
    axes: [
      {
        orient: 'bottom',
        type: 'band',
        visible: false,
        sampling: false,
        label: {
          // autoHide: true,
          autoLimit: true
        }
      },
      {
        orient: 'left',
        type: 'linear',
        title: {
          visible: true,
          text: 'Item Return Count'
        }
      }
    ],
    label: {
      visible: true,
      textType: 'rich',
      formatMethod: (_text, datum) => {
        if (!datum) return '';
        const { productAvatarLocal } = datum;
        return {
          type: 'rich',
          text: [
            {
              image: `${import.meta.env.VITE_PRODUCT_AVATAR_URL}${productAvatarLocal}`,
              width: 75,
              height: 75
            }
          ]
        };
      }
    },
    legends: [
      {
        type: 'discrete',
        visible: false
      }
    ],
    tooltip: {
      activeType: 'dimension',
      style: {
        titleLabel: {
          wordBreak: 'break-all'
        },
        keyLabel: {
          spacing: 100
        }
      },
      dimension: {
        content: [
          {
            key: 'Item Return Count',
            value: v => numberFormat(v?.productReturnNum, NumeralFormat.Number)
          },
          {
            key: 'Return Share',
            value: v => numberFormat(v?.productReturnToTotalReturnRatio, NumeralFormat.Percent)
          },
          {
            key: 'Item Order Volume',
            value: v => numberFormat(v?.productOrderNum || 0, NumeralFormat.Number)
          },
          {
            key: 'Item Return Rate',
            value: v => numberFormat(v?.productReturnToTotalOrdersRatio, NumeralFormat.Percent)
          }
        ]
      },
      updateElement(el, _actualTooltip, params) {
        const { changePositionOnly } = params;
        if (!changePositionOnly) {
          const { productAvatarLocal, productName } = params?.dimensionInfo?.[0].data?.[0].datum?.[0] || '';
          const titleEl = el.querySelector('.vchart-tooltip-title');
          titleEl?.remove();
          const titleWidth = (el.querySelector('.vchart-tooltip-content-box') as HTMLElement)?.offsetWidth;
          const titleWrap = document.createElement('div');
          titleWrap.style.display = 'flex';
          titleWrap.style.flexDirection = 'column';
          titleWrap.style.alignItems = 'center';
          titleWrap.style.gap = '16px';
          titleWrap.style.marginBottom = '6px';
          titleWrap.style.fontSize = '14px';
          titleWrap.style.fontWeight = 'bold';
          titleWrap.style.width = `${titleWidth}px`;
          titleWrap.classList.add('vchart-tooltip-title');

          const img = document.createElement('img');
          img.setAttribute('id', 'avatar');
          img.style.width = '70px';
          img.style.height = '70px';
          img.setAttribute('src', `${import.meta.env.VITE_PRODUCT_AVATAR_URL}${productAvatarLocal}`);

          const tit = document.createElement('span');
          tit.textContent = productName;

          titleWrap.appendChild(img);
          titleWrap.appendChild(tit);

          el.prepend(titleWrap);
        }
      }
    }
  };
  return res;
});
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" title="Top 10 Return Products">
    <VBarChart style="height: 400px" :chart-options="chartOptions" />
  </NCard>
</template>

<style lang="scss" scoped></style>
