<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-07 17:32:20
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-08-09 15:08:05
 * @FilePath: \tiksage-frontend\src\views\analysis\modules\creator-analysis-tabs.vue
 * @Description: creator-analysis-tabs
-->
<script setup lang="tsx">
import { reactive, ref, watch } from 'vue';
import type { DataTableColumn } from 'naive-ui';
import { NAvatar, NEllipsis, NFlex, NTag, NText } from 'naive-ui';
import { delay, divide } from 'lodash-es';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { getFallbackImage } from '@/utils/fake-image';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { NumeralFormat } from '@/enum';
import CreatorDetail from '../../report/modules/creator-detail.vue';

interface Props {
  report?: Api.Analysis.ReportInfo;
}

interface Emits {
  (e: 'reset'): void;
}

type TabType = 'current' | 'competitor' | 'recommend';

type Option = { label: string; value: string | number };

const props = defineProps<Props>();

const emit = defineEmits<Emits>();

const { numberFormat } = useNumberFormat();

const shopValue = ref();
const selectOptions = ref<Option[]>([]);

const showTypeValue = ref('list');
const showTypeOptions = [
  { icon: 'tabler:menu-2', value: 'list' },
  { icon: 'tabler:layout-grid-filled', value: 'card' }
];

const isShow = ref(false);

const tabValue = ref<TabType>('current');

const loading = ref(false);

const toggleTab = (value: TabType) => {
  tabValue.value = value;
  if (value === 'competitor') {
    isShow.value = true;
  } else {
    isShow.value = false;
  }
};

const showData = ref<Api.Analysis.CreatorInfo[]>();

watch(
  [() => props.report, () => tabValue.value],
  async ([newReport, newTabValue]) => {
    loading.value = true;
    if (!newReport) {
      delay(() => {
        loading.value = false;
      }, 1000);
      return;
    }
    const { shopCurrentCreators, competitorShopCreators, recommendedCreators } = newReport;
    let options;
    switch (newTabValue) {
      case 'current':
        showData.value = shopCurrentCreators;
        break;
      case 'competitor':
        options = competitorShopCreators.map((list, index) => {
          return {
            label: list.shopName,
            value: index
          };
        });
        showData.value = competitorShopCreators[0].competitorCreators;
        selectOptions.value = options;
        shopValue.value = options[0].value;
        break;
      case 'recommend':
        showData.value = recommendedCreators.map(creator => {
          return {
            ...creator,
            avatar: `${import.meta.env.VITE_CREATOR_AVATAR_URL}${creator.avatar}`
          };
        });
        break;
      default:
    }
    delay(() => {
      loading.value = false;
    }, 1000);
  },
  { immediate: true }
);

watch(
  () => shopValue.value,
  async newShopIdx => {
    loading.value = true;
    if (!props.report) {
      delay(() => {
        loading.value = false;
      }, 1000);
      return;
    }
    const { competitorShopCreators } = props.report;
    showData.value = competitorShopCreators[newShopIdx].competitorCreators;
    delay(() => {
      loading.value = false;
    }, 1000);
  }
);

const columns: DataTableColumn<Api.Analysis.CreatorInfo>[] = reactive([
  {
    key: 'score',
    title: 'Score',
    align: 'center',
    fixed: 'left',
    width: 80,
    render(rowData) {
      return (
        <NText class="text-xl color-amber" strong>
          {rowData.score}
        </NText>
      );
    }
  },
  {
    key: 'creatorId',
    title: 'Creator',
    // align: 'center',
    fixed: 'left',
    width: 250,
    render: row => {
      return (
        <NFlex justify="start" align="center" wrap={false}>
          <NAvatar
            style={`width:50px;height:50px`}
            round
            src={row.avatar}
            fallbackSrc={getFallbackImage(50, 50)}
          ></NAvatar>
          <NFlex vertical justify="center">
            <NEllipsis style="max-width:150px" class="font-bold" lineClamp={1}>
              {row.creatorId}
            </NEllipsis>
            <NEllipsis style="max-width:150px" class="text-neutral" lineClamp={1}>
              {row.nickname}
            </NEllipsis>
            {tabValue.value === 'competitor' ? (
              <NFlex align="center" wrap={false}>
                <SvgIcon class="text-dark" icon="tabler:building-store" />
                <NEllipsis style="max-width:120px" class="text-neutral" lineClamp={1}>
                  {selectOptions.value[shopValue.value]?.label || '-'}
                </NEllipsis>
              </NFlex>
            ) : undefined}
          </NFlex>
        </NFlex>
      );
    }
  },
  {
    key: 'categoryJson',
    title: 'Categories',
    width: 200,
    render(rowData) {
      return (
        <NFlex>
          {JSON.parse(rowData.categoryJson).map((cgy: any) => {
            return (
              <NTag size="small" bordered={false}>
                {cgy}
              </NTag>
            );
          })}
        </NFlex>
      );
    }
  },
  {
    key: 'followerNum',
    title: 'Followers',
    width: 180,
    render(rowData) {
      const { followerMaleRatio, followerFemaleRatio } = rowData;
      let showGender;
      let showGenderRatio;
      if (followerMaleRatio > followerFemaleRatio) {
        showGender = 'Male';
        showGenderRatio = followerMaleRatio;
      } else {
        showGender = 'Female';
        showGenderRatio = followerFemaleRatio;
      }
      return (
        <NFlex vertical>
          {/* <NEllipsis style="max-width:150px" lineClamp={1} class="font-bold"></NEllipsis> */}
          <NFlex align="center">
            <SvgIcon icon="tabler:users" />
            <NText>{numberFormat(rowData.followerNum, NumeralFormat.Number)}</NText>
            {<SvgIcon icon={showGender === 'Male' ? 'twemoji:male-sign' : 'twemoji:female-sign'} />}
            {`${showGenderRatio}%`}
          </NFlex>
          <NFlex align="center">
            {JSON.parse(rowData.followerAgeJson).map((cgy: any) => {
              return (
                <NTag size="small" bordered={false}>
                  {cgy}
                </NTag>
              );
            })}
          </NFlex>
        </NFlex>
      );
    }
  },
  {
    key: 'gmvNum',
    title: 'GMV',
    align: 'center',
    width: 110
  },
  {
    key: 'unitsSoldNum',
    title: 'Units Sold',
    align: 'center',
    width: 110
  },
  {
    key: 'videosNum',
    title: 'No. of Videos',
    align: 'center',
    width: 110
  },
  {
    key: 'avgViewsNum',
    title: 'Avg. Views',
    align: 'center',
    width: 110
  }
]);

const dollarKeys = ['gmvNum'];
const percentKeys = [''];

const renderCell = (value: any, _rowData: any, column: any) => {
  if (dollarKeys.includes(column.key)) {
    return numberFormat(value, NumeralFormat.Dollar);
  }
  if (percentKeys.includes(column.key)) {
    return numberFormat(divide(Number(value), 100), NumeralFormat.Percent);
  } else if (typeof value === 'number') {
    return numberFormat(value, NumeralFormat.Number);
  }
  return value;
};

const rowProps = (rowData: any) => {
  return {
    style: 'cursor:pointer',
    onClick: () => {
      window.open(rowData.homepage, '_blank');
    }
  };
};

const handleReset = () => {
  emit('reset');
};
</script>

<template>
  <NCard class="h-full card-wrapper" content-class="flex-col" :bordered="false" title="Creator Match List">
    <template #header-extra>
      <ButtonIcon tooltip-content="Reset" icon="tabler:zoom-reset" @click="handleReset" />
      <ButtonIcon tooltip-content="Report History" icon="tabler:history" />
    </template>
    <NTabs v-model:value="tabValue" pane-class="h-full" type="line" :on-update:value="toggleTab">
      <template #suffix>
        <NSelect
          v-show="isShow"
          v-model:value="shopValue"
          size="small"
          :consistent-menu-width="false"
          :options="selectOptions"
          default-value="1111"
        ></NSelect>
      </template>
      <NTab name="current" :tab="`${report?.shopName || 'My Shop'}`"></NTab>
      <NTab name="competitor" tab="Competitor"></NTab>
      <NTab name="recommend" tab="Recommended"></NTab>
    </NTabs>
    <NSpin v-if="loading" class="m-auto"></NSpin>
    <NFlex v-else vertical class="flex-1">
      <NFlex class="mt-8px text-icon" justify="end">
        <NRadioGroup id="radio" v-model:value="showTypeValue" size="small">
          <NRadioButton v-for="item in showTypeOptions" :key="item.value" :value="item.value">
            <div class="mt-7px">
              <SvgIcon :icon="item.icon" />
            </div>
          </NRadioButton>
        </NRadioGroup>
      </NFlex>
      <div class="flex-1">
        <NDataTable
          v-if="showTypeValue === 'list'"
          class="h-full"
          :flex-height="true"
          size="small"
          :columns="columns"
          :data="showData"
          :render-cell="renderCell"
          scroll-x="1200"
          :row-props="rowProps"
        ></NDataTable>
        <NGrid
          v-if="showTypeValue === 'card'"
          class="h-full overflow-y-auto"
          x-gap="16"
          y-gap="16"
          item-responsive
          responsive="screen"
        >
          <NGi v-for="data in showData" :key="data.id" span="24 m:12 l:8 xl:6 2xl:6">
            <CreatorDetail :data="data" />
          </NGi>
        </NGrid>
      </div>
      <!-- <ShopCreatorDetail /> -->
    </NFlex>
  </NCard>
</template>

<style scoped></style>
