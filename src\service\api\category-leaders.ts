import { request } from '../request';

export function fetchGetCategoryList() {
  return request<string[]>({
    url: '/shopPerformance/listQueryCategory',
    method: 'get'
  });
}
export function fetchGetMonthByCategory(category: string) {
  return request<string[]>({
    url: '/shopPerformance/listQueryMonth',
    method: 'get',
    params: { category }
  });
}

export function fetchGetTopGmvShopList(category: string, month: string) {
  return request<Api.CategoryLeaders.TopGmvShopListResponse[]>({
    url: '/shopPerformance/queryShopPerformance',
    method: 'get',
    params: { category, month }
  });
}

export function fetchGetTopGmvShopById(id: number) {
  return request<Api.CategoryLeaders.TopGmvShop>({
    url: '/shopPerformance/getShopPerformance',
    method: 'get',
    params: { id }
  });
}

export function fetchGetCreatorsWidthGmv(data: Api.CategoryLeaders.CreatorWithGmvSearchParams) {
  return request<Api.CategoryLeaders.CreatorWithGmvResponse>({
    url: '/shopPerformance/listShopCreators',
    method: 'post',
    data
  });
}
