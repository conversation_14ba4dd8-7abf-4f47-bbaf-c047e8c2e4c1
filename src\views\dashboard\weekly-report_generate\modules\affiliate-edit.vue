<script setup lang="tsx">
import { ref, watch } from 'vue';
import { NInputNumber } from 'naive-ui';
import { isNumber, pick } from 'lodash-es';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';
import { renderCell } from './shared';
import SampleMetricsTable from './sample-metrics-table.vue';

interface Emits {
  (e: 'update', key: string, value: string | number): void;
}

interface Props {
  isShow?: boolean;
  reportData?: Api.WeeklyReport.WeeklyReportData;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { numberFormat } = useNumberFormat();

const toolbarConfig = ref({
  excludeKeys: ['uploadImage']
});

const canEdit = ref(false);

const keyArr: Array<keyof Api.WeeklyReport.tableData> = [
  'cycleTitle',
  'sampleItems',
  'pending',
  'postingCount',
  'videoImpressions',
  'videoGmv',
  'affiliateOrders'
];

const totalData = ref<any>([]);

const totalColumns = ref<any>([
  {
    key: 'cycleTitle',
    title: 'Date',
    align: 'center',
    width: 150
  },
  {
    key: 'sampleItems',
    title: 'Sample orders',
    align: 'center',
    width: 150
  },
  {
    key: 'pending',
    title: 'Pending',
    align: 'center',
    width: 150,
    render: editRender('pending')
  },
  {
    key: 'postingCount',
    title: 'Video posted',
    align: 'center',
    width: 150
  },
  {
    key: 'videoImpressions',
    title: 'Video impressions',
    align: 'center',
    width: 150,
    render: editRender('videoImpressions')
  },
  {
    key: 'videoGmv',
    title: 'Affiliate video GMV',
    align: 'center',
    width: 150,
    unit: '$',
    render: editRender('videoGmv', NumeralFormat.Real_Dollar)
  },
  {
    key: 'affiliateOrders',
    title: 'Affiliate video orders',
    align: 'center',
    width: 180,
    render: editRender('affiliateOrders')
  }
]);

function renderEditCell(value: any, unit?: NumeralFormat) {
  if (!isNumber(value)) return value;
  if (unit) {
    return numberFormat(value, unit);
  } else if (typeof value === 'number') {
    return numberFormat(value, NumeralFormat.Real_Number);
  }
  return value;
}

function editRender(key: string, unit?: NumeralFormat) {
  return (rowData: any, rowIndex: number) => {
    return canEdit.value ? (
      <div class="w-full">
        <NInputNumber
          placeholder=""
          value={rowData[key]}
          onUpdateValue={v => handleUpdateValue(rowIndex, key, v)}
        ></NInputNumber>
      </div>
    ) : (
      <div class="whitespace-pre-wrap">{renderEditCell(rowData[key], unit)}</div>
    );
  };
}

function handleUpdateValue(rowIndex: number, key: string, value: any) {
  switch (rowIndex) {
    case 0:
      emit('update', `lastMonthData.${key}`, value);
      break;
    case 1:
      emit('update', `thisMonthData.${key}`, value);
      break;
    case 2:
      emit('update', `lastWeekData.${key}`, value);
      break;
    case 3:
      emit('update', `thisWeekData.${key}`, value);
      break;
    default:
      break;
  }
}

function initTableData() {
  const res: Api.WeeklyReport.tableData[] = [];
  if (!props.reportData) return res;
  const { lastMonthData, thisMonthData, lastWeekData, thisWeekData } = props.reportData;

  // 使用lodash的pick方法根据keyArr获取对象的特定属性
  if (lastMonthData) res.push(pick(lastMonthData, keyArr));
  if (thisMonthData) res.push(pick(thisMonthData, keyArr));
  if (lastWeekData) res.push(pick(lastWeekData, keyArr));
  if (thisWeekData) res.push(pick(thisWeekData, keyArr));

  return res;
}

function handleUpdateHtml(v: string, key: string) {
  emit('update', key, v);
}

function handleEdit() {
  canEdit.value = !canEdit.value;
}

watch(
  () => props.reportData,
  () => {
    totalData.value = initTableData();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div class="flex-col gap-4">
    <NFormItem label-style="font-weight: 500;font-size: 16px;" label="2. Affiliates update">
      <div v-if="isShow" class="html-content">
        <div class="html-reset" v-html="reportData?.affiliateHtml || ''"></div>
      </div>
      <WangEditor
        v-else
        :value-html="reportData?.affiliateHtml || ''"
        :toolbar-config="toolbarConfig"
        @update:value-html="v => handleUpdateHtml(v, 'affiliateHtml')"
      ></WangEditor>
    </NFormItem>
    <div class="flex-col gap-2">
      <div v-if="!isShow" class="flex justify-end">
        <NButton type="primary" @click="handleEdit">Edit</NButton>
      </div>
      <NDataTable
        striped
        :single-line="false"
        :columns="totalColumns"
        :data="totalData"
        :render-cell="renderCell"
      ></NDataTable>
    </div>
    <SampleMetricsTable :data="reportData?.sampleMetrics || []" :is-show="isShow" />

    <div v-if="isShow" class="html-content">
      <div class="html-reset" v-html="reportData?.affiliateHtmlBottom || ''"></div>
    </div>
    <WangEditor
      v-else
      :value-html="reportData?.affiliateHtmlBottom || ''"
      :toolbar-config="toolbarConfig"
      @update:value-html="v => handleUpdateHtml(v, 'affiliateHtmlBottom')"
    ></WangEditor>
  </div>
</template>

<style scoped lang="scss"></style>
