<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-07 17:03:02
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-09-18 14:47:38
 * @FilePath: \tiksage-frontend\src\views\analysis\modules\ideal-form.vue
 * @Description: ideal-form
-->
<script setup lang="tsx">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { delay } from 'lodash-es';
import { fetchGetCategoryTree } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { followerAgesOptions, followersGenderOptions } from '@/views/creator-center/creator-database/modules/data';
import ShopSearch from './shop-search.vue';

interface Emits {
  (e: 'validate', validate: boolean): void;
}

const emit = defineEmits<Emits>();

const categoryTreeOptions = ref<Api.CreatorResources.CategoryTree[]>([]);
const getCategoryTree = async () => {
  const { data, error } = await fetchGetCategoryTree();
  if (error) return;
  categoryTreeOptions.value = data;
};

type Model = CommonType.RecordNullable<Api.Analysis.DiagnosisParam>;

const createModel = (): Model => {
  return {
    shopName: null,
    shopAvatar: null,
    categoryArr: [],
    followerAgeArr: [],
    followerGender: 'All'
  };
};

const model = defineModel<Model>('model', { required: true });

const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule } = useFormRules();

type RuleKey = Exclude<keyof Model, 'shopAvatar'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  shopName: defaultRequiredRule,
  categoryArr: defaultRequiredRule,
  // status: defaultRequiredRule,
  followerAgeArr: defaultRequiredRule,
  followerGender: defaultRequiredRule
};

const handleShopChange = (shopInfo: Api.Analysis.ShopInfo | null) => {
  model.value.shopName = shopInfo?.shopName;
  model.value.shopAvatar = shopInfo?.avatar;
};

const handleReset = () => {
  window.$dialog?.info({
    closable: false,
    title: 'Reset the form?',
    content: 'Your information will be reset',
    positiveText: 'Reset',
    onPositiveClick() {
      model.value = createModel();
    },
    negativeText: 'Cancel'
  });
};

const [confirmLoading, toggleConfirmLoading] = useToggle(false);

const handleConfirm = async () => {
  toggleConfirmLoading(true);
  try {
    await validate();
    emit('validate', true);
  } catch {
    emit('validate', false);
  } finally {
    delay(() => {
      toggleConfirmLoading(false);
    }, 500);
  }
};

getCategoryTree();
</script>

<template>
  <NForm ref="formRef" :model="model" :rules="rules">
    <NGrid item-responsive responsive="screen">
      <NFormItemGi class="text-bluegray" span="24">
        Enter your target shop name to analyze your partnered Creators and competitors' collaborations
      </NFormItemGi>
      <NFormItemGi span="24" label="Shop Name" path="shopName">
        <ShopSearch @change="handleShopChange" />
      </NFormItemGi>
      <NFormItemGi class="text-bluegray" span="24">Fill in the screening criteria for the creator you need</NFormItemGi>
      <NFormItemGi span="24" label="Category" path="categoryArr">
        <NTreeSelect
          v-model:value="model.categoryArr"
          :options="categoryTreeOptions"
          key-field="localName"
          label-field="localName"
          max-tag-count="responsive"
          multiple
          clearable
          placeholder="Categories"
        ></NTreeSelect>
      </NFormItemGi>
      <NFormItemGi span="24" label="Follower Age" path="followerAgeArr">
        <NCheckboxGroup v-model:value="model.followerAgeArr" class="w-full">
          <NFlex class="w-full" :size="0" justify="space-between" :wrap="false">
            <NCheckbox
              v-for="age in followerAgesOptions"
              :key="age.label"
              :value="age.value"
              :label="age.label"
            ></NCheckbox>
          </NFlex>
        </NCheckboxGroup>
      </NFormItemGi>
      <NFormItemGi span="24" label="Followers Gender" path="followerGender">
        <NSelect
          v-model:value="model.followerGender"
          :options="followersGenderOptions"
          placeholder="Follower Gender"
        ></NSelect>
      </NFormItemGi>
      <NFormItemGi class="flex justify-end" span="24">
        <NButton class="mr-16px" @click="handleReset">Reset</NButton>
        <NButton :loading="confirmLoading" type="primary" @click="handleConfirm">Diagnosis</NButton>
      </NFormItemGi>
    </NGrid>
  </NForm>
</template>

<style scoped></style>
