<template>
  <NFlex vertical justify="center" align="center" class="h-300px w-400px" :size="16">
    <div class="relative h-100px w-100px">
      <div class="container">
        <span></span>
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
    <div class="card">
      <div class="loader">
        <p>loading</p>
        <div class="words">
          <span class="word">Information</span>
          <span class="word">Data</span>
          <span class="word">Results</span>
          <span class="word">Information</span>
        </div>
      </div>
    </div>
  </NFlex>
</template>

<style scoped lang="scss">
.container {
  position: absolute;
  top: 50%;
  left: 50%;
  border-radius: 50%;
  height: 96px;
  width: 96px;
  animation: rotate_3922 1.2s linear infinite;
  background-color: #9b59b6;
  background-image: linear-gradient(#9b59b6, #84cdfa, #5ad1cd);
}

.container span {
  position: absolute;
  border-radius: 50%;
  height: 100%;
  width: 100%;
  background-color: #9b59b6;
  background-image: linear-gradient(#9b59b6, #84cdfa, #5ad1cd);
}

.container span:nth-of-type(1) {
  filter: blur(5px);
}

.container span:nth-of-type(2) {
  filter: blur(10px);
}

.container span:nth-of-type(3) {
  filter: blur(25px);
}

.container span:nth-of-type(4) {
  filter: blur(50px);
}

.container::after {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  background-color: #fff;
  border: solid 5px #ffffff;
  border-radius: 50%;
}

@keyframes rotate_3922 {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* From Uiverse.io by kennyotsu */
.card {
  /* color used to softly clip top and bottom of the .words container */
  // padding: 1rem 2rem;
  // border-radius: 1.25rem;
  margin-left: 50px;
}
.loader {
  color: rgb(124, 124, 124);
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 25px;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
  height: 40px;
  padding: 10px 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  border-radius: 8px;
}

.words {
  overflow: hidden;
  position: relative;
}
.words::after {
  content: '';
  position: absolute;
  inset: 0;
  z-index: 20;
}

.word {
  display: block;
  height: 100%;
  padding-left: 6px;
  color: #a855f7;
  animation: spin_4991 3s infinite;
}

@keyframes spin_4991 {
  10% {
    -webkit-transform: translateY(-102%);
    transform: translateY(-102%);
  }

  25% {
    -webkit-transform: translateY(-100%);
    transform: translateY(-100%);
  }

  35% {
    -webkit-transform: translateY(-202%);
    transform: translateY(-202%);
  }

  50% {
    -webkit-transform: translateY(-200%);
    transform: translateY(-200%);
  }

  75% {
    -webkit-transform: translateY(-302%);
    transform: translateY(-302%);
  }

  100% {
    -webkit-transform: translateY(-300%);
    transform: translateY(-300%);
  }

  // 85% {
  //   -webkit-transform: translateY(-402%);
  //   transform: translateY(-402%);
  // }

  // 100% {
  //   -webkit-transform: translateY(-400%);
  //   transform: translateY(-400%);
  // }
}
</style>
