<script setup lang="tsx">
import { watch } from 'vue';
import { fetchDeleteMessageTemplate, fetchGetMessageTemplateListByPage } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { dateFormat } from '@/utils/date';
import ButtonIcon from '@/components/custom/button-icon.vue';

type Message = Api.CreatorNetwork.MessageTemplate;

const show = defineModel<boolean>('show', {
  required: true
});

const { data, columns, loading, pagination, getData } = useTable({
  apiFn: fetchGetMessageTemplateListByPage,
  apiParams: {
    current: 1,
    size: 20
  },
  columns: () => [
    {
      key: 'id',
      title: 'ID',
      width: 50
    },
    {
      key: 'name',
      title: 'Name',
      // width: 200,
      ellipsis: {
        tooltip: true
      }
    },
    {
      key: 'title',
      title: 'Title'
      // width: 200
    },
    {
      key: 'createTime',
      title: 'Creation Time',
      // width: 180,
      render(row) {
        return dateFormat(row.createTime);
      }
    },
    {
      key: 'operate',
      title: 'Actions',
      width: 120,
      render(row) {
        return (
          <div class="flex gap-2">
            <ButtonIcon
              icon="solar:trash-bin-trash-linear"
              tooltipContent="Delete"
              tooltipPlacement="top"
              type="error"
              onClick={() => handleDelete(row)}
            />
          </div>
        );
      }
    }
  ]
});

function handleDelete(row: Message) {
  const delDialog = window.$dialog?.warning({
    title: 'Delete Message',
    content: `Are you sure you want to delete the message "${row.title}"?`,
    positiveText: 'Delete',
    negativeText: 'Cancel',
    onPositiveClick: async () => {
      delDialog && (delDialog.loading = true);
      const { error: delErr } = await fetchDeleteMessageTemplate(row.id);
      if (!delErr) {
        window.$message?.success('Message deleted successfully');
        getData();
      }
      delDialog && (delDialog.loading = false);
    }
  });
}

watch(
  () => show.value,
  newVal => {
    if (newVal) {
      getData();
    }
  }
);
</script>

<template>
  <NDrawer v-model:show="show" width="800px">
    <NDrawerContent title="Message Management" closable>
      <NDataTable
        class="h-full"
        remote
        flex-height
        :bordered="false"
        :loading="loading"
        :columns="columns"
        :data="data"
        :pagination="pagination"
      />
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
