/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-08 15:00:59
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-08-16 15:00:46
 * @FilePath: \tiksage-frontend\src\service\api\analysis.ts
 * @Description: analysis api
 */

import { request } from '../request';

// fetch to create a diagnosis
export function fetchDiagnosis(data: Api.Analysis.DiagnosisParams) {
  return request({
    url: '/analyzer/getShopsCreatorAndScore',
    method: 'post',
    data
  });
}

// fetch to get report
export function fetchAnalysisReport() {
  return request<Api.Analysis.ReportInfo>({
    url: '/analyzer/getLastData',
    method: 'get'
  });
}

// fetch to get report history
export function fetchAnalysisReportHistoryById(taskId: string) {
  return request<Api.Analysis.ReportInfo>({
    timeout: 50000,
    url: '/analyzer/getDataByTaskId',
    method: 'get',
    params: { taskId }
  });
}

// fetch to get report history
export function fetchAnalysisReportHistoryList(data: Api.Common.CommonSearchParams) {
  return request<Api.Analysis.ReportListResponse>({
    timeout: 50000,
    url: '/analyzer/getHistoryPullRecord',
    method: 'get',
    params: { ...data }
  });
}

export function fetchQueryShopInfomation(shopName: string) {
  return request<Api.Analysis.ShopInfo>({
    url: '/aha-creator/searchShop',
    method: 'get',
    params: { shopName }
  });
}

export function fetchDownloadExcel(taskId: number) {
  return request({
    responseType: 'blob',
    url: '/analyzer/export',
    method: 'get',
    params: { taskId }
  });
}

export function fetchDeleteById(taskId: number) {
  return request({
    url: '/analyzer/deleteTask',
    method: 'delete',
    params: { taskId }
  });
}
