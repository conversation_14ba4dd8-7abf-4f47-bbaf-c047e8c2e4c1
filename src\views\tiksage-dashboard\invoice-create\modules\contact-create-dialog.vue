<script setup lang="ts">
import { computed, ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { fetchCreateContact } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

interface Emit {
  (e: 'change'): void;
}

const emit = defineEmits<Emit>();

interface Props {
  shopId: number;
}

const props = defineProps<Props>();

const show = defineModel('show', {
  type: Boolean,
  required: true,
  default: false
});

const [loading, toggleLoading] = useToggle(false);
const model = ref<Api.Invoice.ContactorCreateParams>(createDefaultContact());

function createDefaultContact(): Api.Invoice.ContactorCreateParams {
  return {
    name: '',
    email: '',
    shopId: props.shopId,
    isDefault: false
  };
}

const { formRef, validate } = useNaiveForm();

const rules = computed(() => {
  const { defaultRequiredRule } = useFormRules();
  return {
    name: [defaultRequiredRule]
  };
});

function handleCancel() {
  model.value = createDefaultContact();
  show.value = false;
}

async function handleSava() {
  await validate();
  toggleLoading(true);

  const { error } = await fetchCreateContact(model.value);
  if (!error) {
    window.$message?.success('Contact created successfully');
    emit('change');
    model.value = createDefaultContact();
    show.value = false;
  }
  toggleLoading(false);
}
</script>

<template>
  <NModal v-model:show="show" preset="dialog">
    <template #header>
      <span>Add New Contact</span>
    </template>
    <NForm ref="formRef" :model="model" :rules="rules">
      <NFormItem label="Name" path="name">
        <NInput v-model:value="model.name" placeholder="Client name"></NInput>
      </NFormItem>
      <NFormItem label="Email" path="email">
        <NInput v-model:value="model.email" placeholder="Email" />
      </NFormItem>
      <NCheckbox v-model:checked="model.isDefault">Set as default contact</NCheckbox>
    </NForm>
    <template #action>
      <div class="flex justify-end gap-4">
        <NButton @click="handleCancel">Cancel</NButton>
        <NButton type="primary" :loading="loading" @click="handleSava">Save</NButton>
      </div>
    </template>
  </NModal>
</template>

<style scoped></style>
