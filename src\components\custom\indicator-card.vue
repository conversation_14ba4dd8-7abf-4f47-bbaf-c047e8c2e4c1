<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-26 14:17:14
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-15 11:37:23
 * @FilePath: \tiksage-frontend\src\views\category-intelligence\modules\indicator-card.vue
 * @Description: indicator-card
-->
<script setup lang="ts">
import { computed, ref, watchEffect } from 'vue';
import { divide, isNumber, toNumber } from 'lodash-es';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';

interface Props {
  metric: CommonType.Component.Metric<any>;
}
const props = defineProps<Props>();

const { numberFormat, extractParts } = useNumberFormat();

const percent = computed(() => {
  const { value, prevValue } = props.metric;
  if (!isNumber(prevValue)) return '0%';
  return numberFormat(divide(value - prevValue, prevValue), NumeralFormat.PlusPercent);
});

const isInteger = ref<boolean>(false);

const prefix = ref('');
const showNum = ref(0);
const suffix = ref('');
watchEffect(() => {
  isInteger.value = false;
  const num = numberFormat(props.metric.value, props.metric.unit as NumeralFormat);
  const parts = extractParts(num);

  prefix.value = parts?.prefix || '';
  showNum.value = toNumber(parts?.number) || 0;

  if (showNum.value % 1 === 0) {
    isInteger.value = true;
  }
  suffix.value = parts?.suffix || '';
});
</script>

<template>
  <NFlex id="indicator-container" class="flex-1 card-wrapper bg-white p-20px dark:border-dark dark:bg-dark" vertical>
    <NFlex :wrap="false" justify="space-between" align="center">
      <NFlex vertical>
        <CountTo
          class="text-2xl text-dark font-semibold dark:text-white"
          :prefix="prefix"
          :start-value="0"
          :end-value="showNum"
          :suffix="suffix"
          :decimals="isInteger ? 0 : props.metric.decimals"
        />
        <NFlex align="center" :wrap="false">
          <NText class="text-sm text-gray font-normal">{{ props.metric.title }}</NText>
          <Tip v-if="metric.description.length" :description="props.metric.description" />
        </NFlex>
      </NFlex>
      <NIconWrapper :size="58" class="bg-primary/10 color-primary">
        <SvgIcon class="h-30px w-30px" :icon="props.metric.icon" />
      </NIconWrapper>
    </NFlex>
    <NFlex v-if="metric.isConversion" align="center" class="mt-10px rounded bg-coolgray-1 p-10px text-gray">
      <CycleRatio :percent="percent" text />
      <span class="text-xs">
        {{ metric.conversionDescription }}
      </span>
    </NFlex>
  </NFlex>
</template>

<style scoped lang="scss">
// #indicator-container::before {
//   content: '';
//   --at-apply: absolute top-0 left-0 w-10px h-100% rounded-md bg-primary;
// }
</style>
