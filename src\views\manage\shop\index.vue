<script setup lang="tsx">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { NAvatar, NButton, NTag } from 'naive-ui';
import { isNil } from 'lodash-es';
import { fetchBindShopUser, fetchGetShopUserList, fetchGetUserShopListByPage } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useTable } from '@/hooks/common/table';
import { getFallbackImage } from '@/utils/fake-image';
import UserAuthModal from '../modules/user-auth-modal.vue';

const authStore = useAuthStore();
const checkedRowKeys = ref<number[]>([]);
const [show, toggleShow] = useToggle(false);
const selectShopId = ref<number>();

const { columns, loading, data, pagination, updateSearchParams, getData } = useTable({
  immediate: false,
  apiFn: fetchGetUserShopListByPage,
  apiParams: {
    current: 1,
    size: 10
  },
  columns() {
    return [
      {
        key: 'shopId',
        title: 'Shop',
        render(rowData) {
          return (
            <div class="flex-y-center gap-2">
              <NAvatar src={rowData.avatar} fallbackSrc={getFallbackImage(50, 50)}></NAvatar>
              <div class="flex-col">
                <span class="font-bold">{rowData.shopName}</span>
                <span>@{rowData.shopCode || '-'}</span>
              </div>
            </div>
          );
        }
      },
      {
        key: 'isApiAuthorized',
        title: 'Api Authorized',
        align: 'center',
        render(rowData) {
          return (
            <div class="flex-center">
              {rowData.isApiAuthorized ? (
                <NTag size="small" bordered={false} type="primary">
                  Yes
                </NTag>
              ) : (
                <NTag size="small" bordered={false} type="error">
                  No
                </NTag>
              )}
            </div>
          );
        }
      },
      {
        key: 'operate',
        width: 100,
        render(rowData) {
          return (
            <div class="flex-y-center gap-2">
              <NButton type="primary" size="small" ghost onClick={() => handleShowUserModal(rowData.shopId)}>
                Users
              </NButton>
            </div>
          );
        }
      }
    ];
  }
});

function handleShowUserModal(shopId: number) {
  selectShopId.value = shopId;
  toggleShow(true);
}

async function handleGetCheckedRowKeys() {
  let res: number[] = [];
  if (isNil(selectShopId.value)) return res;

  const { data: ids, error: idListErr } = await fetchGetShopUserList(selectShopId.value);
  if (!idListErr) {
    res = ids;
  }

  return res;
}

async function handleBindShopUser(checkedKeys: number[] | string[]) {
  if (isNil(selectShopId.value)) return false;

  const { error: bindUserErr } = await fetchBindShopUser(selectShopId.value, checkedKeys as number[]);
  if (!bindUserErr) {
    return true;
  }

  return false;
}

function initData() {
  updateSearchParams({
    userId: Number(authStore.userInfo.id)
  });
  getData();
}

initData();
</script>

<template>
  <div ref="wrapperRef" class="flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NCard class="min-h-400px flex-1 card-wrapper" title="Shop List" :bordered="false" size="small">
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :bordered="false"
        :columns="columns"
        :data="data"
        :loading="loading"
        :row-key="row => row.id"
        remote
        :pagination="pagination"
        class="sm:h-full"
      />
    </NCard>
    <UserAuthModal v-model:show="show" :get-checked-row-keys="handleGetCheckedRowKeys" :submit="handleBindShopUser" />
  </div>
</template>

<style scoped></style>
