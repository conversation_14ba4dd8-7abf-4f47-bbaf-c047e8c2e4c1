import { request } from '../request';

export function fetchGetVideoApprovalList(data: Api.VideoManage.VideoApprovalListSearchParams) {
  return request<Api.VideoManage.VideoApprovalListResponse[]>({
    url: '/approval/listVideoApproval',
    method: 'post',
    data
  });
}

export function fetchCreateVideoApproval(data: Api.VideoManage.CreateVideoParams) {
  return request({
    url: '/approval/createVideoApproval',
    method: 'post',
    data
  });
}

export function fetchGetVideoApprovalInfoById(id: number) {
  return request<Api.VideoManage.VideoInfo>({
    url: '/approval/getVideoApproval',
    method: 'get',
    params: { id }
  });
}

export function fetchGetApprovalUserOptions() {
  return request<Api.VideoManage.ApprovalUserOption[]>({
    url: '/approval/getApprovalUser',
    method: 'get'
  });
}

export function fetchGetShopProductOptions(data: Api.VideoManage.ApprovalUserOption) {
  return request<Api.VideoManage.ShopProductOption[]>({
    url: '/approval/selectShopProductDropDown',
    method: 'post',
    data
  });
}

export function fetchGetProductList(data: Api.VideoManage.ShopProductListSearchParams) {
  return request<Api.VideoManage.ShopProductListResponse>({
    url: '/approval/selectShopProductPage',
    method: 'post',
    data
  });
}

export function fetchUpdateShopProductStatus(params: Api.VideoManage.UpdateShopProductStatusSearchParams) {
  return request({
    url: '/approval/updateDesignatedStatus',
    method: 'put',
    params
  });
}

export function fetchGetShopOptions() {
  return request<Api.VideoManage.ShopOption[]>({
    url: '/shop/shopDropDownSelection',
    method: 'get'
  });
}

export function fetchApproveVideo(id: number) {
  return request({
    url: '/approval/videoApproved',
    method: 'put',
    params: { id }
  });
}

export function fetchRejectVideo(data: Api.VideoManage.RejectVideoParams) {
  return request({
    url: '/approval/videoRejected',
    method: 'put',
    data
  });
}

export function fetchDeleteVideo(id: number) {
  return request({
    url: '/approval/deleteVideoApproval',
    method: 'delete',
    params: { id }
  });
}

export function fetchUploadFile(file: File) {
  return request<{ fileName: string }>({
    url: '/approval/uploadVideo',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    method: 'post',
    data: {
      file
    }
  });
}
