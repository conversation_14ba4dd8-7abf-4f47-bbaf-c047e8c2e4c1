/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-02 11:49:42
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-09-05 17:37:18
 * @FilePath: \tiksage-frontend\src\hooks\custom\numberFormat.ts
 * @Description: useNumberFormat
 */
import { isFinite, isNil } from 'lodash-es';
import numbro from 'numbro';
import { NumeralFormat } from '@/enum';
export function useNumberFormat() {
  const numberFormat = (value: any, type: NumeralFormat = NumeralFormat.Number) => {
    let val = value;
    if (isNil(val)) val = 0;
    if (!isFinite(val)) val = -0;
    // let result
    // switch (type) {
    //   case NumeralFormat.Dollar:
    //     result = numbro(value).formatCurrency({
    //       average: true,
    //       mantissa: 2
    //     })
    //     break
    //   case NumeralFormat.Percent:
    //     result = numbro(value).format(NumeralFormat.Percent)
    //     break
    //   case NumeralFormat.PlusPercent:
    //     result = numbro(value).format({
    //       forceSign: true,
    //       thousandSeparated: true,
    //       output: 'percent',
    //       mantissa: 2
    //     })
    //     break
    //   default:
    //     result = numbro(value).format({
    //       average: true,
    //       mantissa: 2
    //     })
    // }
    // return result
    return numbro(val).format(type);
  };
  const unFormat = (value: any) => {
    return numbro.unformat(value);
  };

  const extractParts = (str: string) => {
    // Attempt to match a prefix (non-digit characters), a number (possibly with a decimal point), and a suffix (non-digit characters)
    // Note: This regex assumes the prefix and suffix are as short as possible, and only matches the first encountered non-digit part as the suffix
    const regex = /^([^0-9]*)([0-9]+(\.[0-9]+)?)([^0-9]*)$/;
    const match = str.match(regex);

    if (match) {
      // match[1] is the prefix
      // match[2] is the number part (possibly including a decimal point)
      // match[4] is the suffix (note: since we used capturing groups to skip the decimal digits part, the suffix starts from the first non-digit character)
      // Note: We're not using match[3] as it only matches the decimal digits part (if present), and we want the entire number part
      return {
        prefix: match[1],
        number: match[2],
        suffix: match[4]
      };
    }
    // If no parts are matched, return an empty object or an appropriate error message
    return {};
  };

  return {
    numberFormat,
    unFormat,
    extractParts
  };
}
