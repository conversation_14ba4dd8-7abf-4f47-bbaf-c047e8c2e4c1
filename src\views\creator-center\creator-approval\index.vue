<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-11-18 09:30:47
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-29 14:29:46
 * @FilePath: \tiksage-frontend\src\views\creator-manage\creator-approval\index.vue
 * @Description: creator-approval
-->
<script setup lang="ts">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { fetchGetApprovalUserOptions } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useAuth } from '@/hooks/business/auth';
import { useRouterPush } from '@/hooks/common/router';
import TabContainer from './modules/tab-container.vue';
import CreatorNewDrawer from './modules/creator-new-drawer.vue';

const { hasAuth } = useAuth();
const authStore = useAuthStore();

const { routerPushByKeyWithRedirect } = useRouterPush();

const [addDrawerShow, toggleAddDrawerShow] = useToggle(false);

function handleNewCreators() {
  toggleAddDrawerShow(true);
}

const childRef = ref<typeof TabContainer>();

function handleAdd(clientId?: number | null) {
  if (childRef.value) {
    childRef.value.getData({ size: 12, current: 1, client: clientId, status: 0 });
  }
}

const clientOptions = ref<Api.VideoManage.ApprovalUserOption[]>([]);

const [loading, toggleLoading] = useToggle(false);

async function initClientOptions() {
  toggleLoading(true);
  if (hasAuth('creator-approval:approver')) {
    clientOptions.value = [
      {
        userId: Number(authStore.userInfo.id),
        shopName: '',
        shopId: -1
      }
    ];
  }
  if (hasAuth('creator-approval:operator')) {
    const { data: optionsData, error: optionsError } = await fetchGetApprovalUserOptions();
    if (!optionsError) {
      clientOptions.value = optionsData;
    }
  }
  toggleLoading(false);
}

function handleLinkTo() {
  routerPushByKeyWithRedirect('creator-center_creator-approval-video-status');
}

initClientOptions();
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="card-wrapper" :bordered="false" title="Creator Approval">
      <template #header-extra>
        <div class="flex justify-end gap-4">
          <NButton v-if="hasAuth('creator-approval:operator')" strong secondary @click="handleNewCreators">
            <template #icon>
              <icon-solar:add-circle-linear />
            </template>
            New Creators
          </NButton>
          <NButton strong secondary @click="handleLinkTo">
            <template #icon>
              <icon-solar:videocamera-record-linear />
            </template>
            Creator Content
          </NButton>
        </div>
      </template>
    </NCard>
    <NSpin v-if="loading" class="flex-1"></NSpin>
    <template v-else>
      <TabContainer ref="childRef" :client-options="clientOptions" />
      <CreatorNewDrawer v-model:show="addDrawerShow" :client-options="clientOptions" @submit="handleAdd" />
    </template>
  </NFlex>
</template>

<style scoped></style>
