<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-09-19 10:09:17
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-05 11:11:56
 * @FilePath: \tiksage-frontend\src\components\vchart\v-sparkline-chart.vue
 * @Description: visactor-sparkline-chart
-->
<script setup lang="ts">
import { onBeforeUnmount, onMounted, onUpdated, ref } from 'vue';
import { merge } from 'lodash-es';
import { VChart } from '@visactor/vchart';
import { theme } from '@/constants/visactor-vchart';
import { optionsHOC } from '@/utils/chart-options';

interface Props {
  chartOptions?: Visactor.VChart.IAreaChartSpec;
}
const props = defineProps<Props>();

let chart: Visactor.VChart.IVChart;

const chartContainer = ref<HTMLDivElement>();

function parseSpec(chartProps: Props) {
  const { chartOptions } = chartProps;

  let baseOptions: Visactor.VChart.IAreaChartSpec = {
    type: 'area',
    padding: 0,
    seriesField: 'type',
    data: {
      values: []
    },
    point: {
      visible: false
    },
    line: {
      style: {
        curveType: 'monotone'
      }
    },
    axes: [
      {
        orient: 'bottom',
        visible: false
      }
    ],
    tooltip: {
      dimension: {
        title: {
          visible: false
        }
      }
    },
    area: {
      style: {
        fill: {
          gradient: 'linear',
          x0: 0.5,
          y0: 0,
          x1: 0.5,
          y1: 1,
          stops: [
            {
              offset: 0,
              opacity: 1
            },
            {
              offset: 1,
              opacity: 0
            }
          ]
        }
      }
    },
    ...theme
  };

  if (!chartOptions) return baseOptions;

  baseOptions = optionsHOC(baseOptions, chartOptions, false);

  return chartOptions ? merge(baseOptions, chartProps.chartOptions) : baseOptions;
}

function createOrUpdateChart(chartProps: Props) {
  if (chartContainer.value && !chart) {
    chart = new VChart(parseSpec(chartProps), {
      dom: chartContainer.value
    });

    chart.renderSync();
    return true;
  } else if (chart) {
    chart.updateSpec(parseSpec(chartProps));
    chart.renderSync();

    return true;
  }
  return false;
}

onMounted(() => {
  createOrUpdateChart(props);
});

onUpdated(() => {
  createOrUpdateChart(props);
});

onBeforeUnmount(() => {
  if (chart) {
    chart.release();
  }
});
</script>

<template>
  <div ref="chartContainer" class="h-40px w-full"></div>
</template>

<style scoped></style>
