<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import dayjs from 'dayjs';
import { upperFirst } from 'lodash-es';
import { TimeFormat } from '@/enum';

interface Emits {
  (e: 'change', value: string[]): void;
}

interface Props {
  undisableDateRange?: string[];
  defaultSelecetedType?: NaiveUI.DatePickerType;
  defaultDateTypes?: Array<NaiveUI.DatePickerType>;
}

const props = withDefaults(defineProps<Props>(), {
  defaultDateTypes: () => ['week', 'month', 'year'],
  defaultSelecetedType: 'month'
});
const emit = defineEmits<Emits>();

const dateTypeOptions = computed<CommonType.Option<NaiveUI.DatePickerType>[]>(() => {
  return props.defaultDateTypes.map(item => ({
    label: upperFirst(item),
    value: item
  }));
});
const dateType = ref<NaiveUI.DatePickerType>(props.defaultSelecetedType);

const formatValue = ref<string>();

// transform date range to real date range
function transformRealDateRange(type: NaiveUI.DatePickerType, value: string): string[] {
  let res: any[] = [];
  const currentRange = type === 'week' ? dayjs(value, 'YYYY-wo') : dayjs(value);
  let previousRange;

  switch (type) {
    case 'week':
      previousRange = currentRange.subtract(1, 'week');
      res = [
        currentRange.startOf('week'),
        currentRange.endOf('week'),
        previousRange.startOf('week'),
        previousRange.endOf('week')
      ];
      break;
    case 'month':
      previousRange = currentRange.subtract(1, 'month');
      res = [
        currentRange.startOf('month'),
        currentRange.endOf('month'),
        previousRange.startOf('month'),
        previousRange.endOf('month')
      ];
      break;
    case 'year':
      previousRange = currentRange.subtract(1, 'year');
      res = [
        currentRange.startOf('year'),
        currentRange.endOf('year'),
        previousRange.startOf('year'),
        previousRange.endOf('year')
      ];
      break;
    default:
      break;
  }
  return [
    res[0].format(TimeFormat.CN_DATE),
    res[1].format(TimeFormat.CN_DATE),
    res[2].format(TimeFormat.CN_DATE),
    res[3].format(TimeFormat.CN_DATE)
  ];
}

// init current date range
function initFormatValue(type: NaiveUI.DatePickerType) {
  let res = '';
  const now = dayjs(props.undisableDateRange?.[1]);

  switch (type) {
    case 'week':
      res = now.format('YYYY-wo');
      break;
    case 'month':
      res = now.format('YYYY-MM');
      break;
    case 'year':
      res = now.format('YYYY');
      break;
    default:
      break;
  }
  return res;
}

function isDateDisabled(current: number) {
  if (!props.undisableDateRange) return false;
  const [start, end] = props.undisableDateRange;
  if (start && end) {
    return current < dayjs(start).valueOf() || current > dayjs(end).valueOf();
  }
  return current > dayjs().valueOf();
}

watch(
  () => dateType.value,
  newVal => {
    formatValue.value = initFormatValue(newVal);
  },
  {
    immediate: true
  }
);

watch(
  () => formatValue.value,
  newVal => {
    if (newVal) {
      emit('change', transformRealDateRange(dateType.value, newVal));
    }
  },
  {
    immediate: true
  }
);
</script>

<template>
  <div class="flex gap-4">
    <NButtonGroup>
      <NButton
        v-for="opt in dateTypeOptions"
        :key="opt.value"
        strong
        secondary
        :type="dateType === opt.value ? 'primary' : 'default'"
        @click="dateType = opt.value"
      >
        {{ opt.label }}
      </NButton>
    </NButtonGroup>
    <NDatePicker
      v-model:formatted-value="formatValue"
      input-readonly
      :format="dateType === 'week' ? 'YYYY-wo' : undefined"
      :actions="dateType !== 'week' ? ['confirm'] : []"
      :type="dateType"
      :is-date-disabled="isDateDisabled"
    />
  </div>
</template>

<style scoped></style>
