<script setup lang="ts">
import { computed, watch } from 'vue';
import { assign, isNil } from 'lodash-es';
import { useVChart } from '@/hooks/common/vchart';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { initPieLegend } from '@/utils/chart-options';
import { NumeralFormat } from '@/enum';

interface Props {
  data: Api.CreatorPerformance.CreatorLevel[];
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const pieDefaultSpec: Visactor.VChart.IPieChartSpec = {
  type: 'pie',
  data: {
    values: []
  },
  outerRadius: 0.8,
  innerRadius: 0.5,
  layoutRadius: layoutRect => {
    const { width, height } = layoutRect;

    return Math.min(width, height) / 2.2;
  },
  padAngle: 0.6,
  valueField: 'value',
  categoryField: 'type',
  pie: {
    style: {
      cornerRadius: 10
    },
    state: {
      hover: {
        outerRadius: 0.85,
        stroke: '#000',
        lineWidth: 1
      },
      selected: {
        outerRadius: 0.85,
        stroke: '#000',
        lineWidth: 1
      }
    }
  },
  legends: {
    visible: false,
    orient: 'bottom'
  },
  label: {
    visible: true
  },
  tooltip: {
    mark: {
      title: {
        visible: false
      },
      content: [
        {
          key: v => {
            if (v?.type === '-1') {
              return 'Unknown';
            }
            return v ? v.type : '';
          },
          value: datum => {
            const isGMV = !isNil(datum?.gmvCreatorCount);
            return `${datum && numberFormat(datum.value, isGMV ? NumeralFormat.Dollar : NumeralFormat.Number)} (${datum && datum._percent_}%)`; // eslint-disable-line no-underscore-dangle
          }
        }
      ]
    }
  },
  emptyPlaceholder: {
    showEmptyCircle: true,
    emptyCircle: {
      style: {
        innerRadius: 0.5,
        fill: '#F4F5F5'
      }
    }
  }
};

const { domRef: pieRef, updateSpec } = useVChart(() => pieDefaultSpec);
const { domRef: pieGMVRef, updateSpec: updateGMVSpec } = useVChart(() => pieDefaultSpec);

const pieValues = computed(() => {
  return props.data.map(item => {
    return {
      type: item.level !== '-1' ? `L${item.level}` : 'Unknown',
      value: item.creatorCount
    };
  });
});

const pieGMVValues = computed(() => {
  return props.data.map(item => {
    return {
      type: item.level !== '-1' ? `L${item.level}` : 'Unknown',
      value: item.gmv,
      gmvCreatorCount: item.gmvCreatorCount
    };
  });
});

function initPieData(originalData: any[]) {
  const total = pieValues.value.reduce((pre, cur) => {
    return pre + cur.value;
  }, 0);

  updateSpec(opts => {
    const res = assign({}, opts, {
      data: [
        {
          id: 'number',
          values: originalData
        }
      ],
      label: {
        visible: false
      },
      legends: initPieLegend(originalData, total, 10, NumeralFormat.Number)
    });
    return res;
  });
}

function initPieGMVData(originalData: any[]) {
  const total = pieGMVValues.value.reduce((pre, cur) => {
    return pre + cur.value;
  }, 0);
  updateGMVSpec(opts => {
    const res = assign({}, opts, {
      data: [
        {
          id: 'dollar',
          values: originalData
        }
      ],
      label: {
        visible: false
      },
      legends: customInitPieGmvLegend(originalData, total, 10, NumeralFormat.Dollar)
    });
    return res;
  });
}

// eslint-disable-next-line max-params
function customInitPieGmvLegend(
  data: any[],
  total: number,
  sliceNumber = 5,
  unit = NumeralFormat.Dollar,
  lengendWidth = '50%'
) {
  return {
    visible: true,
    orient: 'right',
    data: (legendDatum: any) => {
      return legendDatum.slice(0, sliceNumber).map((d: any) => {
        const originalDatum = data.find(v => v.type === d.label);
        const val = originalDatum?.value || 0;
        const gmvCreatorCount = originalDatum?.gmvCreatorCount || 0;
        const value = `${gmvCreatorCount} | ${numberFormat(val, unit)}(${numberFormat(val / total, NumeralFormat.Percent)})`;
        return {
          ...d,
          value
        };
      });
    },
    item: {
      width: lengendWidth,
      spaceRow: 8,
      autoEllipsisStrategy: 'valueFirst',
      shape: {
        style: {
          symbolType: 'circle'
        }
      },
      label: {
        style: {
          fontSize: 14,
          fill: '#9ca3af'
        }
      },
      value: {
        alignRight: true,
        style: {
          textAlign: 'right',
          fontSize: 14,
          fill: '#000',
          fontWeight: 'bold',
          ellipsis: false
        }
      }
    },
    autoPage: false
  };
}

watch(
  () => props.data,
  newVal => {
    if (newVal) {
      initPieData(pieValues.value);
      initPieGMVData(pieGMVValues.value);
    }
  }
);
</script>

<template>
  <NGrid :cols="2" :x-gap="16" :y-gap="16">
    <NGi>
      <NCard class="card-wrapper" :bordered="false" title="Creator Confirmed Performance">
        <div ref="pieRef" class="h-350px w-full"></div>
      </NCard>
    </NGi>
    <NGi>
      <NCard class="card-wrapper" :bordered="false">
        <template #header>
          <div class="flex-y-center gap-2">
            <span>Creator GMV Performance</span>
            <Tip description="GMV-Generating Users / GMV / Percentage of Total GMV" />
          </div>
        </template>
        <div ref="pieGMVRef" class="h-350px w-full"></div>
      </NCard>
    </NGi>
  </NGrid>
</template>

<style scoped></style>
