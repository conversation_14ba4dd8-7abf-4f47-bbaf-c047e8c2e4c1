<script setup lang="ts">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { fetchGetEmailTaskConfig, fetchUpdateEmailTaskConfig } from '@/service/api';

function createDefaultModel() {
  return {
    holidaySendingFlag: false,
    sendInterval: null,
    periodEnd: null,
    periodStart: null,
    singleSendLimit: null
  };
}
const model = ref<Api.CreatorNetwork.EmailTaskConfigResponse>(createDefaultModel());

async function initData() {
  const { data, error } = await fetchGetEmailTaskConfig();
  if (!error) {
    model.value = data;
  }
}

async function handleSubmit() {
  const { error } = await fetchUpdateEmailTaskConfig(model.value);
  // After the update failed, re-obtain the Model
  if (error) {
    await initData();
  }
}

const [resetLoading, toggleResetLoading] = useToggle(false);

async function handleReset() {
  toggleResetLoading(true);
  model.value = createDefaultModel();
  await handleSubmit();
  toggleResetLoading(false);
}

initData();
</script>

<template>
  <NCard class="card-wrapper" segmented :bordered="false" title="Mass Sending Management">
    <template #header-extra>
      <NPopconfirm type="warning" @positive-click="handleReset">
        <template #trigger>
          <NButton strong secondary type="warning" :loading="resetLoading">
            <template #icon>
              <icon-solar:restart-circle-linear />
            </template>
            Reset
          </NButton>
        </template>
        Are you sure you want to reset?
      </NPopconfirm>
    </template>
    <NForm label-placement="left" label-width="130" label-align="left">
      <NGrid cols="2">
        <NGi>
          <NCard :bordered="false" title="Sending Frequency Settings">
            <NFormItem label="Single Send Limit">
              <NInputGroup>
                <NInputNumber
                  v-model:value="model.singleSendLimit"
                  class="w-300px"
                  placeholder="Enter number"
                  :min="1"
                  clearable
                  @blur="handleSubmit"
                >
                  <template #suffix>
                    <span>emails</span>
                  </template>
                </NInputNumber>
              </NInputGroup>
            </NFormItem>
            <NFormItem label="Send Interval">
              <NInputGroup>
                <NInputNumber
                  v-model:value="model.sendInterval"
                  class="w-300px"
                  placeholder="Enter number"
                  :min="1"
                  clearable
                  @blur="handleSubmit"
                >
                  <template #suffix>
                    <span>minutes</span>
                  </template>
                </NInputNumber>
              </NInputGroup>
            </NFormItem>
          </NCard>
        </NGi>
        <NGi>
          <NCard :bordered="false" title="Mass Sending Time Settings">
            <NFormItem label="Sending Period">
              <NInputGroup>
                <NInputGroup>
                  <NTimePicker
                    v-model:formatted-value="model.periodStart"
                    placeholder="Start Time"
                    clearable
                    @blur="handleSubmit"
                  />
                  <div class="flex-center px-2">to</div>
                  <NTimePicker
                    v-model:formatted-value="model.periodEnd"
                    placeholder="End Time"
                    clearable
                    @blur="handleSubmit"
                  />
                  <div class="flex-center px-1">(UTC-8)</div>
                </NInputGroup>
              </NInputGroup>
            </NFormItem>
            <!--
 <NFormItem label="Holiday Sending">
              <NSwitch v-model:value="model.holidaySendingFlag" @update:value="handleSubmit" />
            </NFormItem> 
-->
          </NCard>
        </NGi>
      </NGrid>
    </NForm>
  </NCard>
</template>

<style scoped></style>
