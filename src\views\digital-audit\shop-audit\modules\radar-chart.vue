<script setup lang="ts">
import { computed } from 'vue';
import { useVChart } from '@/hooks/common/vchart';

interface Props {
  data: Api.ShopAudit.ScoreList[];
}

const props = defineProps<Props>();

const radarSpec = computed<Visactor.VChart.IChartSpec>(() => {
  return {
    type: 'radar',
    padding: 0,
    outerRadius: 0.8,
    color: ['#a855f7'],
    data: [
      {
        id: 'radarData',
        values: props.data
      }
    ],
    categoryField: 'title',
    valueField: 'score',
    point: {
      visible: true
    },
    area: {
      visible: true, // display area
      state: {
        // The style in the hover state of the area
        // hover: {
        //   fillOpacity: 0.5
        // }
      }
    },
    line: {
      style: {
        lineWidth: 2
      }
    },
    label: {
      visible: true,
      style: {
        fill: '#000',
        fontWeight: '700',
        background: 'rgba(255, 255, 255, 0.75)',

        boundsPadding: [2, 4, 2, 4]
      }
    },
    axes: [
      {
        orient: 'radius', // radius axis
        zIndex: 100,
        min: 0,
        max: 100,
        domainLine: {
          visible: false
        },
        label: {
          visible: false,
          space: 0,
          style: {
            textAlign: 'center',
            stroke: '#fff',
            lineWidth: 4,
            wordBreak: 'break word'
          }
        },
        grid: {
          smooth: false,
          style: {
            stroke: '#808080', // 更深的灰色
            lineDash: [0]
          }
        }
      },
      {
        orient: 'angle', // angle axis
        zIndex: 50,
        tick: {
          visible: false
        },
        domainLine: {
          visible: false
        },
        label: {
          visible: true,
          autoLimit: true,
          space: 10,
          style: {
            fill: '#000',
            fontWeight: '700',
            background: 'rgba(240, 240, 240, 0.8)',
            boundsPadding: [4, 8, 4, 8]
          }
        },
        grid: {
          style: {
            stroke: '#808080', // 保持一致
            lineDash: [0]
          }
        }
      }
    ]
  };
});

const { domRef: radarRef } = useVChart(() => radarSpec.value);
</script>

<template>
  <div ref="radarRef" class="h-330px"></div>
</template>

<style scoped></style>
