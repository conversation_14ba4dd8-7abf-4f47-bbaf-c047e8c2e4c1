<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-29 10:49:32
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-09-29 16:23:56
 * @FilePath: \tiksage-frontend\src\views\category-intelligence\modules\shop-composition-card.vue
 * @Description: shop-composition-card
-->
<script setup lang="ts">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { orderBy } from 'lodash-es';
import { fetchShopMonData } from '@/service/api';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';
import CategoryShopSelect from './category-shop-select.vue';

interface Props {
  shopType: number;
  month: string;
}
const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const [loading, toggleLoading] = useToggle(false);

const chartData = ref<Visactor.VChart.ITreemapChartSpec>();

// eslint-disable-next-line max-params
const initData = async (shopType: number, month: string, category: string, thirdCategory: string) => {
  const params = {
    shopType,
    month,
    categroy: category,
    thirdCategory
  };
  const { data, error } = await fetchShopMonData(params);
  if (error) return;
  initChart(data);
};

function initChart(data: Api.CategoryIntelligence.ShopMonResponse) {
  const sortData = orderBy(data, ['salesMob'], ['desc']);

  const result: Visactor.VChart.ITreemapChartSpec = {
    type: 'treemap',
    data: [
      {
        name: 'shops',
        values: [
          {
            name: 'A',
            children: sortData
          }
        ]
      }
    ],
    seriesField: 'shopName',
    categoryField: 'shopName',
    valueField: 'salesMob',
    splitType: 'binary',
    tooltip: {
      visible: false
    },
    label: {
      visible: true,
      style: {
        text: (datum: any) => {
          const { shopName, salesMob } = datum.datum[1];
          return [shopName, `Gross Sales: ${numberFormat(salesMob, NumeralFormat.Dollar)}`];
        },
        fontSize: 12,
        suffixPosition: 'middle'
      }
    }
  };
  chartData.value = result;
}

async function handleSelectChange(model: { category: string; thirdCategory: string; shopIds: number[] }) {
  toggleLoading(true);
  await initData(props.shopType, props.month, model.category, model.thirdCategory);
  toggleLoading(false);
}
</script>

<template>
  <NCard :bordered="false" content-class="min-h-385px flex-center" class="card-wrapper">
    <template #header>Brand Shop Sales Composition</template>
    <template #header-extra>
      <CategoryShopSelect :month="month" :shop-type="shopType" show-third-category @update:value="handleSelectChange" />
    </template>
    <NSpin v-if="loading"></NSpin>
    <div v-else class="w-full flex-1">
      <VTreemapChart :chart-options="chartData" />
    </div>
  </NCard>
</template>

<style scoped></style>
