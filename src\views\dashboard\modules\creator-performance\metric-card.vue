<script setup lang="ts">
import { watch } from 'vue';
import { NCard } from 'naive-ui';
import dayjs from 'dayjs';
import { useVChart } from '@/hooks/common/vchart';
import { handleChartPoint } from '@/utils/chart-options';
import { TimeFormat } from '@/enum';
interface Props {
  metric: {
    title: string;
    icon: string;
    value: number;
    rating: string;
    color: string;
    bgColor: string;
    chartColor: string;
    chartValue: { x: string; y: number }[];
  };
}

const props = defineProps<Props>();

const defaultSpec: Visactor.VChart.IAreaChartSpec = {
  type: 'area',
  padding: [5, 0, 5, 0],
  data: {
    values: []
  },
  seriesField: 'type',
  xField: 'x',
  yField: 'y',
  color: [props.metric.color],
  point: {
    visible: false
  },
  line: {
    style: {
      curveType: 'monotone'
    }
  },
  axes: [
    {
      orient: 'bottom',
      visible: false
    },
    {
      orient: 'left',
      visible: false
    }
  ],
  tooltip: {
    mark: {
      visible: false
    },
    dimension: {
      title: {
        visible: true,
        value: datum => {
          return dayjs(datum?.x).format(TimeFormat.US_DATE);
        }
      }
    }
  },
  area: {
    style: {
      fill: {
        gradient: 'linear',
        x0: 0.5,
        y0: 0,
        x1: 0.5,
        y1: 1,
        stops: [
          {
            offset: 0,
            opacity: 1,
            color: props.metric.bgColor
          },
          {
            offset: 1,
            opacity: 0
          }
        ]
      }
    }
  }
};

const { domRef: lineRef, updateSpec } = useVChart(() => defaultSpec);

watch(
  () => props.metric.chartValue,
  () => {
    updateSpec(oldOpts => {
      return {
        ...oldOpts,
        ...handleChartPoint(props.metric.chartValue.length === 1),
        data: { values: props.metric.chartValue }
      };
    });
  }
);
</script>

<template>
  <NCard class="card-wrapper" content-class="flex-col gap-4 flex-nowrap" :bordered="false">
    <div class="flex gap-4">
      <div class="w-100px flex-col gap-2">
        <div class="h-100px w-full flex-center rounded-xl" :style="{ backgroundColor: metric.bgColor }">
          <SvgIcon :icon="metric.icon" class="text-50px" :style="{ color: metric.color }" />
        </div>
        <div class="h-30px w-full flex-center rounded-lg font-500" :style="{ backgroundColor: metric.bgColor }">
          {{ metric.rating }}
        </div>
      </div>
      <div class="flex-col-center flex-1 items-center gap-4">
        <span class="text-xl font-500">{{ metric.title }}</span>
        <CountTo class="text-2xl font-bold" :end-value="metric.value" />
      </div>
    </div>
    <div class="">
      <div ref="lineRef" class="h-120px w-full"></div>
    </div>
  </NCard>
</template>

<style scoped></style>
