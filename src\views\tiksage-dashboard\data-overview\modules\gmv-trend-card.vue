<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import dayjs from 'dayjs';
import { assign, delay, random } from 'lodash-es';
import { fetchGetGmvTrendOnTikSageDashboard } from '@/service/api';
import { useVChart } from '@/hooks/common/vchart';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat, TimeFormat } from '@/enum';
import { lineAreaDefaultSpec, transformData } from './chart';

interface Props {
  dateRange: string[] | undefined;
}
const props = defineProps<Props>();

const isYear = computed(() => {
  if (!props.dateRange) return false;
  const [startDate, endDate] = props.dateRange;
  const start = dayjs(startDate);
  const end = dayjs(endDate);

  return end.diff(start, 'month', true) >= 1;
});

const { numberFormat } = useNumberFormat();

const selected = ref<string[]>(['gmvTotal', 'gmvLive', 'gmvVideo']);

const selectOptions: CommonType.Option[] = [
  {
    label: 'Total GMV',
    value: 'gmvTotal'
  },
  {
    label: 'Live GMV',
    value: 'gmvLive'
  },
  {
    label: 'Video GMV',
    value: 'gmvVideo'
  },
  {
    label: 'Product Card GMV',
    value: 'gmvProductCard'
  },
  {
    label: 'Affiliate GMV',
    value: 'gmvAffiliate'
  },
  {
    label: 'Non-Affiliate GMV',
    value: 'gmvNoAffiliate'
  }
];

const realData = ref<Api.TikSageDashboard.GmvTrendResponse>([]);

const gmvTrendSpec = computed<Visactor.VChart.IAreaChartSpec>(() => {
  return { ...lineAreaDefaultSpec };
});
const { updateSpec, domRef: gmvTrendRef } = useVChart(() => gmvTrendSpec.value);

function formatSpec() {
  const cusSpec: Visactor.VChart.IAreaChartSpec = {
    type: 'area',
    data: [
      {
        id: `${random(100)}`,
        values: transformData({
          data: realData.value,
          selected: selected.value,
          options: selectOptions,
          xField: 'belongDate'
        }) as any
      }
    ],
    seriesField: 'type',
    xField: 'x',
    yField: 'value',
    stack: false,
    tooltip: {
      activeType: 'dimension',
      dimension: {
        title: {
          value: v => {
            if (v) {
              if (isYear.value) {
                return dayjs(v?.x).format(TimeFormat.US_DATE_NO_DAY);
              }
              return dayjs(v?.x).format(TimeFormat.US_DATE);
            }
            return v;
          }
        },
        content: {
          key: v => {
            return v ? (v?.type as string) : '';
          },
          // keyFormatter: '{type}',
          value: v => {
            return v ? numberFormat(v[cusSpec.yField as string], NumeralFormat.Dollar) : v;
          }
        }
      }
    },
    axes: [
      {
        orient: 'bottom',
        label: {
          autoHide: true,
          formatMethod(v) {
            if (v) {
              if (isYear.value) {
                return dayjs(v as string).format(TimeFormat.US_DATE_NO_DAY);
              }
              return dayjs(v as string).format(TimeFormat.US_DATE);
            }
            return v;
          }
        }
      },
      {
        type: 'linear',
        orient: 'left',
        label: {
          formatMethod: v => {
            return numberFormat(v, NumeralFormat.Dollar);
          }
        }
      }
    ]
  };
  return cusSpec;
}

const loading = ref(false);

async function initData() {
  if (!props.dateRange) return;
  const [startDateStr, endDateStr, perviousStartDateStr, perviousEndDateStr] = props.dateRange;
  const { data, error } = await fetchGetGmvTrendOnTikSageDashboard({
    startDateStr,
    endDateStr,
    perviousStartDateStr,
    perviousEndDateStr
  });

  if (!error) {
    realData.value = data;
    loading.value = true;
    updateSpec(opts => {
      return assign({}, opts, formatSpec());
    });
    delay(() => {
      loading.value = false;
    }, 500);
  }
}

function handleMetricsChange(value: string[]) {
  if (value.length === 0) return;
  selected.value = value;
  updateSpec(opts => {
    return assign({}, opts, formatSpec());
  });
}

watch(
  () => props.dateRange,
  () => {
    if (!props.dateRange) return;
    initData();
  },
  {
    immediate: true
  }
);
</script>

<template>
  <NCard class="card-wrapper" content-class="min-h-420px flex-center" :bordered="false" title="GMV Trend">
    <template #header-extra>
      <NPopselect
        :value="selected"
        :options="selectOptions"
        :consistent-menu-width="false"
        multiple
        placeholder="Mertics"
        @update:value="handleMetricsChange"
      >
        <NButton secondary>Mertics +{{ selected.length || 0 }}</NButton>
      </NPopselect>
      <!-- <NSelect class="min-w-150px"></NSelect> -->
    </template>
    <NSpin v-show="loading" class="m-auto"></NSpin>
    <div v-show="!loading" ref="gmvTrendRef" class="h-400px w-full"></div>
  </NCard>
</template>

<style scoped></style>
