<script setup lang="tsx">
import { onMounted, onUnmounted, ref } from 'vue';
import { useIntervalFn, useToggle } from '@vueuse/core';
import { NEllipsis, NTag } from 'naive-ui';
import dayjs from 'dayjs';
import { fetchGetInviteHistory, fetchStartInviteTask } from '@/service/api';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { useUserStore } from '@/store/modules/user';
import { useRouterPush } from '@/hooks/common/router';
import { useTable } from '@/hooks/common/table';
import ButtonConfirm from '@/components/custom/button-confirm.vue';
import ButtonIcon from '@/components/custom/button-icon.vue';
import { TimeFormat } from '@/enum';
import TaskDetailDrawer from './modules/task-detail-drawer.vue';
import TaskSearch from './modules/task-search.vue';

const userstore = useUserStore();

const { getDictionaryByCodeType } = useDictionaryStore();
const { routerPushByKey } = useRouterPush();

const statusOptions = ref<Api.Dictionary.DictionaryItem<number>[]>([]);

const { data, columns, pagination, loading, getData, searchParams } = useTable({
  apiFn: fetchGetInviteHistory,
  apiParams: {
    current: 1,
    size: 10
  },
  columns() {
    return [
      {
        key: 'invitationName',
        title: 'Invitation name',
        width: 250,
        render(rowData) {
          return (
            <div class="flex-col">
              <NEllipsis lineClamp={1}>{rowData.invitationName}</NEllipsis>
              <span class="text-coolgray">{rowData.productCount} product</span>
            </div>
          );
        }
      },
      {
        key: 'shopId',
        title: 'Shop',
        align: 'center',
        width: 200,
        render(rowData) {
          const shopName = userstore.userShops.find(v => v.shopId === rowData.shopId)?.shopName;
          if (!shopName) return '-';
          return (
            <NTag size="small" bordered={false} type="default">
              {shopName}
            </NTag>
          );
        }
      },
      {
        key: 'totalCount',
        title: 'Creator Invited',
        align: 'center'
      },
      {
        key: 'createTime',
        title: 'Create Date',
        align: 'center',
        width: 280,
        render(rowData) {
          return dayjs(rowData.createTime).format(TimeFormat.US_TIME_24);
        }
      },
      {
        key: 'status',
        title: 'Status',
        align: 'center',
        width: 150,
        render(rowData) {
          const attrs = statusOptions.value.find(v => v.code === rowData.status);
          if (!attrs) return '-';
          const { name, description } = attrs;
          const desc = JSON.parse(description);

          return (
            <NTag size="small" bordered={false} type={desc.buttonType as NaiveUI.ThemeColor}>
              <div class="flex-center gap-2">
                <span>{name}</span>
              </div>
            </NTag>
          );
        }
      },
      {
        key: 'operate',
        width: 100,
        render(rowData) {
          let attrs = statusOptions.value.find(v => v.code === rowData.status)?.description;
          if (!attrs) return '-';
          attrs &&= JSON.parse(attrs);
          const canView = attrs.buttonType !== 'primary' || false;
          return (
            <div class="flex items-center justify-end gap-8px">
              <ButtonIcon
                text
                quaternary={false}
                icon="solar:eye-linear"
                disabled={!canView}
                onClick={() => handleOpenDetail(rowData.id)}
              />
              {attrs ? (
                <ButtonConfirm
                  icon={attrs.taskButtonProps.icon}
                  confirmText={attrs.taskButtonProps.confirmText}
                  buttonProps={attrs.taskButtonProps.buttonProps}
                  onPositiveClick={() => handlePlayOrPause(rowData.id, attrs.taskButtonProps.icon)}
                />
              ) : (
                <div class="w-20px" />
              )}
            </div>
          );
        }
      }
    ];
  }
});

const [visible, toggleVisible] = useToggle(false);
const taskId = ref<number | null>(null);
function handleOpenDetail(id: number) {
  taskId.value = null;
  taskId.value = id;
  toggleVisible(true);
}

function handleSearch() {
  searchParams.current = 1;
  searchParams.size = 10;
  getData();
}

async function handlePlayOrPause(id: number, icon: string) {
  if (icon === 'tabler:player-play-filled') {
    const { error } = await fetchStartInviteTask(id);
    if (error) return;
  }
  // else if (icon === 'tabler:player-pause-filled') {
  //   const { error } = await fetchPauseTikTokTask(id);
  //   if (error) return;
  // }
  getData();
}

async function initStatusOptions() {
  const taskStatusOpts = await getDictionaryByCodeType<number>('email_task_status');
  if (!taskStatusOpts) return;
  statusOptions.value = taskStatusOpts;
}
function handleBack() {
  routerPushByKey('creator-center_creator-outreach_find-creator');
}

function initData() {
  initStatusOptions();
}

const { pause, resume } = useIntervalFn(
  () => {
    getData();
  },
  1000 * 60 * 5
);

onMounted(() => {
  resume();
});

onUnmounted(() => {
  pause();
});

initData();
</script>

<template>
  <div class="flex-col gap-4">
    <NCard class="card-wrapper" :bordered="false" title="Creator Outreach">
      <template #header-extra>
        <div class="flex justify-end gap-4">
          <ButtonBack :back-callback="handleBack" />
        </div>
      </template>
      <TaskSearch v-model:value="searchParams" :status-options="statusOptions" @change="handleSearch" />
    </NCard>
    <NCard class="h-full card-wrapper" :bordered="false" title="Task List">
      <NDataTable
        class="h-full"
        remote
        flex-height
        :loading="loading"
        :bordered="false"
        :columns="columns"
        :data="data"
        :pagination="pagination"
      >
        <template #empty>
          <NEmpty description="Start by importing or selecting creators to populate the list." />
        </template>
      </NDataTable>
    </NCard>
    <TaskDetailDrawer v-model:show="visible" :task-id="taskId" />
  </div>
</template>

<style scoped></style>
