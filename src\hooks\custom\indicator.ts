/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-11 09:56:41
 * @LastEditors: <PERSON><PERSON>n<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-09-25 14:14:29
 * @FilePath: \tiksage-frontend\src\hooks\custom\indicator.ts
 * @Description: hook for getting indicators
 */
import type { UnwrapRef } from 'vue';
import { computed, ref } from 'vue';
import { useAuthStore } from '@/store/modules/auth';

type Option = CommonType.Option<string> & Api.Auth.IndicatorValue;

type Options = Option[][];

export function useIndicator<T extends string | string[] = string[]>(
  allKey: Api.Auth.AllIndicatorKey[],
  userKey: Api.Auth.userIndicatorKey,
  type: 'default' | 'brand' = 'default'
) {
  const { userInfo, updateUserIndicators, updateUserBrandIndicators } = useAuthStore();

  const getCheckedValue = (): T => {
    const { indicator, brandIndicator } = userInfo;
    const userIndicators = type === 'default' ? indicator.userIndicators : brandIndicator.userIndicators;

    const data = userIndicators![userKey] as T;
    return data;
  };
  const checkedValue = ref<T>(getCheckedValue());

  const displayValue = ref<T>(getCheckedValue());

  const options = computed<Options>(() => {
    const { indicator, brandIndicator } = userInfo;

    const allIndicators = type === 'default' ? indicator.allIndicators : brandIndicator.allIndicators;

    function formatOptions(key: Api.Auth.AllIndicatorKey) {
      const list = allIndicators![key];
      if (!list.length) return [];
      return list.map(v => {
        return {
          ...v,
          label: v.title,
          value: v.title
        };
      });
    }
    return allKey.map(key => {
      return formatOptions(key);
    });
  });

  const updateCheckedValue = async () => {
    const fn = type === 'default' ? updateUserIndicators : updateUserBrandIndicators;
    const flag = await fn(userKey, checkedValue.value as T);
    if (flag) {
      checkedValue.value = getCheckedValue() as UnwrapRef<T>;
      displayValue.value = getCheckedValue() as UnwrapRef<T>;
    }
  };

  const resetCheckedValue = () => {
    checkedValue.value = getCheckedValue() as UnwrapRef<T>;
  };

  return {
    displayValue,
    checkedValue,
    options,
    updateCheckedValue,
    resetCheckedValue
  };
}
