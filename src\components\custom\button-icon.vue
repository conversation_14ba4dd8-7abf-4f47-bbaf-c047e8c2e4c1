<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-21 11:43:22
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-12-03 17:14:09
 * @FilePath: \tiksage-frontend\src\components\custom\button-icon.vue
 * @Description: button-icon
-->
<script setup lang="ts">
import type { ButtonProps, PopoverPlacement } from 'naive-ui';
import { twMerge } from 'tailwind-merge';

defineOptions({
  name: 'ButtonIcon',
  inheritAttrs: false
});

interface Props extends /* @vue-ignore */ ButtonProps {
  /** Button class */
  class?: unknown;
  /** Iconify icon name */
  icon?: string;
  /** Tooltip content */
  tooltipContent?: string;
  /** Tooltip placement */
  tooltipPlacement?: PopoverPlacement;
  zIndex?: number;
  rotate?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  class: undefined,
  icon: '',
  tooltipContent: '',
  tooltipPlacement: 'bottom',
  zIndex: 98,
  rotate: false
});

const DEFAULT_CLASS = 'h-[36px] text-icon';
</script>

<template>
  <NTooltip :placement="tooltipPlacement" :z-index="zIndex" :disabled="!tooltipContent">
    <template #trigger>
      <NButton quaternary :class="twMerge(DEFAULT_CLASS, props.class as string | undefined)" v-bind="$attrs">
        <div class="flex-center gap-8px">
          <slot>
            <SvgIcon class="text-xl" :class="{ 'animate-spin': rotate }" :icon="icon" />
          </slot>
        </div>
      </NButton>
    </template>
    {{ tooltipContent }}
  </NTooltip>
</template>

<style scoped></style>
