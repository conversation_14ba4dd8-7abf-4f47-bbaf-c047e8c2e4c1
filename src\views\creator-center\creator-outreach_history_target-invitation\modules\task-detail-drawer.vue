<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { NAvatar, NEllipsis, NTag } from 'naive-ui';
import {
  fetchExportFailedCreators,
  fetchGetInviteTaskCreatorListByAccepted,
  fetchGetInviteTaskDetail
} from '@/service/api';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { useTable } from '@/hooks/common/table';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { downloadFile } from '@/utils/download';
import { getFallbackImage } from '@/utils/fake-image';
import TagPopover from '@/components/custom/tag-popover.vue';
import { NumeralFormat } from '@/enum';
import TiktokProductItemCusCommission from '../../creator-outreach_create_target-invitation/modules/tiktok-product-item-cus-commission.vue';

const { VITE_CREATOR_AVATAR_URL } = import.meta.env;

interface Props {
  taskId: number | null;
}

const props = defineProps<Props>();

const show = defineModel('show', {
  type: Boolean,
  default: false
});

const taskDetail = ref<Api.CreatorOutreachInvite.InviteTaskDetail | null>(null);

const { numberFormat } = useNumberFormat();
const { getDictionaryByCodeType } = useDictionaryStore();
const taskStatusOptions = ref<Api.Dictionary.DictionaryItem<number>[]>([]);
const taskSenderStatusOptions = ref<Api.Dictionary.DictionaryItem<number>[]>([]);

const [taskLoading, toggleTaskLoading] = useToggle(false);

const taskIndicators = computed(() => {
  return [
    {
      title: 'Total Recipients',
      value: taskDetail.value?.totalCount || 0,
      color: '#000'
    },
    {
      title: 'Successful',
      value: taskDetail.value?.successCount || 0,
      color: '#52c41a'
    },
    {
      title: 'Failed',
      value: taskDetail.value?.failCount || 0,
      color: '#ff3d33'
    }
  ];
});

const statusObj = computed(() => {
  if (!taskStatusOptions.value.length || !taskDetail.value) return null;
  const attr = taskStatusOptions.value.find(item => item.code === taskDetail.value?.status);

  return {
    ...attr,
    description: JSON.parse(attr?.description)
  };
});

const productList = computed(() => {
  if (!taskDetail.value) return [];
  return JSON.parse(taskDetail.value?.productsInfo);
});

const { data, columns, pagination, loading, getData, updateSearchParams } = useTable({
  immediate: false,
  apiFn: fetchGetInviteTaskCreatorListByAccepted,
  apiParams: {
    current: 1,
    size: 20
  },
  columns: () => [
    {
      key: 'index',
      title: '',
      align: 'center',
      width: 70
    },
    {
      key: 'creatorId',
      title: 'Creator',
      fixed: 'left',
      width: 300,
      render(rowData) {
        const avatarUrl = `${VITE_CREATOR_AVATAR_URL}${rowData.creatorAvatar}`;
        const categoryList = JSON.parse(rowData.categoryJson);
        const followers = numberFormat(rowData.followerNum, NumeralFormat.Number);
        return (
          <div class="flex-y-center gap-2">
            <div class="flex-center flex-shrink-0">
              <NAvatar size="large" round src={avatarUrl} fallbackSrc={getFallbackImage(50, 50)} />
            </div>
            <div class="flex-col">
              <div class="flex flex-nowrap justify-between gap-2">
                <NEllipsis class="font-bold" lineClamp={1}>
                  @{rowData.creatorId || '-'}
                </NEllipsis>
              </div>
              <div>
                {categoryList.length ? (
                  <TagPopover tags={categoryList} showFirst />
                ) : (
                  <NTag size="small" bordered={false}>
                    Other
                  </NTag>
                )}
              </div>
              <span class="text-coolgray">Followers: {followers || '-'}</span>
            </div>
          </div>
        );
      }
    },
    {
      key: 'acceptedCount',
      title: 'Product added to showcase',
      align: 'center'
    }
  ]
});

async function handleExportFailed() {
  if (!props.taskId) return;
  const { data: exportData, error: exportErr } = await fetchExportFailedCreators(props.taskId);
  if (!exportErr) {
    downloadFile(exportData, 'xlsx', `${taskDetail.value?.invitationName}-Failed Creators`);
  }
}

async function initTask() {
  if (props.taskId) {
    taskLoading.value = true;
    const { data: taskInfoData, error } = await fetchGetInviteTaskDetail(props.taskId);
    if (!error) {
      taskDetail.value = taskInfoData;
      updateSearchParams({
        taskId: props.taskId
      });
      getData();
    }
    taskLoading.value = false;
  }
}

async function initStatusOptions() {
  const taskStatusOpts = await getDictionaryByCodeType<number>('email_task_status');
  if (!taskStatusOpts) return;
  taskStatusOptions.value = taskStatusOpts;
}

async function initTaskSenderStatusOptions() {
  const taskDetailStatusOpts = await getDictionaryByCodeType<number>('message_status');
  if (!taskDetailStatusOpts) return;
  taskSenderStatusOptions.value = taskDetailStatusOpts;
}

async function init() {
  await initStatusOptions();
  await initTaskSenderStatusOptions();
  await initTask();
}

watch(
  () => show.value,
  async newVal => {
    if (newVal) {
      toggleTaskLoading(true);
      await init();
      toggleTaskLoading(false);
    }
  }
);
</script>

<template>
  <NDrawer v-model:show="show" :width="1200">
    <NDrawerContent class="bg-#F7FAFC" body-content-class="flex-col gap-16px" closable>
      <NCard class="card-wrapper" :bordered="false" title="Task Detail"></NCard>
      <NGrid item-responsive responsive="screen" :x-gap="16" :y-gap="16">
        <NGi span="12">
          <NCard class="h-full card-wrapper" segmented :bordered="false">
            <NDescriptions label-class="text-coolgray" content-class="" :columns="1" label-placement="left">
              <NDescriptionsItem label="Task Name">
                <span>{{ taskDetail?.invitationName || '-' }}</span>
              </NDescriptionsItem>
              <NDescriptionsItem label="Task Status">
                <span v-if="!statusObj">-</span>
                <NTag v-else :type="statusObj?.description.buttonType" :bordered="false" size="small">
                  {{ statusObj?.name }}
                </NTag>
              </NDescriptionsItem>
            </NDescriptions>
          </NCard>
        </NGi>
        <NGi span="12">
          <NGrid class="" :cols="3" :x-gap="16" :y-gap="16">
            <NGi v-for="indicator in taskIndicators" :key="indicator.title" class="h-full">
              <div class="h-full flex justify-between card-wrapper rounded-8px bg-white px-16px py-24px">
                <div class="flex-col">
                  <span class="text-coolgray">{{ indicator.title }}</span>
                  <span class="text-xl font-bold" :style="{ color: indicator.color }">{{ indicator.value }}</span>
                </div>
                <div v-if="indicator.title === 'Failed' && indicator.value > 0" class="flex-center">
                  <NButton quaternary @click="handleExportFailed">
                    <template #icon>
                      <icon-solar:export-linear />
                    </template>
                  </NButton>
                </div>
              </div>
            </NGi>
          </NGrid>
        </NGi>
      </NGrid>
      <NCard class="card-wrapper" content-class="flex-col gap-4" segmented :bordered="false" title="Invite Product">
        <TiktokProductItemCusCommission
          v-for="product in productList"
          :key="product.product_id"
          is-show
          :product="product"
        />
      </NCard>
      <NCard class="card-wrapper" segmented :bordered="false" title="Confirmed Invitations List">
        <NDataTable
          class="h-full min-h-400px"
          flex-height
          remote
          :loading="loading"
          :bordered="false"
          :columns="columns"
          :data="data"
          :pagination="pagination"
        ></NDataTable>
      </NCard>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
