<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { useTimestamp } from '@vueuse/core';
import dayjs from 'dayjs';
import { TimeFormat } from '@/enum';

const timestamp = useTimestamp({ offset: 0 });

const options: CommonType.Option<string>[] = [
  {
    label: 'UTC',
    value: 'UTC'
  },
  {
    label: 'GMT',
    value: 'GMT'
  },
  {
    label: 'PST',
    value: 'Etc/GMT+8'
  },
  {
    label: 'PDT',
    value: 'Etc/GMT+7'
  }
];

const inputValue = ref<string>();
const currentTimeZone = ref<string>();
const targetTimeZone = ref<string>();
const targetValue = ref<string>();

function convert() {
  const valid = dayjs(inputValue.value).isValid();
  let currentTime;

  if (valid) {
    if (currentTimeZone.value) {
      currentTime = dayjs.tz(inputValue.value, currentTimeZone.value);
    }
    if (targetTimeZone.value) {
      targetValue.value = currentTime?.tz(targetTimeZone.value).format(TimeFormat.US_TIME_12);
    }
  } else {
    targetValue.value = '';
  }
}

async function handleCopy() {
  try {
    await navigator.clipboard.writeText(targetValue.value || '');
    window.$message?.success('Copy success.');
  } catch (_e) {
    console.log(_e);
    window.$message?.error('Copy failure.');
  }
}

watchEffect(() => {
  convert();
});
</script>

<template>
  <NPopover trigger="click">
    <template #trigger>
      <NFlex class="p-r-16px text-xs hover:cursor-pointer" vertical :size="1">
        <NTag size="small" :bordered="false">{{ dayjs(timestamp).format(TimeFormat.US_TIME_12) }}</NTag>
        <NTag size="small" :bordered="false">{{ dayjs(timestamp).tz('Etc/GMT+8').format(TimeFormat.US_TIME_12) }}</NTag>
      </NFlex>
    </template>
    <NCard class="h-400px w-600px" :bordered="false" segmented title="Time Tool">
      <NGrid x-gap="16" y-gap="16" item-responsive responsive="screen">
        <NFormItemGi span="12" label="Input time">
          <NInput v-model:value="inputValue" placeholder="YYYY-MM-DD HH:mm:ss" />
        </NFormItemGi>
        <NFormItemGi span="6" label="Current time zone">
          <NSelect v-model:value="currentTimeZone" :options="options" />
        </NFormItemGi>
        <NFormItemGi span="6" label="Target time zone">
          <NSelect v-model:value="targetTimeZone" :options="options" />
        </NFormItemGi>
      </NGrid>
      <NDivider>Convert</NDivider>
      <NFlex justify="center">
        <!--
 <NTag>{{ targetValue }}</NTag>
        <ButtonCopy v-if="targetValue" :copy="targetValue" />
-->
        <div v-if="targetValue" class="buttons" @click="handleCopy">
          <button class="btn">
            <span></span>
            <p data-start="good luck!" data-text="Copy" :data-title="targetValue"></p>
          </button>
        </div>
      </NFlex>
    </NCard>
  </NPopover>
</template>

<style scoped>
.buttons {
  display: flex;
  justify-content: space-around;
  top: 20px;
  left: 20px;
}

.buttons button {
  width: 300px;
  height: 50px;
  background-color: white;
  margin: 20px;
  color: rgb(var(--primary-600-color));
  position: relative;
  overflow: hidden;
  font-size: 14px;
  letter-spacing: 1px;
  font-weight: 500;
  text-transform: uppercase;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
}

.buttons button:before,
.buttons button:after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  background-color: rgb(var(--primary-color));
  transition: all 0.3s cubic-bezier(0.35, 0.1, 0.25, 1);
}

.buttons button:before {
  right: 0;
  top: 0;
  transition: all 0.5s cubic-bezier(0.35, 0.1, 0.25, 1);
}

.buttons button:after {
  left: 0;
  bottom: 0;
}

.buttons button span {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  margin: 0;
  padding: 0;
  z-index: 1;
}

.buttons button span:before,
.buttons button span:after {
  content: '';
  position: absolute;
  width: 2px;
  height: 0;
  background-color: rgb(var(--primary-color));
  transition: all 0.3s cubic-bezier(0.35, 0.1, 0.25, 1);
}

.buttons button span:before {
  right: 0;
  top: 0;
  transition: all 0.5s cubic-bezier(0.35, 0.1, 0.25, 1);
}

.buttons button span:after {
  left: 0;
  bottom: 0;
}

.buttons button p {
  padding: 0;
  margin: 0;
  transition: all 0.4s cubic-bezier(0.35, 0.1, 0.25, 1);
  position: absolute;
  width: 100%;
  height: 100%;
}

.buttons button p:before,
.buttons button p:after {
  position: absolute;
  width: 100%;
  transition: all 0.4s cubic-bezier(0.35, 0.1, 0.25, 1);
  z-index: 1;
  left: 0;
}

.buttons button p:before {
  content: attr(data-title);
  top: 50%;
  transform: translateY(-50%);
}

.buttons button p:after {
  content: attr(data-text);
  top: 150%;
  color: rgb(var(--primary-color));
}

.buttons button:hover:before,
.buttons button:hover:after {
  width: 100%;
}

.buttons button:hover span {
  z-index: 1;
}

.buttons button:hover span:before,
.buttons button:hover span:after {
  height: 100%;
}

.buttons button:hover p:before {
  top: -50%;
  transform: rotate(5deg);
}

.buttons button:hover p:after {
  top: 50%;
  transform: translateY(-50%);
}

.buttons button.start {
  background-color: rgb(var(--primary-color));
  box-shadow: 0px 5px 10px -10px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.buttons button.start p:before {
  top: -50%;
  transform: rotate(5deg);
}

.buttons button.start p:after {
  color: white;
  transition: all 0s ease;
  content: attr(data-start);
  top: 50%;
  transform: translateY(-50%);
  animation: start 0.3s ease;
  animation-fill-mode: forwards;
}

@keyframes start {
  from {
    top: -50%;
  }
}

.buttons button.start:hover:before,
.buttons button.start:hover:after {
  display: none;
}

.buttons button.start:hover span {
  display: none;
}

.buttons button:active {
  outline: none;
  border: none;
}

.buttons button:focus {
  outline: 0;
}
</style>
