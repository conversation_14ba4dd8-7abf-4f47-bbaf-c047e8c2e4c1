<script setup lang="ts">
interface Props {
  width: number;
  height: number;
  src: string;
  fallbackSrc: string;
}

const props = defineProps<Props>();
</script>

<template>
  <NPopover trigger="hover" placement="right-start">
    <template #trigger>
      <NImage
        preview-disabled
        :width="props.width"
        :height="props.height"
        :src="props.src"
        :fallback-src="props.fallbackSrc"
      ></NImage>
    </template>
    <div>
      <NImage preview-disabled width="140" height="140" :src="props.src" :fallback-src="props.fallbackSrc"></NImage>
    </div>
  </NPopover>
</template>

<style scoped></style>
