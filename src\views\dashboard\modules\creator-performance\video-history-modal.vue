<script setup lang="ts">
import { watch } from 'vue';
import { useToggle } from '@vueuse/core';
import dayjs from 'dayjs';
import { subtract } from 'lodash-es';
import { fetchGetVideoDailyData } from '@/service/api';
import { useVChart } from '@/hooks/common/vchart';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat, TimeFormat } from '@/enum';

interface Props {
  videoId?: string;
  shopId?: number;
  showAds?: boolean;
}

const props = defineProps<Props>();

const modelShow = defineModel('show', {
  type: Boolean,
  default: false
});

const [loading, toggleLoading] = useToggle(false);

const { numberFormat } = useNumberFormat();

const defaultSpec: Visactor.VChart.IAreaChartSpec = {
  type: 'area',
  padding: 0,
  data: [],
  seriesField: 'type',
  xField: 'x',
  yField: 'y',
  stack: false,
  // color: ['#a855f7'],
  point: {
    visible: false
  },
  line: {
    style: {
      curveType: 'monotone'
    }
  },
  series: [
    {
      id: 'gmvSeries',
      dataId: 'gmv',
      type: 'area',
      tooltip: {
        dimension: {
          title: {
            visible: true,
            value: datum => {
              return dayjs(datum?.x).format(TimeFormat.US_DATE);
            }
          },
          content: {
            key: v => {
              return v ? (v?.type as string) : '';
            },
            value: datum => {
              return numberFormat(datum?.y, NumeralFormat.Dollar);
            }
          }
        }
      }
    },
    {
      id: 'engagementSeries',
      dataId: 'engagement',
      type: 'area',
      tooltip: {
        dimension: {
          title: {
            visible: true,
            value: datum => {
              return dayjs(datum?.x).format(TimeFormat.US_DATE);
            }
          },
          content: {
            key: v => {
              return v ? (v?.type as string) : '';
            },
            value: datum => {
              return numberFormat(datum?.y, NumeralFormat.Number);
            }
          }
        }
      }
    },
    {
      id: 'impressionsSeries',
      dataId: 'impressions',
      type: 'area',
      tooltip: {
        dimension: {
          title: {
            visible: true,
            value: datum => {
              return dayjs(datum?.x).format(TimeFormat.US_DATE);
            }
          },
          content: {
            key: v => {
              return v ? (v?.type as string) : '';
            },
            value: datum => {
              return numberFormat(datum?.y, NumeralFormat.Number);
            }
          }
        }
      }
    },
    {
      id: 'ordersSeries',
      dataId: 'orders',
      type: 'area',
      tooltip: {
        dimension: {
          title: {
            visible: true,
            value: datum => {
              return dayjs(datum?.x).format(TimeFormat.US_DATE);
            }
          },
          content: {
            key: v => {
              return v ? (v?.type as string) : '';
            },
            value: datum => {
              return numberFormat(datum?.y, NumeralFormat.Number);
            }
          }
        }
      }
    }
  ],
  axes: [
    {
      orient: 'bottom',
      visible: true,
      label: {
        formatMethod(text: any) {
          return dayjs(text).format(TimeFormat.US_DATE);
        }
      }
    },
    {
      id: 'left',
      orient: 'left',
      visible: true,
      seriesId: ['gmvSeries'],
      // title: {
      //   visible: true,
      //   text: 'GMV'
      // },
      label: {
        formatMethod: v => {
          return numberFormat(v, NumeralFormat.Dollar);
        }
      }
    },
    {
      id: 'right1',
      orient: 'right',
      visible: true,
      seriesId: ['engagementSeries'],
      // title: {
      //   visible: true,
      //   text: 'Engagement'
      // },
      label: {
        formatMethod: v => {
          return numberFormat(v, NumeralFormat.Number);
        }
      },
      sync: {
        axisId: 'left',
        tickAlign: true,
        zeroAlign: true
      }
    },
    {
      orient: 'right',
      visible: true,
      seriesId: ['impressionsSeries'],
      // title: {
      //   visible: true,
      //   text: 'Impressions'
      // },
      label: {
        formatMethod: v => {
          return numberFormat(v, NumeralFormat.Number);
        }
      },
      sync: {
        axisId: 'left',
        tickAlign: true,
        zeroAlign: true
      }
    },
    {
      id: 'right2',
      orient: 'right',
      visible: true,
      seriesId: ['ordersSeries'],
      // title: {
      //   visible: true,
      //   text: 'Impressions'
      // },
      label: {
        formatMethod: v => {
          return numberFormat(v, NumeralFormat.Number);
        }
      },
      sync: {
        axisId: 'left',
        tickAlign: true,
        zeroAlign: true
      }
    }
  ],
  area: {
    style: {
      fill: {
        gradient: 'linear',
        x0: 0.5,
        y0: 0,
        x1: 0.5,
        y1: 1,
        stops: [
          {
            offset: 0,
            opacity: 1
          },
          {
            offset: 1,
            opacity: 0
          }
        ]
      }
    }
  },
  legends: {
    orient: 'top',
    visible: true
  }
};

const { domRef: historyAreaRef, updateSpec: updateHistorySpec } = useVChart(() => defaultSpec);

function formatData(data: Api.CreatorPerformance.VideoHistory[]) {
  const gmvRes: any[] = [];
  const nonAdGmvRes: any[] = [];
  const adsRes: any[] = [];
  const engagementRes: any[] = [];
  const viewsRes: any[] = [];
  const ordersRes: any[] = [];
  data.forEach(i => {
    gmvRes.push({
      x: i.belongDate,
      type: 'Total GMV',
      y: i.gmv
    });
    nonAdGmvRes.push({
      x: i.belongDate,
      type: 'Non-ad GMV',
      y: subtract(i.gmv, i.adsGmv)
    });
    adsRes.push({
      x: i.belongDate,
      type: 'Ad GMV',
      y: i.adsGmv
    });
    engagementRes.push({
      x: i.belongDate,
      type: 'Engagement',
      y: i.engagement
    });
    viewsRes.push({
      x: i.belongDate,
      type: 'Impressions',
      y: i.views
    });
    ordersRes.push({
      x: i.belongDate,
      type: 'Orders',
      y: i.orderCnt
    });
  });
  return [
    {
      id: 'gmv',
      values: props.showAds ? gmvRes.concat(nonAdGmvRes, adsRes) : gmvRes
    },
    {
      id: 'engagement',
      values: engagementRes
    },
    {
      id: 'impressions',
      values: viewsRes
    },
    {
      id: 'orders',
      values: ordersRes
    }
  ];
}

async function initData() {
  if (!props.videoId || !props.shopId) return;
  toggleLoading(true);
  const { data, error } = await fetchGetVideoDailyData(props.videoId, props.shopId);

  if (!error) {
    setTimeout(() => {
      updateHistorySpec(oldOpts => {
        return { ...oldOpts, data: formatData(data) };
      });
    });
  }
  toggleLoading(false);
}

watch(
  () => modelShow.value,
  newVal => {
    if (newVal) {
      initData();
    } else {
      updateHistorySpec(oldOpts => {
        return { ...oldOpts, data: [] };
      });
    }
  }
);
</script>

<template>
  <NModal v-model:show="modelShow" class="w-848px" preset="card" closable>
    <template #header>Video Performance</template>
    <NSpin :show="loading">
      <div class="flex-col gap-4">
        <div ref="historyAreaRef" class="h-400px w-800px"></div>
      </div>
    </NSpin>
  </NModal>
</template>

<style scoped></style>
