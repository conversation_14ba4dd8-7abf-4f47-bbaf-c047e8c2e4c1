<script setup lang="ts">
import { ref } from 'vue';
import dayjs from 'dayjs';
import { useUserStore } from '@/store/modules/user';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { TimeFormat } from '@/enum';

interface Emits {
  (e: 'change'): void;
}

const emit = defineEmits<Emits>();

const userStore = useUserStore();

const { getDictionaryByCodeType } = useDictionaryStore();

interface Props {
  statusOptions: Api.Dictionary.DictionaryItem[];
}

defineProps<Props>();

const model = defineModel<Api.CreatorOutreachInvite.InviteHistorySearchParams>('value', {
  required: true,
  default: {
    taskName: null,
    status: null,
    contactType: null,
    startTime: null,
    endTime: null,
    shopIds: null,
    freeSamples: null
  }
});

function handleChange(key: keyof Api.CreatorOutreachInvite.InviteHistorySearchParams, value: any) {
  (model.value as any)[key] = value;
  emit('change');
}

function handleDateChange(value: [number, number] | null) {
  if (value) {
    model.value.startTime = dayjs(value[0]).tz('Etc/GMT+8').format(TimeFormat.CN_TIME_24_NO_TIMEZONE);
    model.value.endTime = dayjs(value[1]).tz('Etc/GMT+8').format(TimeFormat.CN_TIME_24_NO_TIMEZONE);
  } else {
    model.value.startTime = null;
    model.value.endTime = null;
  }
  emit('change');
}

const contactTypeOptions = ref<Api.Dictionary.DictionaryItem[]>([]);

const freeSampleOptions = ref<CommonType.Option<number>[]>([]);

async function initContactTypeOptions() {
  const data = await getDictionaryByCodeType<number>('contact_type');
  if (!data) return;
  contactTypeOptions.value = data;
}

async function initFreeSampleOptions() {
  const data = await getDictionaryByCodeType<number>('invite_free_sample');
  if (!data) return;
  freeSampleOptions.value = data.map(item => ({
    label: item.name,
    value: item.code
  }));
}

function init() {
  initContactTypeOptions();
  initFreeSampleOptions();
}

init();
</script>

<template>
  <div class="flex-col">
    <NGrid cols="6" :x-gap="16" :y-gap="16">
      <NGi>
        <NInput placeholder="Task Name" clearable @change="handleChange('taskName', $event)">
          <template #prefix>
            <icon-tabler:search />
          </template>
        </NInput>
      </NGi>
      <NGi>
        <NSelect
          :consistent-menu-width="false"
          placeholder="Shop"
          :options="userStore.userShops"
          value-field="shopId"
          label-field="shopName"
          :max-tag-count="1"
          clearable
          multiple
          @update:value="handleChange('shopIds', $event)"
        ></NSelect>
      </NGi>
      <NGi>
        <NSelect
          placeholder="Status"
          clearable
          :options="statusOptions"
          value-field="code"
          label-field="name"
          @update:value="handleChange('status', $event)"
        />
      </NGi>
      <NGi>
        <NSelect
          :consistent-menu-width="false"
          placeholder="Free Samples"
          clearable
          :options="freeSampleOptions"
          @update:value="handleChange('freeSamples', $event)"
        />
      </NGi>
      <!--
 <NGi>
        <NSelect
          placeholder="Contact Type"
          clearable
          :options="contactTypeOptions"
          value-field="code"
          label-field="name"
          @update:value="handleChange('contactType', $event)"
        />
      </NGi>
-->
      <NGi>
        <ButtonDate clearable :default-value="-1" :show-quick-button="false" @update:timestamp="handleDateChange" />
      </NGi>
    </NGrid>
  </div>
</template>

<style scoped></style>
