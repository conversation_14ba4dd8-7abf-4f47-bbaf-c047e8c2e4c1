<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-30 09:56:10
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-11 17:40:07
 * @FilePath: \tiksage-frontend\src\views\video-manage\list\modules\video-info-drawer.vue
 * @Description: video-info-drawer
-->
<script setup lang="ts">
import { computed, ref, watchEffect } from 'vue';
import { useToggle } from '@vueuse/core';
import dayjs from 'dayjs';
import { isNil } from 'lodash-es';
import { fetchApproveVideo, fetchGetVideoApprovalInfoById, fetchRejectVideo } from '@/service/api';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { getFallbackImage } from '@/utils/fake-image';
import { NumeralFormat, TimeFormat } from '@/enum';

const { VITE_PRODUCT_AVATAR_URL } = import.meta.env;

interface Props {
  role: 'operator' | 'approver';
  id?: number;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submit', type?: 'next'): void;
}

const emit = defineEmits<Emits>();

const { numberFormat } = useNumberFormat();

const show = defineModel('show', {
  required: true,
  default: false
});

const showData = ref<Api.VideoManage.VideoInfo>();
const [rejectShow, toggleRejectShow] = useToggle(false);

const videoSrc = computed(() => {
  if (!showData.value) return '';
  const res = `${import.meta.env.VITE_VIDEO_URL}${showData.value.localVideo}`;
  return res;
});

type TagAttr = {
  type: 'primary' | 'success' | 'error';
  desc: 'Pending' | 'Approved' | 'Rejected';
} | null;

const tagAttr = computed<TagAttr>(() => {
  let res: TagAttr;
  switch (showData.value?.status) {
    case 0:
      res = {
        type: 'primary',
        desc: 'Pending'
      };
      break;
    case 1:
      res = {
        type: 'success',
        desc: 'Approved'
      };
      break;
    case 2:
      res = {
        type: 'error',
        desc: 'Rejected'
      };
      break;
    default:
      res = null;
  }

  return res;
});

watchEffect(async () => {
  if (show.value && !isNil(props.id)) {
    const { data, error } = await fetchGetVideoApprovalInfoById(props.id);
    if (!error) {
      showData.value = data;
    }
  }
});

// approver

const rejectReason = ref<string>('');

async function handleApproveVideo(id?: number) {
  if (isNil(id)) return;

  const { error } = await fetchApproveVideo(id);
  if (error) return;
  window.$message?.success('Approve success.');
  emit('submit', 'next');
}

function handleRejectBack() {
  toggleRejectShow(false);
  rejectReason.value = '';
}

async function handleRejectVideo(type?: 'next') {
  if (isNil(props.id)) return;

  const { error } = await fetchRejectVideo({ id: props.id, notes: rejectReason.value });
  if (error) return;
  window.$message?.success('Reject success.');

  rejectShow.value = false;
  rejectReason.value = '';

  if (type === 'next') {
    emit('submit', 'next');
  } else {
    show.value = false;
    emit('submit');
  }
}
</script>

<template>
  <NDrawer v-model:show="show" width="450px">
    <NDrawerContent title="Video Preview">
      <NSpin :show="!showData" content-class="flex-col gap-16px min-h-400px">
        <template v-if="showData">
          <div class="w-full">
            <Vidstack :src="videoSrc" />
          </div>
          <NCard class="card-wrapper" size="small" segmented embedded title="Video Information">
            <NDescriptions :column="2" label-placement="left" label-class="font-bold text-gray">
              <NDescriptionsItem label="By">{{ showData?.creatorName }}</NDescriptionsItem>
              <NDescriptionsItem label="For">{{ showData?.clientName }}</NDescriptionsItem>
              <NDescriptionsItem content-class="w-full" :span="2" label="Product">
                <NPopover placement="top" trigger="hover">
                  <template #trigger>
                    <div class="w-280px ellipsis-text">
                      {{ showData?.productName }}
                    </div>
                  </template>
                  <div class="w-200px flex-col-center">
                    <NImage
                      preview-disabled
                      :src="`${VITE_PRODUCT_AVATAR_URL}${showData?.productAvatarLocal}`"
                      :fallback-src="getFallbackImage(50, 50)"
                    ></NImage>
                    <span>{{ showData?.productName }}</span>
                  </div>
                </NPopover>
              </NDescriptionsItem>
              <NDescriptionsItem :span="2" label="Payment Account">{{ showData?.payment }}</NDescriptionsItem>
              <NDescriptionsItem :span="2" label="Flat Fee">
                {{ numberFormat(showData?.flatFee, NumeralFormat.Dollar) }}
              </NDescriptionsItem>
              <NDescriptionsItem :span="2" label="Submitter">
                <div class="flex-col">
                  <span>{{ showData?.submitter }}</span>
                  <span>{{ dayjs(showData?.createTime).format(TimeFormat.US_TIME_24) }}</span>
                </div>
              </NDescriptionsItem>
            </NDescriptions>
          </NCard>
          <NCard
            class="card-wrapper"
            content-class="flex-center min-h-250px"
            size="small"
            title="Review Progress"
            segmented
            embedded
          >
            <template #header-extra>
              <NTag v-if="tagAttr" :type="tagAttr.type" :bordered="false">{{ tagAttr.desc }}</NTag>
            </template>
            <template v-if="tagAttr?.type === 'primary'">
              <template v-if="role === 'operator'">
                <NEmpty description="Pending Review...">
                  <template #icon>
                    <!-- <icon-noto:hourglass-not-done /> -->
                    <icon-eos-icons:hourglass />
                  </template>
                </NEmpty>
              </template>
              <template v-if="role === 'approver'">
                <NFlex v-if="rejectShow" vertical :size="16">
                  <NInput
                    v-model:value="rejectReason"
                    class="h-200px"
                    type="textarea"
                    :resizable="false"
                    placeholder="what made you decide to decline?"
                  />
                  <NFlex justify="flex-end" align="center">
                    <NButton type="default" @click="handleRejectBack()">Back</NButton>
                    <NButton type="error" @click="handleRejectVideo()">Reject</NButton>
                    <NButton type="error" @click="handleRejectVideo('next')">Reject & Next</NButton>
                  </NFlex>
                </NFlex>
                <NFlex v-else justify="space-around">
                  <NPopconfirm positive-text="Approve" negative-text="Back" @positive-click="handleApproveVideo(id)">
                    <template #trigger>
                      <NButton size="small" secondary type="success">Approve & Next</NButton>
                    </template>
                    Are you sure you want to approve this video?
                  </NPopconfirm>
                  <NButton size="small" secondary type="error" @click="toggleRejectShow(true)">Reject</NButton>
                </NFlex>
              </template>
            </template>
            <NEmpty v-if="tagAttr?.type === 'success'">
              <template #icon>
                <icon-tabler:progress-check class="text-success" />
              </template>
              <NDescriptions label-class="font-bold text-gray" :column="1" label-placement="left">
                <NDescriptionsItem label="By">{{ showData?.clientName }}</NDescriptionsItem>
                <NDescriptionsItem label="Date">
                  {{ dayjs(showData?.updateTime).format(TimeFormat.US_TIME_12) }}
                </NDescriptionsItem>
              </NDescriptions>
            </NEmpty>
            <NEmpty v-if="tagAttr?.type === 'error'">
              <template #icon>
                <icon-tabler:face-id-error class="text-error" />
              </template>
              <NDescriptions label-class="font-bold text-gray" :column="1" label-placement="left">
                <NDescriptionsItem label="By">{{ showData?.clientName }}</NDescriptionsItem>
                <NDescriptionsItem label="Date">
                  {{ dayjs(showData?.updateTime).format(TimeFormat.US_TIME_12) }}
                </NDescriptionsItem>
              </NDescriptions>
              <div class="m-t-16px w-340px">
                <NInput class="h-200px" type="textarea" :resizable="false" :value="showData?.notes" disabled></NInput>
              </div>
            </NEmpty>
          </NCard>
        </template>
      </NSpin>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
