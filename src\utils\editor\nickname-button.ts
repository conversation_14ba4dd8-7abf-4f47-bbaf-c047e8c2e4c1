import type { IButtonMenu, IDomEditor } from '@wangeditor-next/editor';

/* eslint-disable class-methods-use-this */
export class NickNameButton implements IButtonMenu {
  title = 'Add nickname';
  tag = 'button';

  // Get the value when the menu is executed, and return the empty string or false if it is not used.
  getValue(): string | boolean {
    return '{{nickname}}';
  }

  // Whether the menu needs to be activated (such as selecting a bold text, the "bold" menu will be activated), and returned to false if it is not used.
  isActive(): boolean {
    return false;
  }

  // Whether the menu needs to be disabled (if H1 is selected, the "reference" menu is disabled), and then returns FALSE
  isDisabled(): boolean {
    return false;
  }

  // 点击菜单时触发的函数
  exec(editor: IDomEditor, value: string | boolean) {
    if (this.isDisabled()) return;
    editor.insertText(value as string); // Value is the return value of this.Value (editor)
  }
}
