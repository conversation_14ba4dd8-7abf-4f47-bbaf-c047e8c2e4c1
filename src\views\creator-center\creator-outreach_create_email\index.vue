<script setup lang="tsx">
import { ref } from 'vue';
import type { FormItemRule, SelectOption } from 'naive-ui';
import { NDescriptions, NDescriptionsItem, NInput, NTag, NText, useDialog } from 'naive-ui';
import {
  fetchGetEmailAccountList,
  fetchGetEmailTemplateOptions,
  fetchStartTask,
  fetchStartTestEmail
} from '@/service/api';
import { useRouterPush } from '@/hooks/common/router';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import UploadCreatorCard from '../creator-outreach/modules/upload-creator-card.vue';

const { routerPushByKey, routerBack } = useRouterPush();

type Model = Api.CreatorNetwork.StartTaskParams;

function createDefaultModel(): Model {
  return {
    emailList: [],
    taskName: null,
    senderEmailId: null,
    templateId: null
  };
}

const model = ref<Model>(createDefaultModel());

const { formRef, validate } = useNaiveForm();

const { defaultRequiredRule } = useFormRules();

const rules: Record<keyof Model, FormItemRule[]> = {
  taskName: [defaultRequiredRule],
  senderEmailId: [defaultRequiredRule],
  emailList: [defaultRequiredRule],
  templateId: [defaultRequiredRule]
};

const dialog = useDialog();

const testEmail = ref<string>('');

async function handleTestSend() {
  await validate();
  const d = dialog.create({
    title: 'Send Test Email',
    showIcon: false,
    style: 'width: 400px;',
    negativeText: 'Cancel',
    positiveText: 'Send',
    content() {
      return (
        <NDescriptions>
          <NDescriptionsItem label="Test Email Address">
            <NInput
              placeholder="Test Email Address"
              value={testEmail.value}
              onUpdateValue={v => (testEmail.value = v)}
            />
          </NDescriptionsItem>
        </NDescriptions>
      );
    },
    async onPositiveClick() {
      d.loading = true;
      const { error } = await fetchStartTestEmail({ ...model.value, testEmail: testEmail.value });
      if (!error) {
        window.$message?.success('Test email has been sent to your test inbox. Please check');
        testEmail.value = '';
      }
      d.loading = false;
      d.destroy();
    },
    onNegativeClick() {
      d.destroy();
    }
  });
}

async function handleSendAll() {
  await validate();

  const d = dialog.create({
    title: 'Confirm Bulk Email Sending',
    content() {
      return (
        <div>
          You are about to send this email to{' '}
          <NText type="primary" strong>
            {model.value.emailList.length}
          </NText>{' '}
          creators with valid email addresses. <br />
          Please confirm to proceed with the bulk email sending task.
        </div>
      );
    },
    positiveText: 'Send Now',
    negativeText: 'Cancel',
    async onPositiveClick() {
      d.loading = true;
      const { error } = await fetchStartTask(model.value);
      if (!error) {
        window.$message?.success(`The mass sending task has been created.`);
        routerPushByKey('creator-center_creator-outreach_history_email');
      }
      d.loading = false;
      d.destroy();
    }
  });
}

function handleBack() {
  routerBack();
  model.value = createDefaultModel();
}

function handleAddEmailAccount() {
  routerPushByKey('creator-center_creator-outreach_settings_email');
}

function handleAddEmailTemplate() {
  routerPushByKey('creator-center_creator-outreach_settings_email');
}

function handleCreatorDataChange(value: Api.CreatorNetwork.CreatorInfo[]) {
  model.value.emailList = value;
}

const emailTemplateOptions = ref<Api.CreatorNetwork.GetEmailTemplateOptions[]>([]);

async function fetchEmailTemplateOptions() {
  const { data: templateOptions } = await fetchGetEmailTemplateOptions();
  emailTemplateOptions.value = templateOptions ?? [];
}

const emailAccountOptions = ref<Api.CreatorNetwork.EmailAccount[]>([]);

async function fetchEmailAccountOptions() {
  const { data: accountOptions } = await fetchGetEmailAccountList();
  if (accountOptions) {
    emailAccountOptions.value = accountOptions.map(item => {
      return {
        ...item,
        disabled: item.invalidGrant
      };
    });
  }
}

function renderEmailAccount(option: SelectOption) {
  const tip = option.invalidGrant ? 'Authorization expires' : 'Active';
  return (
    <div class="flex justify-between gap-4">
      <span>{option.email}</span>
      <NTag size="small" bordered={false} type={option.invalidGrant ? 'error' : 'success'}>
        {tip}
      </NTag>
    </div>
  );
}

function initOptions() {
  fetchEmailTemplateOptions();
  fetchEmailAccountOptions();
}

initOptions();
</script>

<template>
  <div class="flex-col gap-4">
    <NCard class="flex-1 card-wrapper" segmented :bordered="false" title="Create Mass Email Task">
      <template #header-extra>
        <ButtonBack :back-callback="handleBack" />
      </template>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="Task Name" path="taskName">
          <NInput v-model:value="model.taskName" class="max-w-400px" placeholder="Enter task name" />
        </NFormItem>
        <NFormItem label="Sender Email Account" path="senderEmailId">
          <div class="flex items-center gap-16px">
            <NSelect
              v-model:value="model.senderEmailId"
              class="w-400px"
              :options="emailAccountOptions"
              value-field="id"
              label-field="email"
              :render-label="renderEmailAccount"
              placeholder="Select sender email account"
            />
            <NButton text type="primary" @click="handleAddEmailAccount">Add a new email account</NButton>
          </div>
        </NFormItem>
        <NFormItem label="Email Template" path="templateId">
          <div class="flex items-center gap-16px">
            <NSelect
              v-model:value="model.templateId"
              class="w-400px"
              :options="emailTemplateOptions"
              value-field="id"
              label-field="title"
              placeholder="Select email  template"
            />
            <NButton text type="primary" @click="handleAddEmailTemplate">Add a new email template</NButton>
          </div>
        </NFormItem>
        <NFormItem label="Creator Data Selection" path="emailList">
          <UploadCreatorCard type="email" @change="handleCreatorDataChange" />
        </NFormItem>
      </NForm>
      <template #action>
        <div class="flex justify-end gap-16px">
          <NButton @click="handleBack">Cancel</NButton>
          <NButton @click="handleTestSend">Test Send</NButton>
          <NButton type="primary" @click="handleSendAll">Save and Start Task</NButton>
        </div>
      </template>
    </NCard>
  </div>
</template>

<style scoped></style>
