<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-11-04 16:34:57
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-19 11:27:13
 * @FilePath: \tiksage-frontend\src\views\return-refund\modules\order-source-card.vue
 * @Description: order-source-card
-->
<script setup lang="tsx">
import { reactive } from 'vue';
import type { PaginationProps } from 'naive-ui';
import { NButton, NEllipsis, NFlex, NImage, NPopover, NScrollbar, NText, NThing } from 'naive-ui';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { getFallbackImage } from '@/utils/fake-image';
import Tip from '@/components/custom/tip.vue';
import { NumeralFormat } from '@/enum';

interface Props {
  data: Api.ReturnRefund.OrderSource[];
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const pagination = reactive<PaginationProps>({
  // showSizePicker: true,
  pageSizes: [10, 15, 20, 25, 30]
});

const columns: NaiveUI.DataTableBaseColumn<Api.ReturnRefund.OrderSource>[] = [
  {
    key: 'orderSource',
    align: 'center',
    title: 'Creator Info',
    width: 250
  },
  {
    key: 'returnOrderNum',
    align: 'center',
    title: 'R&R Orders'
  },
  {
    key: 'creatorTotalOrderNum',
    align: 'center',
    title: () => {
      return (
        <NFlex justify="center" align="center" wrap={false}>
          <span>Total Orders</span>
          <Tip description="The total number of orders placed, including those that were refunded." />
        </NFlex>
      );
    },
    sorter: 'default',
    sortOrder: 'descend'
  },
  {
    key: 'returnAffiliateOrderRatio',
    align: 'center',
    title: 'R&R Rate in All Affiliated Orders'
  },
  {
    key: 'returnCreatorOrderRatio',
    align: 'center',
    title: `R&R Rate of Creator's Total Orders`
  },
  {
    key: 'returnPrdouctList',
    align: 'center',
    title: 'R&R Order Product',
    render(rowData) {
      return rowData?.returnPrdouctList?.length ? (
        <NPopover>
          {{
            trigger: () => (
              <NButton text type="primary">
                {rowData?.returnPrdouctList.length || 0}
              </NButton>
            ),
            default: () => (
              <NFlex vertical size={16}>
                <NText class="font-bold">Product List</NText>
                <NScrollbar style={{ maxHeight: '400px' }}>
                  {rowData!.returnPrdouctList.map(p => (
                    <NThing content-indented>
                      {{
                        avatar: () => (
                          <NImage
                            width={50}
                            height={50}
                            src={`${import.meta.env.VITE_PRODUCT_AVATAR_URL}${p.productAvatarLocal}`}
                            fallbackSrc={getFallbackImage(50, 50)}
                          />
                        ),
                        header: () => <NEllipsis style="width:250px">{p.productName}</NEllipsis>,
                        description: () => (
                          <NFlex justify="space-between" align="center" wrap={false}>
                            <NFlex class="text-sm text-gray">
                              <span>Rate:{numberFormat(p?.ratio, NumeralFormat.Percent)}</span>
                            </NFlex>
                            <span>x {p.returnOrderNum}</span>
                          </NFlex>
                        )
                      }}
                    </NThing>
                  ))}
                </NScrollbar>
              </NFlex>
            )
          }}
        </NPopover>
      ) : (
        '-'
      );
    }
  }
];

const dollarKeys = [''];
const percentKeys = ['returnCreatorOrderRatio', 'returnAffiliateOrderRatio'];

const renderCell = (value: any, _rowData: any, column: any) => {
  if (dollarKeys.includes(column.key)) {
    return numberFormat(value, NumeralFormat.Dollar);
  }
  if (percentKeys.includes(column.key)) {
    return numberFormat(value, NumeralFormat.Percent);
  } else if (typeof value === 'number') {
    return numberFormat(value, NumeralFormat.Number);
  }
  return value;
};
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" title="Return/Refund Orders by Creator">
    <NDataTable
      :min-height="432"
      size="small"
      :bordered="false"
      :data="props.data"
      :columns="columns"
      :pagination="pagination"
      :render-cell="renderCell"
    ></NDataTable>
  </NCard>
</template>

<style scoped></style>
