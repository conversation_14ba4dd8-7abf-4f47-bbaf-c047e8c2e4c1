<script setup lang="ts">
interface Props {
  label?: string;
  content?: string;
}

withDefaults(defineProps<Props>(), {
  label: 'Created at',
  content: ''
});
</script>

<template>
  <NCard class="card-wrapper" content-class="flex justify-between items-center" :bordered="false">
    <div class="flex items-center gap-16px">
      <SystemLogo class="text-32px text-primary" />
      <icon-local-logo-title class="h-24px w-78px text-primary" />
    </div>
    <NText v-if="content" class="text-sm text-gray">
      <span>{{ label }}</span>
      <span class="text-gray-500">:</span>
      <span>{{ content }}</span>
    </NText>
  </NCard>
</template>

<style scoped></style>
