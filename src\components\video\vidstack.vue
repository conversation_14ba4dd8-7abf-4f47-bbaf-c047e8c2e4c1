<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-30 09:56:10
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-01 15:05:42
 * @FilePath: \tiksage-frontend\src\components\video\vidstack.vue
 * @Description: vidstack
-->
<script setup lang="ts">
import 'vidstack/icons';
import 'vidstack/bundle';

interface Props {
  src: string;
  showControl?: boolean;
}

withDefaults(defineProps<Props>(), {
  showControl: true
});
</script>

<template>
  <media-player load="visible" plays-inline view-type="video" title="Sprite Fight" cross-origin :src="src">
    <media-provider></media-provider>
    <!-- Layouts -->
    <!-- <media-plyr-layout></media-plyr-layout> -->
    <media-video-layout v-if="showControl" />
  </media-player>
</template>

<style scoped></style>
