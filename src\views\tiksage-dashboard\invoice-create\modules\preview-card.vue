<script setup lang="ts">
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';

const { numberFormat } = useNumberFormat();

type Model = CommonType.RecordNullable<Api.Invoice.InvoiceCreator>;
const value = defineModel<Model>('value', {
  required: true
});
</script>

<template>
  <div class="flex-col gap-4">
    <div class="flex justify-between">
      <div class="flex-col-center gap-0">
        <icon-local-logo class="text-150px" />
        <icon-local-logo-title class="h-75px text-150px" />
      </div>
      <div class="flex-col items-end justify-start">
        <span>TikSage</span>
        <span>8143216879</span>
        <span>15657 N Hayden Rd #1086</span>
        <span>Scottsdale, AZ 85260</span>
      </div>
    </div>
    <NGrid :cols="4">
      <NGi>
        <NDescriptions>
          <NDescriptionsItem label="Billed To">{{ value?.clientName || '-' }}</NDescriptionsItem>
        </NDescriptions>
      </NGi>
      <NGi>
        <NDescriptions :columns="1">
          <NDescriptionsItem label="Date of Issue">{{ value?.issueDate || '-' }}</NDescriptionsItem>
          <NDescriptionsItem label="Due Date">{{ value?.dueDate || '-' }}</NDescriptionsItem>
        </NDescriptions>
      </NGi>
      <NGi>
        <NDescriptions>
          <NDescriptionsItem label=" Invoice Number">{{ value?.invoiceNoStr }}</NDescriptionsItem>
        </NDescriptions>
      </NGi>
      <NGi class="flex justify-end">
        <NDescriptions label-align="right">
          <NDescriptionsItem label="Amount Due (USD)">
            <div class="flex justify-end text-6">{{ numberFormat(value?.total || 0, NumeralFormat.Real_Dollar) }}</div>
          </NDescriptionsItem>
        </NDescriptions>
      </NGi>
    </NGrid>
    <div class="mt-4">
      <table class="w-full">
        <colgroup>
          <col span="1" />
          <col span="1" />
          <col span="1" />
          <col span="1" />
        </colgroup>
        <thead class="border-t-4px border-t-#4E6979">
          <tr>
            <th>Description</th>
            <th>Rate</th>
            <th>Qty</th>
            <th>Line Total</th>
          </tr>
        </thead>
        <tbody v-show="value?.invoiceDetailList?.length">
          <tr v-for="item in value?.invoiceDetailList" :key="item.itemName" class="tr-b-2px">
            <td class="flex-col">
              <span>{{ item?.itemName || '-' }}</span>
              <span class="text-xs">({{ item?.itemDesc || '-' }})</span>
            </td>
            <td>
              <span>{{ numberFormat(item?.rate || 0, NumeralFormat.Real_Dollar) }}</span>
            </td>
            <td>
              <span>{{ item?.qty || 0 }}</span>
            </td>
            <td>
              <span>{{ numberFormat(item?.total || 0, NumeralFormat.Real_Dollar) }}</span>
            </td>
          </tr>
        </tbody>
        <tfoot>
          <tr>
            <td colspan="2"></td>
            <td class="pt-4">Subtotal</td>
            <td class="pt-4">{{ numberFormat(value?.subtotal || 0, NumeralFormat.Real_Dollar) }}</td>
          </tr>
          <tr>
            <td colspan="2"></td>
            <td class="tr-b-2px">Tax</td>
            <td class="tr-b-2px">{{ numberFormat(value?.tax || 0, NumeralFormat.Real_Dollar) }}</td>
          </tr>
          <tr>
            <td colspan="2"></td>
            <td>Total</td>
            <td>{{ numberFormat(value?.subtotal + value?.tax || 0, NumeralFormat.Real_Dollar) }}</td>
          </tr>
          <tr>
            <td colspan="2"></td>
            <td class="tr-b-4px">Amount Paid</td>
            <td class="tr-b-4px">{{ numberFormat(value?.amountPaid || 0, NumeralFormat.Real_Dollar) }}</td>
          </tr>
          <tr>
            <td colspan="2"></td>
            <td><span class="text-gray">Amount Due (USD)</span></td>
            <td>{{ numberFormat(value?.total || 0, NumeralFormat.Real_Dollar) }}</td>
          </tr>
        </tfoot>
      </table>
    </div>
    <div class="flex-col">
      <NDescriptions>
        <NDescriptionsItem label="Notes">
          <span>
            Please send payment to Chase Checking:
            <br />
            Account number *********
            <br />
            Routing number ********* (ACH transaction and direct deposit)
            <br />
            Routing number ********* (wire transfer)
            <br />
            Thank you!
          </span>
        </NDescriptionsItem>
      </NDescriptions>
    </div>
  </div>
</template>

<style scoped lang="scss">
$size1: 2px;
$size2: 4px;

.tr-b-#{$size1} {
  border-bottom: $size1 solid #ccd0d9;
}
.tr-b-#{$size2} {
  border-bottom: $size2 solid #ccd0d9;
}

table td:nth-child(1),
table th:nth-child(1) {
  text-align: left; /* 第二列左对齐 */
}
table td:nth-child(2),
table th:nth-child(2) {
  text-align: right; /* 第二列左对齐 */
}
table td:nth-child(3),
table th:nth-child(3) {
  text-align: right; /* 第二列左对齐 */
}
table td:nth-child(4),
table th:nth-child(4) {
  text-align: right; /* 第二列左对齐 */
}
</style>
