<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-09 10:05:03
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-10 15:58:30
 * @FilePath: \tiksage-frontend\src\views\home\modules\comparison-card.vue
 * @Description: comparison-card
-->
<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { add } from 'lodash-es';
import { useDashboardStore } from '@/store/modules/dashboard';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { useIndicator } from '@/hooks/custom/indicator';
import { getRatio } from '@/utils/number';
import { NumeralFormat } from '@/enum';

interface Props {
  model: {
    title: string;
    allIndicatorKey: Api.Auth.AllIndicatorKey[];
    userIndicatorKey: Api.Auth.userIndicatorKey;
  };
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const chartOptions = ref<Visactor.VChart.IAreaChartSpec>();

// data
const { displayValue, checkedValue, options, updateCheckedValue } = useIndicator<string>(
  props.model.allIndicatorKey,
  props.model.userIndicatorKey
);

const handleValueChange = (value: string) => {
  checkedValue.value = value;
  updateCheckedValue();
};

const dashboardStore = useDashboardStore();

type ShowDataItem = {
  total: number;
  data: any[];
};

type ShowData = {
  videoAffiliate: ShowDataItem;
  videoOwn: ShowDataItem;
  liveAffiliate: ShowDataItem;
  liveOwn: ShowDataItem;
};

function createShowData() {
  return {
    videoAffiliate: { total: 0, data: [] },
    videoOwn: { total: 0, data: [] },
    liveAffiliate: { total: 0, data: [] },
    liveOwn: { total: 0, data: [] }
  };
}

const showData = reactive<ShowData>(createShowData());

const initData = () => {
  const chartData = dashboardStore.initIndicatorData(displayValue.value, options.value);
  const { fieldsArr } = chartData[0]!.option;
  if (fieldsArr) {
    for (const field of fieldsArr) {
      const total = dashboardStore.dashboardData.currentTotalData[field.key] as number;
      showData[field.title as keyof ShowData] = {
        total,
        data: chartData[0]?.fieldValues ? chartData[0].fieldValues[field.title] : []
      };
    }
  }
  initChartOptions([...showData.videoAffiliate.data, ...showData.liveAffiliate.data]);
};

function initChartOptions(values: any[]) {
  const data: any[] = [
    {
      id: 'dollarY',
      values: values.map(v => {
        return {
          ...v,
          type: v.type.includes('video') ? 'Video' : 'Live'
        };
      })
    }
  ];
  chartOptions.value = {
    type: 'area',
    data,
    xField: 'time',
    yField: 'y'
  };
}

// computed show data
const videoAffiliatePercent = computed(() => {
  const alt = add(showData.videoAffiliate.total, showData.liveAffiliate.total);
  if (!alt) return 0;
  return getRatio(showData.videoAffiliate.total, alt);
});
const videoOwnPercent = computed(() => {
  const alt = add(showData.videoOwn.total, showData.liveOwn.total);
  if (!alt) return 0;
  return getRatio(showData.videoOwn.total, alt);
});
const liveAffiliatePercent = computed(() => {
  const alt = add(showData.videoAffiliate.total, showData.liveAffiliate.total);
  if (!alt) return 0;
  return getRatio(showData.liveAffiliate.total, alt);
});
const liveOwnPercent = computed(() => {
  const alt = add(showData.videoOwn.total, showData.liveOwn.total);
  if (!alt) return 0;
  return getRatio(showData.liveOwn.total, alt);
});

const affiliateTotal = computed(() => {
  return add(showData.videoAffiliate.total, showData.liveAffiliate.total);
});

const ownTotal = computed(() => {
  return add(showData.videoOwn.total, showData.liveOwn.total);
});

// chart type toggle
enum TypeValue {
  AFFILIATE,
  OWN
}

const chartTypeOption = reactive([
  { label: 'Affiliate', value: TypeValue.AFFILIATE },
  { label: 'Non-affiliate', value: TypeValue.OWN }
]);

const chartType = ref(TypeValue.AFFILIATE);

const loading = ref(false);

watch(
  () => chartType.value,
  newChartType => {
    loading.value = true;
    if (newChartType === TypeValue.AFFILIATE) {
      initChartOptions([...showData.videoAffiliate.data, ...showData.liveAffiliate.data]);
    } else {
      initChartOptions([...showData.videoOwn.data, ...showData.liveOwn.data]);
    }
    setTimeout(() => {
      loading.value = false;
    }, 1000);
  }
);

watch(
  () => displayValue.value,
  () => {
    initData();
  }
);

initData();
</script>

<template>
  <NCard :bordered="false">
    <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive class="mb-19px">
      <NGi span="12">
        <NFlex :wrap="false" justify="space-between" align="center">
          <NText class="text-size-18px font-500">{{ props.model.title }}</NText>
        </NFlex>
      </NGi>
      <NGi span="12">
        <NFlex justify="right">
          <NSelect
            class="w-72px"
            :value="checkedValue"
            :on-update-value="handleValueChange"
            :options="options[0]"
            :consistent-menu-width="false"
          />
          <NRadioGroup v-model:value="chartType">
            <NRadioButton
              v-for="item in chartTypeOption"
              :key="item.value"
              second
              :label="item.label"
              :value="item.value"
            ></NRadioButton>
          </NRadioGroup>
        </NFlex>
      </NGi>
    </NGrid>
    <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
      <NGi span="12">
        <NTable :bordered="false" class="h-full text-nowrap">
          <thead>
            <tr>
              <th></th>
              <th colspan="2" :style="{ textAlign: 'center' }">Affiliate</th>
              <th colspan="2" :style="{ textAlign: 'center' }">Non-affiliate</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="gray">
                <NFlex align="center">
                  <SvgIcon icon="solar:videocamera-record-bold-duotone" class="text-icon text-primary" />
                  <NText class="text-gray">Video</NText>
                </NFlex>
              </td>
              <td class="font-bold">
                {{ numberFormat(showData.videoAffiliate.total, NumeralFormat.Dollar) }}
              </td>
              <td class="gray">
                {{ numberFormat(videoAffiliatePercent, NumeralFormat.Percent) }}
              </td>
              <td class="font-bold">
                {{ numberFormat(showData.videoOwn.total, NumeralFormat.Dollar) }}
              </td>
              <td class="gray">
                {{ numberFormat(videoOwnPercent, NumeralFormat.Percent) }}
              </td>
            </tr>
            <tr>
              <td class="gray">
                <NFlex align="center">
                  <SvgIcon icon="solar:play-stream-bold-duotone" class="text-icon text-primary" />
                  <NText class="text-gray">Live</NText>
                </NFlex>
              </td>
              <td class="font-bold">{{ numberFormat(showData.liveAffiliate.total, NumeralFormat.Dollar) }}</td>
              <td class="gray">
                {{ numberFormat(liveAffiliatePercent, NumeralFormat.Percent) }}
              </td>
              <td class="font-bold">{{ numberFormat(showData.liveOwn.total, NumeralFormat.Dollar) }}</td>
              <td class="gray">
                {{ numberFormat(liveOwnPercent, NumeralFormat.Percent) }}
              </td>
            </tr>
            <tr>
              <td>
                <NFlex align="center">
                  <div class="w-14px"></div>
                  <NText class="text-gray">Total</NText>
                </NFlex>
              </td>
              <td class="font-bold">{{ numberFormat(affiliateTotal, NumeralFormat.Dollar) }}</td>
              <td class="gray">
                {{ numberFormat(videoAffiliatePercent + liveAffiliatePercent, NumeralFormat.Percent) }}
              </td>
              <td class="font-bold">{{ numberFormat(ownTotal, NumeralFormat.Dollar) }}</td>
              <td class="gray">
                {{ numberFormat(videoOwnPercent + liveOwnPercent, NumeralFormat.Percent) }}
              </td>
            </tr>
          </tbody>
        </NTable>
      </NGi>
      <NGi span="12" class="relative min-h-300px">
        <NSpin v-if="loading"></NSpin>
        <VLineAreaChart v-else style="height: 300px" :chart-options="chartOptions" />
      </NGi>
    </NGrid>
  </NCard>
</template>

<style scoped>
.gray {
  color: #9ca3af;
}
</style>
