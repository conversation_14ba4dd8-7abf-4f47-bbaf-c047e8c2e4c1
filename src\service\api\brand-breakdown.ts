import { request } from '../request';

export function fetchGetDashboardDataByBrand(data: Api.BrandBreakDown.DashboardSearchParamsByBrand) {
  return request<Api.Dashboard.DashboardData>({
    url: '/shop/listShopsDailyDataByBrand',
    method: 'post',
    data
  });
}

export function fetchGetSubBrandShopOptionsByShopId(shopId: number) {
  return request<string[]>({
    url: '/shop/listShopBrands',
    method: 'get',
    params: { shopId }
  });
}
