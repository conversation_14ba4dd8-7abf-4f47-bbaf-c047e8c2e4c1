/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-21 11:43:22
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-07 17:40:48
 * @FilePath: \tiksage-frontend\src\service\api\tool.ts
 * @Description: tool api
 */
import { adhocRequest, request } from '../request';

/** video downloader */

export function fetchVideoDownload(params: Api.Tool.VideoDownloaderSearchParams) {
  return adhocRequest({
    timeout: 1000 * 60 * 5,
    responseType: 'blob',
    url: '/tool/downloadVideo',
    method: 'get',
    params
  });
}

/** creators export */

export function fetchGetCreatorsExportTaskList(data: Api.Common.CommonSearchParams) {
  return request<Api.Tool.CreatorTaskListResponse>({
    url: '/tool/pageListCreatorTasks',
    method: 'post',
    data
  });
}

export function fetchCreateCreatorsExportTask(data: Api.Tool.CreateTaskParams) {
  return request({
    url: '/tool/importCreators',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    method: 'post',
    params: data.creatorIdListStr
      ? { creatorIdListStr: data.creatorIdListStr, description: data.description }
      : { description: data.description },
    data: data.file
      ? {
          file: data.file
        }
      : undefined
  });
}

export function fetchRunCreatorsExportTask(taskId: number) {
  return request({
    url: '/tool/runCreatorTask',
    method: 'post',
    data: { taskId }
  });
}

export function fetchDownloadRunCreatorsExportTaskExcel(taskId: number) {
  return request({
    responseType: 'blob',
    url: '/tool/exportCreatorTaskData',
    method: 'get',
    params: { taskId }
  });
}

export function fetchDownloadVideoScraperTaskExcel(keyword: string) {
  return adhocRequest({
    timeout: 1000 * 60 * 5,
    responseType: 'blob',
    url: '/tool/downloadSearchVideosData',
    method: 'get',
    params: { keyword }
  });
}
