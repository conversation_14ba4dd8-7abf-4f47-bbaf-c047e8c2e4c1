<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import dayjs from 'dayjs';
import { set } from 'lodash-es';
import {
  fetchExportWeeklyReport,
  fetchGetWeeklyReportDataByOperator,
  fetchRebuildWeeklyReport,
  fetchReleaseWeeklyReport,
  fetchRetractWeeklyReport,
  fetchUpdateAdsByOperator,
  fetchUpdateAffiliateByOperator,
  fetchUpdateCampaignsByOperator,
  fetchUpdatePerformanceByOperator
} from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useUserStore } from '@/store/modules/user';
import { downloadFile } from '@/utils/download';
import ShopInfoCard from '@/views/dashboard/modules/shop-info-card.vue';
import OverallPerformanceEdit from './modules/overall-performance-edit.vue';
import AffiliateEdit from './modules/affiliate-edit.vue';
import AdsEdit from './modules/ads-edit.vue';
import CampaignEdit from './modules/campaign-edit.vue';

const authStore = useAuthStore();
const userStore = useUserStore();

type SearchParams = Api.WeeklyReport.WeeklyReportDataParams;

const searchParams = ref<SearchParams>(createDefaultSearchParams());

const shopOptions = ref<CommonType.Option<number>[]>([]);

const [loading, toggleLoading] = useToggle(true);
const [saveLoading, toggleSaveLoading] = useToggle(false);
const [publishLoading, togglePublishLoading] = useToggle(false);
const [retractLoading, toggleRetractLoading] = useToggle(false);
const [rebuildLoading, toggleRebuildLoading] = useToggle(false);

const originalReportData = ref<Api.WeeklyReport.WeeklyReportData>();
const reportData = ref<Api.WeeklyReport.WeeklyReportData>(createDefaultReport());

const isPublished = computed<boolean>(() => {
  return reportData.value?.status === 1;
});

const canSave = computed<boolean>(() => {
  return !isPublished.value;
});

const canExport = computed<boolean>(() => {
  return !loading.value;
});

const waterText = computed<string>(() => {
  return canSave.value ? '' : 'Published';
});

const activeTab = ref<'performance' | 'affiliate' | 'ads' | 'campaigns'>('performance');

const saveFunc = {
  performance: fetchUpdatePerformanceByOperator,
  affiliate: fetchUpdateAffiliateByOperator,
  ads: fetchUpdateAdsByOperator,
  campaigns: fetchUpdateCampaignsByOperator
};

function createDefaultReport(): Api.WeeklyReport.WeeklyReportData {
  return {
    reportId: 0,
    adsHtml: '',
    adsHtmlBottom: '',
    affiliateHtml: '',
    affiliateHtmlBottom: '',
    campaignHtml: '',
    campaignHtmlBottom: '',
    performanceHtml: '',
    performanceHtmlBottom: '',
    campaignList: [],
    cycleTitle: '',
    lastMonthData: createDefaultTableData(),
    lastWeekData: createDefaultTableData(),
    month: '',
    monthStr: '',
    shopId: 0,
    shopName: '',
    thisMonthData: createDefaultTableData(),
    thisWeekData: createDefaultTableData(),
    videoPending: 0,
    week: '',
    selectCampaignIdList: [],
    sampleMetrics: [],
    status: 0,
    showAds: true,
    showGmvMaxAds: false
  };
}

function createDefaultTableData(): Api.WeeklyReport.tableData {
  return {
    adAddsToCart: 0,
    adAddsToCartRate: 0,
    adAov: 0,
    adCheckoutsInitiated: 0,
    adCheckoutsInitiatedRate: 0,
    adClicks: 0,
    adCost: 0,
    adCpa: 0,
    adCpc: 0,
    adCtr: 0,
    adCvr: 0,
    adGmv: 0,
    adImpressions: 0,
    adPurchases: 0,
    adRoas: 0,
    affiliateGmv: 0,
    affiliateOrders: 0,
    aov: 0,
    avgBuyers: 0,
    brandMetricsBOList: [],
    cycleTitle: '',
    endDate: '',
    gmv: 0,
    itemsSold: 0,
    ifGmvMaxAd: false,
    maxAdAov: 0,
    maxAdCost: 0,
    maxAdCpa: 0,
    maxAdGmv: 0,
    maxAdPurchases: 0,
    maxAdRoas: 0,
    orders: 0,
    pending: 0,
    postingCount: 0,
    sampleItems: 0,
    startDate: '',
    totalItems: 0,
    videoGmv: 0,
    videoImpressions: 0
  };
}

function dateDisabled(current: number) {
  // Less than the first day of the week
  const thisWeekStart = dayjs().startOf('week').valueOf();
  return current >= thisWeekStart;
}

function createDefaultSearchParams() {
  return {
    shopId: 0,
    week: dayjs().subtract(1, 'week').format('YYYY-wo')
  };
}

async function handlePublish() {
  if (!reportData.value) return;
  togglePublishLoading(true);
  const { error: createOrUpdateErr } = await fetchReleaseWeeklyReport(reportData.value.reportId);
  if (!createOrUpdateErr) {
    window.$message?.success('Release Successfully.');
    initReportData();
  }
  togglePublishLoading(false);
}

async function handleRetract() {
  if (!reportData.value) return;
  toggleRetractLoading(true);
  const { error: retractErr } = await fetchRetractWeeklyReport(reportData.value.reportId);
  if (!retractErr) {
    window.$message?.success('Retract Successfully.');
    initReportData();
  }
  toggleRetractLoading(false);
}

async function handleSave(type: keyof typeof saveFunc) {
  if (!reportData.value) return;
  toggleSaveLoading(true);
  const { error: createOrUpdateErr } = await saveFunc[type](reportData.value);
  if (!createOrUpdateErr) {
    window.$message?.success('Save Successfully.');

    // Show a dialog to remind the user to publish the report
    // if (canPublish.value) {
    //   window.$dialog?.info({
    //     title: 'Report Saved',
    //     content:
    //       'Your report has been saved successfully. Would you like to publish it now to make it visible to users?',
    //     positiveText: 'Publish Now',
    //     negativeText: 'Later',
    //     onPositiveClick: handlePublish
    //   });
    // } else {
    //   window.$message?.info('Please publish the report to make it visible to users.');
    // }

    initReportData();
  }
  toggleSaveLoading(false);
}

async function handleExportPDF() {
  if (!reportData.value) return;
  const { data: exportData, error: exportErr } = await fetchExportWeeklyReport(reportData.value.reportId);
  if (!exportErr) {
    window.$message?.success('Export Successfully.');
    downloadFile(exportData, 'pdf', 'Weekly Report');
  }
}

function handleUpdateReport(key: string, value: any) {
  if (!reportData.value) return;
  set(reportData.value, key, value);
}

function handleRefresh() {
  initReportData();
}

async function handleRebuild() {
  toggleRebuildLoading(true);
  toggleLoading(true);
  const { reportId, status } = reportData.value!;
  const { data: rebuildData, error: rebuildErr } = await fetchRebuildWeeklyReport(searchParams.value);
  if (!rebuildErr) {
    window.$message?.success('Rebuild Successfully.');
    reportData.value = { ...rebuildData, reportId, status };
  }
  toggleLoading(false);
  toggleRebuildLoading(false);
}

async function initShopOptions() {
  const userShops = await userStore.getUserShop();

  shopOptions.value = userShops.map(item => ({ label: item.shopName, value: item.shopId }));

  searchParams.value.shopId = userShops[0].shopId;
}

async function initReportData() {
  toggleLoading(true);
  const { data: originalData, error: originalDataErr } = await fetchGetWeeklyReportDataByOperator(searchParams.value);
  if (!originalDataErr) {
    reportData.value = { ...originalData };
    originalReportData.value = originalData;
  }
  toggleLoading(false);
}

function initData() {
  initShopOptions();
}

initData();

watch(
  () => searchParams.value,
  () => {
    if (searchParams.value.shopId === 0) return;
    initReportData();
  },
  {
    deep: true
  }
);
</script>

<template>
  <div class="flex-col gap-4">
    <NCard class="card-wrapper" :bordered="false" title="Generate Weekly Report">
      <template #header-extra>
        <div class="flex-col gap-4">
          <NPopselect
            v-model:value="searchParams.shopId"
            :disabled="authStore.userInfo.userShopIds.length <= 1"
            :options="shopOptions"
          >
            <NButton quaternary>
              <div class="flex items-center justify-end">
                <ShopInfoCard :id="searchParams.shopId" />
                <icon-solar:alt-arrow-down-line-duotone v-if="authStore.userInfo.userShopIds.length > 1" />
              </div>
            </NButton>
          </NPopselect>
        </div>
      </template>
    </NCard>
    <NCard class="h-full min-h-400px card-wrapper" content-class="flex-col" segmented :bordered="false">
      <template #header>
        <div class="flex items-center gap-4">
          <NTag v-if="isPublished" type="success" :bordered="false" size="small">
            <template #icon>
              <icon-svg-spinners:pulse-multiple />
            </template>
            Published
          </NTag>
          <NTag v-else type="warning" :bordered="false" size="small">
            <template #icon>
              <icon-svg-spinners:pulse-multiple />
            </template>
            No published
          </NTag>
        </div>
      </template>
      <template #header-extra>
        <div class="flex justify-end gap-4">
          <NButtonGroup>
            <NButton type="primary" secondary>Week</NButton>
            <NDatePicker
              v-model:formatted-value="searchParams.week"
              class="cus-border w-120px"
              :disabled="loading"
              type="week"
              format="YYYY-wo"
              :is-date-disabled="dateDisabled"
              :actions="[]"
            ></NDatePicker>
          </NButtonGroup>

          <NPopconfirm v-if="!isPublished" @positive-click="handlePublish">
            <template #trigger>
              <NButton secondary :loading="publishLoading" type="success">
                <template #icon>
                  <icon-solar:rocket-linear />
                </template>
                Publish
              </NButton>
            </template>
            After the release, users will see this report. Are you sure you want to release it?
          </NPopconfirm>

          <NPopconfirm v-else @positive-click="handleRetract">
            <template #trigger>
              <NButton secondary :loading="retractLoading" type="warning">
                <template #icon>
                  <icon-solar:archive-down-linear />
                </template>
                Retract
              </NButton>
            </template>
            After being removed from the shelves, the report will no longer be visible to users.Are you sure you want to
            take it off?
          </NPopconfirm>

          <NButton secondary :disabled="!canExport" @click="handleExportPDF">
            <template #icon>
              <icon-solar:export-linear />
            </template>
            Export
          </NButton>
        </div>
      </template>
      <NSpin class="h-full" :show="loading" description="Generating report...">
        <div v-if="!rebuildLoading" class="flex-col gap-4">
          <div class="flex-y-center justify-end gap-2">
            <NTooltip>
              <template #trigger>
                <NButton secondary :disabled="!canSave" @click="handleRefresh">
                  <template #icon>
                    <icon-solar:refresh-linear />
                  </template>
                  Reload
                </NButton>
              </template>
              Get the latest weekly report data and delete unsaved modifications.
            </NTooltip>
            <NPopconfirm @positive-click="handleRebuild">
              <template #trigger>
                <NButton :disabled="!canSave" secondary :loading="rebuildLoading" type="default">
                  <template #icon>
                    <icon-solar:undo-right-linear />
                  </template>
                  Rebuild
                </NButton>
              </template>
              <div>
                <div>Are you sure you want to regenerate the report?</div>
                <div class="text-error">This action resets all modifications!</div>
              </div>
            </NPopconfirm>
            <NDivider vertical />
            <NPopconfirm @positive-click="handleSave(activeTab)">
              <template #trigger>
                <NButton :disabled="!canSave" secondary :loading="saveLoading" type="primary">
                  <template #icon>
                    <icon-solar:diskette-linear />
                  </template>
                  Save
                </NButton>
              </template>
              <div>
                <div>Only the content of the current tab will be saved!</div>
                <div class="mt-1 text-red-500">Unsaved content in other tabs will be lost.</div>
              </div>
            </NPopconfirm>
          </div>
          <NTabs v-model:value="activeTab" type="segment" animated>
            <NTabPane class="relative" tab="Overall Performance" name="performance">
              <NWatermark
                :class="{ 'pointer-events-none': !canSave }"
                :height="150"
                :width="300"
                :global-rotate="-45"
                :content="waterText"
              >
                <OverallPerformanceEdit v-model:report-data="reportData" @update="handleUpdateReport" />
              </NWatermark>
            </NTabPane>
            <NTabPane tab="Affiliates" name="affiliate">
              <NWatermark
                :class="{ 'pointer-events-none': !canSave }"
                :height="150"
                :width="300"
                :global-rotate="-45"
                :content="waterText"
              >
                <AffiliateEdit :report-data="reportData" @update="handleUpdateReport" />
              </NWatermark>
            </NTabPane>
            <NTabPane tab="Ads" name="ads">
              <NWatermark
                :class="{ 'pointer-events-none': !canSave }"
                :height="150"
                :width="300"
                :global-rotate="-45"
                :content="waterText"
              >
                <AdsEdit :report-data="reportData" :can-save="canSave" @update="handleUpdateReport" />
              </NWatermark>
            </NTabPane>
            <NTabPane tab="Campaigns" name="campaigns">
              <NWatermark
                :class="{ 'pointer-events-none': !canSave }"
                :height="150"
                :width="300"
                :global-rotate="-45"
                :content="waterText"
              >
                <CampaignEdit :report-data="reportData" @update="handleUpdateReport" />
              </NWatermark>
            </NTabPane>
          </NTabs>
        </div>
      </NSpin>
    </NCard>
  </div>
</template>

<style scoped>
:deep(.cus-border .n-input) {
  border-radius: 0 6px 6px 0 !important;
}

:deep(.n-data-table .n-data-table-th) {
  background-color: #eaf4ff !important;
  color: #5a5a5a !important;
}

:deep(.n-data-table .n-data-table-td.n-data-table-td--summary) {
  background-color: #ffffff !important;
}
</style>
