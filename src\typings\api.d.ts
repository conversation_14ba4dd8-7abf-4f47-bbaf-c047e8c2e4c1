/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-24 15:56:40
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-12-02 15:00:49
 * @LastEditTime: 2024-09-12 15:08:35
 * @FilePath: \tiksage-frontend\src\typings\api.d.ts
 * @Description: api.d.ts
 */

/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
      /** total pages */
      pages: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /**
     * enable status
     *
     * - 0: Normal
     * - 1: Inactive
     */
    type EnableStatus = 0 | 1;

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy?: number;
      /** record create time */
      createTime?: string;
      /** record updater */
      updateBy?: string;
      /** record update time */
      updateTime?: string;
    } & T;

    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;
  }

  namespace Dashboard {
    type DashboardSearchParams = CommonType.RecordNullable<{
      brand: string;
      market: string;
      shopIdsArr: number[];
      startDateStr: string;
      endDateStr: string;
    }>;

    /** GetShopsDailyDataDTO */
    type DashboardData = {
      currentRangeArr: ShopDailyDataRangeDTO[];
      currentTotalData: ShopDailyDataDTO;
      previousTotalData: ShopDailyDataDTO;
    };

    /** ShopDailyDataRangeDTO */
    type ShopDailyDataRangeDTO = {
      avgCtrPerDay?: number;
      avgCtrPerDayLive?: number;
      avgCtrPerDayProductCard?: number;
      avgCtrPerDayVideo?: number;
      avgCvrPerDay?: number;
      avgCvrPerDayLive?: number;
      avgCvrPerDayMultiContent?: number;
      avgCvrPerDayProductCard?: number;
      avgCvrPerDayVideo?: number;
      avgOrderValue?: number;
      belongDate?: string;
      buyersAffiliate?: number;
      buyersLive?: number;
      buyersMultiChannel?: number;
      buyersMultiContent?: number;
      buyersNonAffiliate?: number;
      buyersProductCard?: number;
      buyersTotal?: number;
      buyersVideo?: number;
      firstTimeBuyers?: number;
      gmvAffiliate?: number;
      gmvAffiliateLive?: number;
      gmvAffiliateVideo?: number;
      gmvLive?: number;
      gmvNonAffiliate?: number;
      gmvOwnLive?: number;
      gmvOwnVideo?: number;
      gmvProductCard?: number;
      gmvTotal?: number;
      gmvVideo?: number;
      grossSalesAffiliate?: number;
      grossSalesNonAffiliate?: number;
      grossSalesProductCard?: number;
      grossSalesTotal?: number;
      grossSalesVideo?: number;
      livesAvgViews?: number;
      livesCtor?: number;
      livesCtr?: number;
      livesGpm?: number;
      livesViews?: number;
      newFollowers?: number;
      newLives?: number;
      newVideos?: number;
      orderedSkusAffiliate?: number;
      orderedSkusLive?: number;
      orderedSkusNonAffiliate?: number;
      orderedSkusProductCard?: number;
      orderedSkusTotal?: number;
      orderedSkusVideo?: number;
      refunds?: number;
      shippingFees?: number;
      soldQuantity?: number;
      tax?: number;
      totalOrders?: number;
      videosCtor?: number;
      videosCtr?: number;
      videosGpm?: number;
      videosViews?: number;
      visitorsLive?: number;
      visitorsMultiContent?: number;
      visitorsProductCard?: number;
      visitorsTotal?: number;
      visitorsVideo?: number;
      clicks?: number;
      impressions?: number;
      onsiteShopping?: number;
      spend?: number;
    };

    /** ShopDailyDataDTO */
    type ShopDailyDataDTO = {
      avgCtrPerDay?: number;
      avgCtrPerDayLive?: number;
      avgCtrPerDayProductCard?: number;
      avgCtrPerDayVideo?: number;
      avgCvrPerDay?: number;
      avgCvrPerDayLive?: number;
      avgCvrPerDayMultiContent?: number;
      avgCvrPerDayProductCard?: number;
      avgCvrPerDayVideo?: number;
      avgOrderValue?: number;
      buyersAffiliate?: number;
      buyersLive?: number;
      buyersMultiChannel?: number;
      buyersMultiContent?: number;
      buyersNonAffiliate?: number;
      buyersProductCard?: number;
      buyersTotal?: number;
      buyersVideo?: number;
      firstTimeBuyers?: number;
      gmvAffiliate?: number;
      gmvAffiliateLive?: number;
      gmvAffiliateVideo?: number;
      gmvLive?: number;
      gmvNonAffiliate?: number;
      gmvOwnLive?: number;
      gmvOwnVideo?: number;
      gmvProductCard?: number;
      gmvTotal?: number;
      gmvVideo?: number;
      grossSalesAffiliate?: number;
      grossSalesNonAffiliate?: number;
      grossSalesProductCard?: number;
      grossSalesTotal?: number;
      grossSalesVideo?: number;
      livesAvgViews?: number;
      livesCtor?: number;
      livesCtr?: number;
      livesGpm?: number;
      livesViews?: number;
      newFollowers?: number;
      newLives?: number;
      newVideos?: number;
      orderedSkusAffiliate?: number;
      orderedSkusLive?: number;
      orderedSkusNonAffiliate?: number;
      orderedSkusProductCard?: number;
      orderedSkusTotal?: number;
      orderedSkusVideo?: number;
      refunds?: number;
      shippingFees?: number;
      soldQuantity?: number;
      tax?: number;
      totalOrders?: number;
      videosCtor?: number;
      videosCtr?: number;
      videosGpm?: number;
      videosViews?: number;
      visitorsLive?: number;
      visitorsMultiContent?: number;
      visitorsProductCard?: number;
      visitorsTotal?: number;
      visitorsVideo?: number;
      clicks?: number;
      impressions?: number;
      onsiteShopping?: number;
      spend?: number;
    };

    type DashboardTopSearchParams = CommonType.RecordNullable<{
      brand: string;
      keyWord: string;
      market: string;
      shopIdsArr: number[];
      topNum: number;
    }> & {
      startDateStr: string;
      endDateStr: string;
    };

    type CreatorsTop = {
      affiliateFollowers: number;
      affiliateGmv: number;
      affiliateGmvLive: number;
      affiliateGmvShoppableVideo: number;
      affiliateGmvShowcase: number;
      affiliateLives: number;
      affiliateShoppableVideos: number;
      avatar: string;
      avgOrderValue: number;
      buyers: number;
      creatorId: string;
      ctr: number;
      nickname: string;
      estCommission: number;
    };

    type CreatorsTopResponse = {
      creatorTopArr: CreatorsTop[];
    };

    type ProductTop = {
      gmv: number;
      totalImpressions: number;
      liveClickRate: number;
      liveImpressions: number;
      liveUnitsSold: number;
      productAvatar: string;
      productAvatarLocal: string;
      productCardClickRate: number;
      productCardImpressions: number;
      productCardUnitsSold: number;
      productId: string;
      productName: string;
      unitsSold: number;
      videoClickRate: number;
      videoImpressions: number;
      videoUnitsSold: number;
      priceRange: string;
    };

    type ProductTopResponse = {
      productTopArr: ProductTop[];
    };

    type DashboardShopSearchParams = Common.CommonSearchParams &
      CommonType.RecordNullable<{
        shopIdsArr?: number[] | null;
        endDateStr: string;
        startDateStr: string;
      }>;

    type DashboardShopResponse = Common.PaginatingQueryRecord<DashboardShop>;

    type DashboardShop = {
      shopId: number;
      avatar: string;
      buyers: number;
      followers: number;
      gmv: number;
      lives: number;
      productNum: number;
      shopCode: string;
      shopName: string;
      topSellingProductImgList: TopSellingProduct[];
      totalOrders: number;
      videos: number;
      visitors: number;
      activeProductNum: number;
    };

    type TopSellingProduct = {
      productId: string;
      productAvatarLocal: string;
    };

    type TodayPerformance = {
      affiliateGMV: number;
      conversionRate: number;
      endDate: string;
      gmv: number;
      liveGmv: number;
      orderCnt: number;
      productCardGmv: number;
      startDate: string;
      updatedTime: Date;
      updatedTimestamp: number;
      videoGmv: number;
      visitorCnt: number;
    };

    type TodayPerformanceResponse = {
      todayPerformance: TodayPerformance;
      todayAffiliateSales: TodayCreatorResponse;
      todayAffiliateVideo: TodayVideoResponse;
    };

    type TodaySearchParams = CommonType.RecordNullable<
      Common.CommonSearchParams & {
        shopId: number;
        creatorId?: string;
      }
    >;

    type TodayCreator = {
      affiliateFollowers: number;
      affiliateGmv: number;
      affiliateGmvLive: number;
      affiliateGmvShoppableVideo: number;
      affiliateGmvShowcase: number;
      affiliateLives: number;
      affiliateShoppableVideos: number;
      avatar: string;
      avatarLocal: string;
      avgOrderValue: number;
      creatorId: string;
      ctr: number;
      estCommission: number;
      nickname: string;
      orders: number;
    };

    type TodayCreatorResponse = Common.PaginatingQueryRecord<TodayCreator>;

    type TodayVideo = {
      affiliateCTR: number;
      affiliateGmv: number;
      backupUrl: string;
      creatorAvatar: string;
      creatorHandle: string;
      creatorId: string;
      postTimestamp: number;
      shoppableVideoGPM: number;
      title: string;
      video: string;
      videoAvatar: string;
      videoAvatarLocal: string;
      videoID: string;
      videoUrl: string;
    };

    type TodayVideoResponse = Common.PaginatingQueryRecord<TodayVideo>;

    type ShopData = {
      shopName: string;
      avatar: string;
    };

    type CommonDateSearchParams = {
      startDate: string;
      endDate: string;
    };

    type ProductCreatorSearchParams = {
      productId: string;
      shopId: number;
    } & CommonDateSearchParams;

    type ProductCreator = {
      avatar: string;
      avatarLocal: string;
      creatorId: string;
      gmv: number;
      nickname: string;
      unitSold: number;
    };

    type ProductCreatorResponse = ProductCreator[];

    type ContentTopSearchParams = {
      shopId: number;
      type: number;
    } & CommonDateSearchParams;

    type LiveContentTop = {
      comments: number;
      creatorAvatar: string;
      creatorHandle: string;
      creatorId: string;
      ctr: number;
      endTimestamp: number;
      estCommission: number;
      gmv: number;
      itemSold: number;
      likes: number;
      liveAvatar: string;
      liveAvatarLocal: string;
      liveID: string;
      newFollowers: number;
      productImpressions: number;
      shares: number;
      startTimestamp: number;
      title: string;
      views: number;
    };

    type LiveContentTopResponse = LiveContentTop[];

    type VideoContentTop = {
      ctr: number;
      gmv: number;
      backupUrl: string;
      comments: number;
      creatorAvatar: string;
      creatorHandle: string;
      creatorId: string;
      itemSold: number;
      likes: number;
      newFollowers: number;
      postTimestamp: number;
      productImpressions: number;
      shares: number;
      title: string;
      video: string;
      videoAvatar: string;
      videoAvatarLocal: string;
      videoID: string;
      videoUrl: string;
      views: number;
    };

    type VideoContentTopResponse = VideoContentTop[];

    type MonthlyData = {
      liveComments: number;
      liveLikes: number;
      liveProductClicks: number;
      liveProductImpressions: number;
      liveShares: number;
      liveViewers: number;
      liveViews: number;
      monthStr: string;
      productCardClicks: number;
      productCardViewers: number;
      productCardViews: number;
      videoComments: number;
      videoLikes: number;
      videoProductClicks: number;
      videoProductImpressions: number;
      videoShares: number;
      videoViews: number;
    };

    type ConversionAnalysisSearchParams = {
      shopId: number;
      startDateStr: string;
      endDateStr: string;
    };

    type ConversionAnalysisResponse = {
      clickData: IndiactorData;
      engagementData: IndiactorData;
      gmvData: IndiactorData;
      impressionData: IndiactorData;
      itemsSoldData: IndiactorData;
    };

    type IndiactorData = {
      ads: number;
      comments: number;
      likes: number;
      live: number;
      productCard: number;
      shares: number;
      total: number;
      video: number;
    };

    type ProductTrend = {
      belongDate: string;
      gmv: number;
    };
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      token: string;
      refreshToken?: string;
    }

    type AllIndicatorKey =
      | 'performanceSalesArr'
      | 'performanceTrafficArr'
      | 'performancecontentArr'
      | 'salesArr'
      | 'trafficArr'
      | 'contentArr'
      | 'breakdownAffiliateArr'
      | 'breakdownChannelArr'
      | 'contentAffiliateOwnArr'
      | 'adsArr';

    type userIndicatorKey =
      | 'breakdownAffiliate'
      | 'breakdownChannel'
      | 'contentAffiliateOwn'
      | 'contentArr'
      | 'performanceArr'
      | 'salesArr'
      | 'trafficArr'
      | 'adsArr';

    type UnitType = '$' | '%';

    type Field = {
      title: string;
      key: keyof Api.Dashboard.ShopDailyDataDTO;
      annotate: string | null;
    };

    type IndicatorValue = {
      color: string | null;
      icon: string | null;
      unit: UnitType | null;
      fieldsArr?: Field[];
    } & Field;

    type Indicators = {
      allIndicators: Record<AllIndicatorKey, IndicatorValue[]> | null;
      userIndicators: Record<userIndicatorKey, string | string[]> | null;
    };

    type SelectOption = {
      label: string;
      value: string | number;
      children?: SelectOption[];
    };

    interface UserInfo {
      id: string;
      userName: string;
      avatar: string;
      roles: string[];
      indicator: Indicators;
      brandIndicator: Indicators;
      selectOptions: SelectOption[];
      userShopIds: number[];
      userMarkets: string[];
      homePage: string;
      buttonCodeList: string[];
      categoryLeadersDefault: string;
      userCategoryIds: number[];
      [key: string]: any;
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  /**
   * namespace SystemManage
   *
   * backend api module: "systemManage"
   */
  namespace SystemManage {
    /** role */
    type Role = Common.CommonRecord<{
      /** role name */
      roleName: string;
      /** role code */
      roleCode: string;
      /** role description */
      roleDesc: string;
      /** role sort */
      roleSort: number;
      /** del_flag 0 = existence 1 = deletion */
      delFlag: number;
    }>;

    /** role search params */
    type RoleSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.Role, 'roleName' | 'roleCode'> & Api.Common.CommonSearchParams
    >;

    /** role list */
    type RoleList = Common.PaginatingQueryRecord<Role>;

    type MenuButtonGroup = {
      buttonCodeList: string[];
      menuId: number;
    };

    /** update role-menu params */
    type RoleMenuParams = {
      menuButtonList?: MenuButtonGroup[];
      /** role id */
      roleId: number;
    };

    type RoleMenuResponse = {
      menuButtonList: MenuButtonGroup[];
    };

    type ButtonTree = {
      buttonCode: string;
      buttonDesc: string;
      menuId: number;
      menuName: string;
    };

    /**
     * user gender
     *
     * - "1": "male"
     * - "2": "female"
     */
    type UserGender = '1' | '2';

    /** user */
    type User = Common.CommonRecord<{
      /** avatar */
      avatar?: string;
      /** Delete flag (0 for exists, 1 for deletion */
      delFlag?: number;
      email?: string;
      /** user name */
      userName?: string;
      /** invalid login times */
      invalidLoginTimes?: number;
      /** last login date */
      lastLoginDate?: Date;
      /** last login ip */
      lastLoginIp?: string;
      /** password */
      password?: string;
      /** 0: admin 1: user */
      /** role list */
      roleIdList?: number[] | null;
      /** telephone number */
      tel?: string;
      /** record status */
      status: Api.Common.EnableStatus | null;
      homePage?: string | null;
    }>;

    /** user search params */
    type UserSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.User, 'userName' | 'tel' | 'email' | 'status'> & Api.Common.CommonSearchParams
    >;

    type UserUpdateParams = Pick<
      Api.SystemManage.User,
      'userName' | 'email' | 'avatar' | 'status' | 'homePage' | 'roleIdList'
    > & {
      userId: number;
    };

    /** user list */
    type UserList = Common.PaginatingQueryRecord<User>;

    /**
     * menu type
     *
     * - "1": directory
     * - "2": menu
     */
    type MenuType = '1' | '2';

    type MenuButton = {
      /**
       * button code
       *
       * it can be used to control the button permission
       */
      code: string;
      /** button description */
      desc: string;
    };

    /**
     * icon type
     *
     * - "1": iconify icon
     * - "2": local icon
     */
    type IconType = '1' | '2';

    type MenuPropsOfRoute = Pick<
      import('vue-router').RouteMeta,
      | 'i18nKey'
      | 'keepAlive'
      | 'constant'
      | 'order'
      | 'href'
      | 'hideInMenu'
      | 'activeMenu'
      | 'multiTab'
      | 'fixedIndexInTab'
      | 'query'
    >;

    type Menu = Common.CommonRecord<{
      id: number;
      /** parent menu id */
      parentId: number;
      /** menu type */
      menuType: MenuType;
      /** menu name */
      menuName: string;
      /** route name */
      routeName: string;
      /** route path */
      path: string;
      /** component */
      component?: string;
      /** iconify icon name or local icon name */
      icon: string;
      /** icon type */
      iconType: IconType;
      /** value for hideInMenu: 0=display 1=hide */
      visible: number;
      /** value for order */
      menuSort: number;
      /** buttons */
      buttons?: MenuButton[] | null;
      buttonJson?: string | null;
      buttonCodeList: string[];
      activeMenu?: import('@elegant-router/types').RouteKey;
      /** children menu */
      children?: Menu[] | null;
    }> &
      MenuPropsOfRoute;

    /** menu list */
    type MenuList = Common.PaginatingQueryRecord<Menu>;

    type MenuTree = {
      id: number;
      menuName: string;
      parentId: number;
      children?: MenuTree[];
    };

    type UserLogSearchParams = {
      endTime: string;
      id?: number;
      startTime: string;
    };

    type UserLogResponse = {
      frequencies?: Frequencies[];
      loginRecords?: SysUserLoginLog[];
      pageView?: number;
      sysUserActionLogs?: SysUserActionLog[];
      uniqueVisitor?: number;
      userFrequencies?: UserFrequency[];
      userLogByDays?: UserLogByDay[];
    };

    type Frequencies = {
      count: number;
      pageName: App.I18n.I18nRouteKey;
    };

    type SysUserLoginLog = {
      id: number;
      loginIp: string;
      timestamp: Date;
      userId: number;
    };

    type SysUserActionLog = {
      actionContent: string;
      methodName: string;
      pageName: string;
      timestamp: Date;
      usageCount: number;
    };

    type UserFrequency = {
      loginCount: number;
      userId: string;
      userName: string;
      userPageCountList: UserPageCount[];
    };

    type UserPageCount = {
      count: number;
      pageName: string;
    };

    type UserLogByDay = {
      date: string;
      pageView: number;
      uniqueVisitor: number;
    };

    type UserPageViewSearchParams = CommonType.RecordNullable<
      Common.CommonSearchParams & {
        id?: number;
        startTime: string;
        endTime: string;
      }
    >;

    type UserPageViewLog = {
      address: string;
      city: string;
      country: string;
      ip: string;
      pageName: string;
      region: string;
      timestamp: Date;
      userName: string;
    };

    type UserPageViewLogResponse = Common.PaginatingQueryRecord<UserPageViewLog>;

    type DashboardCookieParams = CommonType.RecordNullable<{
      shopId: number;
      affiliateCenterCookie: string;
      sellerCenterCookie: string;
    }>;

    type DashboardCookieResponse = {
      affiliateUpdated: boolean;
      sellerUpdated: boolean;
    };

    type DashboardDataParams = CommonType.RecordNullable<{
      shopId: number;
      startDate: string;
      endDate: string;
    }>;

    type ResetPasswordResponse = {
      password: string;
    };
  }

  namespace CreatorResources {
    type FuzzySearchCreatorResponse = {
      creatorList: Pick<Creator, 'id' | 'avatar' | 'nickname'>[];
    };

    type CreatorSearchParams = CommonType.RecordNullable<{
      /** avg_views_range_arr */
      avgViewsRangeArr?: AvgViewsRangeVO[];
      /** category_arr */
      categoryArr?: string[];
      /** follower_age_arr */
      followerAgeArr?: string[];
      /** follow_gender */
      followerGender?: string;
      /** followers_max */
      followersMax?: number;
      /** followers_min */
      followersMin?: number;
      /** follower_range_arr */
      followersRangeArr?: FollowersRangeVO[];
      /** gmv_max */
      gmvMax?: number;
      /** gmv_min */
      gmvMin?: number;
      /** id or name */
      idName?: string;
      /** units_sold_max */
      unitsSoldMax?: number;
      /** units_sold_min */
      unitsSoldMin?: number;
    }> &
      Api.Common.CommonSearchParams;

    /** AvgViewsRangeVO，avg views range param */
    interface AvgViewsRangeVO {
      /** avg_views_max */
      avgViewsMax?: number;
      /** avg_views_min */
      avgViewsMin?: number;
    }

    /** FollowersRangeVO，followers range param */
    interface FollowersRangeVO {
      /** followers_max */
      followersMax?: number;
      /** followers_min */
      followersMin?: number;
    }

    type CreatorList = Common.PaginatingQueryRecord<Creator>;

    enum SourceType {
      TikTok = 0,
      Other = 1
    }

    type Creator = {
      avatar: string;
      avgViews: string;
      categoryJson: string;
      creatorId: number;
      follower: string;
      followerAgeJson: string;
      followerGender: string;
      followerGenderRatio: number;
      gmv: string;
      id: string;
      nickname: string;
      unitsSold: string;
      sourceType: SourceType;
    };

    type CategoryTree = {
      id: string;
      localName: string;
      parentId: string;
      children?: CategoryTree[];
      permissionStatuses: string[];
      leaf: boolean;
    };
  }

  namespace User {
    type ChangePasswordParams = {
      oldPassword: string;
      newPassword: string;
    };

    type Shop = {
      avatar: string;
      avatarLocal: string;
      brand: string;
      createTime: Date;
      id: number;
      market: string;
      shopCode: string;
      /** platform shop id */
      shopId: number;
      shopName: string;
      /** owner */
      userId: number;
      isApiAuthorized: boolean;
    };

    type UserShop = {
      shopId: number;
      shopName: string;
    };

    type UserIndicatorsParams = {
      userIndicators?: string;
      userBrandIndicators?: string;
    };

    type UserShopSearchParams = Common.CommonSearchParams &
      CommonType.RecordNullable<{
        userId: number;
      }>;

    type UserShopListResponse = Common.PaginatingQueryRecord<Shop>;
  }

  namespace CategoryIntelligence {
    type ShopTypeParams = {
      shopType: number;
    };

    type MonthParams = {
      month: string;
    };

    type CategoryParams = {
      category: string;
    };

    type TotalMetrics = {
      activeSkus: number;
      contentVolume: number;
      followersGained: number;
      previousContentVolume: number;
      previousFollowersGained: number;
      previousSalesMob: number;
      previousUnitsSoldGained: number;
      salesMob: number;
      totalContentVolume: number;
      totalFollowers: number;
      totalSales: number;
      totalShops: number;
      totalUnitsSold: number;
      unitsSoldGained: number;
    };

    type ShopMetrics = {
      activeSkus: number;
      category: string;
      contentVolume: number;
      followersGained: number;
      previousActiveSkus: number;
      previousContentVolume: number;
      previousFollowersGained: number;
      previousSalesMob: number;
      previousUnitsSold: number;
      salesMob: number;
      unitsSold: number;
    };

    type ShopRangItem = {
      belongDate: string;
      categroy: string;
      contentVolume: number;
      followersGained: number;
      salesMob: number;
      unitsSold: number;
    };

    type OverviewSearchParams = ShopTypeParams &
      MonthParams & {
        subCategory?: string;
      };

    type OverviewResponse = TotalMetrics & {
      categoryList: ShopMetrics[];
    };

    type ShopMonParams = ShopTypeParams &
      MonthParams & {
        categroy: string;
        thirdCategory?: string;
      };

    type ShopsMonTotalData = {
      activeSkus: number;
      contentVolume: number;
      followersGained: number;
      previousSalesMob: number;
      salesMob: number;
      shopName: string;
      totalFollowers: number;
      unitsSold: number;
    };

    type ShopMonResponse = ShopsMonTotalData[];

    type ProductInfo = {
      avatar: string;
      avatarBak: string;
      brand: string;
      category: string;
      commissionSpending: number;
      directDiscountDepth: string;
      gmv: string;
      productName: string;
      shopName: string;
      rate: string;
      rsp: string;
      salesQuantity: string;
      url: string;
    };

    type TopParams = CommonType.RecordNullable<
      ShopTypeParams &
        Common.CommonSearchParams &
        MonthParams &
        CategoryParams & {
          shopIds: number[];
          thirdCategory: string;
        }
    >;

    type ProductTop10Response = Common.PaginatingQueryRecord<ProductInfo>;

    type ShopOption = {
      shopId: number;
      shopName: string;
      subCategroy: string;
    };

    type ShopsByCategoryResponse = ShopOption[];

    type CreatorInfo = {
      affiliateBrandsJson: string;
      avatar: string;
      category: string;
      creatorId: string;
      followers: string;
      gmv: string;
      gmvPerBuyer: string;
      gpm: string;
      lives: string;
      nickName: string;
      videos: string;
    };

    type ShopCategorySearchParams = Api.CategoryIntelligence.ShopTypeParams &
      Api.CategoryIntelligence.CategoryParams &
      Api.CategoryIntelligence.MonthParams & {
        thirdCategory?: string;
      };

    type CreatorTop10Response = Common.PaginatingQueryRecord<CreatorInfo>;

    type ProductPriceSearchParams = ShopTypeParams &
      MonthParams &
      CategoryParams & {
        thirdCategory?: string;
      };

    type PriceInfo = {
      contentNum: number;
      low: number;
      sales: number;
      shopsNum: number;
      spusNum: number;
      unitsSold: number;
      up: number;
    };

    type ProductPriceResponse = PriceInfo[];

    type CreatorSyncDateResponse = { syncDate: string };

    type IndiactorTrendResponse = {
      contentList: MonthData[];
      followersList: MonthData[];
      salesList: MonthData[];
      soldList: MonthData[];
    };

    type MonthData = {
      belongMonth: string;
      number: number;
    };

    type VideoLiveSearchParams = Common.CommonSearchParams &
      CommonType.RecordNullable<
        {
          contentType: number;
          month: string;
          shopType: number;
        } & CategoryParams & {
            shopIds: number[];
          }
      >;

    type VideoLiveResponse = Common.PaginatingQueryRecord<VideoLiveTopInfo>;

    type VideoLiveTopInfo = {
      adFlag: number;
      avatarLocal: string;
      creatorAvatarLocal: string;
      creatorId: string;
      creatorNickname: string;
      followers: string;
      gmv: string;
      gmvNum: number;
      likes: string;
      publishDate: Date;
      sold: string;
      soldNum: number;
      title: string;
      views: string;
      url: string;
    };

    type CoreDataParams = MonthParams &
      ShopTypeParams & {
        subCategory: string;
      };

    type CoreDataResponse = {
      categoryDataList?: ShopMetrics[];
    };
  }

  namespace Analysis {
    type DiagnosisParam = {
      /** category_arr */
      categoryArr: string[];
      /** follower_age_arr */
      followerAgeArr: string[];
      /** follow_gender */
      followerGender: string;
      /** shop name */
      shopName: string;
      // shop avatar
      shopAvatar: string;
    };

    type DiagnosisParams = CommonType.RecordNullable<DiagnosisParam>[];

    type ReportInfo = {
      /** Request category_arr */
      categoryArr: string[];
      /** category_json */
      categoryJson: string;
      /** List of recommended creators */
      competitorShopCreators: CompetitorInfo[];
      /** Request follow_gender */
      followerGender: string;
      /** Request follower_range_arr */
      followersRangeArr: FollowersRange[];
      /** List of recommended creators */
      recommendedCreators: CreatorInfo[];
      shopAvatar: string;
      /** A list of your store's current creators */
      shopCurrentCreators: CreatorInfo[];
      /** Request shop_name */
      shopName: string;
      grabTaskStatus: TaskStatus;
    };

    type CompetitorInfo = {
      competitorCreators: CreatorInfo[];
      shopName: string;
    };

    type CreatorInfo = {
      /** avatar */
      avatar: string;
      avgViewsNum: number;
      /** category */
      categoryJson: string;
      creatorId: string;
      /** follower age */
      followerAgeJson: string;
      /** follower female ratio */
      followerFemaleRatio: number;
      /** follower male ratio */
      followerMaleRatio: number;
      /** follower num */
      followerNum: number;
      gmvNum: number;
      homepage: string;
      id: number;
      /** nickname */
      nickname: string;
      /** Comprehensive scoring */
      score: number;
      unitsSoldNum: number;
      isRepeat: boolean;
    };

    type FollowersRange = {
      /** followers_max */
      followersMax: number;
      /** followers_min */
      followersMin: number;
    };

    type SysGrabTask = TaskInfo & {
      requestParameters: CreatorScore;
      shopInfo: AhaShop;
    };

    type CreatorScore = {
      /** category_arr */
      categoryArr: string[];
      /** follower_age_arr */
      followerAgeArr: string[];
      /** follow_gender */
      followerGender: string;
      /** shop name */
      shopName: string;
    };

    type AhaShop = {
      /** @aha_shop_type 1=own own 2=competition competition */
      ahaShopType: number;
      avatar: string;
      category: string;
      createTime: Date;
      id: number;
      shopName: string;
      taskId: number;
      updateTime: Date;
    };

    enum TaskStatus {
      NEW = 0, // create but no running
      RUNNING = 10, // create and running
      FAIL = 11, // error but will repeat
      SUCCESS = 20, // successful completion
      SHOP_NOT_FIND = 21, // completed but errors
      CATEGORY_NOT_FIND = 22 // done but errors
    }

    type TaskInfo = {
      createdBy: number;
      /** create_time */
      createTime: Date;
      extraData: string;
      grabTaskMessage: string;
      /** @grab_task_status 0=new new 10=running running 11=fail fail 20=success success */
      grabTaskStatus: TaskStatus;
      /** @task_type 1=aha_creator aha_creator */
      grabTaskType: number;
      id: number;
      shopCategory: string;
      stepIndex: number;
      /** update_time */
      updateTime: Date;
    };

    type ReportListResponse = Common.PaginatingQueryRecord<SysGrabTask>;

    type ShopInfo = {
      ifFind: boolean;
      shopName: string;
      avatar: string;
    };
  }

  namespace Tool {
    type PlatformType = 1 | 2;

    type VideoDownloaderSearchParams = {
      url: string;
      platform: PlatformType;
    };

    type CreateTaskParams = {
      description?: string;
      creatorIdListStr?: string;
      file?: File;
    };

    type CreatorTaskInfo = {
      id: number;
      createTime: Date;
      grabTaskMessage: string;
      grabTaskStatus: number;
      updateTime: Date;
      description: string;
    };

    type CreatorTaskListResponse = Common.PaginatingQueryRecord<CreatorTaskInfo>;
  }

  namespace OperationalData {
    type ClientDataSearchParams = Common.CommonSearchParams &
      CommonType.RecordNullable<{
        year: number;
        month: number;
      }>;

    type MonthlyData = {
      advertising: number;
      advertisingFormatted?: string;
      clients: number;
      clientName: string;
      gmv: number;
      gmvFormatted?: string;
      grossProfit: number;
      grossProfitFormatted?: string;
      income: number;
      incomeFormatted?: string;
      expenditure: number;
      expenditureFormatted?: string;
      orderNum: number;
      affiliates: number;
      id: number;
      livestreams: number;
      month: number;
      videos: number;
      year: number;
    };

    type DashboardResponse = Common.PaginatingQueryRecord<MonthlyData>;

    type MonthlyDataCreateParams = Omit<MonthlyData, 'id' | 'gmvFormatted' | 'grossProfitFormatted'>;
    type MonthlyDataUpdateParams = Omit<MonthlyData, 'gmvFormatted' | 'grossProfitFormatted'>;
  }

  namespace DashboardJazwares {
    type BaseSearchParams = CommonType.RecordNullable<{
      brand: string;
      market: string;
      shopIdsArr: number[];
      startDateStr: string;
      endDateStr: string;
    }>;

    type DashboardTopSearchParams = CommonType.RecordNullable<{
      keyWord: string;
      topNum: number;
      productBrands: string;
    }> &
      BaseSearchParams;

    type SubBrandMetric = {
      subBrand: string;
      gmv: number;
      liveImpressions: number;
      productCardImpressions: number;
      shopTabListingImpressions: number;
      videoImpressions: number;
      unitsSold: number;
    };

    type SubBrandMetricResponse = SubBrandMetric[];

    type DashboardShop = {
      activeProductNum: number;
      avatar: string;
      gmv: number;
      liveGmv: number;
      orderCnt: number;
      productCardGmv: number;
      shopCode: string;
      shopId: number;
      shopName: string;
      shopTabGmv: number;
      topSellingProductImgList: TopSellingProduct[];
      unitsSold: number;
      videoGmv: number;
    };

    type TopSellingProduct = {
      productId: string;
      productAvatarLocal: string;
    };
  }

  namespace VideoManage {
    type VideoCard = {
      client: number;
      clientName: string;
      createTime: Date;
      creatorName: string;
      flatFee: string;
      id: number;
      localVideo: string;
      notes: string;
      payment: string;
      productAvatarLocal: string;
      productId: number;
      productName: string;
      status: number;
      updateTime: Date;
    };

    type VideoApprovalListSearchParams = CommonType.RecordNullable<{
      client: number;
      creatorName: string;
      product: string;
    }> & {
      /** approval status 0:Pending Review<br> 1:Approved<br> 2:Rejected<br> */
      status: number;
    };

    type VideoApprovalListResponse = {
      listVideoApproval: VideoCard[];
      /** month format YYYY-MM */
      month: string;
    };

    type CreateVideoParams = CommonType.RecordNullable<{
      client: number;
      creatorName: string;
      flatFee: number;
      localVideo: string;
      payment: string;
      productId: number;
    }>;

    type VideoInfo = {
      client: number;
      clientName: string;
      createTime: Date;
      creatorName: string;
      flatFee: number;
      id: number;
      localVideo: string;
      notes: string;
      payment: string;
      productAvatarLocal: string;
      productId: number;
      productName: string;
      /** approval status 0:Pending Review<br> 1:Approved<br> 2:Rejected<br> */
      status: number;
      updateTime: Date;
      submitter: string;
    };

    type ApprovalUserOption = {
      shopName: string;
      userId: number;
      shopId: number;
    };

    type ShopProductOption = {
      productId: string;
      productName: string;
      productAvatarLocal: string;
    };

    type ShopProduct = {
      createTime: Date;
      id: number;
      productAvatar: string;
      productAvatarLocal: string;
      productId: string;
      productName: string;
      shopId: number;
      shopName: string;
      status: number;
      updateTime: Date;
    };

    type ShopProductListSearchParams = Common.CommonSearchParams &
      CommonType.RecordNullable<
        Pick<ShopProduct, 'shopId' | 'status'> & {
          product: string;
        }
      >;

    type UpdateShopProductStatusSearchParams = Pick<ShopProduct, 'id' | 'status'>;
    type ShopProductListResponse = Common.PaginatingQueryRecord<ShopProduct>;

    type ShopOption = {
      shopId: number;
      shopName: string;
    };

    type RejectVideoParams = Pick<VideoInfo, 'id' | 'notes'>;
  }

  namespace ReturnRefund {
    type OverviewSearchParams = {
      endTimeStamp: number;
      startTimeStamp: number;
      shopId: number;
    };

    type OverviewResponse = {
      affiliateTotalOrderNum: number;
      exchangeNum: number;
      refundAndExchangeNum: number;
      refundNum: number;
      returnAmount: number;
      returnAndRefundNum: number;
      returnProduct: ReturnProduct[];
      returnReason: ReturnReason[];
      returnOnlyNum: number;
      returnTotalNum: number;
      orderSourceList?: OrderSource[];
    };

    type ReturnProductDetail = {
      orderSource: string;
      productName: string;
      ratio: number;
      returnOrderNum: number;
      productAvatarLocal: string;
    };

    type OrderSource = {
      creatorTotalOrderNum: number;
      orderIds: string;
      orderIdsList: string[];
      orderSource: string;
      returnAffiliateOrderRatio: number;
      returnCreatorOrderRatio: number;
      returnOrderNum: number;
      returnPrdouctList: ReturnProductDetail[];
    };

    type ReturnProduct = {
      productAvatarLocal: string;
      productName: string;
      productOrderNum: number;
      productReturnNum: number;
      /** 该商品退单占该商品总单数比例 */
      productReturnToTotalOrdersRatio: number;
      /** 该商品退单占总退单比例 */
      productReturnToTotalReturnRatio: number;
    };

    type ReturnReason = {
      num: number;
      reason: string;
    };

    type ReturnOrderDetailSearchParams = Common.CommonSearchParams &
      CommonType.RecordNullable<
        OverviewSearchParams & {
          orderId?: string;
        }
      >;

    type ReturnOrderDetail = {
      /** 0: non-closed 1:Request closed */
      closedFlag: number;
      formatReturnPrice: string;
      formatTotalAmountPaid: string;
      id: number;
      orderId: string;
      orderStatus: string;
      productList: Product[];
      reason: string;
      reasonNote: string;
      requestStatus: string;
      returnPrice: number;
      reverseId: string;
      reverseStatus: string;
      reverseTime: number;
      /** reverseType 2:Refund only 3:Return and refund 5:Exchange only speculate: 1:Return only 4:Refund and exchange */
      reverseType?: number;
      shopId: number;
    };

    type Product = {
      paidUnitPrice: string;
      productAvatarLocal: string;
      productName: string;
      quantity: number;
      returnUnitPrice: string;
      sellerSku: string;
      skuName: string;
    };

    type ReturnOrderDetailResponse = Common.PaginatingQueryRecord<ReturnOrderDetail>;
  }

  namespace CreatorManage {
    type UploadCreatorExcelSearchParams = CommonType.RecordNullable<{
      client: number;
      shopId: number;
      tags: string;
      file: File;
    }>;

    type DuplicateCreatorList = CreatorApprovalInfo[];

    type CreatorApprovalListSearchParams = Common.CommonSearchParams &
      CommonType.RecordNullable<{
        client: number;
        creatorName: string;
        endTime: string;
        // yyyy-MM-dd HH:mm:ss
        startTime: string;
        status: number;
        tags: string;
      }>;

    type CreatorApprovalListByPageResponse = Common.PaginatingQueryRecord<CreatorApprovalInfo>;

    type CreatorApprovalListResponse = {
      listCreatorApproval: CreatorApprovalInfo[];
      month: string;
    };
    type CreatorApprovalInfo = {
      client: number;
      clientName: string;
      commission: string;
      createBy: number;
      createTime: Date;
      creator: string;
      creatorAvatarLocal: string;
      creatorName: string;
      email: string;
      followers: string;
      id: number;
      inviteFlag: number;
      mainContact: string;
      // 1:email  2:phone  3:whatsapp  4:message  5:otherContact
      mainContactType: number;
      message: string;
      monthlyRev: string;
      notes: string;
      otherContact: string;
      phone: string;
      productId: string;
      productList: FirstProduct[];
      shoppableVideo: string;
      slottingFee: string;
      /** approval status 0:Pending Review<br> 1:Approved<br> 2:Rejected<br> */
      status: number;
      submitter: string;
      tags: string;
      ttAccountLink: string;
      updateTime: string;
      videoGpm: string;
      whatsapp: string;
    };

    type FirstProduct = {
      productAvatar: string;
      productAvatarLocal: string;
      productId: string;
      productName: string;
    };

    type CreatorProductVideoListSearchParams = Common.CommonSearchParams & {
      client: number;
    } & CommonType.RecordNullable<{
        creatorName: string;
        product: string;
        releaseTimeEnd: number;
        releaseTimeStart: number;
        shopId: number;
        status: number;
        adCodeFlag: number;
      }>;

    type CreatorProductVideo = {
      creatorAvatarLocal: string;
      creatorName: string;
      productList: CreatorSampleProduct[];
      shopName: string;
    };

    type CreatorSampleProduct = {
      applyId: number;
      commissionRate: number;
      /**
       * 10:To Review 20:Ready to Ship 30:Shipped 40:Content pending 51:Rejected 52:Expired 53:Unfulfilled 57:Canceled
       * 100:Completed
       */
      currStatus: number;
      productName: string;
      /**
       * 10:Invite for Sample 20:Shipping samples 40:Video Pending 51:Rejected 52:Expired 53:Unfulfilled 57:Canceled
       * 100:Video Post
       */
      productStatus: number;
      skuAvatar: string;
      videoList: CreatorSampleVideo[];
    };

    type CreatorSampleVideo = {
      adCode: string;
      clientVisible: number;
      contentId: string;
      /**
       * 1: live 2:video
       */
      contentType: number;
      finishTimestamp: number;
      note: string;
      releaseTimestamp: number;
      videoDesc: string;
    };

    type CreatorProductVideoResponse = Common.PaginatingQueryRecord<CreatorProductVideo>;

    type UpdateCommonParams = {
      contentId: string;
    };
  }

  namespace ShopAudit {
    type ShopAuditInfo = {
      accountAvatar: string;
      accountAvatarLocal: string;
      accountFollowers: string;
      accountId: string;
      accountName: string;
      accountTotalGmv: string;
      avatar: string;
      avatarLocal: string;
      category: string;
      saShopTotalPOList: SaShopTotalPO[];
      shopName: string;
      grabTaskStatus: number;
      totalGmv: number;
      updateTime: string;
    };

    type SaShopTotalPO = {
      affiliateGmvPercent: number;
      avatar: string;
      avatarLocal: string;
      category: string;
      /** create_time */
      createTime: Date;
      creators: number;
      gmv: number;
      id: number;
      lives: number;
      shopName: string;
      unitsSold: number;
      /** update_time */
      updateTime: Date;
      url: string;
      videos: number;
    };

    type ShopAuditScore = {
      scoreList: ScoreList[];
      totalScore: number;
    };

    type ScoreList = {
      rating: number;
      report: string;
      score: number;
      title: string;
    };

    type ShopAuditReportResponse = Common.PaginatingQueryRecord<ShopAuditReport>;

    type ShopAuditReport = {
      avatar: string;
      createdBy: number;
      createTime: Date;
      delFlag: number;
      extraData: string;
      grabTaskMessage: string;
      /** @grab_task_status 0=new new 10=running running 11=fail fail 20=success success */
      grabTaskStatus: number;
      /** @task_type 1=aha_creator aha_creator */
      grabTaskType: number;
      id: number;
      scoreData: string;
      shopCategory: string;
      shopName: string;
      stepIndex: number;
      totalScore: number;
      updateTime: Date;
    };
  }

  namespace CategoryLeaders {
    type TopGmvShop = {
      belongMonth: string;
      category: string;
      createTime: Date;
      id: number;
      shopAccountData: LeaderShopAccount;
      shopAvatarLocal: string;
      shopLivesData: LeaderShopContent[];
      shopMonthData: LeaderShopMonth;
      shopName: string;
      shopOverviewData: LeaderShopOverview;
      shopProductData: LeaderShopProduct;
      shopUrl: string;
      shopVideosData: LeaderShopContent[];
      sort: number;
      updateTime: Date;
      totalCreatorNum: number;
    };

    type LeaderShopAccount = {
      accountAvatarLocal: string;
      accountId: string;
      accountName: string;
      followers: string;
    };

    type LeaderShopContent = {
      accountAvatar: string;
      accountAvatarLocal: string;
      accountId: string;
      accountName: string;
      cover: string;
      coverLocal: string;
      gmv: string;
      postTime: string;
      sold: string;
      title: string;
      viewers: string;
      homePage: string;
    };

    type LeaderShopMonth = {
      affiliates: string;
      affiliatesGrowth: string;
      gmv: string;
      gmvGrowth: string;
      lives: string;
      livesGrowth: string;
      price: string;
      priceGrowth: string;
      sold: string;
      soldGrowth: string;
      videos: string;
      videosGrowth: string;
    };

    type LeaderShopOverview = {
      sshop?: boolean;
      averagePrice: string;
      lastUpdateDate: string;
      launchDate: string;
      positiveRatingRate: string;
      qualityScore: string;
      rate: string;
      respondsWithin24h: string;
      shipsWithin48h: string;
      totalAffiliates: string;
      totalGmv: string;
      totalLives: string;
      totalProducts: string;
      totalSold: string;
      totalVideos: string;
      url: string;
    };

    type LeaderShopProduct = {
      commissionRate: string;
      homePage: string;
      launchDate: string;
      monthlyGmv: string;
      monthlyGmvGrowth: string;
      monthlySold: string;
      monthlySoldGrowth: string;
      price: string;
      productAvatarLocal: string;
      productName: string;
      totalGmv: string;
      totalSold: string;
      url: string;
    };

    type TopGmvShopListResponse = {
      belongMonth: string;
      category: string;
      createTime: Date;
      id: number;
      shopAvatarLocal: string;
      shopMonthData: LeaderShopMonth;
      shopName: string;
      shopOverviewData: LeaderShopOverview;
      shopProductData: LeaderShopProduct;
      sort: number;
      updateTime: Date;
    };

    type CreatorWithGmvSearchParams = Common.CommonSearchParams &
      CommonType.RecordNullable<{
        shopId: number;
      }>;

    type CreatorWithGmvResponse = Common.PaginatingQueryRecord<CreatorWithGmv>;

    type CreatorWithGmv = {
      creatorId: string;
      creatorNickname: string;
      followers: string;
      followersNum: number;
      gmv: string;
      gmvNum: number;
      gpmLive: string;
      gpmVideo: string;
      productNum: number;
      sold: string;
      soldNum: number;
      url: string;
    };
  }

  namespace CreatorNetwork {
    type AddEmailTemplateParams = {
      name: string;
      title: string;
      content: string;
    };

    type GetEmailTemplateListParams = Common.CommonSearchParams;

    type GetEmailTemplateListResponse = Common.PaginatingQueryRecord<EmailTemplate>;

    type EmailTemplate = {
      content: string;
      createdBy: number;
      createTime: Date;
      id: number;
      name: string;
      title: string;
      updateTime: Date;
    };

    type EmailTaskConfigResponse = {
      holidaySendingFlag: boolean;
      sendInterval: number | null;
      periodEnd: string | null;
      periodStart: string | null;
      singleSendLimit: number | null;
    };

    type UploadCreatorDataByExcelParams = {
      file: File;
    };

    type UploadCreatorDataByExcelResponse = {
      failList: CreatorInfo[];
      successList: CreatorInfo[];
    };

    type CreatorInfo = {
      creatorId: string;
      creatorName: string;
      email: string;
    };

    type StartTaskParams = {
      emailList: CreatorInfo[];
      taskName: string | null;
      senderEmailId: number | null;
      templateId: number | null;
    };

    type StartTestEmailParams = StartTaskParams & {
      testEmail: string;
    };

    type GetEmailTemplateOptions = {
      content: string;
      createdBy: number;
      createTime: Date;
      id: number;
      name: string;
      title: string;
      updateTime: Date;
    };

    type GetTaskHistoryListParams = CommonType.RecordNullable<
      Common.CommonSearchParams & {
        /** 0:email 1:message */
        contactType: number;
        endTime: string;
        startTime: string;
        /** 0: sending 1:completed -1:paused */
        status: number;
        taskName: string;
      }
    >;

    type GetTaskHistoryListResponse = Common.PaginatingQueryRecord<TaskHistory>;

    type TaskHistory = {
      /** 0:email 1:message */
      contactType: number;
      content: string;
      createdBy: number;
      createTime: Date;
      failCount: number;
      /** attachment */
      filePath: string;
      id: number;
      readCount: number;
      readRatio: number;
      sendingCount: number;
      sentCount: number;
      shopId: number;
      successCount: number;
      taskName: string;
      title: string;
      totalCount: number;
      unreadCount: number;
      updateTime: Date;
      status: number;
      senderEmail: string;
    };

    type EmailAccount = {
      createTime: Date;
      email: string;
      /** 1: gmail 2: message */
      emailType: number;
      invalidGrant: boolean;
      id: number;
    };

    type TaskDetail = {
      /** 0:email 1:message */
      contactType?: number;
      content?: string;
      createdBy?: number;
      createTime?: Date;
      failCount?: number;
      /** attachment */
      filePath?: string;
      id?: number;
      readCount?: number;
      readRatio?: number;
      sendingCount?: number;
      sentCount?: number;
      shopId?: number;
      /** 0: sending 1:completed -1:paused */
      status?: number;
      successCount?: number;
      taskName?: string;
      title?: string;
      totalCount?: number;
      unreadCount?: number;
      updateTime?: Date;
    };

    type EmailTaskSenderListParams = CommonType.RecordNullable<
      Common.CommonSearchParams & {
        taskId: number;
        status: number;
      }
    >;

    type EmailTaskSenderListResponse = Common.PaginatingQueryRecord<EmailTaskSender>;

    type EmailTaskSender = {
      /** 0: Waiting for reply 1: Successful alliance 9: Rejected */
      communicationProgress?: number;
      content?: string;
      createdBy?: number;
      createTime?: Date;
      creatorId?: string;
      creatorName?: string;
      email?: string;
      errorMessage?: string;
      id?: number;
      lastReadIp?: string;
      messageId?: string;
      remark?: string;
      /** -1: Sending failed 0:sending 1: Sent 2: Read */
      status?: number;
      taskId?: number;
      title?: string;
      updateTime?: Date;
    };

    type ProductParams = Common.CommonSearchParams &
      CommonType.RecordNullable<{
        shopId: number;
        searchParams: {
          searchKey: number;
          searchValue: string;
        }[];
      }>;

    type ProductResponse = Common.PaginatingQueryRecord<{ [property: string]: any }>;

    type RunTaskByTikTokParams = {
      content: string;
      creatorList: string[];
      productIds: string[];
      shopId: number | null;
      taskName: string;
      title: string;
      products: any[];
      messageModule: number;
    };

    type TaskHistoryByTikTokParams = CommonType.RecordNullable<
      Common.CommonSearchParams & {
        /** 0:email 1:message */
        contactType: number;
        endTime: string;
        startTime: string;
        /** 0: sending 1:completed -1:paused */
        status: number;
        taskName: string;
        shopIds: number[];
      }
    >;

    type TaskHistoryByTikTokResponse = Common.PaginatingQueryRecord<TaskHistoryByTikTok>;

    type TaskHistoryByTikTok = {
      /** 0: message */
      contactType: number;
      content: string;
      createdBy: number;
      createTime: string;
      failCount: number;
      id: number;
      productId: string;
      shopId: number;
      /** -1:paused 0:new 1: sending 2:completed */
      status: number;
      successCount: number;
      taskName: string;
      title: string;
      totalCount: number;
      updateTime: string;
      messageModule: number;
    };

    type TaskDetailByTikTok = {
      /** 0: message */
      contactType: number;
      content: string;
      createdBy: number;
      createTime: Date;
      failCount: number;
      id: number;
      productId: string;
      products: { [key: string]: any }[];
      productsInfo: string;
      shopId: number;
      /** -1:paused 0:new 1: sending 2:completed */
      status: number;
      successCount: number;
      taskName: string;
      title: string;
      totalCount: number;
      updateTime: Date;
      messageModule: number;
    };

    type TaskInfoCreatorsByTikTokParams = Common.CommonSearchParams &
      CommonType.RecordNullable<{
        taskId: number;
        communicationProgress: number;
        creatorId: number;
        status: number;
      }>;

    type TaskInfoCreatorsByTikTokResponse = Common.PaginatingQueryRecord<TaskInfoCreatorsByTikTok>;

    type TaskInfoCreatorsByTikTok = {
      categoryJson: string;
      content: string;
      createdBy: number;
      createTime: Date;
      creatorAvatar: string;
      creatorId: string;
      creatorName: string;
      creatorOecId: string;
      errorMessage: string;
      followerNum: number;
      id: number;
      nickname: string;
      productId: string;
      remark: string;
      status: number;
      taskId: number;
      title: string;
      updateTime: Date;
    };

    type FindCreatorParams = Common.CommonSearchParams &
      CommonType.RecordNullable<{
        averageViewsPerLiveMin: number;
        averageViewsPerVideoMin: number;
        avgCommissionRateMax: number;
        contentType: string;
        creatorCategory: string[];
        engagementRateMin: number;
        fastGrowing: boolean;
        followerAge: string[];
        followerGender: string;
        followers: string;
        gmv: string;
        gmvLevel: string[];
        selectedTag: string[];
        unitsSold: string;
        idName: string;
      }>;

    type FindCreatorResponse = Common.PaginatingQueryRecord<FindCreator>;

    type FindCreator = {
      avatarLocal: string;
      avgLiveViews: string;
      avgVideoViews: string;
      categoryJson: string;
      contentType: string;
      creatorId: number;
      engagementRate: number;
      follower: string;
      followerAgeJson: string;
      followerGender: string;
      gmv: string;
      gmvNum: number;
      id: string;
      creatorOecuid: string;
      nickname: string;
      tagArr: number[];
      unitsSold: string;
    };

    type FindCreatorIdParams = {
      limitStart: number;
      limitEnd: number;
      averageViewsPerLiveMin: number;
      averageViewsPerVideoMin: number;
      avgCommissionRateMax: number;
      contentType: string;
      creatorCategory: string[];
      engagementRateMin: number;
      fastGrowing: boolean;
      followerAge: string[];
      followerGender: string;
      followers: string;
      gmv: string;
      gmvLevel: string;
      selectedTag: string[];
      unitsSold: string;
      idName: string;
    };

    type CreateMessageTemplateParams = {
      name: string;
      title: string;
      content: string;
    };

    type MessageTemplateListByPageResponse = Common.PaginatingQueryRecord<MessageTemplate>;

    type MessageTemplate = {
      content: string;
      createdBy: number;
      createTime: Date;
      delFlag: number;
      id: number;
      name: string;
      title: string;
      updateTime: Date;
    };
  }

  namespace Dictionary {
    type GetDictionaryParams = {
      type: string;
    };

    type DictionaryItem<T extends string | number = number> = {
      code: T;
      description: any;
      isEnabled: number;
      name: string;
    };

    type Dictionary = {
      code: string;
      createTime: Date;
      delFlag: number;
      description: string;
      id: number;
      isEnabled: number;
      name: string;
      sort: number;
      type: string;
      updateTime: Date;
    };

    type DictionaryListParams = Common.CommonSearchParams &
      CommonType.RecordNullable<{
        type: string;
        isEnabled: number;
      }>;

    type DictionaryListResponse = Common.PaginatingQueryRecord<Dictionary>;

    type AddOrUpdateDictionaryParams = Pick<
      Dictionary,
      'type' | 'code' | 'name' | 'description' | 'sort' | 'isEnabled'
    >;

    type DictionaryType = {
      type: string;
      typeDesc: string;
    };

    type GetDictionaryTypeResponse = DictionaryType[];
  }

  namespace CreatorCenter {
    type RunningTask = {
      createTime: Date;
      fail: number;
      fileName: string;
      grabTaskStatus: number;
      pending: number;
      success: number;
      taskId: number;
      total: number;
    };

    type CreatorCenterTaskListResponse = Common.PaginatingQueryRecord<CreatorCenterTask>;

    type CreatorCenterTask = {
      createTime: Date;
      fail: number;
      fileName: string;
      grabTaskStatus: number;
      pending: number;
      success: number;
      taskId: number;
      total: number;
    };

    type CreatorCenterSearchParams = CommonType.RecordNullable<
      Common.CommonSearchParams & {
        avgViewsMax: number;
        avgViewsMin: number;
        client: string;
        creatorCategory: string;
        creatorId: string;
        evaluatedType: number;
        followerAgeArr: string[];
        followerGender: string;
        followersMax: number;
        followersMin: number;
        gender: string;
        gmvMax: number;
        gmvMin: number;
        language: string;
        productCategory: string;
        race: string;
      }
    >;

    type CreatorCenterListResponse = Common.PaginatingQueryRecord<CreatorCenter>;

    type CreatorCenter = {
      aicCreatorStatus: number;
      avatarLocal: string;
      avgViews: string;
      category: string;
      client: string;
      commission: string;
      creatorId: string;
      email: string;
      estPostRate: string;
      evaluatedType: number;
      follower: string;
      followerAgeJson: string;
      followerFemaleRatio: number;
      followerMaleRatio: number;
      gender: string;
      gmv: string;
      gpm: string;
      id: number;
      language: string;
      nickname: string;
      phone: string;
      productCategoryJson: string;
      race: string;
      shoppableVideo: string;
      slottingFee: string;
      sold: string;
    };

    type CreatorUpdateEvaluatedParams = {
      id: number;
      evaluatedType: number;
    };
  }
  namespace TikSageDashboard {
    type ShopCommissionSearchParams = Common.CommonSearchParams &
      CommonType.RecordNullable<{
        shopName: string;
      }>;

    type DateParams = {
      startDateStr: string;
      endDateStr: string;
      perviousStartDateStr?: string;
      perviousEndDateStr?: string;
    };

    type OverviewDataResponse = {
      currentData: PartnerOverviewData;
      previousData: PartnerOverviewData;
      sellerCount: number;
      sellerGmvCount: number;
    };

    type PartnerOverviewData = {
      estCommission: number;
      monthlyEstCommission: number;
      gmvAffiliate: number;
      gmvLive: number;
      gmvNoAffiliate: number;
      gmvProductCard: number;
      gmvShopTab: number;
      gmvTotal: number;
      gmvVideo: number;
      unitSales: number;
    };

    type ShopCommissionListResponse = Common.PaginatingQueryRecord<ShopCommission>;

    type ShopCommission = {
      avatar: string;
      avatarLocal: string;
      commissions: CommissionTier[];
      dashboardRoute: string;
      dueDateOffset: number;
      invoiceCount: number;
      isEnabled: number;
      oecSellerId: string;
      shopCode: string;
      shopId: number;
      shopName: string;
      introductoryFee: IntroductoryFee;
    };

    type ShopCommissionUpdateParams = {
      commissions: CommissionTier[];
      shopId: number;
      dueDateOffset: number;
      introductoryFee: IntroductoryFee;
    };

    type IntroductoryFee = {
      amount: number;
      endDate: string | null;
      startDate: string | null;
    };

    type CommissionTier = {
      commissionType: number;
      commissionValue: number;
      maxGmv: number | null;
      minGmv: number;
    };

    type TopListParams = Common.CommonSearchParams &
      CommonType.RecordNullable<
        DateParams & {
          shopName: string;
          reverseFlag: boolean;
          sortField: string;
        }
      >;

    type ProductTopListResponse = Common.PaginatingQueryRecord<Product>;

    type Product = {
      gmv: number;
      productAvatarLocal: string;
      productId: string;
      productName: string;
    };

    type SellerListResponse = Common.PaginatingQueryRecord<Seller>;

    type Seller = {
      commissionJson: string;
      dashboardRoute: string;
      days: number;
      estCommission: number;
      gmvAffiliate: number;
      gmvLive: number;
      gmvNoAffiliate: number;
      gmvProductCard: number;
      gmvRate: number;
      gmvShopTab: number;
      gmvTotal: number;
      gmvVideo: number;
      isEnabled: number;
      monthlyEstCommission: number;
      perviousGmvAffiliate: number;
      perviousGmvLive: number;
      perviousGmvProductCard: number;
      perviousGmvTotal: number;
      perviousGmvVideo: number;
      perviousUnitSales: number;
      shopAvatarLocal: string;
      shopId: number;
      shopName: string;
      shopOverviewDailyData: ShopOverviewDailyData[];
      unitSales: number;
    };

    type ShopOverviewDailyData = {
      belongDate: string;
      gmvAffiliate: number;
      gmvLive: number;
      gmvNoAffiliate: number;
      gmvProductCard: number;
      gmvShopTab: number;
      gmvTotal: number;
      gmvVideo: number;
    };

    type MonthlyDataResponse = MonthlyData[];

    type MonthlyData = {
      estCommission?: number;
      gmv?: number;
      /** month format YYYY-MM */
      month?: string;
    };

    type GmvTrendResponse = GmvTrend[];

    type GmvTrend = {
      belongDate: string;
      gmvAffiliate: number;
      gmvLive: number;
      gmvNoAffiliate: number;
      gmvProductCard: number;
      gmvShopTab: number;
      gmvTotal: number;
      gmvVideo: number;
    };

    type DateRangeResponse = {
      endDateStr: string;
      startDateStr: string;
      updateTime: string;
    };

    type LastMonthEstCommissionResponse = {
      estCommission: number;
      gmv: number;
      /** month format YYYY-MM */
      month: string;
      dueDate: string;
    };

    type SellersContributionResponse = {
      shopCommissionPie: ShopCommissionPie[];
      shopGmvPie: ShopGmvPie[];
    };

    type ShopCommissionPie = {
      commission: number;
      shopName: string;
    };

    type ShopGmvPie = {
      gmv: number;
      shopName: string;
    };
  }

  namespace Invoice {
    type InvoiceHistoryParams = Common.CommonSearchParams &
      CommonType.RecordNullable<{
        shopId: number;
      }>;
    type InvoiceHistoryResponse = Common.PaginatingQueryRecord<InvoiceHistory>;
    type InvoiceHistory = {
      amountPaid: number;
      clientName: string;
      createTime: string;
      deprecate: boolean;
      dueDate: string;
      id: number;
      invoiceNo: number;
      invoiceNoStr: string;
      issueDate: string;
      month: string;
      paymentAmount: number | null;
      paymentDate: string | null;
      paymentFlag: number;
      shopId: number;
      tax: number;
      total: number;
      updateTime: string;
    };

    type InvoiceCreator = {
      amountPaid: number;
      clientName: string;
      dueDate: string;
      invoiceDetailList: InvoiceItem[];
      invoiceNo: number;
      issueDate: string;
      shopId: number;
      tax: number;
      total: number;
      [property: string]: any;
    };

    type InvoiceItem = {
      itemDesc: string;
      itemName: string;
      qty: number;
      rate: number;
      sort: number;
      total: number;
    };

    type Contactor = {
      email: string;
      name: string;
    };

    type ContactorCreateParams = Contactor & {
      isDefault: boolean;
      shopId: number;
    };

    type defaultInvoiceInfo = {
      defaultClientName: string;
      invoiceNo: number;
      invoiceNoStr: string;
    };
  }

  namespace Ads {
    type AdsMetricsParams = {
      startDateStr: string;
      endDateStr: string;
      shopId: number;
    };

    type AdsMetricsResponse = {
      clicks: number;
      impressions: number;
      onsiteShopping: number;
      showAds: boolean;
      spend: number;
    };

    type AdsMetricsParamsByBrand = AdsMetricsParams & {
      brandList: string[];
    };
  }

  namespace Tag {
    type Tag = {
      id: number;
      name: string;
      color: string;
      createTime?: Date;
      updateTime?: Date;
    };

    type CreateTagParams = {
      name: string;
      color: string;
    };

    type SetTagByCreatorParams = {
      creatorId: number;
      tagIds: number[];
    };

    type BatchSetTagByCreatorParams = {
      creatorIds: number[];
      tagIds: number[];
    };
  }

  namespace CreatorPerformance {
    type CreatorPerformanceSearchParams = {
      shopIdsArr: number[];
      brandList?: string[] | null;
      startDateStr: string;
      endDateStr: string;
    };

    type CreatorPerformanceResponse = {
      creatorLevelDTOS: CreatorLevel[];
      creatorPerformanceTotalData: CreatorPerformance;
      currentRangeData: CreatorPerformanceDaily[];
    };

    type CreatorLevel = {
      level: string;
      creatorCount: number;
      creatorCountRatio: number;
      gmv: number;
      gmvRatio: number;
      gmvCreatorCount: number;
    };

    type CreatorPerformance = {
      creatorOutreached: number;
      freeSample: number;
      videoPost: number;
    };

    type CreatorPerformanceDaily = CreatorPerformance & {
      belongDate: Date;
    };

    type CreatorPerformanceMonthlyResponse = CreatorPerformanceMonthly[];

    type CreatorPerformanceMonthly = {
      creatorOutreached: number;
      freeSample: number;
      videoPost: number;
      yearMonth: string;
    };

    type VideoData = {
      data: ShopsVideo[];
      endDate: string;
      startDate: string;
    };

    type ShopsVideo = {
      ad: boolean;
      adsGmv: number;
      allGmv: number;
      avatarLocal: string;
      creatorGmv: number;
      creatorHandle: string;
      creatorId: string;
      follower: string;
      gmvTotal: number;
      historyPerformance: VideoHistory[];
      level: string;
      nickname: string;
      publishTime: number;
      videoAvatarLocal: string;
      videoId: string;
      videoName: string;
    };

    type VideoHistory = {
      belongDate: string;
      gmv: number;
      engagement: number;
      views: number;
      adsGmv: number;
      orderCnt: number;
      adsOrderCnt: number;
    };

    type LastDayResponse = {
      startDateStr: string;
      endDateStr: string;
    };

    type ProductsAnalysisSearchParams = CreatorPerformanceSearchParams & {
      brand?: string;
      brandList?: string[] | null;
      period?: string;
    };

    type ProductsAnalysisResponse = ProductAnalysisData[];

    type ProductAnalysisData = {
      brand: string;
      goal?: string;
      productAvatarLocal: string;
      productId: string;
      productName: string;
      productShortName: string;
      quantity: number;
      shopName: string;
      targetCount: number;
    };

    type ProductTrackingSearchParams = CreatorPerformanceSearchParams & {
      productId?: string;
    };

    type ProductTrackingResponse = ProductTrackingData[];

    type ProductTrackingData = {
      creatorAvatarLocal: string;
      creatorId: string;
      creatorOecId: string;
      followers: number;
      gmv: number;
      gpm: number;
      itemsSold: number;
      orderTime: string;
      orderTimestamp: number;
      postTime: string;
      postTimestamp: number;
      productAvatarLocal: string;
      productId: string;
      productName: string;
      salesVolume: number;
      status: string;
      ttAccountLink: string;
      videoId: string;
      videos: string;
      videoName: string;
      videoAvatarLocal: string;
    };

    type ProductGoalParams = {
      goal: string;
      period: string;
      productId: string;
      shopId: number;
    };

    type ChangeShortNameParams = {
      productId: string;
      productShortName: string;
    };
  }

  namespace CreatorOutreachInvite {
    type InviteProductsSearchParams = Common.CommonSearchParams &
      CommonType.RecordNullable<{
        shopId: number;
        searchParams: {
          searchKey: number;
          searchValue: string;
        }[];
      }>;

    type InviteProductsResponse = Common.PaginatingQueryRecord<{ [property: string]: any }>;

    type ValidateRepeatProductParams = {
      productIds: string[];
      creatorIds: number[];
      shopId: number | null;
    };

    type ValidateRepeatProductResponse = {
      [key: string]: any;
    };

    type CreateInviteTaskParams = {
      shopId: number | null;
      creatorIdList: number[];
      invitationName: string;
      validUntil?: string | null;
      email: string;
      countryCode?: string;
      phone?: string;
      content?: string;
      contentType?: number | null;
      productIdCommissionList: ProductIdCommission[];
      productList: any[];
      freeSampleAutoReview?: number;
      freeSamples?: number;
    };

    type ProductIdCommission = {
      adsCommission: number;
      commission: number;
      isAdsCommission: number;
      productId: string;
    };

    type InviteHistorySearchParams = Common.CommonSearchParams &
      CommonType.RecordNullable<{
        contactType: number;
        endTime: string;
        freeSamples: number;
        shopIds: number[];
        startTime: string;
        status: number;
        taskName: string;
      }>;

    type InviteHistoryResponse = Common.PaginatingQueryRecord<InviteHistory>;

    type InviteHistory = {
      content: string;
      createdBy: number;
      createTime: string;
      failCount: number;
      id: number;
      invitationName: string;
      shopId: number;
      status: number;
      successCount: number;
      totalCount: number;
      productCount: number;
      updateTime: string;
    };

    type InviteTaskDetail = {
      contactType: number;
      content: string;
      countryCode: string;
      createdBy: number;
      createTime: Date;
      email: string;
      failCount: number;
      freeSampleAutoReview: number;
      freeSamples: number;
      id: number;
      imageUri: string;
      invitationName: string;
      phone: string;
      productIdCommission: string;
      productsInfo: string;
      shopId: number;
      status: number;
      successCount: number;
      totalCount: number;
      updateTime: Date;
      validUntil: Date;
    };

    type InviteTaskCreatorListParams = Common.CommonSearchParams &
      CommonType.RecordNullable<{
        taskId: number;
        communicationProgress: number;
        creatorId: number;
        status: number;
      }>;

    type InviteTaskCreatorListResponse = Common.PaginatingQueryRecord<InviteTaskCreator>;

    type InviteTaskCreator = {
      acceptedCount: number;
      categoryJson: string;
      creatorAvatar: string;
      creatorId: string;
      follower: string;
      followerNum: number;
      nickname: string;
    };

    type SaveInviteTemplateParams = {
      content?: string;
      email?: string;
      phone?: string;
      templateName: string;
    };

    type InviteTemplateOptions = InviteTemplateItem[];

    type InviteTemplateItem = {
      templateId: number;
      templateName: string;
    };

    type InviteTemplate = {
      content: string;
      createdBy: number;
      createTime: Date;
      email: string;
      id: number;
      phone: string;
      templateName: string;
      updateTime: Date;
    };
  }

  namespace WeeklyReport {
    type WeeklyReportDate = {
      id: number;
      shopId: number;
      week: string;
    };

    type WeeklyReportDataParams = {
      shopId: number;
      week: string;
    };

    type WeeklyReportData = {
      reportId: number;
      adsHtml: string;
      adsHtmlBottom: string;
      affiliateHtml: string;
      affiliateHtmlBottom: string;
      campaignHtml: string;
      campaignHtmlBottom: string;
      performanceHtml: string;
      performanceHtmlBottom: string;
      campaignList: CampaignData[];
      cycleTitle: string;
      lastMonthData: tableData;
      lastWeekData: tableData;
      month: string;
      monthStr: string;
      shopId: number;
      shopName: string;
      thisMonthData: tableData;
      thisWeekData: tableData;
      videoPending: number;
      week: string;
      selectCampaignIdList: string[];
      status: number;
      showAds: boolean;
      showGmvMaxAds: boolean;
      sampleMetrics: SampleMetric[];
    };

    type SampleMetric = {
      lastWeekCompletionRate: number;
      lastWeekSampleSent: number;
      lastWeekSampleTarget: number;
      monthlyCompletionRate: number;
      monthlySampleSent: number;
      monthlySampleTarget: number;
      productAvatarLocal: string;
      productId: string;
      productName: string;
      productShortName: string;
      thisWeekSampleTarget: number;
    };

    type CampaignData = {
      month: string;
      campaigns: string;
      date: string;
      type: string;
      campaignDetails: string;
      sellerDiscount: string;
      tiktokSellerDiscount: string;
      totalDiscount: string;
      status: string;
    };

    type tableData = {
      adAddsToCart: number;
      adAddsToCartRate: number;
      adAov: number;
      adCheckoutsInitiated: number;
      adCheckoutsInitiatedRate: number;
      adClicks: number;
      adCost: number;
      adCpa: number;
      adCpc: number;
      adCtr: number;
      adCvr: number;
      adGmv: number;
      adImpressions: number;
      adPurchases: number;
      adRoas: number;
      affiliateGmv: number;
      affiliateOrders: number;
      aov: number;
      avgBuyers: number;
      brandMetricsBOList: BrandMetrics[];
      cycleTitle: string;
      endDate: string;
      gmv: number;
      itemsSold: number;
      ifGmvMaxAd: boolean;
      maxAdAov: number;
      maxAdCost: number;
      maxAdCpa: number;
      maxAdGmv: number;
      maxAdPurchases: number;
      maxAdRoas: number;
      orders: number;
      pending: number;
      postingCount: number;
      sampleItems: number;
      startDate: string;
      totalItems: number;
      videoGmv: number;
      videoImpressions: number;
    };

    type BrandMetrics = {
      brand: string;
      gmv: number;
      sampleItems: number;
      unitsSold: number;
    };

    type CampaignsOptions = CampaignsOptionItem[];

    type CampaignsOptionItem = {
      campaignId: string;
      campaignName: string;
    };

    type WeeklyReportByCampaignIdParams = WeeklyReportDataParams & {
      campaignsList: string[];
    };

    type WeeklyReportByCampaignIdResponse = Pick<
      WeeklyReportData,
      'lastMonthData' | 'lastWeekData' | 'thisMonthData' | 'thisWeekData'
    >;

    type CampaignTemplateParams = {
      templateName: string;
      campaignList: CampaignTemplate[];
    };

    type CampaignTemplate = CampaignData;

    type CampaignList = {
      templateId: number;
      templateName: string;
    }[];
  }

  namespace BrandBreakDown {
    type DashboardSearchParamsByBrand = CommonType.RecordNullable<{
      brandList: string[];
      shopId: number;
      startDateStr: string;
      endDateStr: string;
    }>;
  }
}
