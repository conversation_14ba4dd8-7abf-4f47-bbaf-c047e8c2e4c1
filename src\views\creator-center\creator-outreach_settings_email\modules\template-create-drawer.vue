<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { fetchAddEmailTemplate, fetchChangeEmailTemplate } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

interface Emits {
  (e: 'change'): void;
}

const emit = defineEmits<Emits>();

interface Props {
  type?: 'add' | 'edit' | 'view';
  data?: Api.CreatorNetwork.EmailTemplate | null;
}

const props = defineProps<Props>();

const show = defineModel('show', {
  type: Boolean,
  default: false
});

type Model = Api.CreatorNetwork.AddEmailTemplateParams;

function createDefaultFormModel(): Model {
  return {
    name: '',
    title: '',
    content: ''
  };
}

const formModel = ref<Model>(createDefaultFormModel());

const [loading, toggleLoading] = useToggle(false);

const { formRef, validate } = useNaiveForm();

const { defaultRequiredRule } = useFormRules();

const rules = computed<Record<Exclude<keyof Model, 'content'>, App.Global.FormRule | App.Global.FormRule[]>>(() => {
  return {
    name: defaultRequiredRule,
    title: defaultRequiredRule
  };
});

function handleCancel() {
  show.value = false;
  formModel.value = createDefaultFormModel();
}

async function handleSubmit() {
  await validate();
  toggleLoading(true);
  const { error } = await fetchAddEmailTemplate(formModel.value);
  if (!error) {
    window.$message?.success('Add email template succefully.');
    emit('change');
    show.value = false;
  }
  toggleLoading(false);
}

async function handleChangeTemplate() {
  await validate();
  toggleLoading(true);
  const { error } = await fetchChangeEmailTemplate(formModel.value);
  if (!error) {
    window.$message?.success('Change email template succefully.');
    emit('change');
    show.value = false;
  }
  toggleLoading(false);
}

watch(
  () => props,
  newVal => {
    if (!show.value) return;
    if (newVal.type !== 'add') {
      if (newVal.data) {
        formModel.value = newVal.data;
      } else {
        formModel.value = createDefaultFormModel();
      }
    } else {
      formModel.value = createDefaultFormModel();
    }
  },
  {
    deep: true,
    immediate: true
  }
);
</script>

<template>
  <NDrawer v-model:show="show" width="800px">
    <NDrawerContent closable>
      <div>
        <NForm ref="formRef" class="h-full" :disabled="type === 'view'" :model="formModel" :rules="rules">
          <NFormItem label="Template Name" path="name">
            <NInput v-model:value="formModel.name" class="max-w-400px" placeholder="Enter template name" />
          </NFormItem>
          <NFormItem label="Email Subject" path="subject">
            <NInput v-model:value="formModel.title" class="max-w-400px" placeholder="Enter email subject" />
          </NFormItem>
          <NFormItem class="h-full" label="Email Content" path="content">
            <div class="h-full flex-col gap-16px">
              <WangEditor v-model:value-html="formModel.content" class="" :readonly="type === 'view'" />
            </div>
          </NFormItem>
        </NForm>
      </div>
      <template #footer>
        <div class="flex justify-end gap-16px">
          <NButton @click="handleCancel">Cancel</NButton>
          <NButton v-if="type === 'add'" type="primary" :loading="loading" @click="handleSubmit">Add Template</NButton>
          <NButton v-if="type === 'edit'" type="warning" :loading="loading" @click="handleChangeTemplate">
            Change Template
          </NButton>
        </div>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
