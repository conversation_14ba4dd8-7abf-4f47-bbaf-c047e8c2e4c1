<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-30 09:56:10
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-11 10:19:59
 * @FilePath: \tiksage-frontend\src\views\video-manage\list\modules\create-video-model.vue
 * @Description: create-video-model
-->
<script setup lang="tsx">
import { ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import type { SelectGroupOption, SelectOption, UploadFileInfo } from 'naive-ui';
import { NAvatar, NEllipsis, NFlex } from 'naive-ui';
import { omit } from 'lodash-es';
import {
  fetchCreateVideoApproval,
  fetchGetApprovalUserOptions,
  fetchGetShopProductOptions,
  fetchUploadFile
} from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { getFallbackImage } from '@/utils/fake-image';

interface Emits {
  (e: 'upload'): void;
}

const emit = defineEmits<Emits>();

const show = defineModel('show', {
  required: true,
  default: false
});

const approvalUserOptions = ref<Api.VideoManage.ApprovalUserOption[]>();

const shopProductOptions = ref<Api.VideoManage.ShopProductOption[]>();

const { formRef, validate, restoreValidation } = useNaiveForm();

const { defaultRequiredRule } = useFormRules();

type Model = Api.VideoManage.CreateVideoParams & {
  files: UploadFileInfo[];
};

function createDefaultModel(): Model {
  return {
    creatorName: '',
    client: null,
    productId: null,
    payment: '',
    flatFee: null,
    files: [],
    localVideo: ''
  };
}

const model = ref<Model>(createDefaultModel());

type RuleKey = Exclude<keyof Model, 'localVideo'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  creatorName: defaultRequiredRule,
  client: defaultRequiredRule,
  productId: defaultRequiredRule,
  payment: defaultRequiredRule,
  flatFee: defaultRequiredRule,
  files: defaultRequiredRule
};

const [submitLoading, toggleSubmitLoading] = useToggle(false);

async function handleSubmit(type: 'create' | 'next') {
  await validate();
  toggleSubmitLoading(true);

  const { data, error: uploadError } = await fetchUploadFile(model.value.files[0].file as File);
  if (!uploadError) {
    const params = {
      ...omit(model.value, 'files'),
      localVideo: data.fileName
    };

    const { error: createError } = await fetchCreateVideoApproval(params);
    if (!createError) {
      window.$message?.success('Create Success.');
      emit('upload');

      model.value = createDefaultModel();
      if (type === 'next') {
        restoreValidation();
      } else {
        show.value = false;
      }
    }
  }

  toggleSubmitLoading(false);
}

async function initClientOptions() {
  const { data: clientOptions, error: clientError } = await fetchGetApprovalUserOptions();
  if (!clientError) {
    approvalUserOptions.value = clientOptions;
  }
}

async function initProductOptions(data: Api.VideoManage.ApprovalUserOption) {
  const { data: productOptions, error: productError } = await fetchGetShopProductOptions(data);
  if (!productError) {
    shopProductOptions.value = productOptions;
  }
}

function handleVideoLimit(data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) {
  if (data.file.type !== 'video/mp4') {
    window.$message?.warning('Upload a video in MP4 format.');
    return false;
  }

  const fileSize = data.file.file?.size;
  if (fileSize && fileSize > 1024 * 1024 * 500) {
    window.$message?.warning('The file you’re trying to upload is too large. Maximum allowed size is 500MB.');
    return false;
  }

  return true;
}

function handleCancel() {
  model.value = createDefaultModel();
  show.value = false;
}

function renderProductOption(option: SelectOption | SelectGroupOption) {
  const src = `${import.meta.env.VITE_PRODUCT_AVATAR_URL}${option.productAvatarLocal}`;

  return (
    <NFlex align="center" size={16}>
      <NAvatar round size="small" src={src} fallbackSrc={getFallbackImage(50, 50)} />
      <NEllipsis class="flex-1">{option.productName}</NEllipsis>
    </NFlex>
  );
}

watch(
  () => model.value.client,
  newVal => {
    model.value.productId = null;
    const params = approvalUserOptions.value?.find(v => v.userId === newVal);
    if (params) {
      initProductOptions(params);
    }
  }
);

watch(
  () => show.value,
  newVal => {
    newVal && initClientOptions();
  }
);
</script>

<template>
  <NModal v-model:show="show" style="width: 800px" preset="card">
    <template #header>
      <span>Upload File</span>
    </template>
    <NFlex>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFlex :wrap="false" :size="16">
          <NFormItem label="Video File" path="files">
            <NUpload
              ref="uploadRef"
              v-model:file-list="model.files"
              class="h-284px w-300px"
              directory-dnd
              :max="1"
              @before-upload="handleVideoLimit"
            >
              <NUploadDragger>
                <NFlex class="h-236px" vertical justify="center" align="center" :size="16">
                  <SvgIcon class="text-4xl text-primary" icon="solar:cloud-upload-bold-duotone" />
                  <NText>Drag & drop your files here or choose files.</NText>
                  <NText>*500 MB max file size.*</NText>
                </NFlex>
              </NUploadDragger>
            </NUpload>
          </NFormItem>
          <NFlex class="flex-1">
            <NGrid :cols="2" :x-gap="16" :y-gap="16" item-responsive>
              <NFormItemGi label="Creator ID" path="creatorName">
                <NInput v-model:value="model.creatorName" placeholder="Put in the creator's ID" />
              </NFormItemGi>
              <NFormItemGi label="Client" path="client">
                <NSelect
                  v-model:value="model.client"
                  :options="approvalUserOptions"
                  value-field="userId"
                  label-field="shopName"
                  placeholder="Put in the client account name"
                />
              </NFormItemGi>
              <NFormItemGi label="Product" path="productId">
                <NSelect
                  v-model:value="model.productId"
                  :options="shopProductOptions"
                  value-field="productId"
                  label-field="productName"
                  placeholder="Put in the product name"
                  :render-label="renderProductOption"
                  filterable
                />
              </NFormItemGi>
              <NFormItemGi label="Payment" path="payment">
                <NInput v-model:value="model.payment" placeholder="Put in the creator's payment Account" />
              </NFormItemGi>
              <NFormItemGi label="Flat Fee" path="flatFee">
                <NInputNumber v-model:value="model.flatFee" placeholder="Put in the Flat Fee">
                  <template #prefix>$</template>
                </NInputNumber>
              </NFormItemGi>
            </NGrid>
          </NFlex>
        </NFlex>
      </NForm>
    </NFlex>
    <template #footer>
      <NFlex justify="flex-end" align="center" :size="16">
        <NButton @click="handleCancel">Back</NButton>
        <NButton type="primary" :loading="submitLoading" @click="handleSubmit('create')">Save</NButton>
        <NButton type="primary" :loading="submitLoading" @click="handleSubmit('next')">Save & Next</NButton>
      </NFlex>
    </template>
  </NModal>
</template>

<style scoped></style>
