<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-28 15:38:22
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jar<PERSON>@axwuxi.com
 * @LastEditTime: 2024-09-26 13:38:11
 * @FilePath: \tiksage-frontend\src\views\manage\log-analysis\modules\overview-card.vue
 * @Description: overview-card
-->
<script setup lang="ts">
import { computed } from 'vue';
import dayjs from 'dayjs';

type MetricKey = Extract<keyof Api.SystemManage.UserLogResponse, 'uniqueVisitor' | 'pageView'>;

interface Props {
  metrics: CommonType.Component.Metric<MetricKey>[];
  chartData: Api.SystemManage.UserLogByDay[];
}

const props = defineProps<Props>();

const newChartOptions = computed(() => {
  const values: any[] = [];
  props.metrics.forEach(m => {
    props.chartData.forEach(d => {
      values.push({
        time: dayjs(d.date).unix(),
        type: m.title,
        y: d[m.key]
      });
    });
  });
  const result: Visactor.VChart.IAreaChartSpec = {
    type: 'area',
    data: [
      {
        id: 'numberY',
        values
      }
    ],
    xField: 'time',
    yField: 'y'
  };
  return result;
});
</script>

<template>
  <NCard class="card-wrapper" content-class="flex-col" :bordered="false" title="Trend">
    <NFlex>
      <IndicatorCard v-for="metric in metrics" :key="metric.key" style="background-color: #f3f4f6" :metric="metric" />
    </NFlex>
    <div class="h-400px">
      <VLineAreaChart :chart-options="newChartOptions" />
    </div>
  </NCard>
</template>

<style scoped></style>
