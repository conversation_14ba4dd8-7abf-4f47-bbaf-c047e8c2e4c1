<script setup lang="ts">
import { ref } from 'vue';

interface Emits {
  (e: 'change', value: Model): void;
}

const emit = defineEmits<Emits>();

interface Model {
  creatorName?: string;
  product?: string;
}
const model = ref<Model>({
  creatorName: undefined,
  product: undefined
});

function handleInputChange(value: string, key: keyof Model) {
  model.value[key] = value;
  emit('change', model.value);
}
</script>

<template>
  <NFlex :size="16">
    <NInput
      style="width: 200px"
      clearable
      placeholder="Creator's ID"
      @change="v => handleInputChange(v, 'creatorName')"
    >
      <template #prefix>
        <icon-tabler:search class="text-gray" />
      </template>
    </NInput>
    <NInput
      style="width: 200px"
      clearable
      placeholder="Product name or ID"
      @change="v => handleInputChange(v, 'product')"
    >
      <template #prefix>
        <icon-tabler:search class="text-gray" />
      </template>
    </NInput>
  </NFlex>
</template>

<style scoped></style>
