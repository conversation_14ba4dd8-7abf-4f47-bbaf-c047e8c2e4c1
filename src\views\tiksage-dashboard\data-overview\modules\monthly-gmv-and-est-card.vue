<script setup lang="ts">
import { computed, onMounted } from 'vue';
import dayjs from 'dayjs';
import { fetchGetMonthlyGmvAndEstOnTikSageDashboard } from '@/service/api';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { useVChart } from '@/hooks/common/vchart';
import { NumeralFormat, TimeFormat } from '@/enum';
import { barDefaultSpec, transformData } from './chart';

const cardTitle = computed(() => {
  return `${dayjs().get('year')} Monthly GMV and Estimated Commission`;
});

const gmvAndEstSpec = computed<Visactor.VChart.IBarChartSpec>(() => {
  return { ...barDefaultSpec };
});
const { updateSpec, domRef: gmvAndEstRef } = useVChart(() => gmvAndEstSpec.value);

const { numberFormat } = useNumberFormat();

const opts = [
  { label: 'GMV', value: 'gmv' },
  { label: 'Estimated Commission', value: 'estCommission' },
  { label: 'Invoice Commission', value: 'invoiceCommission' }
];

function formatSpec(data: Api.TikSageDashboard.MonthlyDataResponse) {
  if (!data) return {};
  const cusSpec: Visactor.VChart.IBarChartSpec = {
    type: 'bar',
    data: [
      {
        id: 'barEst',
        // values: transformData(data, ['estCommission'], opts, 'month')
        values: transformData({
          data,
          selected: ['estCommission'],
          options: opts,
          xField: 'month'
        })
      },
      {
        id: 'barGmv',
        values: transformData({
          data,
          selected: ['gmv'],
          options: opts,
          xField: 'month'
        })
      },
      {
        id: 'barInvoice',
        values: transformData({
          data,
          selected: ['invoiceCommission'],
          options: opts,
          xField: 'month'
        })
      }
    ],
    xField: ['x', 'type'],
    yField: 'value',
    seriesField: 'type',
    tooltip: {
      activeType: 'dimension',
      dimension: {
        title: {
          value: v => {
            return v ? dayjs(v?.x).format(TimeFormat.US_DATE_NO_DAY) : v;
          }
        },
        content: {
          key: v => {
            return v ? (v?.type as string) : '';
          },
          // keyFormatter: '{type}',
          value: v => {
            return v ? numberFormat(v[cusSpec.yField as string], NumeralFormat.Dollar) : v;
          }
        }
      }
    },
    series: [
      {
        type: 'bar',
        id: 'seriesInvoice',
        dataId: 'barInvoice'
      },
      {
        type: 'bar',
        id: 'seriesEst',
        dataId: 'barEst'
      },
      {
        type: 'bar',
        id: 'seriesGmv',
        dataId: 'barGmv'
      }
    ],
    axes: [
      {
        orient: 'bottom',
        label: {
          formatMethod(v) {
            return v ? dayjs(v as string).format(TimeFormat.US_DATE_NO_DAY) : v;
          }
        }
      },
      {
        id: 'axisInvoice',
        type: 'linear',
        orient: 'left',
        seriesId: ['seriesInvoice'],
        label: {
          style: {
            fill: '#3779FE'
          },
          formatMethod: v => {
            return numberFormat(v, NumeralFormat.Dollar);
          }
        },
        sync: {
          axisId: 'axisEst',
          tickAlign: true
        }
      },
      {
        id: 'axisEst',
        type: 'linear',
        orient: 'left',
        seriesId: ['seriesEst'],
        label: {
          style: {
            fill: '#1AC6FF'
          },
          formatMethod: v => {
            return numberFormat(v, NumeralFormat.Dollar);
          }
        }
      },
      {
        id: 'axisGmv',
        type: 'linear',
        orient: 'right',
        seriesId: ['seriesGmv'],
        label: {
          style: {
            fill: '#FF8A00'
          },
          formatMethod: v => {
            return numberFormat(v, NumeralFormat.Dollar);
          }
        },
        sync: {
          axisId: 'axisEst',
          tickAlign: true
        }
      }
    ]
  };
  return cusSpec;
}

async function initData() {
  const { data, error } = await fetchGetMonthlyGmvAndEstOnTikSageDashboard();
  if (!error) {
    updateSpec(oldOpts => {
      return Object.assign(oldOpts, formatSpec(data));
    });
  }
}

onMounted(() => {
  initData();
});
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" :title="cardTitle">
    <div ref="gmvAndEstRef" class="h-400px"></div>
  </NCard>
</template>

<style scoped></style>
