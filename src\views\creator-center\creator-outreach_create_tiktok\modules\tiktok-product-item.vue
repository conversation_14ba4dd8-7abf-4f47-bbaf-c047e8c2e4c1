<script setup lang="ts">
import { computed } from 'vue';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { getFallbackImage } from '@/utils/fake-image';
import { NumeralFormat } from '@/enum';

const { VITE_PRODUCT_AVATAR_URL } = import.meta.env;

interface Props {
  product: any;
  handleDelete?: (productId: string) => void;
}

const props = defineProps<Props>();

const isShowDelete = computed(() => {
  return props.handleDelete !== undefined;
});

const { numberFormat } = useNumberFormat();

function handleImageUrl(url: string) {
  if (url.includes('https')) {
    return url;
  }

  return `${VITE_PRODUCT_AVATAR_URL}${url}`;
}
</script>

<template>
  <div class="flex gap-4 border rounded-xl p-2">
    <NImage
      class="flex-shrink-0"
      :width="96"
      :height="96"
      :src="handleImageUrl(product.affiliate_product.base_product.image_url)"
      :fallback-src="getFallbackImage(96, 96)"
    />
    <div class="flex-col justify-center gap-2">
      <NEllipsis :line-clamp="1" :tooltip="{ contentStyle: 'width:400px;' }">
        {{ product.affiliate_product.base_product.title }}
      </NEllipsis>
      <span class="text-coolgray">ID: {{ product.affiliate_product.base_product.product_id }}</span>
      <div class="flex items-center gap-2 font-bold">
        <span>
          Price:
          {{
            product.affiliate_product.base_product.price.max_price ==
            product.affiliate_product.base_product.price.min_price
              ? `${product.affiliate_product.base_product.price.min_price_format}${product.affiliate_product.base_product.price.max_price_format}`
              : `${product.affiliate_product.base_product.price.min_price_format}~${product.affiliate_product.base_product.price.max_price_format}`
          }}
        </span>
        <NDivider vertical />
        <span>
          Commission rate:
          {{
            numberFormat(
              product.affiliate_product.current_commission_rate.commission_rate / 10000,
              NumeralFormat.Percent
            )
          }}
        </span>
      </div>
    </div>
    <div v-if="isShowDelete" class="min-w-100px flex flex-1 items-center justify-end">
      <ButtonIcon
        secondary
        icon="solar:trash-bin-minimalistic-linear"
        @click="handleDelete && handleDelete(product.affiliate_product.base_product.product_id)"
      ></ButtonIcon>
    </div>
  </div>
</template>

<style scoped></style>
