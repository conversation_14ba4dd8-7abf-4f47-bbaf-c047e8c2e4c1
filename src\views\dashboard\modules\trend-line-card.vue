<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-24 14:05:16
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-24 15:55:00
 * @FilePath: \tiksage-frontend\src\views\home\modules\trend-line-card.vue
 * @Description: performance trend
-->
<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { divide } from 'lodash-es';
import { useDashboardStore } from '@/store/modules/dashboard';
import { useBrandBreakDownStore } from '@/store/modules/brand-breakdown';
import { useIndicator } from '@/hooks/custom/indicator';
interface Props {
  type?: 'default' | 'brand';
  model: {
    title: string;
    allIndicatorKey: Api.Auth.AllIndicatorKey[];
    userIndicatorKey: Api.Auth.userIndicatorKey;
  };
}

interface Emits {
  (e: 'download'): void;
}

const emit = defineEmits<Emits>();

const dashboardStore = useDashboardStore();
const brandStore = useBrandBreakDownStore();

const props = withDefaults(defineProps<Props>(), {
  type: 'default'
});

// data
const { displayValue, checkedValue, options, updateCheckedValue, resetCheckedValue } = useIndicator(
  props.model.allIndicatorKey,
  props.model.userIndicatorKey,
  props.type
);

const checkboxData = computed(() => {
  const res = [
    {
      title: 'Sales',
      boxs: options.value[0]
    },
    {
      title: 'Marketing',
      boxs: options.value[1]
    }
  ];

  if (props.type === 'default') {
    res.push({
      title: 'Content',
      boxs: options.value[2]
    });
  }

  return res;
});

const loading = ref(false);

const chartOptions = ref<any>();

const initData = () => {
  loading.value = true;

  const chartData =
    props.type === 'brand'
      ? brandStore.initIndicatorData(displayValue.value, options.value)
      : dashboardStore.initIndicatorData(displayValue.value, options.value);
  const option = {
    type: 'area',
    data: [] as any,
    xField: 'time',
    yField: 'y'
  };

  const data: any[] = [
    {
      id: 'dollarY',
      values: []
    },
    {
      id: 'percentY',
      values: []
    },
    {
      id: 'numberY',
      values: []
    }
  ];

  chartData.forEach(chart => {
    if (!chart) return;
    if (chart.option.unit === '$') {
      data[0].values = data[0].values.concat(chart.values);
    } else if (chart.option.unit === '%') {
      const newValues = chart.values.map(v => {
        v[option.yField] = divide(v[option.yField], 100);
        return v;
      });
      data[1].values = data[1].values.concat(newValues);
    } else {
      data[2].values = data[2].values.concat(chart.values);
    }
  });

  option.data = data;
  chartOptions.value = option;

  setTimeout(() => {
    loading.value = false;
  }, 1000);
};

watch(
  () => displayValue.value,
  () => {
    initData();
  }
);

// modal
const [modalVisible, toggleVisible] = useToggle(false);
const handleCancelSelect = () => {
  resetCheckedValue();
  toggleVisible();
};
const handleConfirmSelect = async () => {
  if (checkedValue.value.length > 0 && checkedValue.value.length < 5) {
    await updateCheckedValue();
    toggleVisible(false);
  }
};

function isDisabled(value: string) {
  return checkedValue.value.length >= 4 && !checkedValue.value.includes(value);
}

function handleDownload() {
  emit('download');
}

initData();
</script>

<template>
  <NCard :bordered="false" content-class="min-h-385px" title="Performance Trend" class="card-wrapper">
    <template #header-extra>
      <NButtonGroup>
        <ButtonIcon
          icon="solar:pen-2-linear"
          tooltip-content="Select Metrics"
          tooltip-placement="top"
          @click="toggleVisible()"
        />
        <ButtonIcon
          icon="solar:download-linear"
          tooltip-content="Export"
          tooltip-placement="top"
          @click="handleDownload"
        />
      </NButtonGroup>
    </template>
    <NSpin v-if="loading" />
    <VLineAreaChart v-else :chart-options="chartOptions" />
    <NModal
      v-model:show="modalVisible"
      preset="dialog"
      title="Select Metrics"
      :closable="false"
      :mask-closable="false"
      :close-on-esc="false"
      style="width: 70%"
    >
      <NFlex vertical>
        <NText type="warning">* You can choose to view 1 to 4 metrics.</NText>
        <NFlex v-for="group in checkboxData" :key="group.title" vertical>
          <NGradientText type="info" :size="20">{{ group.title }}</NGradientText>
          <NCheckboxGroup v-model:value="checkedValue">
            <NGrid x-gap="16" y-gap="16" item-responsive responsive="screen">
              <NGi v-for="item in group.boxs" :key="item.key" span="6">
                <NCheckbox :value="item.value" :label="item.label" :disabled="isDisabled(item.value)"></NCheckbox>
              </NGi>
            </NGrid>
          </NCheckboxGroup>
        </NFlex>
      </NFlex>
      <template #action>
        <NFlex>
          <NButton size="small" @click="handleCancelSelect">Cancel</NButton>
          <NButton size="small" type="primary" @click="handleConfirmSelect">Confirm</NButton>
        </NFlex>
      </template>
    </NModal>
  </NCard>
</template>

<style scoped></style>
