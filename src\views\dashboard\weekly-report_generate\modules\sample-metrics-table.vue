<script setup lang="tsx">
import { computed, nextTick, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { NumeralFormat } from '@/enum';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { renderCell } from './shared';
import { NInput, NInputNumber } from 'naive-ui';

type TableData = Api.WeeklyReport.SampleMetric;
interface Emits {
  (e: 'update', key: string, value: any): void;
}

interface Props {
  isShow?: boolean;
  data: TableData[];
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const [canEdit, toggleCanEdit] = useToggle(false);

const operateData = ref<TableData[]>([]);

const sampleColumns = ref<NaiveUI.DataTableColumns<TableData>>([
  {
    key: 'productShortName',
    title: 'Product name',
    align: 'center',
    width: 200,
    fixed: 'left'
  },
  {
    key: 'productId',
    title: 'Product ID',
    align: 'center',
    width: 200,
    fixed: 'left'
  },
  {
    key: 'monthlySampleTarget',
    title: 'Monthly sample target',
    align: 'center',
    width: 200
  },
  {
    key: 'monthlySampleSent',
    title: 'Monthly samples sent',
    align: 'center',
    width: 200
  },
  {
    key: 'monthlyCompletionRate',
    title: 'Monthly completion rate',
    align: 'center',
    width: 200,
    unit: NumeralFormat.Percent
  },
  {
    key: 'lastWeekSampleTarget',
    title: 'Last week sample target',
    align: 'center',
    width: 200
  },
  {
    key: 'lastWeekSampleSent',
    title: 'Last week samples sent',
    align: 'center',
    width: 200
  },
  {
    key: 'lastWeekCompletionRate',
    title: 'Last week completion rate',
    align: 'center',
    width: 220,
    unit: NumeralFormat.Percent
  },
  {
    key: 'thisWeekSampleTarget',
    title: 'This week sample target',
    align: 'center',
    width: 200,
    render: editRender('thisWeekSampleTarget')
  }
]);

const sampleWidth = computed(() => {
  return sampleColumns.value.reduce((p, c) => p + (Number(c.width) || 0), 0);
});

function editRender(key: keyof TableData) {
  return (rowData: TableData, rowIndex: number) => {
    return canEdit.value ? (
      <div class="w-full">
        <NInputNumber
          placeholder=""
          min={1}
          precision={0}
          value={rowData[key] as number}
          onUpdateValue={v => {
            // Update the value of the current row
            return handleUpdateValue(rowIndex, key, v);
          }}
        ></NInputNumber>
      </div>
    ) : (
      <div class="whitespace-pre-wrap" innerHTML={rowData[key] as string}></div>
    );
  };
}

function handleEdit() {
  toggleCanEdit();
}

function handleUpdateValue(rowIndex: number, key: keyof TableData, value: any) {
  const newData = [...operateData.value];
  newData[rowIndex][key] = value;
  emit('update', 'sampleMetrics', newData);
}
const selectedRowIdx = ref<number>();
const [showDropdown, toggleShowDropdown] = useToggle(false);
const position = ref({ x: 0, y: 0 });
const dropdownOptions = ref([
  {
    label: () => (
      <div class="flex-y-center gap-2">
        <SvgIcon icon="solar:arrow-to-top-right-linear" />
        Insert Above
      </div>
    ),
    key: 'insertAbove'
  },
  {
    label: () => (
      <div class="flex-y-center gap-2">
        <SvgIcon icon="solar:arrow-to-down-left-linear" />
        Insert Below
      </div>
    ),
    key: 'insertBelow'
  },
  {
    label: () => (
      <div class="flex-y-center gap-2 text-error">
        <SvgIcon icon="solar:trash-bin-minimalistic-linear" />
        Delete
      </div>
    ),
    key: 'delete'
  }
]);

function rowProps(_rowData: any, index: number) {
  return {
    onContextmenu: async (e: MouseEvent) => {
      selectedRowIdx.value = index;
      e.preventDefault();
      toggleShowDropdown(false);
      await nextTick();
      toggleShowDropdown(true);
      position.value = { x: e.clientX, y: e.clientY };
    }
  };
}

function handleSelect(key: string) {
  switch (key) {
    // case 'insertAbove':
    //   data.value.splice(selectedRowIdx.value!, 0, ...createEmptyRows());
    //   break;
    // case 'insertBelow':
    //   data.value.splice(selectedRowIdx.value! + 1, 0, ...createEmptyRows());
    //   break;
    case 'delete':
      operateData.value.splice(selectedRowIdx.value!, 1);
      break;
    default:
      break;
  }
  toggleShowDropdown(false);
}

watch(
  () => props.data,
  newVal => (operateData.value = newVal),
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div class="flex-col gap-2">
    <div v-if="!isShow" class="flex justify-end">
      <NButton type="primary" @click="handleEdit">Edit</NButton>
    </div>
    <NDataTable
      striped
      :scroll-x="sampleWidth"
      :single-line="false"
      :columns="sampleColumns"
      :data="operateData"
      :render-cell="renderCell"
      :options="dropdownOptions"
      :show="showDropdown"
      :x="position.x"
      :y="position.y"
      :row-props="rowProps"
    ></NDataTable>
    <NDropdown
      v-if="!isShow"
      placement="bottom-start"
      trigger="manual"
      :options="dropdownOptions"
      :x="position.x"
      :y="position.y"
      :show="showDropdown"
      @select="handleSelect"
      @clickoutside="toggleShowDropdown(false)"
    />
  </div>
</template>

<style scoped></style>
