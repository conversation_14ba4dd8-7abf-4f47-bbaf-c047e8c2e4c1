<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-09-09 14:45:45
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-08 15:22:59
 * @FilePath: \tiksage-frontend\src\views\dashboard\jazwares\modules\sub-brand-shop-card.vue
 * @Description: sub-brand-shop-card
-->
<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  data?: Api.DashboardJazwares.SubBrandMetricResponse;
}

const props = defineProps<Props>();

const chartOptions = computed<Visactor.VChart.IBarChartSpec>(() => {
  const result: Visactor.VChart.IBarChartSpec = {
    type: 'bar',
    data: [
      {
        id: 'dollarY',
        values: []
      },
      {
        id: 'numberY',
        values: []
      }
    ],
    xField: ['brand', 'type'],
    yField: 'y',
    series: [
      {
        type: 'bar',
        id: 'dollarSeries',
        dataId: 'dollarY',
        seriesField: 'type',
        tooltip: {
          activeType: 'dimension',
          dimension: {
            content: {
              keyFormatter: '{type}',
              value: v => {
                return `$${v!.y}`;
              }
            }
          }
        }
      }
    ]
  };
  if (props.data) {
    for (const ele of props.data) {
      result.data &&
        (result.data as any)[0].values.push({
          brand: ele.subBrand,
          type: 'GMV',
          y: ele.gmv
        });
      result.data &&
        (result.data as any)[1].values.push({
          brand: ele.subBrand,
          type: 'Unit Sold',
          y: ele.unitsSold
        });
    }
  }
  return result;
});
</script>

<template>
  <NCard class="h-full card-wrapper" :bordered="false" content-class="h-400px" title="">
    <template #header>
      <div class="h-34px line-height-34px">Brand Performance</div>
    </template>
    <VBarChart :chart-options="chartOptions" />
  </NCard>
</template>

<style scoped></style>
