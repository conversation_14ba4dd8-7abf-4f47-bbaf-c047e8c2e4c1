<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-16 17:12:01
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-08 10:21:08
 * @FilePath: \tiksage-frontend\src\views\category-intelligence\modules\sku-top10-card.vue
 * @Description: sku-top10-card
-->
<script setup lang="tsx">
import { NButton, NEllipsis, NFlex, NTag } from 'naive-ui';
import { divide, isEmpty, isNil } from 'lodash-es';
import { fetchProductTop10 } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import PopoverImg from '@/components/custom/popover-img.vue';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { NumeralFormat } from '@/enum';
import CategoryShopSelect from './category-shop-select.vue';

interface Props {
  shopType: number;
  month: string;
}

const props = defineProps<Props>();

const { unFormat, numberFormat } = useNumberFormat();

const { data, columns, getData, mobilePagination, loading, updateSearchParams } = useTable({
  apiFn: fetchProductTop10,
  immediate: true,
  apiParams: {
    current: 1,
    size: 10,
    shopType: props.shopType,
    month: props.month
  },
  columns() {
    return [
      {
        key: 'index',
        align: 'center',
        fixed: 'left',
        width: 50,
        render(rowData) {
          if (rowData.index === 1) {
            return (
              <NFlex class="w-100%" justify="center">
                <SvgIcon style="width:25px;height:25px" icon="twemoji:1st-place-medal" />
              </NFlex>
            );
          } else if (rowData.index === 2) {
            return (
              <NFlex class="w-100%" justify="center">
                <SvgIcon style="width:25px;height:25px" icon="twemoji:2nd-place-medal" />
              </NFlex>
            );
          } else if (rowData.index === 3) {
            return (
              <NFlex class="w-100%" justify="center">
                <SvgIcon style="width:25px;height:25px" icon="twemoji:3rd-place-medal" />
              </NFlex>
            );
          }
          return <span>{rowData.index}</span>;
        }
      },
      {
        key: 'productName',
        title: 'Product',
        fixed: 'left',
        width: 250,
        // src={`${import.meta.env.VITE_SHOP_AVATAR_URL}${rowData.avatar}`}
        render: rowData => (
          <NButton text tag="div" focusable={false} onClick={() => window.open(rowData.url, '_blank')}>
            <NFlex align="center" wrap={false}>
              <PopoverImg
                width={50}
                height={50}
                src={`${import.meta.env.VITE_PRODUCT_AVATAR_URL}${rowData.avatar}`}
                fallbackSrc={rowData.avatarBak}
              />
              <NFlex vertical justify="start">
                <NEllipsis class="" style="max-width:180px" tooltip={{ contentClass: 'max-w-300px' }}>
                  {/* <NText>{rowData.productName || '-'}</NText> */}
                  {rowData.productName || '-'}
                </NEllipsis>
                <NFlex align="center" class="line-height-normal" wrap={false}>
                  <SvgIcon class="text-dark" icon="tabler:building-store" />
                  <NEllipsis style="max-width:150px" tooltip={{ contentClass: 'max-w-300px' }}>
                    <span class="text-coolGray">{rowData.shopName || '-'}</span>
                  </NEllipsis>
                </NFlex>
              </NFlex>
            </NFlex>
          </NButton>
        )
      },
      {
        key: 'category',
        title: 'Sub-category',
        align: 'center',
        width: 140,
        render(rowData) {
          if (isNil(rowData.category) || isEmpty(rowData.category)) return '-';
          return (
            <NFlex justify="center">
              <NTag size="small" bordered={false}>
                {rowData.category}
              </NTag>
            </NFlex>
          );
        }
      },
      {
        key: 'gmv',
        title: 'Gross Sales',
        align: 'center',
        sorter(row1, row2) {
          return unFormat(row1.gmv) - unFormat(row2.gmv);
        },
        sortOrder: 'descend',
        width: 140
      },
      {
        key: 'salesQuantity',
        title: 'Units Sold',
        align: 'center',
        width: 140
      },
      {
        key: 'rsp',
        title: 'RSP',
        align: 'center',
        width: 140
      },
      {
        key: 'directDiscountDepth',
        title: 'Discount Depth',
        align: 'center',
        width: 140
      },
      {
        key: 'commissionSpending',
        title: 'Commission Spending',
        align: 'center',
        width: 140
      }
    ];
  }
});

const dollarKeys = ['commissionSpending'];
const percentKeys = ['directDiscountDepth'];

const renderCell = (value: any, _rowData: any, column: any) => {
  if (dollarKeys.includes(column.key)) {
    return numberFormat(value, NumeralFormat.Dollar);
  }
  if (percentKeys.includes(column.key)) {
    return numberFormat(divide(Number(value), 100), NumeralFormat.Percent);
  } else if (typeof value === 'number') {
    return numberFormat(value, NumeralFormat.Number);
  }
  return value;
};

const handleGetData = (model: { category: string; thirdCategory: string; shopIds: number[] }) => {
  updateSearchParams({
    current: 1,
    ...model
  });
  getData();
};
</script>

<template>
  <NCard :bordered="false" class="card-wrapper" title="Top 50 Products by Sales">
    <template #header-extra>
      <CategoryShopSelect
        :month="month"
        :shop-type="shopType"
        show-third-category
        show-shop
        @update:value="handleGetData"
      />
    </template>
    <NDataTable
      :bordered="false"
      size="small"
      :loading="loading"
      :data="data"
      :columns="columns"
      :pagination="mobilePagination"
      :render-cell="renderCell"
      remote
      scroll-x="1140"
    ></NDataTable>
  </NCard>
</template>

<style scoped></style>
