<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useToggle } from '@vueuse/core';
import { isNil } from 'lodash-es';
import {
  fetchDownloadTrendPerformance,
  fetchGetSubBrandShopMetricData,
  fetchGetSubBrandShopOptionsByShopId
} from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useBrandBreakDownStore } from '@/store/modules/brand-breakdown';
import { useUserStore } from '@/store/modules/user';
import { downloadFile } from '@/utils/download';
import ShopInfoCard from '../modules/shop-info-card.vue';
import QuotaCard from '../modules/quota-card.vue';
import TrendLineCard from '../modules/trend-line-card.vue';
import SubBrandShopCard from '../sub-brand/modules/sub-brand-shop-card.vue';
import SubBrandProductCard from '../sub-brand/modules/sub-brand-product-card.vue';
import ShopList from '../sub-brand/modules/shop-list.vue';
import CreatorPerformanceTab from '../modules/creator-performance/creator-performance-tab.vue';
import ProductCard from './modules/product-card.vue';

interface MetricCardInfo {
  title: string;
  allIndicatorKey: Api.Auth.AllIndicatorKey[];
  userIndicatorKey: Api.Auth.userIndicatorKey;
  description?: string;
}

const brandStore = useBrandBreakDownStore();
const authStore = useAuthStore();
const userStore = useUserStore();
const route = useRoute();
const routeparams = route.query;
const [shopReady, toggleShopReady] = useToggle(false);

const shopOptions = ref<CommonType.Option<number>[]>([]);
const brandOptions = ref<CommonType.Option<string>[]>([]);

const tab = ref<'dashboard' | 'creator-performance'>('dashboard');

const indicatorData: Record<'quota' | 'trendLine' | 'contribution' | 'comparison', MetricCardInfo[]> = {
  quota: [
    {
      title: 'Sales',
      allIndicatorKey: ['salesArr'],
      userIndicatorKey: 'salesArr',
      description:
        "The sales metric is elucidated by presenting the shop's sales figures and compared with previous periods."
    },
    {
      title: 'Marketing',
      allIndicatorKey: ['trafficArr'],
      userIndicatorKey: 'trafficArr',
      description:
        'The customer conversion data from viewing to purchasing is succinctly presented and compared with previous periods.'
    },
    {
      title: 'Advertising',
      allIndicatorKey: ['adsArr'],
      userIndicatorKey: 'adsArr',
      description: 'Shop advertising data and performance.'
    }
  ],
  trendLine: [
    {
      title: 'Breakdown By Affiliate ',
      allIndicatorKey: ['performanceSalesArr', 'performanceTrafficArr', 'performancecontentArr'],
      userIndicatorKey: 'performanceArr'
    }
  ],
  contribution: [
    {
      title: 'Breakdown By Channel',
      allIndicatorKey: ['breakdownChannelArr'],
      userIndicatorKey: 'breakdownChannel',
      description: `According to the data source channels for differentiated statistics, such as "GMV":
Live:
The total amount paid for orders placed directly from all LIVEs, including returns and refunds.
Video:
The total amount of paid orders from all shoppable videos, including returns and refunds.
Product card:
The transaction amount generated through Product Card transactions, including cancellations and refunds. This is the transaction amount generated by users who clicked on the Product Card (excluding video and LIVE), entered the product details page, and conducted a transaction during the selected period.`
    },
    {
      title: 'Breakdown By Affiliate ',
      allIndicatorKey: ['breakdownAffiliateArr'],
      userIndicatorKey: 'breakdownAffiliate',
      description: `Affiliate: The affiliate is available to all TikTok Shop creators. You may add affiliate products to your TikTok Shop showcase. After a customer purchases one of your affiliate items, you will earn commissions on qualifying purchases. Your commission is calculated using this formula: Commission = (Revenue - Refunds) * Commission rate.`
    }
  ],
  comparison: [
    {
      title: 'Content Performance Between Affiliate & Non-affiliate',
      allIndicatorKey: ['contentAffiliateOwnArr'],
      userIndicatorKey: 'contentAffiliateOwn'
    }
  ]
};

const searchParams = reactive<Api.BrandBreakDown.DashboardSearchParamsByBrand>(createSearchParams());

const oldSearchParams = computed<Api.Dashboard.DashboardTopSearchParams>(() => {
  return {
    shopIdsArr: [searchParams.shopId as number],
    startDateStr: searchParams.startDateStr as string,
    endDateStr: searchParams.endDateStr as string,
    brand: searchParams.brandList?.[0] || ''
  };
});
const selectedBrand = ref<string>('');

const subBrandMetricData = ref<Api.DashboardJazwares.SubBrandMetricResponse>();

async function getSubBrandMetricData(params: Api.DashboardJazwares.BaseSearchParams) {
  const query = params;
  delete query.brand;
  const { data, error } = await fetchGetSubBrandShopMetricData(query);
  if (error) return;
  subBrandMetricData.value = data;
}

function handleShopUpdate(value: number) {
  toggleShopReady(false);
  searchParams.shopId = value;
  initBrandOptions();
}

function handleChangeDateRange(value: [string, string] | null) {
  if (value) {
    searchParams.startDateStr = value[0];
    searchParams.endDateStr = value[1];
  }
}

async function handleDownload() {
  const { data, error } = await fetchDownloadTrendPerformance(searchParams);
  if (error) return;
  downloadFile(data, 'xlsx', 'Performance Trend');
}

async function initShopOptions() {
  const shopOpts = await userStore.getUserShop(true);

  shopOptions.value = shopOpts.map(item => ({ label: item.shopName, value: item.shopId }));
  if (!routeparams.shopId) {
    searchParams.shopId = shopOpts[0].shopId;
  }
}

async function initBrandOptions() {
  const defaultOpt = [
    {
      label: 'All',
      value: ''
    }
  ];
  if (isNil(searchParams.shopId)) return;

  const { data: brandOpts, error: brandOptsErr } = await fetchGetSubBrandShopOptionsByShopId(searchParams.shopId);
  if (!brandOptsErr) {
    if (!brandOpts.length) {
      brandOptions.value = defaultOpt;
      selectedBrand.value = '';
    } else {
      brandOptions.value = brandOpts.map(item => ({ label: item, value: item }));
      selectedBrand.value = brandOpts[0];
    }
  }

  toggleShopReady(true);
}

function createSearchParams(): Api.BrandBreakDown.DashboardSearchParamsByBrand {
  let shopId;
  if (routeparams.shopId) {
    toggleShopReady(true);
    shopId = Number(routeparams.shopId);
  }

  return {
    brandList: null,
    shopId,
    startDateStr: '',
    endDateStr: ''
  };
}

async function initData() {
  await initShopOptions();
  await initBrandOptions();
  toggleShopReady(true);
}

initData();

watch(
  [() => searchParams, () => tab.value, () => shopReady.value],
  async () => {
    if (shopReady.value && tab.value === 'dashboard') {
      getSubBrandMetricData(oldSearchParams.value);
      brandStore.getDashboardData(searchParams);
    }
  },
  {
    immediate: true,
    deep: true
  }
);

watch(
  () => selectedBrand.value,
  newVal => {
    if (newVal === '') {
      searchParams.brandList = null;
    } else {
      searchParams.brandList = [newVal];
    }
  },
  { immediate: true }
);
</script>

<template>
  <NFlex vertical :size="16" class="h-full">
    <NCard :bordered="false" class="card-wrapper">
      <template #header>
        <NTabs v-model:value="tab" tab-style="font-size: 18px; font-weight: 500;" animated>
          <NTab name="dashboard" tab="Metrics" />
          <NTab name="creator-performance" tab="Creator Performance" />
        </NTabs>
      </template>
      <template #header-extra>
        <NPopselect
          :disabled="authStore.userInfo.userShopIds.length <= 1"
          :value="searchParams.shopId"
          :options="shopOptions"
          @update-value="handleShopUpdate"
        >
          <NButton quaternary>
            <div class="flex items-center justify-end">
              <ShopInfoCard v-if="!isNil(searchParams.shopId)" :id="searchParams.shopId" />
              <icon-solar:alt-arrow-down-line-duotone v-if="authStore.userInfo.userShopIds.length > 1" />
            </div>
          </NButton>
        </NPopselect>
        <NSelect
          v-model:value="selectedBrand"
          class="min-w-150px"
          :consistent-menu-width="false"
          :options="brandOptions"
        ></NSelect>
      </template>
    </NCard>
    <NFlex v-if="tab === 'dashboard'" vertical :size="16">
      <NSpin class="h-full min-h-100vh" :show="!shopReady || brandStore.loading">
        <NSpace class="pb-16px" vertical :size="16">
          <NCard class="card-wrapper" :bordered="false" title=" ">
            <template #header-extra>
              <ButtonDate
                :default-value="-1"
                :start-time="authStore.userInfo.selectStartDateStr"
                :end-time="authStore.userInfo.selectEndDateStr"
                @update:value="handleChangeDateRange"
              />
            </template>
          </NCard>
          <NFlex v-if="!brandStore.loading" vertical :size="16">
            <NGrid :x-gap="16" :y-gap="16" :cols="indicatorData.quota.length">
              <NGi v-for="item in indicatorData.quota" :key="item.title">
                <QuotaCard :model="item" type="brand" />
              </NGi>
            </NGrid>
            <TrendLineCard type="brand" :model="indicatorData.trendLine[0]" @download="handleDownload" />
            <NGrid v-if="subBrandMetricData" :x-gap="16" y-gap="16" responsive="screen" item-responsive>
              <NGi span="16">
                <SubBrandShopCard :data="subBrandMetricData" />
              </NGi>
              <NGi span="8">
                <SubBrandProductCard :data="subBrandMetricData" />
              </NGi>
            </NGrid>
            <ProductCard :params="searchParams" />
            <ShopList :params="oldSearchParams" />
          </NFlex>
        </NSpace>
      </NSpin>
    </NFlex>
    <CreatorPerformanceTab
      v-if="tab === 'creator-performance'"
      sub-brand
      :brand-list="searchParams.brandList!"
      :shop-ids-arr="[searchParams.shopId!]"
      :shop-options="shopOptions"
    />
  </NFlex>
</template>

<style scoped></style>
