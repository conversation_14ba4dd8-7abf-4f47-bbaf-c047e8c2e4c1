/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteRecordRaw, RouteComponent } from 'vue-router';
import type { ElegantConstRoute } from '@elegant-router/vue';
import type { RouteMap, RouteKey, RoutePath } from '@elegant-router/types';

/**
 * transform elegant const routes to vue routes
 * @param routes elegant const routes
 * @param layouts layout components
 * @param views view components
 */
export function transformElegantRoutesToVueRoutes(
  routes: ElegantConstRoute[],
  layouts: Record<string, RouteComponent | (() => Promise<RouteComponent>)>,
  views: Record<string, RouteComponent | (() => Promise<RouteComponent>)>
) {
  return routes.flatMap(route => transformElegantRouteToVueRoute(route, layouts, views));
}

/**
 * transform elegant route to vue route
 * @param route elegant const route
 * @param layouts layout components
 * @param views view components
 */
function transformElegantRouteToVueRoute(
  route: ElegantConstRoute,
  layouts: Record<string, RouteComponent | (() => Promise<RouteComponent>)>,
  views: Record<string, RouteComponent | (() => Promise<RouteComponent>)>
) {
  const LAYOUT_PREFIX = 'layout.';
  const VIEW_PREFIX = 'view.';
  const ROUTE_DEGREE_SPLITTER = '_';
  const FIRST_LEVEL_ROUTE_COMPONENT_SPLIT = '$';

  function isLayout(component: string) {
    return component.startsWith(LAYOUT_PREFIX);
  }

  function getLayoutName(component: string) {
    const layout = component.replace(LAYOUT_PREFIX, '');

    if(!layouts[layout]) {
      throw new Error(`Layout component "${layout}" not found`);
    }

    return layout;
  }

  function isView(component: string) {
    return component.startsWith(VIEW_PREFIX);
  }

  function getViewName(component: string) {
    const view = component.replace(VIEW_PREFIX, '');

    if(!views[view]) {
      throw new Error(`View component "${view}" not found`);
    }

    return view;
  }

  function isFirstLevelRoute(item: ElegantConstRoute) {
    return !item.name.includes(ROUTE_DEGREE_SPLITTER);
  }

  function isSingleLevelRoute(item: ElegantConstRoute) {
    return isFirstLevelRoute(item) && !item.children?.length;
  }

  function getSingleLevelRouteComponent(component: string) {
    const [layout, view] = component.split(FIRST_LEVEL_ROUTE_COMPONENT_SPLIT);

    return {
      layout: getLayoutName(layout),
      view: getViewName(view)
    };
  }

  const vueRoutes: RouteRecordRaw[] = [];

  // add props: true to route
  if (route.path.includes(':') && !route.props) {
    route.props = true;
  }

  const { name, path, component, children, ...rest } = route;

  const vueRoute = { name, path, ...rest } as RouteRecordRaw;

  try {
    if (component) {
      if (isSingleLevelRoute(route)) {
        const { layout, view } = getSingleLevelRouteComponent(component);

        const singleLevelRoute: RouteRecordRaw = {
          path,
          component: layouts[layout],
          meta: {
            title: route.meta?.title || ''
          },
          children: [
            {
              name,
              path: '',
              component: views[view],
              ...rest
            } as RouteRecordRaw
          ]
        };

        return [singleLevelRoute];
      }

      if (isLayout(component)) {
        const layoutName = getLayoutName(component);

        vueRoute.component = layouts[layoutName];
      }

      if (isView(component)) {
        const viewName = getViewName(component);

        vueRoute.component = views[viewName];
      }

    }
  } catch (error: any) {
    console.error(`Error transforming route "${route.name}": ${error.toString()}`);
    return [];
  }

  // add redirect to child
  if (children?.length && !vueRoute.redirect) {
    vueRoute.redirect = {
      name: children[0].name
    };
  }

  if (children?.length) {
    const childRoutes = children.flatMap(child => transformElegantRouteToVueRoute(child, layouts, views));

    if(isFirstLevelRoute(route)) {
      vueRoute.children = childRoutes;
    } else {
      vueRoutes.push(...childRoutes);
    }
  }

  vueRoutes.unshift(vueRoute);

  return vueRoutes;
}

/**
 * map of route name and route path
 */
const routeMap: RouteMap = {
  "root": "/",
  "not-found": "/:pathMatch(.*)*",
  "403": "/403",
  "404": "/404",
  "500": "/500",
  "account": "/account",
  "account_center": "/account/center",
  "analysis": "/analysis",
  "analysis_diagnosis": "/analysis/diagnosis",
  "analysis_list": "/analysis/list",
  "analysis_report": "/analysis/report/:id",
  "category-analysis": "/category-analysis",
  "category-analysis_category-intelligence": "/category-analysis/category-intelligence",
  "category-analysis_category-leaders": "/category-analysis/category-leaders",
  "creator-center": "/creator-center",
  "creator-center_creator-approval": "/creator-center/creator-approval",
  "creator-center_creator-approval-video-status": "/creator-center/creator-approval-video-status",
  "creator-center_creator-database": "/creator-center/creator-database",
  "creator-center_creator-outreach": "/creator-center/creator-outreach",
  "creator-center_creator-outreach_create": "/creator-center/creator-outreach/create",
  "creator-center_creator-outreach_create_email": "/creator-center/creator-outreach/create/email",
  "creator-center_creator-outreach_create_target-invitation": "/creator-center/creator-outreach/create/target-invitation",
  "creator-center_creator-outreach_create_tiktok": "/creator-center/creator-outreach/create/tiktok",
  "creator-center_creator-outreach_find-creator": "/creator-center/creator-outreach/find-creator",
  "creator-center_creator-outreach_history": "/creator-center/creator-outreach/history",
  "creator-center_creator-outreach_history_email": "/creator-center/creator-outreach/history/email",
  "creator-center_creator-outreach_history_target-invitation": "/creator-center/creator-outreach/history/target-invitation",
  "creator-center_creator-outreach_history_tiktok": "/creator-center/creator-outreach/history/tiktok",
  "creator-center_creator-outreach_settings": "/creator-center/creator-outreach/settings",
  "creator-center_creator-outreach_settings_email": "/creator-center/creator-outreach/settings/email",
  "creator-center_creator-profiles": "/creator-center/creator-profiles",
  "dashboard": "/dashboard",
  "dashboard_base": "/dashboard/base",
  "dashboard_brand-breakdown": "/dashboard/brand-breakdown",
  "dashboard_sub-brand": "/dashboard/sub-brand",
  "dashboard_weekly-report": "/dashboard/weekly-report",
  "dashboard_weekly-report_generate": "/dashboard/weekly-report/generate",
  "dashboard_weekly-report_preview": "/dashboard/weekly-report/preview",
  "detail": "/detail",
  "detail_creator": "/detail/creator/:id",
  "detail_video": "/detail/video/:id",
  "digital-audit": "/digital-audit",
  "digital-audit_shop-audit": "/digital-audit/shop-audit",
  "digital-audit_shop-audit-history": "/digital-audit/shop-audit-history",
  "google-auth": "/google-auth",
  "iframe-page": "/iframe-page/:url",
  "login": "/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?",
  "manage": "/manage",
  "manage_common": "/manage/common",
  "manage_dictionary": "/manage/dictionary",
  "manage_log-analysis": "/manage/log-analysis",
  "manage_menu": "/manage/menu",
  "manage_role": "/manage/role",
  "manage_shop": "/manage/shop",
  "manage_user": "/manage/user",
  "manage_user-detail": "/manage/user-detail/:id",
  "operational-data": "/operational-data",
  "operational-data_dashboard": "/operational-data/dashboard",
  "return-refund": "/return-refund",
  "sales-analytics": "/sales-analytics/:id",
  "tiksage-dashboard": "/tiksage-dashboard",
  "tiksage-dashboard_data-overview": "/tiksage-dashboard/data-overview",
  "tiksage-dashboard_invoice-create": "/tiksage-dashboard/invoice-create/:id",
  "tiksage-dashboard_shop-commission": "/tiksage-dashboard/shop-commission",
  "tools": "/tools",
  "tools_creator-export": "/tools/creator-export",
  "tools_index": "/tools/index",
  "tools_product-video-scraper": "/tools/product-video-scraper",
  "tools_time-convert": "/tools/time-convert",
  "tools_video-download": "/tools/video-download",
  "video-manage": "/video-manage",
  "video-manage_approval-list": "/video-manage/approval-list",
  "video-manage_product-list": "/video-manage/product-list",
  "video-manage_upload-list": "/video-manage/upload-list"
};

/**
 * get route path by route name
 * @param name route name
 */
export function getRoutePath<T extends RouteKey>(name: T) {
  return routeMap[name];
}

/**
 * get route name by route path
 * @param path route path
 */
export function getRouteName(path: RoutePath) {
  const routeEntries = Object.entries(routeMap) as [RouteKey, RoutePath][];

  const routeName: RouteKey | null = routeEntries.find(([, routePath]) => routePath === path)?.[0] || null;

  return routeName;
}
