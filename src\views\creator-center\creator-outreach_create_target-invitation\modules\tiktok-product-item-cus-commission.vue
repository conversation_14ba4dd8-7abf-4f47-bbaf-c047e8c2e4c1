<script setup lang="ts">
import { computed } from 'vue';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { getFallbackImage } from '@/utils/fake-image';
import { NumeralFormat } from '@/enum';

const { VITE_PRODUCT_AVATAR_URL } = import.meta.env;

interface Props {
  isShow?: boolean;
  handleDelete?: (productId: string) => void;
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const product = defineModel('product', {
  type: Object,
  required: true
});

const isShowDelete = computed(() => {
  return props.handleDelete !== undefined;
});

const standardEstCommission = computed(() => {
  let res = '-';
  if (product.value.standardCommissionRate) {
    const { min_price, max_price } = product.value.price;
    res = handleEstCommissionValue(min_price, max_price, product.value.standardCommissionRate);
  }
  return res;
});

const shopAdsEstCommission = computed(() => {
  let res = '-';
  if (product.value.isAdsCommission && product.value.shopAdsCommissionRate) {
    const { min_price, max_price } = product.value.price;
    res = handleEstCommissionValue(min_price, max_price, product.value.shopAdsCommissionRate);
  }
  return res;
});

function handleEstCommissionValue(min: number | null, max: number | null, rate: number) {
  if (min === null || max === null) return '-';
  const minCommission = numberFormat(min * (rate / 100), NumeralFormat.Dollar_TwoDecimals);
  const maxCommission = numberFormat(max * (rate / 100), NumeralFormat.Dollar_TwoDecimals);
  if (minCommission === maxCommission) {
    return minCommission;
  }
  return `${minCommission}~${maxCommission}`;
}

function handleImageUrl(url: string) {
  if (url.includes('https')) {
    return url;
  }

  return `${VITE_PRODUCT_AVATAR_URL}${url}`;
}
</script>

<template>
  <div class="flex gap-4 border rounded-xl p-2">
    <NImage
      class="flex-shrink-0"
      :width="96"
      :height="96"
      :src="handleImageUrl(product.image_url)"
      :fallback-src="getFallbackImage(96, 96)"
    />
    <div class="flex-col justify-center gap-2">
      <NEllipsis :line-clamp="1" :tooltip="{ contentStyle: 'width:400px;' }">
        {{ product.title }}
      </NEllipsis>
      <span class="text-coolgray">ID: {{ product.product_id }}</span>
      <div class="flex items-center gap-2 font-bold">
        <span>
          Price:
          {{
            product.price.max_price == product.price.min_price
              ? `${product.price.min_price_format}${product.price.max_price_format}`
              : `${product.price.min_price_format}~${product.price.max_price_format}`
          }}
        </span>
        <NDivider vertical />
        <div class="flex-y-center gap-4">
          <div class="relative flex-y-center gap-2">
            <span>Standard commission rate:</span>
            <Tip
              description="Creators earn a standard commission on orders made through their showcases, videos, and LIVE videos."
            />
            <NInputNumber
              v-model:value="product.standardCommissionRate"
              class="w-120px"
              :min="1"
              :max="80"
              size="small"
              :disabled="props.isShow"
              :show-button="false"
              :precision="2"
              placeholder="1.00-80.00"
            >
              <template #suffix>%</template>
            </NInputNumber>
            <div class="est-commission">Est. commission: {{ standardEstCommission }}</div>
          </div>
          <NDivider vertical />

          <div class="relative flex-y-center gap-2">
            <span>Shop Ads commission:</span>
            <Tip>
              <template #default>
                <span>
                  Set a custom commission rate only for orders that come from ads. If you use creators' videos as ads
                  without setting this, orders from these ads will earn them either the Shop Ads commission you set in
                  open collaboration, or the standard commission you’ve set in this invitation.
                  <br />
                  The same order won't incur both standard commission and Shop Ads commission at the same time.
                </span>
              </template>
            </Tip>
            <NSwitch
              v-model:value="product.isAdsCommission"
              :disabled="props.isShow"
              :checked-value="1"
              :unchecked-value="0"
            ></NSwitch>
            <template v-if="product.isAdsCommission">
              <NInputNumber
                v-model:value="product.shopAdsCommissionRate"
                class="w-120px"
                size="small"
                :min="1"
                :max="80"
                :disabled="props.isShow"
                :show-button="false"
                :precision="2"
                placeholder="1.00-80.00"
              >
                <template #suffix>%</template>
              </NInputNumber>
              <div class="est-commission">Est. commission: {{ shopAdsEstCommission }}</div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div v-if="isShowDelete" class="min-w-100px flex flex-1 items-center justify-end">
      <ButtonIcon
        secondary
        icon="solar:trash-bin-minimalistic-linear"
        @click="handleDelete && handleDelete(product.product_id)"
      ></ButtonIcon>
    </div>
  </div>
  <!--
 <NFormItem label-placement="left" label-style="display:flex; align-items:center;font-weight:bold;">
          <template #label>
            <div class="h-full flex-y-center gap-2">
              <span>Shop Ads commission: </span>
              <Tip>
                <template #default>
                  <span>
                    Set a custom commission rate only for orders that come from ads. If you use creators' videos as ads
                    without setting this, orders from these ads will earn them either the Shop Ads commission you set in
                    open collaboration, or the standard commission you’ve set in this invitation.
                    <br />
                    The same order won't incur both standard commission and Shop Ads commission at the same time.
                  </span>
                </template>
              </Tip>
            </div>
          </template>
          <div class="flex-y-center gap-2">
            <NSwitch v-model:value="canSetAdsRate"></NSwitch>
            <NInputNumber
              v-if="canSetAdsRate"
              class="w-120px"
              size="small"
              :show-button="false"
              :precision="2"
              placeholder="1.00-80.00"
            >
              <template #suffix> % </template>
            </NInputNumber>
          </div>
          <template #feedback>
            <span class="text-xs">Est. commission: -</span>
          </template>
        </NFormItem>
--></template>

<style scoped lang="scss">
.est-commission {
  @apply absolute top-[-20px] right-0 min-w-120px text-xs text-coolgray;
}
</style>
