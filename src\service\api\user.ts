import { request } from '../request';

export function fetchUpdateUserIndicators(data: Api.User.UserIndicatorsParams) {
  return request({
    url: '/user/updateUserIndicators',
    method: 'post',
    data
  });
}

export function fetchChangePassword(data: Api.User.ChangePasswordParams) {
  return request({
    url: '/user/changePassword',
    method: 'put',
    data
  });
}

export function fetchGetUserShop(userId: number) {
  return request<Api.User.Shop[]>({
    url: '/user/getUserShop',
    method: 'get',
    params: {
      userId
    }
  });
}

export function fetchGetOwnerShop() {
  return request<Api.User.Shop[]>({
    url: '/user/getOwnerShop',
    method: 'get'
  });
}

export function fetchDeleteOwnerShop(id: number) {
  return request({
    url: '/ownerShop/delete',
    method: 'post',
    params: { id }
  });
}

export function fetchBindShop(userId: number, shopIds: number[]) {
  return request({
    url: '/user/bindShop',
    method: 'post',
    params: { userId },
    data: shopIds
  });
}

export function fetchGetUserShopListByDashboard(page?: 'dashboard_base' | 'dashboard_sub-brand') {
  return request<Api.User.UserShop[]>({
    url: '/user/getUserShopList',
    method: 'get',
    params: { page }
  });
}

export function fetchGetUserShopListByPage(params: Api.User.UserShopSearchParams) {
  return request<Api.User.UserShopListResponse>({
    url: '/user/getUserShopPage',
    method: 'get',
    params
  });
}
