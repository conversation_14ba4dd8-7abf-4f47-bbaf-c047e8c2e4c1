<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-28 10:37:08
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-08 16:24:49
 * @FilePath: \tiksage-frontend\src\views\video-manage\list\index.vue
 * @Description: upload-list
-->
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import dayjs from 'dayjs';
import { delay } from 'lodash-es';
import { fetchDeleteVideo, fetchGetVideoApprovalList } from '@/service/api';
import { TimeFormat } from '@/enum';
import VideoCard from './modules/video-card.vue';
import TimeVerticalBar from './modules/time-vertical-bar.vue';
import CreateVideoModel from './modules/create-video-model.vue';
import VideoInfoDrawer from './modules/video-info-drawer.vue';
import Search from './modules/search.vue';

enum Status {
  PENDING,
  APPROVALED,
  REJECTED
}

const [createShow, toggleCreateShow] = useToggle(false);
const [infoShow, toggleInfoShow] = useToggle(false);
const [loading, toggleLoading] = useToggle(false);

const showData = ref<Api.VideoManage.VideoApprovalListResponse[]>();
const videoId = ref<number>();

const searchParams = ref<Api.VideoManage.VideoApprovalListSearchParams>({
  status: Status.PENDING
});

async function initData() {
  toggleLoading(true);
  const { data, error } = await fetchGetVideoApprovalList(searchParams.value);
  if (!error) {
    showData.value = data;
  }
  delay(() => {
    toggleLoading(false);
  }, 500);
}

function handleShowInfo(id: number) {
  videoId.value = id;
  toggleInfoShow(true);
}

async function handleDelete(id: number) {
  const { error } = await fetchDeleteVideo(id);

  if (!error) {
    window.$message?.success('Delete Success.');
    initData();
  }
}

function handleSearchChange(value: { creatorName?: string; product?: string }) {
  searchParams.value = { ...searchParams.value, ...value };
}

watch(
  () => searchParams.value,
  () => {
    initData();
  },
  {
    deep: true,
    immediate: true
  }
);
</script>

<template>
  <NFlex vertical :size="16">
    <NCard
      class="min-h-400px flex-1 card-wrapper"
      content-class="flex-col gap-16px"
      :bordered="false"
      title="Video List"
    >
      <template #header-extra>
        <Search @change="handleSearchChange" />
      </template>
      <NTabs v-model:value="searchParams.status">
        <NTab label="Pending Review" :name="Status.PENDING"></NTab>
        <NTab label="Approved" :name="Status.APPROVALED"></NTab>
        <NTab label="Rejected" :name="Status.REJECTED"></NTab>
      </NTabs>
      <NSpin class="h-full" content-class="flex-col" :show="loading">
        <NGrid v-if="!showData?.length" :cols="6" :x-gap="16" :y-gap="16" item-responsive responsive="screen">
          <NGi>
            <NFlex
              class="h-182px border-4px rounded-xl text-gray hover:cursor-pointer hover:border-primary-300 hover:border-dashed hover:color-primary"
              vertical
              justify="center"
              align="center"
              @click="toggleCreateShow(true)"
            >
              <SvgIcon class="text-3xl" icon="tabler-plus" />
              <span>Upload New Video</span>
            </NFlex>
          </NGi>
        </NGrid>
        <TimeVerticalBar v-for="(monthData, idx) in showData" :key="monthData.month">
          <template #title>
            <span>{{ dayjs(monthData.month).format(TimeFormat.US_DATE_NO_DAY) }}</span>
          </template>
          <NGrid :cols="6" :x-gap="16" :y-gap="16" item-responsive responsive="screen">
            <NGi v-if="idx === 0">
              <NFlex
                class="h-182px border-4px rounded-xl text-gray hover:cursor-pointer hover:border-primary-300 hover:border-dashed hover:color-primary"
                vertical
                justify="center"
                align="center"
                @click="toggleCreateShow(true)"
              >
                <SvgIcon class="text-3xl" icon="tabler-plus" />
                <span>Upload New Video</span>
              </NFlex>
            </NGi>
            <NGi v-for="item in monthData.listVideoApproval" :key="item.id">
              <VideoCard class="h-182px" :data="item" deletable @view="handleShowInfo" @delete="handleDelete" />
            </NGi>
          </NGrid>
        </TimeVerticalBar>
      </NSpin>
    </NCard>
    <CreateVideoModel v-model:show="createShow" @upload="initData()" />
    <VideoInfoDrawer :id="videoId" v-model:show="infoShow" role="operator" />
  </NFlex>
</template>

<style scoped></style>
