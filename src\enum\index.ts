/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-05 11:03:34
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-21 11:58:20
 * @FilePath: \tiksage-frontend\src\enum\index.ts
 * @Description: store id
 */
export enum SetupStoreId {
  App = 'app-store',
  Theme = 'theme-store',
  Auth = 'auth-store',
  Route = 'route-store',
  Tab = 'tab-store',
  Dashboard = 'dashboard-store',
  User = 'user-store',
  Dictionary = 'dictionary-store',
  Tag = 'tag-store',
  BrandBreakDown = 'brand-breakdown-store'
}

export enum NumeralFormat {
  Dollar = '$0.[00]a',
  Dollar_TwoDecimals = '$0.00a',
  Real_Dollar = '$0,00.00',
  Number = '0.[00]a',
  Real_Number = '0,00',
  Percent = '0.[00]%',
  PlusPercent = '+0.[00]%',
  PlusPercent_NoDecimals = '+0a%'
}

export enum SourceType {
  TikTok = 0,
  Other = 1
}

export enum TaskStatus {
  NEW = 0, // create but no running
  RUNNING = 10, // create and running
  FAIL = 11, // error but will repeat
  SUCCESS = 20, // successful completion
  SHOP_NOT_FIND = 21, // completed but errors
  CATEGORY_NOT_FIND = 22, // Same as above
  CATEGORY_NOT_SUPPORT = 23 // Same as above
}

export enum PlatformType {
  TIKTOK = 1,
  DOUYIN
}

export enum TimeFormat {
  CN_DATE = 'YYYY-MM-DD',
  US_DATE = 'MMM DD, YYYY',
  US_DATE_SECOND = 'DD/MM/YYYY',
  US_DATE_TIMEZONE = 'MMM DD, YYYY ([UTC]Z)',
  US_DATE_NO_DAY = 'MMM YYYY',
  TIME_24 = 'HH:mm:ss',
  CN_TIME_24_NO_TIMEZONE = 'YYYY-MM-DD HH:mm:ss',
  CN_TIME_24 = 'YYYY-MM-DD HH:mm:ss z',
  US_TIME_24 = 'MMM DD, YYYY HH:mm:ss ([UTC]Z)',
  US_TIME_12 = 'MMM DD, YYYY hh:mm:ss A ([UTC]Z)',
  US_TIME_12_NO_YEAR = 'MM/DD hh:mm:ss A ([UTC]Z)'
}

export enum FilterType {
  MultipleSelect = 'multiple_select',
  Select = 'select',
  RangeSelect = 'range_select',
  MoreThanSelect = 'more_than_select',
  Checkbox = 'checkbox'
}
