<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { divide, multiply } from 'lodash-es';
import { fetchUpdateCommission } from '@/service/api';
import { getFallbackImage } from '@/utils/fake-image';

const { VITE_SHOP_AVATAR_URL } = import.meta.env;

interface Props {
  commissionOptions: Api.Dictionary.DictionaryItem<number>[] | undefined;
  selectedData: Api.TikSageDashboard.ShopCommission | undefined;
}

const props = defineProps<Props>();

const model = ref<Api.TikSageDashboard.ShopCommission>(createDefaultModel());

const lockInPeriod = computed<[string, string] | null>(() => {
  const { startDate, endDate } = model.value.introductoryFee;
  if (!startDate && !endDate) return null;
  return [startDate!, endDate!];
});

const show = defineModel<boolean>('show', {
  required: true
});

const commissionRadios = computed(() => {
  return props.commissionOptions?.map(item => {
    return {
      label: item.name,
      value: item.code
    };
  });
});

// Convert percentage to decimal (for example: 15% -> 0.15)
function percentToDecimal(value: number): number {
  return Number(divide(value, 100).toFixed(4));
}

// Convert decimals to percentages (for example: 0.15 -> 15)
function decimalToPercent(value: number): number {
  return Number(multiply(value, 100).toFixed(2));
}

/**
 * Convert commission values between percentage and decimal
 *
 * @param commissions Commission Array
 * @param type Convert type - 'toPercent': to percentage, 'toDecimal': to decimal
 * @returns Converted commission array
 */
function convertCommissionValues(
  commissions: Api.TikSageDashboard.CommissionTier[],
  type: 'toPercent' | 'toDecimal'
): Api.TikSageDashboard.CommissionTier[] {
  return commissions.map(commission => {
    if (commission.commissionType !== 2) return commission;

    return {
      ...commission,
      commissionValue:
        type === 'toPercent'
          ? decimalToPercent(commission.commissionValue)
          : percentToDecimal(commission.commissionValue)
    };
  });
}

function handleCreateLadder(index: number) {
  const prevMax = model.value.commissions[index - 1]?.maxGmv;

  const newLadder = {
    minGmv: prevMax || null,
    maxGmv: null,
    commissionType: model.value.commissions[index - 1]?.commissionType || 1,
    commissionValue: 0
  };
  return newLadder;
}

function createDefaultModel(): Api.TikSageDashboard.ShopCommission {
  return {
    shopId: 0,
    avatar: '',
    avatarLocal: '',
    shopName: '',
    shopCode: '',
    oecSellerId: '',
    dashboardRoute: '',
    commissions: [
      {
        minGmv: 0,
        maxGmv: null,
        commissionType: 1,
        commissionValue: 0
      }
    ],
    invoiceCount: 0,
    isEnabled: 1,
    dueDateOffset: 30,
    introductoryFee: {
      amount: 0,
      startDate: '',
      endDate: ''
    }
  };
}

// 添加验证函数
function validateCommissions(commissions: Api.TikSageDashboard.CommissionTier[]): boolean {
  for (let i = 0; i < commissions.length; i += 1) {
    const commission = commissions[i];

    // 验证必填字段
    if (commission.commissionValue === null || commission.commissionValue === undefined) {
      window.$message?.error(`Tier ${i + 1}: Commission value is required`);
      return false;
    }

    // 验证commission type
    if (!commission.commissionType) {
      window.$message?.error(`Tier ${i + 1}: Commission type is required`);
      return false;
    }

    // 验证min/max
    if (i > 0) {
      const prevMax = commissions[i - 1].maxGmv;
      if (prevMax === null || prevMax === undefined) {
        window.$message?.error(`Tier ${i}: Max amount is required`);
        return false;
      }

      if (commission.minGmv !== prevMax) {
        window.$message?.error(`Tier ${i + 1}: Min amount must equal to previous tier's max amount`);
        return false;
      }
    }

    // 验证max > min (除了最后一个阶梯)
    if (i < commissions.length - 1) {
      if (commission.maxGmv === null || commission.maxGmv === undefined) {
        window.$message?.error(`Tier ${i + 1}: Max amount is required`);
        return false;
      }

      if (commission.maxGmv <= commission.minGmv) {
        window.$message?.error(`Tier ${i + 1}: Max amount must be greater than min amount`);
        return false;
      }
    }
  }

  return true;
}

async function handleUpdate() {
  // 验证commission设置
  if (!validateCommissions(model.value.commissions)) {
    return;
  }

  const { shopId, commissions, dueDateOffset, introductoryFee } = model.value;
  const params = {
    shopId,
    commissions: convertCommissionValues(commissions, 'toDecimal'),
    dueDateOffset,
    introductoryFee
  };

  const { error } = await fetchUpdateCommission(params);
  if (!error) {
    window.$message?.success('Update Successfully.');
    show.value = false;
  }
}

function handleUpdateLockInPeriod(value: [string, string]) {
  model.value.introductoryFee!.startDate = value[0];
  model.value.introductoryFee!.endDate = value[1];
}

function handleCancel() {
  model.value = createDefaultModel();
  show.value = false;
}

function handleAllowInput(value: string) {
  if (!value) return true;
  return /^\d*\.?\d{0,2}$/.test(value);
}

watch(
  () => show.value,
  newVal => {
    if (newVal) {
      props.selectedData &&
        (model.value = {
          ...props.selectedData,
          commissions: props.selectedData.commissions
            ? convertCommissionValues(props.selectedData.commissions, 'toPercent')
            : [
                {
                  minGmv: 0,
                  maxGmv: null,
                  commissionType: 1,
                  commissionValue: 0
                }
              ]
        });
      if (!model.value.introductoryFee) {
        model.value.introductoryFee = {
          amount: 0,
          startDate: null,
          endDate: null
        };
      }
    }
  }
);

watch(
  () => model.value.commissions,
  newCommissions => {
    // 跳过空数组
    if (!newCommissions?.length) return;

    // 更新每个阶梯的 min 和 max
    newCommissions.forEach((commission, index) => {
      // 第一个阶梯的 minGmv 始终为 0
      if (index === 0) {
        commission.minGmv = 0;
      } else {
        // 其他阶梯的 minGmv 继承自前一个阶梯的 maxGmv
        const prevMax = newCommissions[index - 1]?.maxGmv;
        // 只在前一个阶梯的maxGmv存在时更新当前阶梯的minGmv
        if (prevMax !== null && prevMax !== undefined) {
          commission.minGmv = prevMax;
        }
      }
    });
  },
  {
    deep: true
  }
);

const tipContent = `Commission structure supports two types of rates:

1. Fixed Amount
• A flat fee charged per transaction
• Example: $10 per order regardless of order value

2. Revenue Share Rate
• A percentage of Gross Merchandise Value (GMV)
• Example: 15% of total transaction value

3. Tiered Commission Structure
• Multiple tiers based on GMV thresholds
• Each tier can have its own rate structure
• Example:
  - $0-1,000: $10 per order
  - $1,000-5,000: 10% revenue share
  - $5,000+: 15% revenue share

Note: Each tier's minimum GMV automatically inherits the previous tier's maximum threshold`;
</script>

<template>
  <NDrawer v-model:show="show" width="400px">
    <NDrawerContent closable title="Edit Shop Commission">
      <NForm :model="model">
        <NFormItem>
          <div class="flex-y-center gap-4">
            <NAvatar
              size="large"
              :src="VITE_SHOP_AVATAR_URL + model?.avatarLocal"
              :fallback-src="getFallbackImage(60, 60)"
              round
            ></NAvatar>
            <span class="text-base font-bold">{{ model?.shopName || '-' }}</span>
          </div>
        </NFormItem>
        <NFormItem label-style="font-weight:bold;" label="" path="commissionType">
          <template #label>
            <div class="flex-y-center gap-2">
              <span>Commission Structure</span>
              <Tip>
                <template #default>
                  <div class="whitespace-pre-wrap">{{ tipContent }}</div>
                </template>
              </Tip>
            </div>
          </template>
          <NDynamicInput v-model:value="model.commissions" @create="handleCreateLadder">
            <template #default="{ value, index }">
              <div class="flex-col gap-2">
                <div class="flex items-center gap-2">
                  <span class="text-primary font-medium">Tier {{ index + 1 }}</span>
                </div>
                <NGrid :cols="1" :x-gap="8" :y-gap="8">
                  <NGi>
                    <NInputGroup>
                      <NInputNumber
                        v-model:value="value.minGmv"
                        :show-button="false"
                        :precision="2"
                        disabled
                        placeholder="Min Amount"
                        :allow-input="handleAllowInput"
                      >
                        <template #prefix>$</template>
                      </NInputNumber>
                      <NInputGroupLabel>~</NInputGroupLabel>
                      <NInputNumber
                        v-model:value="value.maxGmv"
                        :show-button="false"
                        :precision="2"
                        placeholder="Max Amount"
                        :allow-input="handleAllowInput"
                      >
                        <template #prefix>$</template>
                      </NInputNumber>
                    </NInputGroup>
                    <!--
 <NInput
                      :value="[value.minGmv, value.maxGmv]"
                      pair
                      separator="-"
                      :placeholder="['Min Amount', 'Max Amount']"
                      :allow-input="handleAllowInput"
                      @update:value="([min, max]) => {
                        value.minGmv = min;
                        value.maxGmv = max;
                      }"
                    >
                      <template #prefix>$</template>
                    </NInput>
-->
                  </NGi>
                  <NGi>
                    <NInputGroup>
                      <NSelect
                        v-model:value="value.commissionType"
                        class="max-w-1/2"
                        :options="commissionRadios"
                        placeholder="Commission Type"
                        :consistent-menu-width="false"
                      ></NSelect>
                      <NInputNumber
                        v-model:value="value.commissionValue"
                        class="max-w-1/2"
                        :min="0"
                        :max="value.commissionType === 2 ? 100 : undefined"
                        :show-button="false"
                        :precision="2"
                        placeholder="Commission Value"
                      >
                        <template v-if="value.commissionType === 1" #prefix>$</template>
                        <template v-if="value.commissionType === 2" #suffix>%</template>
                      </NInputNumber>
                    </NInputGroup>
                  </NGi>
                </NGrid>
              </div>
            </template>
            <template #action="{ index, create, remove }">
              <div class="ml-2 flex-col-center">
                <NButtonGroup vertical>
                  <NButton round size="small" class="text-primary" @click="() => create(index)">+</NButton>
                  <NButton
                    round
                    size="small"
                    class="text-warning"
                    :disabled="model.commissions.length <= 1"
                    @click="() => remove(index)"
                  >
                    -
                  </NButton>
                </NButtonGroup>
              </div>
            </template>
          </NDynamicInput>
        </NFormItem>
        <NDivider />
        <NFormItem label-style="font-weight:bold;" label="Lock-in Period">
          <NDatePicker
            type="daterange"
            date-format="yyyy-MM-dd"
            :formatted-value="lockInPeriod"
            @update:formatted-value="handleUpdateLockInPeriod"
          />
        </NFormItem>
        <NFormItem label-style="font-weight:bold;" label="Minimum Guaranteed Amount">
          <NInputNumber
            v-model:value="model.introductoryFee!.amount"
            :min="0"
            :precision="2"
            placeholder="Minimum Guaranteed Amount"
          >
            <template #prefix>$</template>
          </NInputNumber>
        </NFormItem>
        <NDivider />
        <NFormItem label-style="font-weight:bold;" label="Payment Terms" path="dueDateOffset">
          <NInputNumber v-model:value="model.dueDateOffset" placeholder="Input payment terms in days">
            <template #suffix>days</template>
          </NInputNumber>
          <template #feedback>Number of days until payment is due</template>
        </NFormItem>
      </NForm>
      <template #footer>
        <div class="flex justify-end gap-4">
          <NButton type="default" @click="handleCancel">Cancel</NButton>
          <NButton type="primary" @click="handleUpdate">Save</NButton>
        </div>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
