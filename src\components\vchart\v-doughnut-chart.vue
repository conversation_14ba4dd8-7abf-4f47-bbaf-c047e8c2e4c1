<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-09-19 11:24:39
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-05 11:44:44
 * @FilePath: \tiksage-frontend\src\components\vchart\v-doughnut-chart.vue
 * @Description: visactor-doughnut-chart
-->
<script setup lang="ts">
import { onBeforeUnmount, onMounted, onUpdated, ref } from 'vue';
import { merge } from 'lodash-es';
import VChart from '@visactor/vchart';
import { theme } from '@/constants/visactor-vchart';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';

interface Props {
  chartOptions?: Visactor.VChart.IPieChartSpec;
}
const props = defineProps<Props>();

let chart: Visactor.VChart.IVChart;

const chartContainer = ref<HTMLDivElement>();

const { numberFormat } = useNumberFormat();

function parseSpec(chartProps: Props) {
  const { chartOptions } = chartProps;
  const baseOptions: Visactor.VChart.IPieChartSpec = {
    type: 'pie',
    // data: {
    //   values: [
    //     { type: 'oxygen', value: '46.60' },
    //     { type: 'silicon', value: '27.72' },
    //     { type: 'aluminum', value: '8.13' },
    //     { type: 'iron', value: '5' },
    //     { type: 'calcium', value: '3.63' },
    //     { type: 'sodium', value: '2.83' },
    //     { type: 'potassium', value: '2.59' },
    //     { type: 'others', value: '3.5' }
    //   ]
    // },
    outerRadius: 0.8,
    innerRadius: 0.5,
    layoutRadius: layoutRect => {
      const { width, height } = layoutRect;

      return Math.min(width, height) / 2.2;
    },
    padAngle: 0.6,
    valueField: 'value',
    categoryField: 'type',
    pie: {
      style: {
        cornerRadius: 10
      },
      state: {
        hover: {
          outerRadius: 0.85,
          stroke: '#000',
          lineWidth: 1
        },
        selected: {
          outerRadius: 0.85,
          stroke: '#000',
          lineWidth: 1
        }
      }
    },
    legends: {
      visible: true,
      orient: 'top'
    },
    label: {
      visible: true
    },
    tooltip: {
      mark: {
        title: {
          visible: false
        },
        content: [
          {
            key: v => {
              return v ? (v.type as string) : '';
            },
            value: datum => {
              return `${datum && numberFormat(datum.value, datum?.unit === '$' ? NumeralFormat.Dollar : NumeralFormat.Number)} (${datum && datum._percent_}%)`; // eslint-disable-line no-underscore-dangle
            }
          }
        ]
      }
    },
    emptyPlaceholder: {
      showEmptyCircle: true,
      emptyCircle: {
        style: {
          innerRadius: 0.5,
          fill: '#F4F5F5'
        }
      }
    },
    ...theme
    // indicator: {
    //   visible: true,
    //   trigger: 'select',
    //   limitRatio: 0.6,
    //   title: {
    //     autoFit: true,
    //     style: {
    //       text: datum => {
    //         return datum ? datum['type'] : 'Total';
    //       }
    //     }
    //   },

    //   content: [
    //     {
    //       style: {
    //         fontSize: 16,
    //         text: datum => {
    //           let total = 0;
    //           if (chartOptions) {
    //             const { data } = chartOptions;
    //             console.log(data);
    //             total = (data as any).values.reduce((p: any, c: any) => p.value + c.value, 0);
    //           }
    //           return datum ? datum['value'] : total;
    //         }
    //       }
    //     }
    //   ]
    // }
  };
  return chartOptions ? merge(baseOptions, chartProps.chartOptions) : baseOptions;
}

function createOrUpdateChart(chartProps: Props) {
  if (chartContainer.value && !chart) {
    chart = new VChart(parseSpec(chartProps), {
      dom: chartContainer.value
    });

    chart.renderSync();
    return true;
  } else if (chart) {
    chart.updateSpec(parseSpec(chartProps));
    chart.renderSync();

    return true;
  }
  return false;
}

onMounted(() => {
  createOrUpdateChart(props);
});

onUpdated(() => {
  createOrUpdateChart(props);
});

onBeforeUnmount(() => {
  if (chart) {
    chart.release();
  }
});
</script>

<template>
  <div ref="chartContainer" class="h-400px w-full"></div>
</template>

<style scoped></style>
