<script setup lang="ts">
import AccountManage from './modules/account-manage.vue';
import TemplateManage from './modules/template-manage.vue';
import SendManage from './modules/mass-send-manage.vue';
</script>

<template>
  <div class="flex-col gap-16px">
    <NCard class="card-wrapper" :bordered="false" title="Creator Outreach Settings Center">
      <template #header-extra>
        <ButtonBack back-key="creator-center_creator-outreach_history_email" />
      </template>
    </NCard>
    <AccountManage />
    <TemplateManage />
    <SendManage />
  </div>
</template>

<style scoped></style>
