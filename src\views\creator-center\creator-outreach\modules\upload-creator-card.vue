<script setup lang="ts">
import { computed, ref } from 'vue';
import { useToggle } from '@vueuse/core';
import type { UploadCustomRequestOptions, UploadFileInfo } from 'naive-ui';
import { fetchDownloadErrorCreatorDataByExcel, fetchUploadCreatorDataByExcel } from '@/service/api';
import { downloadFile } from '@/utils/download';

interface Props {
  type: 'email' | 'tiktok';
}

const props = defineProps<Props>();

const templateURL = computed(() => {
  let res = '';
  if (props.type === 'email') {
    res = 'https://product.tiksage.com/images/template/creator_collaboration_template1.0.xlsx';
  }
  if (props.type === 'tiktok') {
    res = 'https://product.tiksage.com/images/template/send_message_creator_template.xlsx';
  }
  return res;
});

interface Emits {
  (e: 'change', value: Api.CreatorNetwork.CreatorInfo[]): void;
}

const emit = defineEmits<Emits>();

function handleVideoLimit(data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) {
  if (!data.file.type?.includes('sheet')) {
    window.$message?.warning('Upload a Excel in xlsx format.');
    return false;
  }

  const fileSize = data.file.file?.size;
  if (fileSize && fileSize > 1024 * 1024 * 100) {
    window.$message?.warning("The file you're trying to upload is too large. Maximum allowed size is 100MB.");
    return false;
  }

  return true;
}

function handleDownloadTemplate() {
  window.open(templateURL.value, '_blank');
}

const [showResult, toggleShowResult] = useToggle(false);

const successData = ref<Api.CreatorNetwork.CreatorInfo[]>([]);
const failData = ref<Api.CreatorNetwork.CreatorInfo[]>([]);

const fileName = ref<string>();

type Alert = {
  type: 'error' | 'default' | 'info' | 'success' | 'warning';
  count: number;
  title: string;
  list?: string[];
};
const alerts = computed<Alert[]>(() => [
  {
    type: 'default',
    count: successData.value.length + failData.value.length,
    title: 'Imported'
  },
  {
    type: 'success',
    count: successData.value.length,
    title: 'Successfully import',
    list: successData.value.map(v => v.creatorId)
  },
  {
    type: 'error',
    count: failData.value.length,
    title: 'Failed to import',
    list: failData.value.map(v => v.creatorId)
  }
]);

const customRequest = async ({ file, onFinish, onError, onProgress }: UploadCustomRequestOptions) => {
  fileName.value = file.file?.name;

  const { data: creatorData, error } = await fetchUploadCreatorDataByExcel({ file: file.file as File }, props.type);
  if (error) {
    onError();
  } else {
    successData.value = creatorData.successList;
    failData.value = creatorData.failList;
    toggleShowResult(true);
    emit('change', creatorData.successList);
    onProgress({ percent: 100 });
  }
  onFinish();
};

async function handleDownloadFailData() {
  if (failData.value.length === 0) return;
  const { data, error } = await fetchDownloadErrorCreatorDataByExcel(failData.value);
  if (!error) {
    downloadFile(data, 'xlsx', 'error-creator-data');
  } else {
    window.$message?.error('Failed to download');
  }
}

function handleReUpload() {
  successData.value = [];
  failData.value = [];
  emit('change', []);

  toggleShowResult(false);
}

defineExpose({
  reset: handleReUpload
});
</script>

<template>
  <div class="in-out w-full flex-col transition-all duration-300">
    <NUpload
      v-if="!showResult"
      ref="uploadRef"
      directory-dnd
      :file-list="undefined"
      :max="1"
      :custom-request="customRequest"
      @before-upload="handleVideoLimit"
    >
      <NUploadDragger>
        <NFlex class="h-220px" vertical justify="center" align="center" :size="16">
          <SvgIcon class="text-4xl text-primary" icon="solar:cloud-upload-bold-duotone" />
          <NText>Drag & drop your files here or choose files.</NText>
          <NText>
            *100 MB max file size. (
            <NButton text type="primary" @click.stop="handleDownloadTemplate">Template</NButton>
            )*
          </NText>
        </NFlex>
      </NUploadDragger>
    </NUpload>
    <NDialog
      v-else
      class="in-out h-270px w-full overflow-hidden border-1px card-wrapper transition-all duration-300"
      content-class="flex-col gap-16px"
      title="Import Results"
      :closable="false"
    >
      <div class="flex-col gap-1">
        <NAlert v-for="alert in alerts" :key="alert.title" class="w-full" :type="alert.type">
          <template #header>
            <div class="flex justify-between">
              <span>{{ alert.title }}</span>
              <div class="flex items-center gap-2">
                <span class="text-coolgray">{{ alert.count }} records</span>
                <NPopover v-if="alert.type !== 'default'" :disabled="!alert.count">
                  <template #trigger>
                    <NButton :disabled="!alert.count" type="primary" text size="small">Details</NButton>
                  </template>
                  <div class="flex-col gap-2">
                    <NTag v-for="item in alert.list" :key="item" size="small" :bordered="false" :type="alert.type">
                      {{ item }}
                    </NTag>
                  </div>
                </NPopover>
              </div>
            </div>
          </template>
        </NAlert>
      </div>
      <div class="flex flex-row-reverse justify-between">
        <NPopconfirm @positive-click="handleReUpload">
          <template #trigger>
            <NButton ghost size="small" type="primary">
              <template #icon><icon-tabler:upload /></template>
              Re-upload
            </NButton>
          </template>
          After clicking, the data will be lost permanently.
          <br />
          Are you sure to upload it again?
        </NPopconfirm>
        <NButton v-if="failData.length > 0" ghost size="small" @click="handleDownloadFailData">
          <template #icon>
            <icon-solar:download-linear />
          </template>
          Download error records
        </NButton>
      </div>
    </NDialog>
  </div>
</template>

<style scoped></style>
