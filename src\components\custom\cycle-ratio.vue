<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-23 10:59:44
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-25 14:52:26
 * @FilePath: \tiksage-frontend\src\components\custom\cycle-ratio.vue
 * @Description: cycle-ratio
-->
<script setup lang="ts">
import { computed } from 'vue';
import { useNumberFormat } from '@/hooks/custom/numberFormat';

interface Props {
  percent: string;
  text?: boolean;
  size?: 'small' | 'medium' | 'large';
}

type Status = 'success' | 'error' | 'default';

const props = withDefaults(defineProps<Props>(), {
  percent: '0%',
  text: false,
  size: 'medium'
});

const { unFormat } = useNumberFormat();

const status = computed<Status>(() => {
  if (!unFormat(props.percent)) return 'default';
  const isPositive = props.percent.startsWith('+') || !props.percent.startsWith('-');
  return isPositive ? 'success' : 'error';
});

const data = computed<string>(() => {
  return props.percent.replace(/[+-]/g, '');
});
</script>

<template>
  <NText
    v-if="text"
    :class="{
      'text-xs': size === 'small',
      'text-lg': size === 'large'
    }"
    :type="status"
  >
    <icon-tabler:trending-up v-if="status === 'success'" />
    <icon-tabler:trending-down v-else-if="status === 'error'" />
    <icon-tabler:minus v-else />
    {{ data }}
  </NText>
  <NTag v-else :bordered="false" :type="status" round :size="size">
    <template #icon>
      <icon-tabler:trending-up v-if="status === 'success'" />
      <icon-tabler:trending-down v-else-if="status === 'error'" />
      <icon-tabler:minus v-else />
    </template>
    {{ data }}
  </NTag>
</template>

<style scoped></style>
