<script lang="ts" setup>
import { computed } from 'vue';
import { useAuthStore } from '@/store/modules/auth';
import { useRouterPush } from '@/hooks/common/router';
import { $t } from '@/locales';

defineOptions({ name: 'ExceptionBase' });

type ExceptionType = '403' | '404' | '500';

interface Props {
  /**
   * Exception type
   *
   * - 403: no permission
   * - 404: not found
   * - 500: service error
   */
  type: ExceptionType;
}

const props = defineProps<Props>();

const { routerPushByKey } = useRouterPush();

const iconMap: Record<ExceptionType, string> = {
  '403': 'no-permission',
  '404': 'not-found',
  '500': 'service-error'
};

const icon = computed(() => iconMap[props.type]);

const authStore = useAuthStore();

function handleLogOut() {
  authStore.resetStore();
  routerPushByKey('login');
}
</script>

<template>
  <div class="size-full min-h-520px flex-col-center gap-24px overflow-hidden">
    <div class="flex text-400px text-primary">
      <SvgIcon :local-icon="icon" />
    </div>
    <div class="flex-center gap-16px">
      <NButton type="primary" @click="routerPushByKey('root')">{{ $t('common.backToHome') }}</NButton>
      <NButton type="primary" @click="handleLogOut">Log out</NButton>
    </div>
  </div>
</template>

<style scoped></style>
