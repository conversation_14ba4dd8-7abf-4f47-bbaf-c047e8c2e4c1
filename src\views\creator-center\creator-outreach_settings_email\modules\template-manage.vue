<script setup lang="tsx">
import { ref } from 'vue';
import { NButton } from 'naive-ui';
import dayjs from 'dayjs';
import { fetchDeleteEmailTemplate, fetchGetEmailTemplateList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { TimeFormat } from '@/enum';
import ButtonIcon from '@/components/custom/button-icon.vue';
import ButtonConfirm from '@/components/custom/button-confirm.vue';
import TemplateCreateDrawer from './template-create-drawer.vue';

const show = ref(false);

const { data, getData, loading, columns, updateSearchParams, pagination } = useTable({
  apiFn: fetchGetEmailTemplateList,
  apiParams: {
    current: 1,
    size: 10
  },
  columns() {
    return [
      {
        title: 'Name',
        key: 'name',
        width: 350,
        ellipsis: {
          tooltip: {
            contentStyle: 'max-width:400px'
          }
        }
      },
      {
        title: 'Subject',
        key: 'title',
        ellipsis: {
          tooltip: {
            contentStyle: 'max-width:400px'
          }
        }
      },
      {
        title: 'Updated Time',
        key: 'updateTime',
        align: 'center',
        width: 300,
        render(row) {
          return dayjs(row.updateTime).tz('Etc/GMT+8').format(TimeFormat.US_TIME_24);
        }
      },
      {
        key: 'operate',
        width: 120,
        render(rowData) {
          return (
            <div class="flex items-center justify-end gap-8px">
              <ButtonIcon text quaternary={false} icon="solar:eye-linear" onClick={() => handlePreview(rowData)} />
              <ButtonIcon text quaternary={false} icon="solar:pen-2-linear" onClick={() => handleEdit(rowData)} />
              <ButtonConfirm
                icon="solar:trash-bin-2-linear"
                confirmText="Are you sure you want to delete?"
                buttonProps={{ text: true, size: 'large', type: 'error' }}
                onPositiveClick={() => handleDelete(rowData.id)}
              />
            </div>
          );
        }
      }
    ];
  }
});

const type = ref<'add' | 'edit' | 'view'>('add');

const editorData = ref<Api.CreatorNetwork.EmailTemplate | null>(null);

function handleAdd() {
  type.value = 'add';
  editorData.value = null;
  show.value = true;
}

async function handleDelete(id: number) {
  const { error } = await fetchDeleteEmailTemplate(id);
  if (!error) {
    window.$message?.success('Delete email template succefully.');
    updateSearchParams({ current: 1, size: 10 });
    getData();
  }
}

function handlePreview(rowData: Api.CreatorNetwork.EmailTemplate) {
  type.value = 'view';
  editorData.value = { ...rowData };
  show.value = true;
}

function handleEdit(rowData: Api.CreatorNetwork.EmailTemplate) {
  type.value = 'edit';
  editorData.value = { ...rowData };
  show.value = true;
}

function handleChange() {
  updateSearchParams({ current: 1, size: 10 });
  getData();
}
</script>

<template>
  <NCard class="card-wrapper" segmented :bordered="false" title="Template Management">
    <template #header-extra>
      <NButton secondary type="primary" @click="handleAdd">
        <template #icon>
          <icon-solar:add-circle-linear />
        </template>
        Add Template
      </NButton>
    </template>
    <NDataTable :bordered="false" :loading="loading" remote :columns="columns" :data="data" :pagination="pagination">
      <template #empty>
        <NEmpty description="No email templates available. Please click the top right to add a template.">
          <template #icon>
            <icon-fluent:mail-template-16-regular />
          </template>
        </NEmpty>
      </template>
    </NDataTable>
    <TemplateCreateDrawer v-model:show="show" :type="type" :data="editorData" @change="handleChange" />
  </NCard>
</template>

<style scoped></style>
