<script setup lang="tsx">
import { watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { NAvatar, NButton, NFlex, NText } from 'naive-ui';
import { fetchGetUserList } from '@/service/api';
import { useTable, useTableOperate } from '@/hooks/common/table';
import callbackImg from '@/assets/svg-icon/avatar.svg';
import { $t } from '@/locales';

interface Props {
  getCheckedRowKeys: () => Promise<number[]>;
  submit: (checkedRowKeys: number[] | string[]) => Promise<boolean>;
}

const props = defineProps<Props>();

const show = defineModel<boolean>('show', {
  default: false
});

const [submitLoading, toggleSubmitLoading] = useToggle(false);

const { columns, data, getData, loading } = useTable({
  immediate: false,
  apiFn: fetchGetUserList,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 1000,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    userName: null,
    tel: null,
    email: null,
    status: 0
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },

    {
      key: 'userName',
      title: $t('page.manage.user.userName'),
      align: 'center',
      width: 150,
      render(row) {
        return (
          <NFlex justify="center" align="center">
            <NAvatar round src={row.avatar} fallbackSrc={callbackImg} />
            <NText class="flex-1">{row.userName}</NText>
          </NFlex>
        );
      }
    },
    {
      key: 'email',
      title: $t('page.manage.user.userEmail'),
      align: 'center',
      minWidth: 200
    }
  ]
});

const { checkedRowKeys } = useTableOperate(data, getData);

async function initRoleUserList() {
  const ids = await props.getCheckedRowKeys();

  checkedRowKeys.value = ids as any;
}

async function handleSubmit() {
  toggleSubmitLoading(true);

  const flag = await props.submit(checkedRowKeys.value);
  if (flag) {
    window.$message?.success?.($t('common.modifySuccess'));
    show.value = false;
  }

  toggleSubmitLoading(false);
}

watch(
  () => show.value,
  newVal => {
    if (newVal) {
      initRoleUserList();
      getData();
    }
  }
);
</script>

<template>
  <NModal v-model:show="show" title="User Auth" preset="card" class="w-480px">
    <NDataTable
      v-model:checked-row-keys="checkedRowKeys"
      class="h-500px"
      remote
      flex-height
      :bordered="false"
      :loading="loading"
      :columns="columns"
      :data="data"
      :row-key="row => row.id"
    ></NDataTable>
    <template #action>
      <div class="flex-y-center flex-nowrap justify-between">
        <div class="">{{ checkedRowKeys.length }} / {{ data.length }} Seleceted</div>
        <div class="flex-y-center gap-2">
          <NButton @click="show = false">
            {{ $t('common.cancel') }}
          </NButton>
          <NButton type="primary" :loading="submitLoading" @click="handleSubmit">
            {{ $t('common.confirm') }}
          </NButton>
        </div>
      </div>
    </template>
  </NModal>
</template>

<style scoped></style>
