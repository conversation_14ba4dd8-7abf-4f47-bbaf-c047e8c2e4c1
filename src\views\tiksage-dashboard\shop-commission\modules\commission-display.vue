<script setup lang="ts">
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';

interface Props {
  commissions: Api.TikSageDashboard.CommissionTier[];
}

defineProps<Props>();
const { numberFormat } = useNumberFormat();

function getCommissionTagType(index: number): 'success' | 'warning' | 'info' | 'default' {
  const types: ('success' | 'warning' | 'info' | 'default')[] = ['success', 'warning', 'info', 'default'];
  return types[index] || 'default';
}

function getCommissionDisplay(commission: Api.TikSageDashboard.CommissionTier) {
  const value = commission.commissionValue;
  return commission.commissionType === 1
    ? numberFormat(value, NumeralFormat.Dollar)
    : numberFormat(value, NumeralFormat.Percent);
}

function formatGMV(value: number) {
  return numberFormat(value, NumeralFormat.Dollar);
}

function getCommissionTypeText(type: number) {
  return type === 1 ? 'Fixed Amount' : 'Revenue Share Rate';
}

function getGMVRangeText(commission: Api.TikSageDashboard.CommissionTier, isOnlyTier: boolean = false) {
  if (isOnlyTier) return commission.commissionType === 1 ? 'Base Fixed Amount' : 'Base Rate';

  return commission.maxGmv
    ? `${formatGMV(commission.minGmv)} - ${formatGMV(commission.maxGmv)}`
    : `${formatGMV(commission.minGmv)} +`;
}
</script>

<template>
  <NFlex vertical justify="center" align="center" class="gap-2">
    <NPopover v-for="(commission, index) in commissions" :key="index" trigger="hover" placement="right">
      <template #trigger>
        <NTag
          :bordered="false"
          round
          size="medium"
          :type="getCommissionTagType(index)"
          class="max-w-220px w-full transition-all-200 hover:(transform-translate-y--1px shadow-sm)"
        >
          <div class="flex justify-between gap-2 px-2">
            <div class="truncate">
              {{ getGMVRangeText(commission, commissions.length === 1) }}
            </div>
            <div class="font-bold">
              {{ getCommissionDisplay(commission) }}
            </div>
          </div>
        </NTag>
      </template>
      <div>
        <div class="mb-2 font-medium">Revenue Tier {{ index + 1 }}</div>
        <NFlex vertical class="gap-2">
          <div class="text-gray-400">
            <div>GMV Threshold:</div>
            <div class="text-primary font-medium">
              {{ formatGMV(commission.minGmv) }} -
              {{ commission.maxGmv ? formatGMV(commission.maxGmv) : 'Unlimited' }}
            </div>
          </div>
          <div class="text-gray-400">
            <div>Commission Structure:</div>
            <div class="text-primary font-medium">
              {{ getCommissionTypeText(commission.commissionType) }}
            </div>
          </div>
          <div class="text-gray-400">
            <div>{{ commission.commissionType === 1 ? 'Commission Value:' : 'Commission Rate:' }}</div>
            <div class="text-lg text-primary font-bold">
              {{ getCommissionDisplay(commission) }}
            </div>
          </div>
        </NFlex>
      </div>
    </NPopover>
  </NFlex>
</template>

<style scoped>
:deep(.n-tag__content) {
  width: 100%;
}
</style>
