import { transformRecordToOption } from '@/utils/common';
import { NumeralFormat } from '@/enum';

export const yesOrNoRecord: Record<CommonType.YesOrNo, App.I18n.I18nKey> = {
  Y: 'common.yesOrNo.yes',
  N: 'common.yesOrNo.no'
};

export const yesOrNoOptions = transformRecordToOption(yesOrNoRecord);

export const unitOptions: Record<Api.Auth.UnitType, NumeralFormat> = {
  $: NumeralFormat.Dollar,
  '%': NumeralFormat.Percent
};
