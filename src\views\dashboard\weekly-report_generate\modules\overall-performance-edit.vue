<script setup lang="ts">
import { computed, ref } from 'vue';
import { isNil, pick } from 'lodash-es';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';
import { renderCell } from './shared';

interface Emits {
  (e: 'update', key: string, value: string | number): void;
}

interface Props {
  isShow?: boolean;
  reportData?: Api.WeeklyReport.WeeklyReportData;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const toolbarConfig = ref({
  excludeKeys: ['uploadImage']
});

const { numberFormat } = useNumberFormat();

const keyArr: Array<keyof Api.WeeklyReport.tableData> = [
  'cycleTitle',
  'gmv',
  'orders',
  'totalItems',
  'itemsSold',
  'sampleItems',
  'avgBuyers',
  'aov',
  'affiliateGmv'
];

const totalData = computed(() => {
  const res: Api.WeeklyReport.tableData[] = [];
  if (!props.reportData) return res;
  const { lastMonthData, thisMonthData, lastWeekData, thisWeekData } = props.reportData;

  // Use lodash's pick method to get specific properties from objects based on keyArr
  if (lastMonthData) res.push(pick(lastMonthData, keyArr));
  if (thisMonthData) res.push(pick(thisMonthData, keyArr));
  if (lastWeekData) res.push(pick(lastWeekData, keyArr));
  if (thisWeekData) res.push(pick(thisWeekData, keyArr));

  return res;
});

const descTitle = computed(() => {
  if (!props.reportData) return '1.-';
  return `1.${props.reportData?.monthStr}&last 7 days ${props.reportData?.cycleTitle}`;
});

const totalColumns = computed<any[]>(() => {
  return [
    {
      key: '',
      title: `${props.reportData?.shopName || ''} TikTok Shop Total`,
      align: 'center',
      children: [
        {
          key: 'cycleTitle',
          title: 'Date',
          align: 'center'
        },
        {
          key: 'gmv',
          title: 'GMV',
          align: 'center',
          unit: NumeralFormat.Real_Dollar
        },
        {
          key: 'orders',
          title: 'Orders',
          align: 'center'
        },
        {
          key: 'totalItems',
          title: 'Total items',
          align: 'center'
        },
        {
          key: 'itemsSold',
          title: 'Sold items',
          align: 'center'
        },
        {
          key: 'sampleItems',
          title: 'Sample items',
          align: 'center'
        },
        {
          key: 'avgBuyers',
          title: 'Avg. customers',
          align: 'center'
        },
        {
          key: 'aov',
          title: 'Avg. order value',
          align: 'center',
          unit: NumeralFormat.Real_Dollar
        },
        {
          key: 'affiliateGmv',
          title: 'Affiliates GMV',
          align: 'center',
          unit: NumeralFormat.Real_Dollar
        }
      ]
    }
  ];
});

// Calculate the function on a quarter-on-month basis
function calculateWow(currentValue: number | null, previousValue: number | null) {
  if (isNil(currentValue) || isNil(previousValue)) return '--';
  if (previousValue === 0) return 0;
  return numberFormat((currentValue - previousValue) / previousValue, NumeralFormat.PlusPercent_NoDecimals);
}

// eslint-disable-next-line max-params
function createWowField(
  currentIndex: number,
  previousIndex: number,
  field: keyof Api.WeeklyReport.tableData,
  data: Api.WeeklyReport.tableData[]
) {
  return {
    value: calculateWow(data?.[currentIndex]?.[field] as number, data?.[previousIndex]?.[field] as number)
  };
}

function totalSummary(pageData: typeof totalData.value) {
  // 本周和上周的索引
  const thisWeekIndex = 3;
  const lastWeekIndex = 2;

  return {
    cycleTitle: { value: 'WoW' },
    gmv: createWowField(thisWeekIndex, lastWeekIndex, 'gmv', pageData),
    orders: createWowField(thisWeekIndex, lastWeekIndex, 'orders', pageData),
    totalItems: createWowField(thisWeekIndex, lastWeekIndex, 'totalItems', pageData),
    itemsSold: createWowField(thisWeekIndex, lastWeekIndex, 'itemsSold', pageData),
    sampleItems: createWowField(thisWeekIndex, lastWeekIndex, 'sampleItems', pageData),
    avgBuyers: createWowField(thisWeekIndex, lastWeekIndex, 'avgBuyers', pageData),
    aov: createWowField(thisWeekIndex, lastWeekIndex, 'aov', pageData),
    affiliateGmv: createWowField(thisWeekIndex, lastWeekIndex, 'affiliateGmv', pageData)
  };
}

const subBrandData = computed(() => {
  if (!props.reportData) return [];
  const { lastMonthData, thisMonthData, lastWeekData, thisWeekData } = props.reportData;
  const timeDataArray = [lastMonthData, thisMonthData, lastWeekData, thisWeekData].filter(Boolean);

  return timeDataArray.map(timeData => {
    if (!timeData?.brandMetricsBOList?.length) return { cycleTitle: timeData.cycleTitle };

    // Create base row data object
    const rowData: any = { cycleTitle: timeData.cycleTitle };

    // Add GMV and Items Sold data for all sub-brands
    timeData.brandMetricsBOList.forEach(({ brand, gmv, unitsSold }) => {
      rowData[`${brand}_gmv`] = gmv;
      rowData[`${brand}_items`] = unitsSold;
    });

    return rowData;
  });
});

// Get all unique sub-brand names
const uniqueBrandNames = computed(() => {
  if (!props.reportData) return new Set<string>();
  const { lastMonthData, thisMonthData, lastWeekData, thisWeekData } = props.reportData;
  const brandNames = new Set<string>();

  [lastMonthData, thisMonthData, lastWeekData, thisWeekData]
    .filter(timeData => timeData?.brandMetricsBOList?.length)
    .forEach(timeData => {
      timeData.brandMetricsBOList.forEach(brand => brandNames.add(brand.brand));
    });

  return brandNames;
});

// Calculate month-on-month changes in brand-specific fields
// eslint-disable-next-line max-params
function createBrandWowField(
  thisWeekIndex: number,
  lastWeekIndex: number,
  brandName: string,
  field: string,
  data: any[]
) {
  const thisWeekValue = data?.[thisWeekIndex]?.[`${brandName}_${field}`];
  const lastWeekValue = data?.[lastWeekIndex]?.[`${brandName}_${field}`];
  return {
    value: calculateWow(thisWeekValue, lastWeekValue)
  };
}

// Group sub-brands, with a maximum of 3 brands per group
const groupedBrandNames = computed(() => {
  const brands = Array.from(uniqueBrandNames.value);
  const result: string[][] = [];

  for (let i = 0; i < brands.length; i += 3) {
    result.push(brands.slice(i, i + 3));
  }

  return result;
});

// Generate column configurations for each brand group
const groupedSubBrandColumns = computed(() => {
  return groupedBrandNames.value.map(brandGroup => {
    const columns: any = [
      {
        key: 'cycleTitle',
        title: 'Date',
        align: 'center',
        width: 200
      }
    ];

    // Create GMV and Items Sold columns for each sub-brand
    brandGroup.forEach(brandName => {
      columns.push({
        title: brandName,
        align: 'center',
        width: 300,
        children: [
          {
            key: `${brandName}_gmv`,
            title: 'GMV',
            align: 'center',
            unit: NumeralFormat.Real_Dollar,
            width: 150
          },
          {
            key: `${brandName}_items`,
            title: 'Items sold',
            align: 'center',
            width: 150
          }
        ]
      });
    });

    return columns;
  });
});

// Create summary functions for each brand group
const groupedSubBrandSummaries = computed(() => {
  return groupedBrandNames.value.map(brandGroup => {
    return (pageData: any) => {
      const result: any = {
        cycleTitle: {
          value: 'WoW'
        }
      };

      // Data indexes for this week and last week
      const thisWeekIndex = 3;
      const lastWeekIndex = 2;

      // Calculate week-over-week changes in GMV and Items Sold for each sub-brand
      brandGroup.forEach(brandName => {
        // Calculate week-over-week change in GMV
        result[`${brandName}_gmv`] = createBrandWowField(thisWeekIndex, lastWeekIndex, brandName, 'gmv', pageData);

        // Calculate week-over-week change in Items Sold
        result[`${brandName}_items`] = createBrandWowField(thisWeekIndex, lastWeekIndex, brandName, 'items', pageData);
      });

      return result;
    };
  });
});

function handleUpdateHtml(v: string, key: string) {
  emit('update', key, v);
}
</script>

<template>
  <div class="flex-col gap-4">
    <NFormItem label-style="font-weight: 500;font-size: 16px;" :label="descTitle">
      <div v-if="isShow" class="html-content">
        <div class="html-reset" v-html="reportData?.performanceHtml || ''"></div>
      </div>
      <WangEditor
        v-else
        :value-html="reportData?.performanceHtml || ''"
        :toolbar-config="toolbarConfig"
        @update:value-html="v => handleUpdateHtml(v, 'performanceHtml')"
      ></WangEditor>
    </NFormItem>
    <NDataTable
      striped
      :single-line="false"
      :columns="totalColumns"
      :data="totalData"
      :render-cell="renderCell"
      :summary="totalSummary"
    ></NDataTable>

    <!-- Display sub-brand tables in groups -->
    <div v-for="(columns, index) in groupedSubBrandColumns" :key="index" class="mt-4">
      <NDataTable
        striped
        :single-line="false"
        :columns="columns"
        :data="subBrandData"
        :render-cell="renderCell"
        :summary="groupedSubBrandSummaries[index]"
      ></NDataTable>
    </div>

    <div v-if="isShow" class="html-content">
      <div class="html-reset" v-html="reportData?.performanceHtmlBottom || ''"></div>
    </div>
    <WangEditor
      v-else
      :value-html="reportData?.performanceHtmlBottom || ''"
      :toolbar-config="toolbarConfig"
      @update:value-html="v => handleUpdateHtml(v, 'performanceHtmlBottom')"
    ></WangEditor>
  </div>
</template>

<style scoped lang="scss"></style>
