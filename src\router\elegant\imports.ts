/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "google-auth": () => import("@/views/_builtin/google-auth/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  account_center: () => import("@/views/account/center/index.vue"),
  analysis_diagnosis: () => import("@/views/analysis/diagnosis/index.vue"),
  analysis_list: () => import("@/views/analysis/list/index.vue"),
  analysis_report: () => import("@/views/analysis/report/[id].vue"),
  "category-analysis_category-intelligence": () => import("@/views/category-analysis/category-intelligence/index.vue"),
  "category-analysis_category-leaders": () => import("@/views/category-analysis/category-leaders/index.vue"),
  "creator-center_creator-approval-video-status": () => import("@/views/creator-center/creator-approval-video-status/index.vue"),
  "creator-center_creator-approval": () => import("@/views/creator-center/creator-approval/index.vue"),
  "creator-center_creator-database": () => import("@/views/creator-center/creator-database/index.vue"),
  "creator-center_creator-outreach": () => import("@/views/creator-center/creator-outreach/index.vue"),
  "creator-center_creator-outreach_create_email": () => import("@/views/creator-center/creator-outreach_create_email/index.vue"),
  "creator-center_creator-outreach_create_target-invitation": () => import("@/views/creator-center/creator-outreach_create_target-invitation/index.vue"),
  "creator-center_creator-outreach_create_tiktok": () => import("@/views/creator-center/creator-outreach_create_tiktok/index.vue"),
  "creator-center_creator-outreach_find-creator": () => import("@/views/creator-center/creator-outreach_find-creator/index.vue"),
  "creator-center_creator-outreach_history_email": () => import("@/views/creator-center/creator-outreach_history_email/index.vue"),
  "creator-center_creator-outreach_history_target-invitation": () => import("@/views/creator-center/creator-outreach_history_target-invitation/index.vue"),
  "creator-center_creator-outreach_history_tiktok": () => import("@/views/creator-center/creator-outreach_history_tiktok/index.vue"),
  "creator-center_creator-outreach_settings_email": () => import("@/views/creator-center/creator-outreach_settings_email/index.vue"),
  "creator-center_creator-profiles": () => import("@/views/creator-center/creator-profiles/index.vue"),
  dashboard_base: () => import("@/views/dashboard/base/index.vue"),
  "dashboard_brand-breakdown": () => import("@/views/dashboard/brand-breakdown/index.vue"),
  "dashboard_sub-brand": () => import("@/views/dashboard/sub-brand/index.vue"),
  "dashboard_weekly-report_generate": () => import("@/views/dashboard/weekly-report_generate/index.vue"),
  "dashboard_weekly-report_preview": () => import("@/views/dashboard/weekly-report_preview/index.vue"),
  detail_creator: () => import("@/views/detail/creator/[id].vue"),
  detail_video: () => import("@/views/detail/video/[id].vue"),
  "digital-audit_shop-audit-history": () => import("@/views/digital-audit/shop-audit-history/index.vue"),
  "digital-audit_shop-audit": () => import("@/views/digital-audit/shop-audit/index.vue"),
  manage_common: () => import("@/views/manage/common/index.vue"),
  manage_dictionary: () => import("@/views/manage/dictionary/index.vue"),
  "manage_log-analysis": () => import("@/views/manage/log-analysis/index.vue"),
  manage_menu: () => import("@/views/manage/menu/index.vue"),
  manage_role: () => import("@/views/manage/role/index.vue"),
  manage_shop: () => import("@/views/manage/shop/index.vue"),
  "manage_user-detail": () => import("@/views/manage/user-detail/[id].vue"),
  manage_user: () => import("@/views/manage/user/index.vue"),
  "operational-data_dashboard": () => import("@/views/operational-data/dashboard/index.vue"),
  "return-refund": () => import("@/views/return-refund/index.vue"),
  "sales-analytics": () => import("@/views/sales-analytics/[id].vue"),
  "tiksage-dashboard_data-overview": () => import("@/views/tiksage-dashboard/data-overview/index.vue"),
  "tiksage-dashboard_invoice-create": () => import("@/views/tiksage-dashboard/invoice-create/[id].vue"),
  "tiksage-dashboard_shop-commission": () => import("@/views/tiksage-dashboard/shop-commission/index.vue"),
  "tools_creator-export": () => import("@/views/tools/creator-export/index.vue"),
  tools_index: () => import("@/views/tools/index/index.vue"),
  "tools_product-video-scraper": () => import("@/views/tools/product-video-scraper/index.vue"),
  "tools_time-convert": () => import("@/views/tools/time-convert/index.vue"),
  "tools_video-download": () => import("@/views/tools/video-download/index.vue"),
  "video-manage_approval-list": () => import("@/views/video-manage/approval-list/index.vue"),
  "video-manage_product-list": () => import("@/views/video-manage/product-list/index.vue"),
  "video-manage_upload-list": () => import("@/views/video-manage/upload-list/index.vue"),
};
