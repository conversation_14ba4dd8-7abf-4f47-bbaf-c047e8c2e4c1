<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-05 14:12:38
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-08-05 16:30:56
 * @FilePath: \tiksage-admin\src\layouts\modules\global-logo\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { useThemeStore } from '@/store/modules/theme';

defineOptions({
  name: 'GlobalLogo'
});

interface Props {
  /** Whether to show the title */
  showTitle?: boolean;
}

withDefaults(defineProps<Props>(), {
  showTitle: true
});

const themeStore = useThemeStore();
</script>

<template>
  <RouterLink to="/" class="w-full flex-center nowrap-hidden">
    <SystemLogo class="text-32px text-primary" />
    <!--
 <h2 v-show="showTitle" class="pl-8px text-16px text-primary font-bold transition duration-300 ease-in-out">
      {{ $t('system.title') }}
    </h2> 
-->
    <div v-show="showTitle" class="pl-8px">
      <icon-local-logo-title-dark v-if="themeStore.darkMode" class="h-30px w-180px text-primary" />
      <icon-local-logo-title v-else class="h-24px w-78px text-primary" />
    </div>
  </RouterLink>
</template>

<style scoped></style>
