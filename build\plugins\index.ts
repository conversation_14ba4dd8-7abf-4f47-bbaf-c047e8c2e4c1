/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-25 11:32:12
 * @LastEditors: <PERSON><PERSON>n<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-10-28 17:09:25
 * @FilePath: \tiksage-frontend\build\plugins\index.ts
 * @Description: index
 */
import type { PluginOption } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import progress from 'vite-plugin-progress';
import { setupElegantRouter } from './router';
import { setupUnocss } from './unocss';
import { setupUnplugin } from './unplugin';
import { setupHtmlPlugin } from './html';
import { vite as vidstack } from 'vidstack/plugins';
import { setupDevtoolsPlugin } from './devtools';

export function setupVitePlugins(viteEnv: Env.ImportMeta, buildTime: string) {
  const plugins: PluginOption = [
    vue({
      template: {
        compilerOptions: {
          isCustomElement: (tag: string) => tag.startsWith('media-'),
        },
      },
    }),
    vueJsx(),
    setupDevtoolsPlugin(viteEnv),
    setupElegantRouter(),
    setupUnocss(viteEnv),
    ...setupUnplugin(viteEnv),
    progress(),
    setupHtmlPlugin(buildTime),
    // setupVidstackElementPlugin(),
    vidstack(),
  ];

  return plugins;
}
