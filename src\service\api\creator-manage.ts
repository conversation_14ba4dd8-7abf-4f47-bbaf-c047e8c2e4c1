/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-11-26 16:53:06
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-26 16:59:53
 * @FilePath: \tiksage-frontend\src\service\api\creator-manage.ts
 * @Description: creator manage api
 */
import { request } from '../request';

export function fetchUploadCreatorExcel(data: Api.CreatorManage.UploadCreatorExcelSearchParams) {
  const { file, ...params } = data;
  return request<Api.CreatorManage.DuplicateCreatorList>({
    url: '/approval/uploadCreatorExcel',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    method: 'post',
    params,
    data: file
      ? {
          file
        }
      : undefined
  });
}

export function fetchGetCreatorApprovalList(data: Api.CreatorManage.CreatorApprovalListSearchParams) {
  return request<Api.CreatorManage.CreatorApprovalListResponse[]>({
    url: '/approval/listCreatorApproval',
    method: 'post',
    data
  });
}

export function fetchGetCreatorApprovalListByPage(data: Api.CreatorManage.CreatorApprovalListSearchParams) {
  return request<Api.CreatorManage.CreatorApprovalListByPageResponse>({
    url: '/approval/pageCreatorApproval',
    method: 'post',
    data
  });
}

export function fetchApproveCreator(id: number) {
  return request({
    url: '/approval/creatorApproved',
    method: 'put',
    params: { id }
  });
}

export function fetchRejectCreator(data: { id: number; notes: string }) {
  return request({
    url: '/approval/creatorRejected',
    method: 'put',
    data
  });
}

// Video Status
export function fetchGetCreatorProductVideoListByOperator(data: Api.CreatorManage.CreatorProductVideoListSearchParams) {
  return request<Api.CreatorManage.CreatorProductVideoResponse>({
    url: '/videoStatus/queryCreatorSample',
    method: 'post',
    data
  });
}

export function fetchGetCreatorProductVideoListByApprover(data: Api.CreatorManage.CreatorProductVideoListSearchParams) {
  return request<Api.CreatorManage.CreatorProductVideoResponse>({
    url: '/videoStatus/queryOwnerCreatorSample',
    method: 'post',
    data
  });
}

export function fetchUpdateAdCode(params: Api.CreatorManage.UpdateCommonParams & { adCode: string }) {
  return request({
    url: '/videoStatus/updateAdCode',
    method: 'put',
    params
  });
}

export function fetchUpdateNote(params: Api.CreatorManage.UpdateCommonParams & { note: string }) {
  return request({
    url: '/videoStatus/updateNote',
    method: 'put',
    params
  });
}

export function fetchUpdateClientVisible(params: Api.CreatorManage.UpdateCommonParams & { status: number }) {
  return request({
    url: '/videoStatus/updateVisibleStatus',
    method: 'put',
    params
  });
}

export function fetchPullCreatorProductVideos(client: number) {
  return request({
    timeout: 1000 * 30,
    url: '/videoStatus/pullCreatorSampleRequests',
    method: 'get',
    params: { client }
  });
}

export function fetchDownloadCreatorProductVideosByOperator(
  data: Api.CreatorManage.CreatorProductVideoListSearchParams
) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/videoStatus/exportCreatorSample',
    method: 'post',
    data
  });
}

export function fetchDownloadCreatorProductVideosByApprover(
  data: Api.CreatorManage.CreatorProductVideoListSearchParams
) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/videoStatus/exportOwnerCreatorSample',
    method: 'post',
    data
  });
}
