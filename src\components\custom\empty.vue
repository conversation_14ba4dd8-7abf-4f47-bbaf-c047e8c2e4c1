<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-06 13:27:42
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-08-07 10:31:43
 * @FilePath: \tiksage-frontend\src\components\custom\empty.vue
 * @Description: empty
-->
<script setup lang="ts">
interface Props {
  description?: string;
}

defineProps<Props>();
</script>

<template>
  <NFlex class="h-full w-full" vertical justify="center" align="center">
    <icon-local-banner class="text-400px text-primary" />
    <NText class="text-2xl text-primary">{{ description }}</NText>
  </NFlex>
</template>

<style scoped></style>
