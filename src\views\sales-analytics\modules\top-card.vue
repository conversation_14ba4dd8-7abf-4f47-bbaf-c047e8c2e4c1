<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-24 17:43:55
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-06-25 22:02:51
 * @FilePath: \tiksage-frontend\src\views\sales-analytics\modules\top-card.vue
 * @Description: top-card
-->
<script setup lang="ts">
import { computed } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { getFallbackImage } from '@/utils/fake-image';

const mockVideo = [
  {
    name: '<PERSON><PERSON> must have !! Its getting warmer outside & the babies love bubbles! Its even fun for the',
    nickname: 'charltte6',
    date: '2024-06-03',
    view_num: 49.95,
    like_num: 3.13,
    share_num: 5.14,
    sale_num: 265.48,
    img: getFallbackImage(60, 85)
  },
  {
    name: 'Replying to @lonnielo This fits so much!! Perfect for beach and pool, stands on its own and super freaking cute! #beachbag #totebag #beachvibes #tiktokshopsummersale #bags @Case-Mate #techtalkla',
    nickname: 'VanillaCrush ',
    date: '2023-08-25',
    view_num: 751.81,
    like_num: 2.07,
    share_num: 5.12,
    sale_num: 115.31,
    img: getFallbackImage(60, 85)
  },
  {
    name: 'Biggest summer sale ever… She IS the competition, yall 👏🏻 Defrizz and cut hair drying time in half, powerful hairdryer without the crazy pricetag!!! #hairdryer #tiktokshopsummersale #tideway #blowdryer #hairstyle',
    nickname: 'VanillaCrush',
    date: '2024-04-03',
    view_num: 52.34,
    like_num: 84,
    share_num: 39,
    sale_num: 89.22,
    img: getFallbackImage(60, 85)
  }
];

interface Props {
  tabValue: string;
}
const appStore = useAppStore();
const cols = computed(() => (appStore.isMobile ? 2 : 3));

const props = defineProps<Props>();
const tabValue = computed(() => props.tabValue);
const emit = defineEmits(['update:tabValue']);
const handleTabsClick = (value: string) => {
  emit('update:tabValue', value);
};
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <!--
 <template #header>
      <NButtonGroup>
        <NButton :focusable="true">Video</NButton>
        <NButton>Live</NButton>
        <NButton>Procudt</NButton>
      </NButtonGroup>
    </template>
-->
    <template #header-extra>
      <NButton>See more</NButton>
    </template>
    <NTabs v-model:value="tabValue" type="segment" animated @update:value="handleTabsClick">
      <NTabPane v-for="item in ['Video', 'Live', 'Product']" :key="item" :name="item" :tab="item">
        <NFlex v-for="(item, index) in mockVideo" :key="item.name" justify="space-between" class="mt-5">
          <NText>No.{{ index + 1 }}</NText>
          <NImage :src="item.img" />
          <NDescriptions label-placement="left" separator="" :column="cols" label-class="label-class" class="flex-1">
            <NDescriptionsItem>
              <template #label>
                <SvgIcon icon="ic:baseline-title" />
              </template>
              <NEllipsis style="max-width: 50px">{{ item.name }}</NEllipsis>
            </NDescriptionsItem>
            <NDescriptionsItem>
              <template #label>
                <SvgIcon icon="ic:baseline-add-link" />
              </template>
              @{{ item.nickname }}
            </NDescriptionsItem>
            <NDescriptionsItem>
              <template #label>
                <SvgIcon icon="ic:baseline-calendar-month" />
              </template>
              {{ item.date }}
            </NDescriptionsItem>
            <NDescriptionsItem>
              <template #label>
                <SvgIcon icon="ic:baseline-remove-red-eye" />
              </template>
              {{ item.view_num }}M
            </NDescriptionsItem>
            <NDescriptionsItem>
              <template #label>
                <SvgIcon icon="ic:baseline-favorite" />
              </template>
              {{ item.like_num }}M
            </NDescriptionsItem>
            <NDescriptionsItem>
              <template #label>
                <SvgIcon icon="ic:baseline-share" />
              </template>
              {{ item.share_num }}M
            </NDescriptionsItem>
            <NDescriptionsItem>
              <template #label>
                <SvgIcon icon="ic:baseline-attach-money" />
              </template>
              {{ item.sale_num }}M
            </NDescriptionsItem>
          </NDescriptions>
        </NFlex>
      </NTabPane>
    </NTabs>
  </NCard>
</template>

<style lang="scss"></style>
