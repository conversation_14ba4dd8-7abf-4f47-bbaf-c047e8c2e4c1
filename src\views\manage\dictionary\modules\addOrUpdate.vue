<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { fetchAddDictionary, fetchAddDictionaryType, fetchUpdateDictionary } from '@/service/api/dictionary';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

interface Emits {
  (e: 'submit'): void;
}

interface Props {
  drawerType: 'add' | 'edit';
  editingData: Api.Dictionary.Dictionary | null;
  typeOptions: { label: string; value: string }[];
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const show = defineModel<boolean>('show', {
  required: true
});

type Model = Api.Dictionary.AddOrUpdateDictionaryParams;

function createDefaultModel(): Model {
  return {
    type: '',
    code: '',
    name: '',
    description: '',
    sort: 0,
    isEnabled: 1
  };
}
const model = ref<Model>(createDefaultModel());

const title = computed(() => {
  return props.drawerType === 'edit' ? 'Edit Dictionary' : 'Add Dictionary';
});

const { formRef, validate } = useNaiveForm();
const rules = computed<Record<string, App.Global.FormRule[]>>(() => {
  const { defaultRequiredRule } = useFormRules();
  return {
    type: [defaultRequiredRule],
    name: [defaultRequiredRule],
    code: [defaultRequiredRule]
  };
});

function initModel() {
  if (props.drawerType === 'edit') {
    model.value = { ...props.editingData } as Model;
  } else {
    model.value = createDefaultModel();
  }
}

async function handleSubmit() {
  await validate();
  if (props.drawerType === 'add') {
    if (!props.typeOptions.find(item => item.value === model.value.type)) {
      const params = {
        type: model.value.type,
        typeDesc: model.value.type
      };
      const { error: addTypeError } = await fetchAddDictionaryType(params);
      if (addTypeError) return;
    }
    const { error: addError } = await fetchAddDictionary(model.value);
    if (!addError) {
      window.$message?.success('Add dictionary successfully.');
      show.value = false;
      emit('submit');
    }
  } else {
    const { error: updateError } = await fetchUpdateDictionary(model.value);
    if (!updateError) {
      window.$message?.success('Update dictionary successfully.');
      show.value = false;
      emit('submit');
    }
  }
}

watch(
  () => show.value,
  () => {
    if (show.value) {
      initModel();
    }
  }
);
</script>

<template>
  <NDrawer v-model:show="show" width="400px">
    <NDrawerContent :title="title" closable>
      <NForm ref="formRef" :rules="rules" :model="model">
        <NFormItem label="Type" path="type">
          <NSelect v-model:value="model.type" tag filterable :options="typeOptions" />
        </NFormItem>
        <NFormItem label="Name" path="name">
          <NInput v-model:value="model.name" />
        </NFormItem>
        <NFormItem label="Code" path="code">
          <NInput v-model:value="model.code" />
        </NFormItem>
        <NFormItem label="Description" path="description">
          <NInput v-model:value="model.description" />
        </NFormItem>
        <NFormItem label="Sort" path="sort">
          <NInputNumber v-model:value="model.sort" />
        </NFormItem>
        <NFormItem v-if="props.drawerType === 'edit'" label="Enabled" path="isEnabled">
          <NRadioGroup v-model:value="model.isEnabled">
            <NRadio :checked="model.isEnabled === 1" :value="1">Enabled</NRadio>
            <NRadio :checked="model.isEnabled === 0" :value="0">Disabled</NRadio>
          </NRadioGroup>
        </NFormItem>
      </NForm>
      <template #footer>
        <div class="flex justify-end gap-4">
          <NButton @click="show = false">Cancel</NButton>
          <NButton type="primary" @click="handleSubmit">Submit</NButton>
        </div>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
