/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-10 15:48:57
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jar<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-28 09:30:20
 * @FilePath: \tiksage-frontend\src\utils\number.ts
 * @Description: number utils
 */
import { divide, isNil } from 'lodash-es';

export function getRatio(dividend: number, divisor: number) {
  // value is not number
  if (isNil(dividend) || isNil(divisor)) return Number.NaN;

  // 0/0 = 100
  if (dividend === 0 && divisor === 0) return 0;

  // ? / 0 = NaN
  if (dividend !== 0 && divisor === 0) return Number.NaN;

  return divide(dividend, divisor);
}
