###
 # @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 # @Date: 2024-06-05 11:03:34
 # @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 # @LastEditTime: 2024-08-29 17:52:04
 # @FilePath: \soybean-admin\.env.prod
 # @Description: config in product environment
###
# backend service base url, prod environment
VITE_SERVICE_BASE_URL=/tiktop-api/

# other backend service base url, prod environment
VITE_OTHER_SERVICE_BASE_URL= `{
  "demo": "http://localhost:9529",
  "adhoc": "/adhoc-api/"
}`

VITE_BASE_URL= /

# backend shop avatar prefix url
VITE_SHOP_AVATAR_URL=/images/shop/avatar/

# backend product avatar prefix url
VITE_PRODUCT_AVATAR_URL=/images/product/avatar/

# backend creator avatar prefix url
VITE_CREATOR_AVATAR_URL=/images/creator/avatar/

VITE_TODAY_VIDEO_AVATAR_URL=/images/today/

VITE_VIDEO_URL=/images/video/

# backend websocket url
VITE_WEBSOCKET_URL=wss://product.tiksage.com/websocket/

VITE_SHOP_AUDIT_AVATAR_URL=/images/shop-audit/avatar/

VITE_SHOP_LEADER_AVATAR_URL=/images/leader-shop/avatar/

VITE_SHOP_LEADER_REPORT_URL=/images/leader-shop/report/

VITE_CREATOR_CENTER_AVATAR_URL=/images/aic-creator/avatar/
