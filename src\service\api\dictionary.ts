import { request } from '../request';

export function fetchGetDictionary<T extends string | number = number>(type: string) {
  return request<Api.Dictionary.DictionaryItem<T>[]>({
    url: '/dictionary/getDict',
    method: 'get',
    params: {
      type
    }
  });
}

export function fetchGetDictionaryList(params: Api.Dictionary.DictionaryListParams) {
  return request<Api.Dictionary.DictionaryListResponse>({
    url: '/dictionary/list',
    method: 'get',
    params
  });
}

export function fetchAddDictionary(data: Api.Dictionary.AddOrUpdateDictionaryParams) {
  return request({
    url: '/dictionary/create',
    method: 'post',
    data
  });
}

export function fetchUpdateDictionary(data: Api.Dictionary.AddOrUpdateDictionaryParams) {
  return request({
    url: '/dictionary/update',
    method: 'put',
    data
  });
}

export function fetchDeleteDictionary(id: number) {
  return request({
    url: '/dictionary/delete',
    method: 'delete',
    params: {
      id
    }
  });
}

export function fetchGetDictionaryType() {
  return request<Api.Dictionary.GetDictionaryTypeResponse>({
    url: '/dictionary/listDictType',
    method: 'get'
  });
}

export function fetchAddDictionaryType(data: Api.Dictionary.DictionaryType) {
  return request({
    url: '/dictionary/createDictType',
    method: 'post',
    data
  });
}
