<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { isNil } from 'lodash-es';
import { enableStatusOptions } from '@/constants/business';
import {
  fetchBindShop,
  fetchCreateUser,
  fetchGetAllRoles,
  fetchGetRolePages,
  fetchGetUserShop,
  fetchUpdateUser
} from '@/service/api';
import { useUserStore } from '@/store/modules/user';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'UserOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.SystemManage.User | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { defaultRequiredRule, formRules } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: $t('page.manage.user.addUser'),
    edit: $t('page.manage.user.editUser')
  };
  return titles[props.operateType];
});

type Model = Api.SystemManage.UserUpdateParams & {
  shopRoles: number[];
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    userId: 0,
    userName: '',
    email: '',
    shopRoles: [],
    status: 0,
    homePage: null,
    roleIdList: null
  };
}

type RuleKey = Extract<keyof Model, 'userName' | 'status' | 'email' | 'roleIdList' | 'homePage'>;

const rules: Record<RuleKey, App.Global.FormRule | App.Global.FormRule[]> = {
  userName: defaultRequiredRule,
  status: defaultRequiredRule,
  email: formRules.email,
  roleIdList: defaultRequiredRule,
  homePage: defaultRequiredRule
};

/** the enabled role options */
const roleOptions = ref<CommonType.Option<number>[]>([]);

async function getRoleOptions() {
  const { error, data } = await fetchGetAllRoles();

  if (!error) {
    const options = data.records.map(item => ({
      label: item.roleName,
      value: item.id
    }));

    roleOptions.value = options;
  }
}

const userStore = useUserStore();

const shopOptions = computed(() => {
  return userStore.userShops;
});

async function getUserRoles() {
  if (isNil(props.rowData?.id)) return;
  const { error, data } = await fetchGetUserShop(props.rowData.id);
  if (!error) {
    if (!data.length) return;
    model.shopRoles = data.map(s => {
      return s.shopId;
    });
  }
}

const homeSelectOptions = ref();

async function getHomePages(ids: number[]) {
  if (!ids.length) return;
  const { data, error } = await fetchGetRolePages(ids);
  if (error) return;
  homeSelectOptions.value = data;
}

function handleInitModel() {
  Object.assign(model, createDefaultModel());

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData, {
      userId: props.rowData.id,
      roleIdList: props.rowData.roleIdList || []
    });
  }
}

function closeDrawer() {
  visible.value = false;
}

function getAvatar() {
  const list = [
    'https://i.postimg.cc/jS8PX11s/Avatar-Maker-1.png',
    'https://i.postimg.cc/bwY11P3C/Avatar-Maker-2.png',
    'https://i.postimg.cc/15QG5w6Z/Avatar-Maker-3.png',
    'https://i.postimg.cc/J45by0cH/Avatar-Maker-4.png'
  ];
  return list[Math.floor(Math.random() * 4)];
}

async function handleSubmit() {
  await validate();
  // request
  let response;
  if (props.operateType === 'edit') {
    const { error } = await fetchBindShop(model.userId, model.shopRoles);
    if (error) return;
    response = await fetchUpdateUser(model);
  } else {
    Object.assign(model, { avatar: getAvatar() });
    response = await fetchCreateUser(model);
  }
  if (response?.error) return;
  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(
  () => model.roleIdList,
  newRoleIds => {
    if (!newRoleIds || !newRoleIds.length) return;
    getHomePages(newRoleIds);
  },
  { immediate: true }
);

watch(visible, () => {
  if (visible.value) {
    if (props.operateType === 'edit') {
      getUserRoles();
    }
    getRoleOptions();
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem :label="$t('page.manage.user.userName')" path="userName">
          <NInput v-model:value="model.userName" :placeholder="$t('page.manage.user.form.userName')" />
        </NFormItem>
        <!--
 <NFormItem :label="$t('page.manage.user.userGender')" path="userGender">
          <NRadioGroup v-model:value="model.userGender">
            <NRadio
              v-for="item in userGenderOptions"
              :key="item.value"
              :value="item.value"
              :label="$t(item.label)"
            />
          </NRadioGroup>
        </NFormItem>
-->
        <!--
 <NFormItem :label="$t('page.manage.user.nickName')" path="nickName">
          <NInput
            v-model:value="model.nickName"
            :placeholder="$t('page.manage.user.form.nickName')"
          />
        </NFormItem>
-->
        <!--
 <NFormItem :label="$t('page.manage.user.userPhone')" path="tel">
          <NInput v-model:value="model.tel" :placeholder="$t('page.manage.user.form.userPhone')" />
        </NFormItem>
-->
        <NFormItem :label="$t('page.manage.user.userEmail')" path="email">
          <NInput v-model:value="model.email" :placeholder="$t('page.manage.user.form.userEmail')" />
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.userStatus')" path="status">
          <NRadioGroup v-model:value="model.status">
            <NRadio
              v-for="item in enableStatusOptions"
              :key="item.value"
              :value="Number(item.value)"
              :label="$t(item.label)"
            />
          </NRadioGroup>
        </NFormItem>
        <NFormItem :label="$t('page.manage.user.userRole')" path="roleIdList">
          <NSelect
            v-model:value="model.roleIdList"
            multiple
            :options="roleOptions"
            :placeholder="$t('page.manage.user.form.userRole')"
          />
        </NFormItem>
        <NFormItem label="User Home Page" path="homePage">
          <NSelect
            v-model:value="model.homePage"
            :options="homeSelectOptions"
            label-field="menuName"
            value-field="routeName"
            placeholder="Select stores"
          />
        </NFormItem>
        <NFormItem label="User Shop Permissions" path="shopRoles">
          <NSelect
            v-model:value="model.shopRoles"
            multiple
            :options="shopOptions"
            label-field="shopName"
            value-field="shopId"
            placeholder="Select stores"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
