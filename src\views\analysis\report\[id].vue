<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-07 17:32:20
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-08 10:23:23
 * @FilePath: \tiksage-frontend\src\views\analysis\modules\creator-analysis-tabs.vue
 * @Description: creator-analysis-tabs
-->
<script setup lang="tsx">
import { computed, nextTick, reactive, ref, watch } from 'vue';
import { useToggle, useWebSocket } from '@vueuse/core';
import type { DataTableColumn } from 'naive-ui';
import { NAvatar, NEllipsis, NFlex, NTag, NText, NTooltip } from 'naive-ui';
import { delay, divide, isNil } from 'lodash-es';
import { fetchAnalysisReportHistoryById } from '@/service/api';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { useRouterPush } from '@/hooks/common/router';
import { getFallbackImage } from '@/utils/fake-image';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { NumeralFormat, TaskStatus } from '@/enum';
import TaskProgress from '../list/modules/task-progress.vue';
import CreatorDetail from './modules/creator-detail.vue';

interface Props {
  id: string;
}

type TabType = 'current' | 'competitor' | 'recommend';

type Option = { label: string; value: string | number };

const props = defineProps<Props>();

const { routerPushByKey } = useRouterPush();

const { numberFormat } = useNumberFormat();

const { open, data: websocketData } = useWebSocket(`${import.meta.env.VITE_WEBSOCKET_URL}websocket/${props.id}`, {
  immediate: false
});

const [firstLoading, toggleFirstLoading] = useToggle(true);

const reportInfo = ref<Api.Analysis.ReportInfo>();

const EmptyDescription = computed(() => {
  let isRunning;
  if (!reportInfo.value) {
    isRunning = false;
  } else {
    isRunning = [TaskStatus.FAIL, TaskStatus.RUNNING].includes(reportInfo.value?.grabTaskStatus);
  }
  return isRunning ? 'Processing, please wait...' : 'No Data';
});

const showData = ref<Api.Analysis.CreatorInfo[]>();

const shopValue = ref();
const selectOptions = ref<Option[]>([]);

const showTypeValue = ref('card');
const showTypeOptions = [
  { icon: 'tabler:layout-grid-filled', value: 'card' },
  { icon: 'tabler:menu-2', value: 'list' }
];

const isShopSelectionShow = ref(false);

const tabValue = ref<TabType>('current');

const [reportLoading, toggleReportLoading] = useToggle(false);

const [listLoading, toggleListLoading] = useToggle(false);

const columns: DataTableColumn<Api.Analysis.CreatorInfo>[] = reactive([
  {
    key: 'score',
    title: 'Score',
    align: 'center',
    fixed: 'left',
    width: 80,
    render(rowData) {
      return (
        <NText class="text-xl color-amber" strong>
          {rowData.score}
        </NText>
      );
    }
  },
  {
    key: 'isRepeat',
    title: '',
    align: 'center',
    fixed: 'left',
    width: 40,
    render(rowData) {
      return rowData.isRepeat ? (
        <NTooltip trigger="hover">
          {{
            trigger: () => <SvgIcon icon="tabler:refresh-alert" class="text-2xl color-warning" />,
            default: () => <span>This creator is currently working with your target shop</span>
          }}
        </NTooltip>
      ) : (
        ''
      );
    }
  },
  {
    key: 'creatorId',
    title: 'Creator',
    // align: 'center',
    fixed: 'left',
    width: 250,
    render: row => {
      return (
        <NFlex justify="start" align="center" wrap={false}>
          <NAvatar
            style={`width:50px;height:50px`}
            round
            src={row.avatar}
            fallbackSrc={getFallbackImage(50, 50)}
          ></NAvatar>
          <NFlex vertical justify="center">
            <NEllipsis style="max-width:150px" class="font-bold" lineClamp={1}>
              {row.creatorId}
            </NEllipsis>
            <NEllipsis style="max-width:150px" class="text-neutral" lineClamp={1}>
              {row.nickname}
            </NEllipsis>
            {tabValue.value === 'competitor' ? (
              <NFlex align="center" wrap={false}>
                <SvgIcon class="text-dark" icon="tabler:building-store" />
                <NEllipsis style="max-width:120px" class="text-neutral" lineClamp={1}>
                  {shopValue.value || '-'}
                </NEllipsis>
              </NFlex>
            ) : undefined}
          </NFlex>
        </NFlex>
      );
    }
  },
  {
    key: 'categoryJson',
    title: 'Categories',
    width: 200,
    render(rowData) {
      return (
        <NFlex>
          {JSON.parse(rowData.categoryJson).map((cgy: any) => {
            return (
              <NTag size="small" bordered={false}>
                {cgy}
              </NTag>
            );
          })}
        </NFlex>
      );
    }
  },
  {
    key: 'followerNum',
    title: 'Followers',
    width: 180,
    render(rowData) {
      const { followerMaleRatio, followerFemaleRatio } = rowData;
      let showGender;
      let showGenderRatio;
      if (followerMaleRatio > followerFemaleRatio) {
        showGender = 'Male';
        showGenderRatio = followerMaleRatio;
      } else {
        showGender = 'Female';
        showGenderRatio = followerFemaleRatio;
      }
      return (
        <NFlex vertical>
          {/* <NEllipsis style="max-width:150px" lineClamp={1} class="font-bold"></NEllipsis> */}
          <NFlex align="center">
            <SvgIcon icon="tabler:users" />
            <NText>{numberFormat(rowData.followerNum, NumeralFormat.Number)}</NText>
            {<SvgIcon icon={showGender === 'Male' ? 'twemoji:male-sign' : 'twemoji:female-sign'} />}
            {`${showGenderRatio}%`}
          </NFlex>
          <NFlex align="center">
            {JSON.parse(rowData.followerAgeJson).map((cgy: any) => {
              return (
                <NTag size="small" bordered={false}>
                  {cgy}
                </NTag>
              );
            })}
          </NFlex>
        </NFlex>
      );
    }
  },
  {
    key: 'gmvNum',
    title: 'GMV',
    align: 'center',
    width: 110
  },
  {
    key: 'unitsSoldNum',
    title: 'Units Sold',
    align: 'center',
    width: 110
  },
  {
    key: 'videosNum',
    title: 'No. of Videos',
    align: 'center',
    width: 110
  },
  {
    key: 'avgViewsNum',
    title: 'Avg. Views',
    align: 'center',
    width: 110
  }
]);

const dollarKeys = ['gmvNum'];
const percentKeys = [''];

const renderCell = (value: any, _rowData: any, column: any) => {
  if (dollarKeys.includes(column.key)) {
    return numberFormat(value, NumeralFormat.Dollar);
  }
  if (percentKeys.includes(column.key)) {
    return numberFormat(divide(Number(value), 100), NumeralFormat.Percent);
  } else if (typeof value === 'number') {
    return numberFormat(value, NumeralFormat.Number);
  }
  return value;
};

const rowProps = (rowData: any) => {
  return {
    style: 'cursor:pointer',
    onClick: () => {
      window.open(rowData.homepage, '_blank');
    }
  };
};

const initReportData = async (id: string) => {
  toggleReportLoading(true);
  const { data, error } = await fetchAnalysisReportHistoryById(id);
  if (error) {
    toggleReportLoading(false);
    return;
  }
  reportInfo.value = data;
  if ([TaskStatus.RUNNING, TaskStatus.FAIL].includes(data.grabTaskStatus)) {
    open();
  }
  await nextTick();
  toggleReportLoading(false);
};

const handleToAdd = () => {
  routerPushByKey('analysis_diagnosis');
};

const handleToReportList = () => {
  routerPushByKey('analysis_list');
};

watch(
  () => props.id,
  newId => {
    initReportData(newId);
  },
  {
    immediate: true
  }
);

const handleShowData = (report: Api.Analysis.ReportInfo, tab: TabType, shop?: string) => {
  const { shopCurrentCreators, competitorShopCreators, recommendedCreators } = report;
  let options;
  let shopIndex;
  switch (tab) {
    case 'current':
      showData.value = shopCurrentCreators;
      break;
    case 'competitor':
      if (!competitorShopCreators.length) {
        shopValue.value = null;
        selectOptions.value = [];
        showData.value = [];
        break;
      }

      options = competitorShopCreators.map((list, index) => {
        if (!isNil(shop)) {
          if (list.shopName === shop) shopIndex = index;
        }
        return {
          label: list.shopName,
          value: list.shopName
        };
      });
      isNil(shopIndex) && (shopIndex = 0);

      selectOptions.value = options;

      shopValue.value = options[shopIndex].value;

      showData.value = competitorShopCreators[shopIndex].competitorCreators;
      break;
    case 'recommend':
      showData.value = recommendedCreators.map(creator => {
        return {
          ...creator,
          avatar: `${import.meta.env.VITE_CREATOR_AVATAR_URL}${creator.avatar}`
        };
      });
      break;
    default:
  }
};

// reportInfo change
watch(
  () => reportInfo.value,
  async newReport => {
    firstLoading.value && toggleReportLoading(true);
    if (!newReport) {
      firstLoading.value &&
        delay(() => {
          toggleReportLoading(false);
        }, 500);
      return;
    }
    handleShowData(newReport, tabValue.value, shopValue.value);
    firstLoading.value &&
      delay(() => {
        toggleReportLoading(false);
      }, 500);

    firstLoading.value && toggleFirstLoading(false);
  }
);

// tabValue change
const handleToggleTab = (value: TabType) => {
  tabValue.value = value;
  toggleListLoading(true);
  if (!reportInfo.value) {
    showData.value = [];
    delay(() => {
      toggleListLoading(false);
    }, 500);
    return;
  }
  handleShowData(reportInfo.value, value, shopValue.value);
  if (value === 'competitor') {
    isShopSelectionShow.value = true;
  } else {
    isShopSelectionShow.value = false;
  }
  delay(() => {
    toggleListLoading(false);
  }, 500);
};

// shopValue change
const handleShopValueChange = (value: string) => {
  shopValue.value = value;
  toggleListLoading(true);
  if (!reportInfo.value) {
    shopValue.value = null;
    delay(() => {
      toggleListLoading(false);
    }, 500);
    return;
  }
  const { competitorShopCreators } = reportInfo.value;
  showData.value = competitorShopCreators.find(csc => csc.shopName === value)?.competitorCreators;
  delay(() => {
    toggleListLoading(false);
  }, 500);
};

watch(
  () => websocketData.value,
  () => {
    reportInfo.value = JSON.parse(websocketData.value);
  }
);
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="h-full card-wrapper" content-class="flex-col min-h-500px" :bordered="false">
      <template #header>
        <NFlex align="center" :wrap="false">
          <NText class="text-size-18px font-500">Creator Match List</NText>
          <div v-if="reportInfo" class="w-150px">
            <TaskProgress :task-status="reportInfo.grabTaskStatus" />
          </div>
        </NFlex>
      </template>
      <template #header-extra>
        <ButtonIcon tooltip-content="Add More Shops" icon="tabler:user-search" @click="handleToAdd" />
        <ButtonIcon tooltip-content="Report List" icon="tabler:history" @click="handleToReportList" />
      </template>
      <NSpin v-if="reportLoading" class="m-auto"></NSpin>
      <div v-else class="flex-col flex-1">
        <NTabs :value="tabValue" pane-class="h-full" type="line" @update:value="handleToggleTab">
          <template #suffix>
            <NSelect
              v-show="isShopSelectionShow"
              class="min-w-80px"
              :value="shopValue"
              size="small"
              :consistent-menu-width="false"
              :options="selectOptions"
              placeholder="Shops"
              @update:value="handleShopValueChange"
            ></NSelect>
          </template>
          <NTab name="current" :tab="`${reportInfo?.shopName || 'My Shop'}`"></NTab>
          <NTab name="competitor" tab="Competitor"></NTab>
          <NTab name="recommend" tab="Recommended"></NTab>
        </NTabs>
        <NFlex vertical class="flex-1">
          <NFlex class="mt-8px text-icon" justify="end">
            <NRadioGroup id="radio" v-model:value="showTypeValue" size="small">
              <NRadioButton v-for="item in showTypeOptions" :key="item.value" :value="item.value">
                <div class="mt-7px">
                  <SvgIcon :icon="item.icon" />
                </div>
              </NRadioButton>
            </NRadioGroup>
          </NFlex>
          <NSpin v-if="listLoading" class="m-auto"></NSpin>
          <NEmpty v-else-if="!showData?.length" class="m-auto" :description="EmptyDescription"></NEmpty>
          <div v-else class="flex-1">
            <NDataTable
              v-if="showTypeValue === 'list'"
              :bordered="false"
              style="height: 100%"
              flex-height
              size="small"
              :columns="columns"
              :data="showData"
              :render-cell="renderCell"
              scroll-x="1200"
              :row-props="rowProps"
            ></NDataTable>
            <NGrid
              v-if="showTypeValue === 'card'"
              class="h-full overflow-y-auto"
              x-gap="16"
              y-gap="16"
              item-responsive
              responsive="screen"
            >
              <NGi v-for="data in showData" :key="data.id" span="6">
                <CreatorDetail :data="data" />
              </NGi>
            </NGrid>
          </div>
        </NFlex>
      </div>
    </NCard>
  </NFlex>
</template>

<style scoped></style>
