<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-25 21:02:02
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-07-09 14:48:56
 * @FilePath: \tiksage-frontend\src\views\sales-analytics\modules\creator-table.vue
 * @Description: create-table
-->
<script setup lang="tsx">
import { onMounted, reactive } from 'vue';
import { NAvatar, NButton, NFlex, NText } from 'naive-ui';
import { random } from 'lodash-es';
import { useRouterPush } from '@/hooks/common/router';
import { getFallbackImage } from '@/utils/fake-image';

const { routerPushByKey } = useRouterPush();
const onNavigateDetail = () => {
  routerPushByKey('detail_creator', { params: { id: '1' } });
};
const columns = reactive([
  {
    key: 'rank',
    title: 'Rank'
  },
  {
    key: 'id',
    title: 'Product Info',
    render: (row: any) => (
      <NButton text onClick={onNavigateDetail}>
        <NFlex align="center">
          <NAvatar src={row.avatar}></NAvatar>
          <NText>{row.name}</NText>
        </NFlex>
      </NButton>
    )
  },
  {
    key: 'revenue',
    title: 'Revenue'
  },
  {
    key: 'unitsSold',
    title: 'UnitsSold'
  },
  {
    key: 'unitsSold',
    title: 'UnitsSold'
  },
  {
    key: 'unitsSold',
    title: 'UnitsSold'
  },
  {
    key: 'unitsSold',
    title: 'UnitsSold'
  },
  {
    key: 'unitsSold',
    title: 'UnitsSold'
  },
  {
    key: 'unitsSold',
    title: 'UnitsSold'
  }
]);

const data = reactive<any[]>([]);

onMounted(() => {
  for (let i = 0; i < 10; i += 1) {
    const element = {
      id: i + 1,
      rank: `No.${i + 1}`,
      name: `Creator${i + 1}`,
      avatar: getFallbackImage(100, 100),
      revenue: random(0, 5000),
      unitsSold: random(0, 1000)
    };
    data.push(element);
  }
});

const onMoreCreator = () => {
  // routerPushByKey('market-potential_creator-resources')
};
</script>

<template>
  <NCard :bordered="false" class="card-wrapper" title="Creator Sales Data">
    <template #header-extra>
      <NButton quaternary @click="onMoreCreator">Find More Creators</NButton>
    </template>
    <NDataTable remote :columns="columns" :data="data"></NDataTable>
  </NCard>
</template>

<style scoped></style>
