<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { NEllipsis } from 'naive-ui';
import dayjs from 'dayjs';
import { delay } from 'lodash-es';
import { fetchGetCreatorsWidthGmv, fetchGetTopGmvShopById } from '@/service/api/category-leaders';
import { useTable } from '@/hooks/common/table';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { downloadReport } from '@/utils/download';
import { getFallbackImage } from '@/utils/fake-image';
import { NumeralFormat, TimeFormat } from '@/enum';
import ShopInfoCard from './shop-info-card.vue';
import IndicatorItem from './indicator-item.vue';
import VideoLiveInfoCard from './video-live-info-card.vue';

const { VITE_SHOP_LEADER_AVATAR_URL } = import.meta.env;
const downloadOptions = [
  {
    label: 'Export as PNG',
    key: 'png'
  },
  {
    label: 'Export as PDF',
    key: 'pdf'
  }
];

interface Props {
  id?: number;
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const visible = defineModel('visible', {
  required: true,
  default: false
});

const [pageLoading, togglePageLoading] = useToggle(false);

const shopData = ref<Api.CategoryLeaders.TopGmvShop>();

const [isExport, toggleExport] = useToggle(false);

const title = computed(() => {
  if (shopData.value?.belongMonth) {
    const month = dayjs.tz(shopData.value?.belongMonth, 'Etc/Gmt+8').format(TimeFormat.US_DATE_NO_DAY);
    return `Shop Performance Report - ${month}`;
  }
  return 'Shop Performance Report';
});

const bestSellingProductAvatarUrl = computed(() => {
  return `${VITE_SHOP_LEADER_AVATAR_URL}${shopData.value?.shopProductData.productAvatarLocal}`;
});

const shopAccountAvatarUrl = computed(() => {
  return `${VITE_SHOP_LEADER_AVATAR_URL}${shopData.value?.shopAccountData.accountAvatarLocal}`;
});

const truncatedProductName = computed(() => {
  const text = shopData.value?.shopProductData.productName || '-';
  if (text.length <= 105) return text;
  return `${text.slice(0, 105)}...`;
});

const shopOverview = computed(() => {
  return [
    {
      title: 'Total GMV',
      value: shopData.value?.shopOverviewData.totalGmv || '-'
    },
    {
      title: 'Total Units Sold',
      value: shopData.value?.shopOverviewData.totalSold || '-'
    },
    {
      title: 'Average Price',
      value: shopData.value?.shopOverviewData.averagePrice || '-'
    },
    {
      title: 'Total Associated Lives',
      value: shopData.value?.shopOverviewData.totalLives || '-'
    },
    {
      title: 'Total Associated Videos',
      value: shopData.value?.shopOverviewData.totalVideos || '-'
    },
    {
      title: 'Total Affiliates',
      value: shopData.value?.shopOverviewData.totalAffiliates || '-'
    }
  ];
});

// eslint-disable-next-line
const currentMonthData = computed(() => {
  return [
    {
      title: 'GMV',
      value: shopData.value?.shopMonthData.gmv || '-',
      percent: shopData.value?.shopMonthData.gmvGrowth || '-'
    },
    {
      title: 'Units Sold',
      value: shopData.value?.shopMonthData.sold || '-',
      percent: shopData.value?.shopMonthData.soldGrowth || '-'
    },
    {
      title: 'Average Price',
      value: shopData.value?.shopMonthData.price || '-',
      percent: shopData.value?.shopMonthData.priceGrowth || '-'
    },
    {
      title: 'Associated Lives',
      value: shopData.value?.shopMonthData.lives || '-',
      percent: shopData.value?.shopMonthData.livesGrowth || '-'
    },
    {
      title: 'Associated Videos',
      value: shopData.value?.shopMonthData.videos || '-',
      percent: shopData.value?.shopMonthData.videosGrowth || '-'
    },
    {
      title: 'Affiliates',
      value: shopData.value?.shopMonthData.affiliates || '-',
      percent: shopData.value?.shopMonthData.affiliatesGrowth || '-'
    }
  ];
});

const bestSellingProductIndicators = computed(() => {
  return [
    {
      title: 'Total GMV',
      value: shopData.value?.shopProductData.totalGmv || '-',
      showPercent: false
    },
    {
      title: 'Total Units Sold',
      value: shopData.value?.shopProductData.totalSold || '-',
      showPercent: false
    },
    {
      title: 'Monthly GMV',
      value: shopData.value?.shopProductData.monthlyGmv || '-',
      showPercent: true,
      percent: shopData.value?.shopProductData.monthlyGmvGrowth || '-'
    },
    {
      title: 'Monthly Units sold',
      value: shopData.value?.shopProductData.monthlySold || '-',
      showPercent: true,
      percent: shopData.value?.shopProductData.monthlySoldGrowth || '-'
    }
  ];
});

const { columns, data, pagination, loading, empty, getData, updateSearchParams } = useTable({
  immediate: false,
  apiFn: fetchGetCreatorsWidthGmv,
  apiParams: {
    current: 1,
    size: 10
  },
  columns() {
    return [
      {
        key: 'index',
        width: 50,
        align: 'center'
      },
      {
        key: 'creatorId',
        title: 'Creator',
        width: 200,
        render(rowData) {
          return (
            <div
              class="flex items-center gap-2 hover:(cursor-pointer text-primary)"
              onClick={() => handleLinkToAccount(rowData.creatorId)}
            >
              <div class="flex-col">
                <NEllipsis class="font-bold">{rowData.creatorNickname}</NEllipsis>
                <span>@{rowData.creatorId || '-'}</span>
              </div>
            </div>
          );
        }
      },
      {
        key: 'gmv',
        title: 'GMV',
        align: 'center',
        sorter: true,
        sortOrder: 'descend'
      },
      {
        key: 'followers',
        title: 'Followers',
        align: 'center'
      },
      {
        key: 'sold',
        title: 'Units Sold',
        align: 'center'
      },
      {
        key: 'gpmLive',
        title: 'Live GPM',
        align: 'center'
      },
      {
        key: 'gpmVideo',
        title: 'Video GPM',
        align: 'center'
      }
    ];
  }
});

const creatorDesc = computed(() => {
  if (!shopData.value) return '';
  const { totalCreatorNum, shopName } = shopData.value;
  const month = dayjs.tz(shopData.value?.belongMonth, 'Etc/Gmt+8').format('MMM');
  const realTotal = numberFormat(totalCreatorNum, NumeralFormat.Real_Number);
  const realGmvNum = numberFormat(pagination.itemCount, NumeralFormat.Real_Number);

  return (
    <span>
      <span class="font-bold">{shopName}</span> partnered with <span class="font-bold">{realTotal}</span>&nbsp;
      influencers in&nbsp;
      <span class="font-bold">{month}</span>, including <span class="font-bold">{realGmvNum}</span> who drove GMV. The
      list is provided below.
    </span>
  );
});

async function handleDownload(type: string) {
  toggleExport(true);
  await downloadReport(type as 'pdf' | 'png', 'performance-report-container');
  toggleExport(false);
}

function handleLinkTo(url: string) {
  if (url) {
    window.open(url, '_blank');
  }
}

function handleLinkToAccount(url: string) {
  if (url) {
    window.open(`https://www.tiktok.com/@${url}`, '_blank');
  }
}

async function getPageData(id: number) {
  togglePageLoading(true);
  const { data: sData, error } = await fetchGetTopGmvShopById(id);
  if (!error) {
    shopData.value = sData;
  } else {
    shopData.value = undefined;
  }

  delay(() => {
    togglePageLoading(false);
  }, 500);
}

function getCreatorsData(id: number) {
  updateSearchParams({
    shopId: id
  });
  getData();
}

watch(
  () => visible.value,
  newVal => {
    if (newVal) {
      if (props.id) {
        getPageData(props.id);

        getCreatorsData(props.id);
      }
    }
  }
);
</script>

<template>
  <NDrawer v-model:show="visible" :width="1200">
    <NDrawerContent class="bg-#F7FAFC" body-content-class="flex-col gap-16px" closable>
      <Loading v-if="pageLoading" class="m-auto" />
      <NEmpty v-else-if="!shopData" class="m-auto" description="No data available. Please try again later." />
      <div v-else id="performance-report-container" class="flex-col gap-16px">
        <NCard class="card-wrapper" :bordered="false" :title="title">
          <template #header-extra>
            <NDropdown trigger="hover" placement="bottom-end" :options="downloadOptions" @select="handleDownload">
              <NButton id="download-button" quaternary>
                <template #icon>
                  <SvgIcon icon="solar:download-linear" />
                </template>
              </NButton>
            </NDropdown>
          </template>
        </NCard>
        <NGrid item-responsive responsive="screen" :x-gap="16" :y-gap="16">
          <NGi span="16">
            <ShopInfoCard
              class="h-full card-wrapper"
              :bordered="false"
              :idx="shopData.sort"
              :data="shopData"
              :more-info="true"
            />
          </NGi>
          <NGi span="8">
            <NCard class="h-full card-wrapper" content-class="flex-center" :bordered="false" title="Official Account">
              <NEmpty
                v-if="!shopData?.shopAccountData"
                class="m-auto"
                description="This shop does not have an official account."
              ></NEmpty>
              <NThing v-else content-indented>
                <template #avatar>
                  <div
                    class="relative h-60px w-60px border-1px rounded-full hover:cursor-pointer"
                    @click="handleLinkToAccount(shopData.shopAccountData.accountId)"
                  >
                    <NImage
                      preview-disabled
                      class="rounded-full"
                      :src="shopAccountAvatarUrl"
                      :fallback-src="getFallbackImage(60, 60)"
                    />
                    <icon-logos:tiktok-icon class="absolute bottom-0 right-0" />
                  </div>
                </template>
                <template #header>
                  <span class="text-xl font-bold">{{ shopData.shopAccountData.accountName }}</span>
                </template>
                <template #description>
                  <NDescriptions label-class="text-coolgray" content-class="" :column="1" label-placement="left">
                    <NDescriptionsItem label="ID">{{ shopData.shopAccountData.accountId }}</NDescriptionsItem>
                    <NDescriptionsItem label="Followers">
                      {{ shopData.shopAccountData.followers || '-' }}
                    </NDescriptionsItem>
                  </NDescriptions>
                </template>
              </NThing>
            </NCard>
          </NGi>
        </NGrid>
        <NCard class="card-wrapper" segmented :bordered="false" title="Shop Overview">
          <div class="flex gap-16px nowrap-hidden">
            <IndicatorItem
              v-for="so in shopOverview"
              :key="so.title"
              class="flex-1"
              :title="so.title"
              :value="so.value"
              :show-percent="false"
            />
          </div>
        </NCard>
        <NCard class="card-wrapper" segmented :bordered="false" title="Current Month Data">
          <div class="flex gap-16px nowrap-hidden">
            <IndicatorItem
              v-for="cm in currentMonthData"
              :key="cm.title"
              class="flex-1"
              :title="cm.title"
              :value="cm.value"
              show-percent
              :percent="cm.percent"
            />
          </div>
        </NCard>
        <NCard
          class="card-wrapper"
          content-class="relative"
          segmented
          :bordered="false"
          title="Best-Selling Product This Month"
        >
          <div class="absolute right-[50%] top-0 h-58px w-58px overflow-hidden bg-transparent">
            <div
              class="absolute right-[-50%] top-[8px] w-[100px] rotate-45 border-y-1px border-primary text-center"
              style="transform-origin: center"
            >
              <span class="inline-block text-primary">BEST</span>
            </div>
          </div>
          <NGrid class="divide-x" :cols="2">
            <NGi>
              <NThing content-indented class="pr-16px">
                <template #avatar>
                  <div
                    class="relative h-180px w-180px overflow-hidden border-1 rounded-md hover:cursor-pointer"
                    @click="handleLinkTo(shopData.shopProductData.homePage)"
                  >
                    <NImage
                      preview-disabled
                      :src="bestSellingProductAvatarUrl"
                      :fallback-src="getFallbackImage(180, 180)"
                    />
                    <icon-logos:tiktok-icon class="absolute right-16px top-16px" />
                  </div>
                </template>
                <template #header>
                  <div class="h-77px overflow-hidden">
                    <NEllipsis
                      v-if="!isExport"
                      style="max-width: 100%"
                      class="pr-25px font-bold"
                      :tooltip="{ contentStyle: 'max-width:400px' }"
                      :line-clamp="3"
                    >
                      {{ shopData.shopProductData.productName || '-' }}
                    </NEllipsis>
                    <div v-else class="line-clamp-3 break-all pr-25px font-bold">
                      {{ truncatedProductName }}
                    </div>
                  </div>
                </template>
                <NDescriptions label-class="text-coolgray" content-class="" :column="1" label-placement="left">
                  <NDescriptionsItem label="Launch Date">{{ shopData.shopProductData.launchDate }}</NDescriptionsItem>
                  <NDescriptionsItem label="Commission Rate">
                    {{ shopData.shopProductData.commissionRate || '-' }}
                  </NDescriptionsItem>
                  <NDescriptionsItem label="Price">{{ shopData.shopProductData.price || '-' }}</NDescriptionsItem>
                </NDescriptions>
              </NThing>
            </NGi>
            <NGi>
              <NGrid class="h-full pl-16px" :cols="2" :x-gap="16" :y-gap="16">
                <NGi v-for="item in bestSellingProductIndicators" :key="item.title">
                  <IndicatorItem
                    :title="item.title"
                    :value="item.value"
                    :show-percent="item.showPercent"
                    :percent="item.percent"
                  />
                </NGi>
              </NGrid>
            </NGi>
          </NGrid>
        </NCard>
        <NCard
          class="card-wrapper"
          content-class="flex-col gap-16px"
          segmented
          :bordered="false"
          title="Key Content for the Best-Selling Product"
        >
          <NGrid class="min-h-369px" :cols="2" x-gap="16">
            <NGi>
              <div class="flex gap-16px nowrap-hidden pb-16px">
                <span class="flex-1 text-xl">Best Live Stream</span>
              </div>
              <NGrid v-if="shopData.shopLivesData.length > 0" :cols="2" x-gap="16">
                <NGi v-for="item in shopData.shopLivesData" :key="item.accountAvatarLocal">
                  <VideoLiveInfoCard class="h-full" :is-export="isExport" type="live" :data="item" />
                </NGi>
              </NGrid>
              <div v-else class="h-full flex-center">
                <NEmpty description="No live stream available at the moment" />
              </div>
            </NGi>
            <NGi>
              <div class="flex gap-16px nowrap-hidden pb-16px">
                <span class="flex-1 text-xl">Best Video</span>
              </div>
              <NGrid v-if="shopData.shopVideosData.length > 0" :cols="2" x-gap="16">
                <NGi v-for="item in shopData.shopVideosData" :key="item.accountAvatarLocal">
                  <VideoLiveInfoCard class="h-full" :is-export="isExport" type="video" :data="item" />
                </NGi>
              </NGrid>
              <div v-else class="h-full flex-center">
                <NEmpty description="No product video available at the moment" />
              </div>
            </NGi>
          </NGrid>
        </NCard>
        <NCard
          v-if="!empty"
          class="card-wrapper"
          content-class="flex-col gap-4"
          segmented
          :bordered="false"
          title="Creators Performance"
        >
          <NAlert :bordered="false"><component :is="creatorDesc" /></NAlert>
          <NDataTable
            remote
            :bordered="false"
            :loading="loading"
            :data="data"
            :columns="columns"
            :pagination="pagination"
          ></NDataTable>
        </NCard>
        <LogoTimeBar
          label="Updated at"
          :content="dayjs(shopData.updateTime).tz('Etc/Gmt+8').format(TimeFormat.US_TIME_24)"
        />
      </div>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
