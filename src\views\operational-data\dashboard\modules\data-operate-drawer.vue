<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import dayjs from 'dayjs';
import {
  fetchAddOperationalDashboardData,
  fetchUpdateOperationalDashboardDataById
} from '@/service/api/operational-data';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.OperationalData.MonthlyData | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});

const { formRef, validate, restoreValidation } = useNaiveForm();
const { formRules, defaultRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: 'Add Data',
    edit: 'Edit Data'
  };
  return titles[props.operateType];
});

type Model = Omit<Api.OperationalData.MonthlyData, 'id'> & {
  date: number | null;
};

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
    year: 0,
    month: 0,
    date: null,
    clients: 0,
    clientName: '',
    grossProfit: 0,
    gmv: 0,
    videos: 0,
    livestreams: 0,
    advertising: 0,
    orderNum: 0,
    income: 0,
    expenditure: 0,
    affiliates: 0
  };
}

type RuleKey = Exclude<
  keyof Model,
  | 'month'
  | 'year'
  | 'grossProfitFormatted'
  | 'gmvFormatted'
  | 'advertisingFormatted'
  | 'clients'
  | 'incomeFormatted'
  | 'expenditureFormatted'
>;

const rules: Record<RuleKey, App.Global.FormRule | App.Global.FormRule[]> = {
  date: defaultRequiredRule,
  clientName: defaultRequiredRule,
  grossProfit: formRules.positiveNum,
  gmv: formRules.positiveNum,
  videos: formRules.positiveNum,
  livestreams: formRules.positiveNum,
  advertising: formRules.positiveNum,
  orderNum: formRules.positiveNum,
  income: formRules.positiveNum,
  expenditure: formRules.positiveNum,
  affiliates: formRules.positiveNum
};

function handleInitModel() {
  Object.assign(model, createDefaultModel());

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData, {
      date: dayjs().year(props.rowData.year).month(props.rowData.month).valueOf()
    });
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();
  // request
  let response;
  model.year = dayjs(model.date).year();
  model.month = dayjs(model.date).month();
  if (props.operateType === 'edit') {
    response = await fetchUpdateOperationalDashboardDataById(model);
  } else {
    response = await fetchAddOperationalDashboardData(model);
  }
  if (response?.error) return;
  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

const format = (value: number | null) => {
  if (value === null) return '';
  return value.toLocaleString('en-US');
};

const parse = (input: string) => {
  const nums = input.replace(/,/g, '').trim();
  if (/^\d+(\.(\d+)?)?$/.test(nums)) return Number(nums);
  return nums === '' ? null : Number.NaN;
};

watch(visible, () => {
  if (visible.value) {
    handleInitModel();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" display-directive="show" :width="360">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="Date" path="date">
          <NDatePicker
            v-model:value="model.date"
            class="w-full"
            type="month"
            input-readonly
            month-format="MMM"
            format="MMM yyyy"
          ></NDatePicker>
        </NFormItem>
        <NFormItem label="Client Name" path="clientName">
          <NInput v-model:value="model.clientName" placeholder="Client Name" class="w-full"></NInput>
        </NFormItem>
        <NFormItem label="Gross Profit" path="grossProfit">
          <NInputNumber
            v-model:value="model.grossProfit"
            class="w-full"
            :min="0"
            :format="format"
            :parse="parse"
            :precision="2"
          >
            <template #prefix>$</template>
          </NInputNumber>
        </NFormItem>
        <NFormItem label="Income" path="income">
          <NInputNumber
            v-model:value="model.income"
            class="w-full"
            :min="0"
            :format="format"
            :parse="parse"
            :precision="2"
          >
            <template #prefix>$</template>
          </NInputNumber>
        </NFormItem>
        <NFormItem label="Expenditure" path="expenditure">
          <NInputNumber
            v-model:value="model.expenditure"
            class="w-full"
            :min="0"
            :format="format"
            :parse="parse"
            :precision="2"
          >
            <template #prefix>$</template>
          </NInputNumber>
        </NFormItem>
        <NFormItem label="GMV" path="gmv">
          <NInputNumber
            v-model:value="model.gmv"
            class="w-full"
            :min="0"
            :format="format"
            :parse="parse"
            :precision="2"
          >
            <template #prefix>$</template>
          </NInputNumber>
        </NFormItem>
        <NFormItem label="Order" path="order">
          <NInputNumber
            v-model:value="model.orderNum"
            class="w-full"
            :min="0"
            :format="format"
            :parse="parse"
          ></NInputNumber>
        </NFormItem>
        <NFormItem label="Affiliates" path="affiliates">
          <NInputNumber
            v-model:value="model.affiliates"
            class="w-full"
            :min="0"
            :format="format"
            :parse="parse"
          ></NInputNumber>
        </NFormItem>
        <NFormItem label="Videos" path="videos">
          <NInputNumber
            v-model:value="model.videos"
            class="w-full"
            :min="0"
            :format="format"
            :parse="parse"
          ></NInputNumber>
        </NFormItem>
        <NFormItem label="Livestreams" path="livestreams">
          <NInputNumber
            v-model:value="model.livestreams"
            class="w-full"
            :min="0"
            :format="format"
            :parse="parse"
          ></NInputNumber>
        </NFormItem>
        <NFormItem label="Advertising" path="advertising">
          <NInputNumber
            v-model:value="model.advertising"
            class="w-full"
            :min="0"
            :format="format"
            :parse="parse"
          ></NInputNumber>
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
