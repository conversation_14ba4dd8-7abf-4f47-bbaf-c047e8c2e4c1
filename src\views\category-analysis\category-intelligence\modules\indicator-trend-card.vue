<script setup lang="ts">
import { watch } from 'vue';
import dayjs from 'dayjs';
import { fetchGetIndiactorTrendData } from '@/service/api';
import { useVChart } from '@/hooks/common/vchart';
import { formatSeriesAndAxes } from '@/utils/chart-options';
import { NumeralFormat, TimeFormat } from '@/enum';

type Key = keyof Api.CategoryIntelligence.IndiactorTrendResponse;

interface Props {
  shopType: number;
}

const props = defineProps<Props>();

const typeObj: Record<Key, string> = {
  contentList: 'Content Volume',
  followersList: 'Followers Gained',
  salesList: 'Gross Sales',
  soldList: 'Units Sold'
};

const defaultSpec: Visactor.VChart.IAreaChartSpec = {
  type: 'area',
  padding: 0,
  data: [],
  xField: 'x',
  yField: 'y',
  seriesField: 'type',
  legends: {
    visible: true,
    position: 'middle',
    orient: 'top'
  },
  axes: [
    {
      orient: 'bottom',
      label: {
        formatMethod(v) {
          return v ? dayjs(v as string).format(TimeFormat.US_DATE_NO_DAY) : v;
        }
      }
    }
  ],
  area: {
    style: {
      fill: {
        gradient: 'linear',
        x0: 0.5,
        y0: 0,
        x1: 0.5,
        y1: 1,
        stops: [
          {
            offset: 0,
            opacity: 1
          },
          {
            offset: 1,
            opacity: 0
          }
        ]
      }
    }
  }
};

const { domRef: chartRef, updateSpec } = useVChart(() => defaultSpec);

function formatData(data: Api.CategoryIntelligence.IndiactorTrendResponse) {
  const result: Visactor.VChart.IDataValues[] = [];
  for (const key in data) {
    if (Object.hasOwn(data, key)) {
      result.push({
        id: key,
        values: data[key as Key].map(d => ({
          x: d.belongMonth,
          y: d.number,
          type: typeObj[key as Key]
        }))
      });
    }
  }
  return result;
}

async function initData(shopType: number) {
  const { data, error } = await fetchGetIndiactorTrendData(shopType);
  if (!error) {
    const specData = formatData(data);
    setTimeout(() => {
      updateSpec(oldOpts => {
        let res = oldOpts;
        specData.forEach(d => {
          res = formatSeriesAndAxes(
            res,
            [d.id as string],
            ['salesList', 'soldList'].includes(d.id as string) ? 'left' : 'right',
            ['salesList'].includes(d.id as string) ? NumeralFormat.Dollar : NumeralFormat.Number,
            undefined,
            true
          ) as any;
        });
        return { ...res, data: specData };
      });
    });
  }
}

watch(
  () => props.shopType,
  newVal => {
    initData(newVal);
  },
  {
    immediate: true
  }
);
</script>

<template>
  <NCard class="card-wrapper" :bordered="false">
    <div ref="chartRef" class="h-400px w-full"></div>
  </NCard>
</template>

<style scoped></style>
