<script setup lang="tsx">
import { ref } from 'vue';
import { N<PERSON><PERSON><PERSON>, NPopconfirm } from 'naive-ui';
import { isNil } from 'lodash-es';
import { useBoolean } from '@sa/hooks';
import {
  fetchBatchDeleteRoleByIds,
  fetchBindRoleUser,
  fetchDeleteRoleById,
  fetchGetRoleList,
  fetchGetRoleUserIdList
} from '@/service/api';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import UserAuthModal from '../modules/user-auth-modal.vue';
import RoleOperateDrawer from './modules/role-operate-drawer.vue';
import MenuAuthModal from './modules/menu-auth-modal.vue';

const appStore = useAppStore();

const { bool: menuAuthVisible, setTrue: openMenuAuthModal } = useBoolean();
const { bool: userAuthVisible, setTrue: openUserAuthModal } = useBoolean();

const roleId = ref<number>(-1);

const onClickMenus = (id: number) => {
  roleId.value = id;
  openMenuAuthModal();
};

function onClickUsers(id: number) {
  roleId.value = id;
  openUserAuthModal();
}

const { columns, columnChecks, data, loading, getData, mobilePagination } = useTable({
  apiFn: fetchGetRoleList,
  apiParams: {
    current: 1,
    size: 10,
    roleName: null,
    roleCode: null
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      width: 64,
      align: 'center'
    },
    {
      key: 'roleName',
      title: $t('page.manage.role.roleName'),
      align: 'center',
      minWidth: 120
    },
    {
      key: 'roleCode',
      title: $t('page.manage.role.roleCode'),
      align: 'center',
      minWidth: 120
    },
    {
      key: 'roleDesc',
      title: $t('page.manage.role.roleDesc'),
      align: 'center',
      minWidth: 120
    },
    // {
    //   key: 'status',
    //   title: $t('page.manage.role.roleStatus'),
    //   align: 'center',
    //   width: 100,
    //   render: (row) => {
    //     if (row.status === null) {
    //       return null
    //     }

    //     const tagMap: Record<Api.Common.EnableStatus, NaiveUI.ThemeColor> = {
    //       1: 'success',
    //       2: 'warning'
    //     }

    //     const label = $t(enableStatusRecord[row.status])

    //     return <NTag type={tagMap[row.status]}>{label}</NTag>
    //   }
    // },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 260,
      render: row => (
        <div class="flex-center gap-8px">
          <NButton type="primary" ghost size="small" onClick={() => edit(row.id)}>
            {$t('common.edit')}
          </NButton>
          <NButton type="primary" ghost size="small" onClick={() => onClickMenus(row.id)}>
            Menus
          </NButton>
          <NButton type="primary" ghost size="small" onClick={() => onClickUsers(row.id)}>
            Users
          </NButton>
          <NPopconfirm onPositiveClick={() => handleDelete(String(row.id))}>
            {{
              default: () => $t('common.confirmDelete'),
              trigger: () => (
                <NButton type="error" ghost size="small">
                  {$t('common.delete')}
                </NButton>
              )
            }}
          </NPopconfirm>
        </div>
      )
    }
  ]
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleAdd,
  handleEdit,
  checkedRowKeys,
  onBatchDeleted,
  onDeleted
  // closeDrawer
} = useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  const { error } = await fetchBatchDeleteRoleByIds(checkedRowKeys.value as string[]);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: string) {
  // request
  const { error } = await fetchDeleteRoleById(id);
  if (error) return;

  onDeleted();
}

async function handleGetCheckedRowKeys() {
  let res: number[] = [];
  if (isNil(roleId.value)) return res;

  const { data: ids, error: idListErr } = await fetchGetRoleUserIdList(roleId.value);
  if (!idListErr) {
    res = ids;
  }

  return res;
}

async function handleBindShopUser(checkedKeys: number[] | string[]) {
  if (isNil(roleId.value)) return false;

  const { error: bindUserErr } = await fetchBindRoleUser(roleId.value, checkedKeys as number[]);
  if (!bindUserErr) {
    return true;
  }

  return false;
}

function edit(id: number) {
  handleEdit(id);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <!--
 <RoleSearch
      v-model:model="searchParams"
      @reset="resetSearchParams"
      @search="getData"
    />
-->
    <NCard :title="$t('page.manage.role.title')" :bordered="false" size="small" class="card-wrapper sm:flex-1-hidden">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="702"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <RoleOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getData"
      />
      <MenuAuthModal v-model:visible="menuAuthVisible" :role-id="roleId" />
      <UserAuthModal
        v-model:show="userAuthVisible"
        :get-checked-row-keys="handleGetCheckedRowKeys"
        :submit="handleBindShopUser"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
