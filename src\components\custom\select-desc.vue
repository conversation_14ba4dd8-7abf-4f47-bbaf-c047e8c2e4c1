<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-08 16:27:26
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-18 10:16:57
 * @FilePath: \tiksage-frontend\src\components\custom\select-desc.vue
 * @Description: custom-description-select
-->
<script setup lang="tsx">
import type { VNodeChild } from 'vue';
import type { SelectOption } from 'naive-ui';
import { NFlex } from 'naive-ui';
import Tip from './tip.vue';

function renderLabel(option: SelectOption): VNodeChild {
  return (
    <NFlex class="truncate" align="center" wrap={false}>
      <span>{option.label}</span>
      {option.description ? <Tip description={option.description as string} /> : ''}
    </NFlex>
  );
}
</script>

<template>
  <NSelect :render-label="renderLabel"></NSelect>
</template>

<style scoped></style>
