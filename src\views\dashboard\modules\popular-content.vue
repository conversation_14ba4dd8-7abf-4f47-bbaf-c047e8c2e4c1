<script setup lang="tsx">
import { ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { NButton, NEllipsis, NFlex, NImage, NText } from 'naive-ui';
import { fetchGetLiveContentTop, fetchGetVideoContentTop } from '@/service/api';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { dateFormat } from '@/utils/date';
import { getFallbackImage } from '@/utils/fake-image';
import ButtonCopy from '@/components/custom/button-copy.vue';
import SvgIcon from '@/components/custom/svg-icon.vue';
import TablerTitleTip from '@/components/custom/tabler-title-tip.vue';
import { NumeralFormat } from '@/enum';
import VideoHistoryModal from './creator-performance/video-history-modal.vue';

const { VITE_TODAY_VIDEO_AVATAR_URL, VITE_CREATOR_AVATAR_URL } = import.meta.env;

interface Props {
  params: Api.Dashboard.DashboardTopSearchParams;
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const selectedOption = ref<string>('video');
const options = ref<CommonType.Option<string>[]>([
  {
    label: 'Video',
    value: 'video'
  },
  {
    label: 'LIVE',
    value: 'live'
  }
]);

const contentType = ref<4 | 5>(5);

const searchParams = ref<Api.Dashboard.ContentTopSearchParams>({
  startDate: '',
  endDate: '',
  shopId: 0,
  type: 5
});

const selectedVideoId = ref<string>();
const [modalShow, toggleModalShow] = useToggle(false);

function createSearchParams(): Api.Dashboard.ContentTopSearchParams {
  return {
    startDate: props.params.startDateStr,
    endDate: props.params.endDateStr,
    shopId: props.params.shopIdsArr?.[0] || 0,
    type: contentType.value
  };
}

const [loading, toggleLoading] = useToggle(false);
const data = ref<Api.Dashboard.LiveContentTop[] | Api.Dashboard.VideoContentTop[]>([]);

async function getContentData(params: Api.Dashboard.ContentTopSearchParams) {
  toggleLoading(true);
  data.value = [];

  let res;
  switch (selectedOption.value) {
    case 'video':
      res = await fetchGetVideoContentTop(params);
      break;
    case 'live':
      res = await fetchGetLiveContentTop(params);
      break;
    default:
      break;
  }

  if (!res?.error) {
    data.value = res?.data as any;
  }
  toggleLoading(false);
}

const videoColumns = ref<NaiveUI.DataTableColumns<Api.Dashboard.VideoContentTop>>([
  {
    key: 'title',
    align: 'center',
    fixed: 'left',
    width: 50,
    render(_rowData, index) {
      if (index === 0) {
        return (
          <NFlex class="w-100%" justify="center">
            <SvgIcon style="width:25px;height:25px" icon="twemoji:1st-place-medal" />
          </NFlex>
        );
      } else if (index === 1) {
        return (
          <NFlex class="w-100%" justify="center">
            <SvgIcon style="width:25px;height:25px" icon="twemoji:2nd-place-medal" />
          </NFlex>
        );
      } else if (index === 2) {
        return (
          <NFlex class="w-100%" justify="center">
            <SvgIcon style="width:25px;height:25px" icon="twemoji:3rd-place-medal" />
          </NFlex>
        );
      }
      return <span>{index + 1}</span>;
    }
  },
  {
    key: 'videoID',
    title: 'Video Information',
    width: 300,
    fixed: 'left',
    render(rowData) {
      return (
        <div
          class="flex flex-nowrap items-center gap-2 hover:(cursor-pointer text-primary)"
          onClick={() => {
            window.open(rowData.video);
          }}
        >
          <div class="relative h-70px w-40px flex-shrink-0">
            <NImage
              class="h-full w-full rounded"
              objectFit="cover"
              src={`${VITE_TODAY_VIDEO_AVATAR_URL}${rowData.videoAvatarLocal}`}
              fallbackSrc={getFallbackImage(40, 71)}
            />
            <div class="absolute inset-0 flex-center rounded bg-black/20">
              <SvgIcon icon="solar:play-circle-bold" class="text-lg text-white" />
            </div>
          </div>
          <NFlex vertical>
            <NEllipsis line-clamp={1} tooltip={{ contentClass: 'max-w-400px' }}>
              {rowData.title}
            </NEllipsis>
            <NFlex>
              <NFlex class="text-gray" justify="center" wrap={false}>
                <span>@{rowData.creatorHandle}</span>
                <ButtonCopy copy={rowData.creatorHandle} />
              </NFlex>
              <NText class="text-xs text-gray">{dateFormat(rowData.postTimestamp)}</NText>
            </NFlex>
          </NFlex>
        </div>
      );
    }
  },
  {
    key: 'gmv',
    title() {
      return (
        <TablerTitleTip
          justify="center"
          title="GMV"
          description="The total amount paid for orders placed directly from the shoppable video, including returns and refunds."
        />
      );
    },
    align: 'center',
    sorter: 'default',
    sortOrder: 'descend',
    width: 120,
    render(rowData) {
      const val = numberFormat(rowData.gmv, NumeralFormat.Dollar);
      return (
        <div class="flex-center gap-2">
          <span>{val}</span>
          <NButton type="primary" size="small" text onClick={() => handleShowTrend(rowData.videoID)}>
            {{
              icon: () => <SvgIcon icon="solar:diagram-up-linear" />
            }}
          </NButton>
        </div>
      );
    }
  },
  {
    key: 'itemSold',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="Units sold"
        description="The total number of units sold directly from the shoppable video."
      />
    ),
    align: 'center',
    width: 120
  },

  {
    key: 'views',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="Views"
        description="Video views.Number of the video views during the selected period."
      />
    ),
    align: 'center',
    width: 120
  },
  {
    key: 'likes',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="Likes"
        description="Number of likes the video received during the selected period."
      />
    ),
    align: 'center',
    width: 120
  },
  {
    key: 'comments',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="Comments"
        description="Total number of comments on the video during the selected period."
      />
    ),
    align: 'center',
    width: 120
  },
  {
    key: 'shares',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="Shares"
        description="Total number of times the video was shared during the selected period."
      />
    ),
    align: 'center',
    width: 120
  },
  {
    key: 'productImpressions',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="Product impressions"
        description="Number of product impressions from the video posted during the selected period for the selected products."
      />
    ),
    align: 'center',
    width: 120
  },
  {
    key: 'newFollowers',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="New followers"
        description="Number of viewers who followed creators from the video during the selected period."
      />
    ),
    align: 'center',
    width: 120
  },
  {
    key: 'ctr',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="CTR"
        description="The number of clicks on video products divided by the cumulative number of times the video has been played during the selected period."
      />
    ),
    align: 'center',
    width: 120
  }
]);

const liveColumns = ref<NaiveUI.DataTableColumns<Api.Dashboard.LiveContentTop>>([
  {
    key: 'title',
    align: 'center',
    fixed: 'left',
    width: 50,
    render(_rowData, index) {
      if (index === 0) {
        return (
          <NFlex class="w-100%" justify="center">
            <SvgIcon style="width:25px;height:25px" icon="twemoji:1st-place-medal" />
          </NFlex>
        );
      } else if (index === 1) {
        return (
          <NFlex class="w-100%" justify="center">
            <SvgIcon style="width:25px;height:25px" icon="twemoji:2nd-place-medal" />
          </NFlex>
        );
      } else if (index === 2) {
        return (
          <NFlex class="w-100%" justify="center">
            <SvgIcon style="width:25px;height:25px" icon="twemoji:3rd-place-medal" />
          </NFlex>
        );
      }
      return <span>{index + 1}</span>;
    }
  },
  {
    key: 'liveID',
    title: 'LIVE Information',
    width: 300,
    fixed: 'left',
    render(rowData) {
      return (
        <div class="flex flex-nowrap items-center gap-2">
          <div class="relative h-71px h-full w-40px flex-shrink-0">
            <NImage
              class="h-full w-full rounded"
              objectFit="cover"
              src={`${VITE_CREATOR_AVATAR_URL}${rowData.liveAvatarLocal}`}
              fallbackSrc={getFallbackImage(40, 71)}
            />
            <div class="absolute inset-0 flex-center rounded bg-black/20">
              <SvgIcon icon="solar:play-circle-bold" class="text-lg text-white" />
            </div>
          </div>
          <NFlex vertical>
            <NEllipsis line-clamp={1} tooltip={{ contentClass: 'max-w-400px' }}>
              {rowData.title}
            </NEllipsis>
            <NFlex>
              <NFlex class="text-gray" justify="center" wrap={false}>
                <span>@{rowData.creatorHandle}</span>
                <ButtonCopy copy={rowData.creatorHandle} />
              </NFlex>
              <NText class="text-xs text-gray">{dateFormat(rowData.endTimestamp)}</NText>
            </NFlex>
          </NFlex>
        </div>
      );
    }
  },
  {
    key: 'gmv',
    title() {
      return (
        <TablerTitleTip
          justify="center"
          title="GMV"
          description="The total amount paid for orders placed directly from the LIVE, including returns and refunds."
        />
      );
    },
    align: 'center',
    sorter: 'default',
    sortOrder: 'descend',
    width: 120
  },
  {
    key: 'itemSold',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="Units sold"
        description="The number of units sold directly from the LIVE."
      />
    ),
    align: 'center',
    width: 120
  },

  {
    key: 'views',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="Views"
        description="Number of views for the LIVE video posted during the selected period."
      />
    ),
    align: 'center',
    width: 120
  },
  {
    key: 'likes',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="Likes"
        description="Number of likes on the LIVE video posted during the selected period."
      />
    ),
    align: 'center',
    width: 120
  },
  {
    key: 'comments',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="Comments"
        description="Number of comments on the LIVE video posted during the selected period."
      />
    ),
    align: 'center',
    width: 120
  },
  {
    key: 'shares',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="Shares"
        description="Number of times the LIVE video posted during the selected period were shared."
      />
    ),
    align: 'center',
    width: 120
  },
  {
    key: 'productImpressions',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="Product impressions"
        description="Number of product impressions during the LIVE video posted during the selected period, including product lists and product cards."
      />
    ),
    align: 'center',
    width: 120
  },
  {
    key: 'newFollowers',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="New followers"
        description="Number of viewers who started following the creator from the LIVE video posted during the selected period."
      />
    ),
    align: 'center',
    width: 120
  },
  {
    key: 'ctr',
    title: () => (
      <TablerTitleTip
        justify="center"
        title="CTR"
        description="Number of product clicks divided by number of product impressions during the LIVE video during the selected period, including products in product lists or on product cards."
      />
    ),
    align: 'center',
    width: 120
  }
]);

const dollarKeys = ['gmv'];
const percentKeys = ['ctr'];

const renderCell = (value: any, _rowData: any, column: any) => {
  if (dollarKeys.includes(column.key)) {
    return numberFormat(value, NumeralFormat.Dollar);
  }
  if (percentKeys.includes(column.key)) {
    return numberFormat(Number(value), NumeralFormat.Percent);
  } else if (typeof value === 'number') {
    return numberFormat(value, NumeralFormat.Number);
  }
  return value;
};

function handleShowTrend(videoId: string) {
  selectedVideoId.value = videoId;
  toggleModalShow(true);
}

watch([() => selectedOption.value, () => searchParams.value.type], () => {
  if (searchParams.value.shopId) {
    getContentData(searchParams.value);
  }
});

watch(
  () => props.params,
  newVal => {
    if (newVal.shopIdsArr?.length) {
      searchParams.value = createSearchParams();
      getContentData(searchParams.value);
    }
  },
  {
    immediate: true
  }
);
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" title="Top 10 Best-Performing Content">
    <template #header-extra>
      <div class="flex gap-4">
        <NSelect v-model:value="selectedOption" :options="options" :consistent-menu-width="false"></NSelect>
        <NRadioGroup v-model:value="searchParams.type">
          <NRadioButton label="Affiliate accounts" :value="5"></NRadioButton>
          <NRadioButton label="Linked accounts" :value="4"></NRadioButton>
        </NRadioGroup>
      </div>
    </template>
    <NDataTable
      size="small"
      :data="data"
      :bordered="false"
      :loading="loading"
      :columns="selectedOption === 'video' ? videoColumns : liveColumns"
      :render-cell="renderCell"
      :scroll-x="1430"
    >
      <template #empty>
        <NEmpty description="No data available for this shop yet"></NEmpty>
      </template>
    </NDataTable>
    <VideoHistoryModal v-model:show="modalShow" :video-id="selectedVideoId" :shop-id="searchParams.shopId" />
  </NCard>
</template>

<style scoped></style>
