<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-26 13:56:49
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-04 16:23:36
 * @FilePath: \tiksage-frontend\src\views\category-intelligence\index.vue
 * @Description: category-intelligence page
-->
<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { last, upperFirst } from 'lodash-es';
import { fetchGetOverview, fetchMonthList } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { NumeralFormat } from '@/enum';
import CreatorTop10Card from './modules/creator-top10-card.vue';
import PieTableCard from './modules/pie-table-card.vue';
import ProductPriceCard from './modules/product-price-card.vue';
import ShopCompositionCard from './modules/shop-composition-card.vue';
import SkuTop10Card from './modules/sku-top10-card.vue';
import IndicatorTrendCard from './modules/indicator-trend-card.vue';
import VideoTop5Card from './modules/video-top-5-card.vue';

export interface MetricItem {
  key: keyof Api.CategoryIntelligence.TotalMetrics;
  title: string;
  description: string;
  value: number;
  unit: NumeralFormat;
  icon: string;
  decimals: number;
  isConversion: boolean;
  prevValue?: number;
  conversionDescription?: string;
}

const authStore = useAuthStore();

const { getDictionaryByCodeType } = useDictionaryStore();

const mainCategoryOptions = ref<Api.Dictionary.DictionaryItem<number>[]>([]);

type Model = Api.CategoryIntelligence.ShopTypeParams & Api.CategoryIntelligence.MonthParams;

const model = reactive<Model>({
  month: '',
  shopType: 0
});

const totalMetrics = reactive<MetricItem[]>([
  {
    key: 'totalShops',
    title: 'Total Shop Count',
    description: `The 'Total Shop Count' indicates the number of top 100 shops by historical total sales in this category, highlighting the market's leading performers.`,
    value: 0,
    unit: NumeralFormat.Number,
    decimals: 0,
    icon: 'solar:shop-2-bold-duotone',
    isConversion: false
  },
  {
    key: 'totalSales',
    title: 'Total Sales',
    description: 'Total Sales represents the combined historical sales revenue of the top 100 shops in this category.',
    value: 0,
    unit: NumeralFormat.Dollar,
    decimals: 2,
    icon: 'solar:dollar-bold-duotone',
    isConversion: false
  },
  {
    key: 'totalUnitsSold',
    title: 'Total Units Sold',
    description: 'Total Units Sold indicates the total historical sales volume of these shops.',
    value: 0,
    unit: NumeralFormat.Number,
    decimals: 2,
    icon: 'solar:bag-2-bold-duotone',
    isConversion: false
  },
  {
    key: 'totalFollowers',
    title: 'No. of Followers',
    description: 'The No. of Followers is the aggregate follower count for all these shops.',
    value: 0,
    unit: NumeralFormat.Number,
    decimals: 2,
    icon: 'solar:users-group-rounded-bold-duotone',
    isConversion: false
  },
  {
    key: 'totalContentVolume',
    title: 'Total Content Volume',
    description: 'The total number of short videos and live sessions in the current period.',
    value: 0,
    unit: NumeralFormat.Number,
    decimals: 2,
    icon: 'solar:play-circle-bold-duotone',
    isConversion: false
  }
]);
const growthMetrics = reactive<MetricItem[]>([
  {
    key: 'salesMob',
    title: 'Gross Sales',
    description: 'The total gross sales revenue for the current month.',
    value: 0,
    unit: NumeralFormat.Dollar,
    decimals: 2,
    icon: 'solar:money-bag-bold-duotone',
    isConversion: true,
    prevValue: 0,
    conversionDescription: 'Vs.previous period'
  },
  {
    key: 'unitsSoldGained',
    title: 'Units Sold Gained ',
    description: '',
    value: 0,
    unit: NumeralFormat.Number,
    decimals: 2,
    icon: 'solar:bag-2-bold-duotone',
    isConversion: true,
    prevValue: 0,
    conversionDescription: 'Vs.previous period'
  },
  {
    key: 'followersGained',
    title: 'Followers Gained ',
    description: '',
    value: 0,
    unit: NumeralFormat.Number,
    decimals: 2,
    icon: 'solar:users-group-two-rounded-bold-duotone',
    isConversion: true,
    prevValue: 0,
    conversionDescription: 'Vs.previous period'
  },

  {
    key: 'contentVolume',
    title: 'Content Volume Gained',
    description: '',
    value: 0,
    unit: NumeralFormat.Number,
    decimals: 2,
    icon: 'solar:graph-up-bold-duotone',
    isConversion: true,
    prevValue: 0,
    conversionDescription: 'Vs.previous period'
  }
]);

const [loading, toggleLoading] = useToggle(true);

const initData = async (params: Model) => {
  const { data, error } = await fetchGetOverview(params);
  if (error) return;
  const newTotalMetrics = totalMetrics.map(metric => {
    return {
      ...metric,
      value: data[metric.key]
    };
  });
  const newGrowthlMetrics = growthMetrics.map(metric => {
    const key = `previous${upperFirst(metric.key)}`;
    return {
      ...metric,
      value: data[metric.key],
      prevValue: data[key as keyof Api.CategoryIntelligence.OverviewResponse]
    };
  });

  Object.assign(totalMetrics, newTotalMetrics);
  Object.assign(growthMetrics, newGrowthlMetrics);
};

const [opstLoading, toggleOptsLoading] = useToggle(true);
async function initMainCategoryOptions() {
  toggleOptsLoading(true);
  const mainCateOpts = await getDictionaryByCodeType<number>('category');
  mainCategoryOptions.value = mainCateOpts || [];
  toggleOptsLoading(false);
}

const [monthLoading, toggleMonthLoading] = useToggle(false);
const dateOptions = ref<CommonType.Option<string>[]>([]);

async function getMonthList(shopType: number) {
  toggleMonthLoading(true);
  const { data: months } = await fetchMonthList({ shopType });
  if (months?.length) {
    dateOptions.value = months.map(month => ({ label: month, value: month }));

    model.month = last(months) || '';
    toggleMonthLoading(false);
  }
}

async function init() {
  toggleLoading(true);

  await initMainCategoryOptions();
  model.shopType = authStore.userInfo.userCategoryIds[0];
  await getMonthList(model.shopType);
  toggleLoading(false);
}

watch(
  () => model.shopType,
  async newShopType => {
    if (!newShopType) return;
    await getMonthList(newShopType);
  }
);

watch(
  () => model.month,
  async newMonth => {
    if (model.shopType && newMonth) {
      await initData(model);
    }
  }
);

init();
</script>

<template>
  <NFlex class="h-full p-16px" vertical :size="16">
    <NCard title="Category Intelligence" content-class="flex-col gap-16px" class="card-wrapper">
      <template #header-extra>
        <NSelect
          v-show="!opstLoading"
          v-model:value="model.shopType"
          :consistent-menu-width="false"
          :options="mainCategoryOptions"
          label-field="name"
          value-field="code"
        ></NSelect>
      </template>
      <NText class="text-sm text-gray">
        Dive into Category Intelligence to uncover the market landscape within your category. This feature offers a
        detailed analysis of leading shops, price tier performance, and top products, providing new users with valuable
        insights to navigate the TikTok commerce ecosystem.
      </NText>
    </NCard>
    <NCard class="card-wrapper" :bordered="false" title="Total Data">
      <NGrid :x-gap="16" :y-gap="16" cols="5" responsive="screen" item-responsive>
        <NGi v-for="metric in totalMetrics" :key="metric.key">
          <IndicatorCardVertical :metric="metric" />
        </NGi>
      </NGrid>
    </NCard>
    <NCard :bordered="false" title="Monthly Data" class="card-wrapper">
      <template #header-extra>
        <div class="flex-y-center gap-2">
          <NButton type="primary" secondary>Month</NButton>
          <NSelect v-model:value="model.month" :options="dateOptions" :consistent-menu-width="false"></NSelect>
        </div>
      </template>
    </NCard>
    <NSpin v-if="loading || monthLoading" class="h-full min-h-100vh"></NSpin>
    <template v-else>
      <NFlex :size="16" vertical>
        <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
          <NGi v-for="metric in growthMetrics" :key="metric.key" span="6">
            <IndicatorCard :metric="metric" />
          </NGi>
        </NGrid>
        <IndicatorTrendCard :shop-type="model.shopType" />
        <PieTableCard :month="model.month" :shop-type="model.shopType" />
        <ProductPriceCard :month="model.month" :shop-type="model.shopType" />
        <ShopCompositionCard :month="model.month" :shop-type="model.shopType" />
        <SkuTop10Card :month="model.month" :shop-type="model.shopType" />
        <VideoTop5Card :month="model.month" :shop-type="model.shopType" />
        <CreatorTop10Card :month="model.month" :shop-type="model.shopType" />
      </NFlex>
    </template>
  </NFlex>
</template>

<style scoped></style>
