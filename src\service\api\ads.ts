import { request } from '../request';

export function fetchGetAdsMetrics(params: Api.Ads.AdsMetricsParams) {
  return request<Api.Dashboard.DashboardData>({
    url: '/ads/getShopAdsData',
    method: 'get',
    params
  });
}

export function fetchGetAdsMetricsByBrand(data: Api.Ads.AdsMetricsParamsByBrand) {
  return request<Api.Dashboard.DashboardData>({
    url: '/ads/getShopAdsDataByBrand',
    method: 'post',
    data
  });
}
