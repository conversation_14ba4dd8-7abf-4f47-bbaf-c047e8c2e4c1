<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-14 09:43:28
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-31 15:35:28
 * @FilePath: \tiksage-frontend\src\views\dashboard\modules\today-contributing-videos-card.vue
 * @Description: today-contributing-videos-card
-->
<script setup lang="tsx">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { NButton, NDataTable, NEllipsis, NFlex, NImage, NSelect, NText } from 'naive-ui';
import { isNumber, uniqBy } from 'lodash-es';
import { fetchTodayVideoList } from '@/service/api';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { useTable } from '@/hooks/common/table';
import { dateFormat } from '@/utils/date';
import { getFallbackImage } from '@/utils/fake-image';
import { NumeralFormat } from '@/enum';
import ButtonCopy from '@/components/custom/button-copy.vue';
import Tip from '@/components/custom/tip.vue';

interface Props {
  updateTime?: number;
  topData: Api.Dashboard.TodayVideo[];
  shopId: number;
}

const props = defineProps<Props>();

const prefixUrl = ref(import.meta.env.VITE_TODAY_VIDEO_AVATAR_URL);

const { numberFormat } = useNumberFormat();

function linkVideo(url: string) {
  return window.open(url, '_blank');
}

const {
  data: tableData,
  columns,
  pagination,
  getData,
  loading,
  updateSearchParams
} = useTable({
  immediate: false,
  apiFn: fetchTodayVideoList,
  apiParams: {
    current: 1,
    size: 10,
    shopId: props.shopId
  },
  columns() {
    return [
      {
        key: 'index',
        width: 50,
        align: 'center'
      },
      {
        key: 'videoID',
        title: 'Video Information',
        width: 250,
        render(rowData) {
          return (
            <div
              class="flex flex-nowrap items-center gap-2 hover:(cursor-pointer text-primary)"
              onClick={() => {
                linkVideo(rowData.video);
              }}
            >
              <div class="w-25px flex-shrink-0">
                <NImage
                  class="rounded"
                  src={`${prefixUrl.value}${rowData.videoAvatarLocal}`}
                  fallbackSrc={getFallbackImage(25, 50)}
                />
              </div>
              <NFlex vertical>
                <NEllipsis line-clamp={1} tooltip={{ contentClass: 'max-w-400px' }}>
                  {rowData.title}
                </NEllipsis>
                <NFlex>
                  <NText class="text-sm text-gray">{dateFormat(rowData.postTimestamp)}</NText>
                </NFlex>
              </NFlex>
            </div>
          );
        }
      },
      {
        key: 'creatorHandle',
        title: 'Creator',
        align: 'center',
        width: 130,
        render(rowData) {
          return (
            <NFlex justify="center" wrap={false}>
              <span>{rowData.creatorHandle}</span>
              <ButtonCopy copy={rowData.creatorHandle} />
            </NFlex>
          );
        }
      },
      {
        key: 'affiliateGmv',
        title() {
          return (
            <NFlex justify="center" align="center" wrap={false}>
              <span>Affiliate shoppable video GMV</span>
              <Tip description="The total amount of paid orders (including returns and refunds) attributed from this affiliate within 14 days of clicking product links in their content (including LIVEs, shoppable videos, and showcase)." />
            </NFlex>
          );
        },
        align: 'center',
        width: 120,
        sorter: 'default',
        sortOrder: 'descend'
      },
      {
        key: 'shoppableVideoGPM',
        title() {
          return (
            <NFlex justify="center" align="center" wrap={false}>
              <span>Shoppable video GPM</span>
              <Tip description="The Avg. shoppable video GPM is the average GMV generated from 1,000 impressions of all shoppable videos." />
            </NFlex>
          );
        },
        align: 'center',
        width: 100
      },
      {
        key: 'affiliateCTR',
        title() {
          return (
            <NFlex justify="center" align="center" wrap={false}>
              <span>Affilliate CTR</span>
              <Tip description="Click-through rate (CTR) is the percentage of viewers who saw a product link in affiliate's content and clicked through to the product detail page during the selected time period." />
            </NFlex>
          );
        },
        align: 'center',
        width: 100
      }
    ];
  }
});

type Keys = keyof Api.Dashboard.TodayVideo;

const dollarKeys: Keys[] = ['affiliateGmv', 'shoppableVideoGPM'];

const percentKeys: Keys[] = ['affiliateCTR'];

function renderCell(value: any, _rowData: any, column: any) {
  if (!isNumber(value)) return value;
  if (dollarKeys.length && dollarKeys.includes(column.key)) {
    return numberFormat(value, NumeralFormat.Dollar);
  }
  if (percentKeys.length && percentKeys.includes(column.key)) {
    return numberFormat(value, NumeralFormat.Percent);
  } else if (typeof value === 'number') {
    return numberFormat(value, NumeralFormat.Number);
  }
  return value || 0;
}

const searchValue = ref<string>('-1');

const [asyncOptionsLoading, toggleAsyncOptionsLoading] = useToggle(false);

const searchOptions = ref<CommonType.Option[]>([
  {
    label: 'All Creators',
    value: '-1'
  }
]);

async function initSearchList() {
  toggleAsyncOptionsLoading(true);
  let options: CommonType.Option[] = [];
  const def: CommonType.Option = {
    label: 'All Creators',
    value: '-1'
  };

  const { data, error } = await fetchTodayVideoList({ size: 50, current: 1, shopId: props.shopId });

  if (!error) {
    options = data.records.map(v => ({
      label: v.creatorHandle,
      value: v.creatorId
    }));
  }

  options.unshift(def);
  options = uniqBy(options, 'value');

  searchOptions.value = options;
  toggleAsyncOptionsLoading(false);
}

function handleSelectUpdate(value: string) {
  searchValue.value = value;
  updateSearchParams({
    current: 1,
    creatorId: value === '-1' ? undefined : value
  });
  getData();
}

function handleSelectFocus() {
  initSearchList();
}

const [drawShow, toggleDrawShow] = useToggle(false);

function handleSeeMore() {
  toggleDrawShow(true);
}

function handleLink(url: string) {
  return window.open(url, '_blank');
}

function handleEnterDrawer() {
  updateSearchParams({ shopId: props.shopId });
  getData();
}
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" title="Contributing Contents" content-class="flex">
    <template #header-extra>
      <NButton icon-placement="right" quaternary @click="handleSeeMore">
        <template #icon>
          <icon-solar:alt-arrow-right-bold-duotone />
        </template>
        See More
      </NButton>
    </template>
    <NEmpty v-if="!topData.length" class="m-auto" description="No Orders" />
    <NList v-else hoverable clickable>
      <NListItem v-for="(video, index) in topData.slice(0, 3)" :key="video.title" @click="handleLink(video.video)">
        <NThing class="flex-1 hover:(text-primary)" content-indented>
          <template #avatar>
            <div class="image-container h-89px w-50px">
              <span class="corner-label">TOP {{ index + 1 }}</span>
              <NImage
                class="rounded"
                :preview-disabled="true"
                :src="prefixUrl + video.videoAvatarLocal"
                :fallback-src="getFallbackImage(50, 89)"
              />
            </div>
          </template>
          <template #header>
            <NEllipsis :line-clamp="1" :tooltip="{ contentClass: 'max-w-400px' }">
              {{ video.title }}
            </NEllipsis>
          </template>
          <template #description>
            <NFlex class="text-xs text-gray">
              <NFlex class="flex-1" align="center">
                <NFlex class="flex-1" vertical>
                  <NFlex align="center">
                    <span>@{{ video.creatorHandle }}</span>
                    <ButtonCopy :copy="video.creatorHandle" />
                  </NFlex>
                  <span>{{ dateFormat(video.postTimestamp) }}</span>
                </NFlex>
                <NText class="flex-1 text-xl font-bold">
                  {{ numberFormat(video.affiliateGmv, NumeralFormat.Dollar) }}
                </NText>
              </NFlex>
            </NFlex>
          </template>
        </NThing>
      </NListItem>
    </NList>
    <NDrawer v-model:show="drawShow" width="80%" @after-enter="handleEnterDrawer">
      <NDrawerContent closable>
        <template #header>
          <NFlex>
            <span>More Data</span>
            <NText class="text-sm text-gray">Last updated: {{ dateFormat(updateTime) }}</NText>
          </NFlex>
        </template>
        <NFlex vertical class="h-full">
          <div>
            <NSelect
              class="w-300px"
              :loading="asyncOptionsLoading"
              :value="searchValue"
              placeholder="Find Creator"
              :options="searchOptions"
              @update:value="handleSelectUpdate"
              @focus="handleSelectFocus"
            />
          </div>
          <NDataTable
            size="small"
            class="h-full"
            flex-height
            remote
            :loading="loading"
            :bordered="false"
            :pagination="pagination"
            :columns="columns"
            :data="tableData"
            :scroll-x="750"
            :render-cell="renderCell"
          >
            <template #empty>
              <NEmpty class="m-auto" description="No Orders"></NEmpty>
            </template>
          </NDataTable>
        </NFlex>
      </NDrawerContent>
    </NDrawer>
  </NCard>
</template>

<style scoped>
.image-container {
  position: relative;

  .corner-label {
    position: absolute;
    top: -4px;
    left: -21px;
    background-color: #a855f7;
    color: #fff;
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 10px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
    transition: background-color 0.3s ease;
    rotate: -20deg;
  }
}
</style>
