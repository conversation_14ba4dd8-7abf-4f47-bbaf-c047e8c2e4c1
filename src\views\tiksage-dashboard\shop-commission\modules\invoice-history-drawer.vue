<script setup lang="tsx">
import { ref, watch } from 'vue';
import { NButton, NDatePicker, NFormItem, NInputNumber, NPopover, NTag } from 'naive-ui';
import dayjs from 'dayjs';
import {
  fetchDownloadInvoicePDF,
  fetchGetInvoiceHistory,
  fetchUpdateInvoiceVoided,
  fetchUpdatePaidStatus
} from '@/service/api';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { useTable } from '@/hooks/common/table';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { useRouterPush } from '@/hooks/common/router';
import ButtonIcon from '@/components/custom/button-icon.vue';
import { NumeralFormat, TimeFormat } from '@/enum';
import SvgIcon from '@/components/custom/svg-icon.vue';

const { VITE_INVOICE_URL } = import.meta.env;

interface Props {
  shopId?: number;
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();
const { routerPushByKey } = useRouterPush();
const { getDictionaryByCodeType } = useDictionaryStore();

const show = defineModel<boolean>('show', {
  required: true
});

const paidStatusDic = ref<Api.Dictionary.DictionaryItem<number>[]>([]);
const selectedInvoice = ref<Api.Invoice.InvoiceHistory>();

const { data, columns, pagination, loading, getData, updateSearchParams } = useTable({
  immediate: false,
  apiFn: fetchGetInvoiceHistory,
  apiParams: {
    size: 10,
    current: 1
  },
  columns() {
    return [
      {
        title: 'ID',
        key: 'invoiceNoStr'
      },
      {
        title: 'Billed To',
        key: 'clientName',
        align: 'center'
      },
      {
        title: 'Date of Issue',
        key: 'issueDate',
        align: 'center',
        sorter: 'default',
        sortOrder: 'descend',
        render(rowData) {
          return dayjs(rowData.issueDate).format(TimeFormat.CN_DATE);
        }
      },
      {
        title: 'Due Date',
        key: 'dueDate',
        align: 'center',
        render(rowData) {
          return dayjs(rowData.dueDate).format(TimeFormat.CN_DATE);
        }
      },
      {
        title: 'Amount Due (USD)',
        key: 'total',
        align: 'center',
        render(rowData) {
          return numberFormat(rowData.total, NumeralFormat.Real_Dollar);
        }
      },
      {
        title: 'Is Voided',
        key: 'deprecate',
        align: 'center',
        render(rowData) {
          const isVoided = rowData.deprecate;
          return (
            <NTag size="small" bordered={false} type={isVoided ? 'warning' : 'primary'}>
              {isVoided ? 'Voided' : 'Valid'}
            </NTag>
          );
        }
      },
      {
        title: 'Paid',
        key: 'amountPaid',
        align: 'center',
        render(rowData) {
          const isVoided = rowData.deprecate;
          if (isVoided) return '-';

          const paidStatus = paidStatusDic.value.find(v => v.code === rowData.paymentFlag);
          const amount = numberFormat(rowData.paymentAmount, NumeralFormat.Real_Dollar);

          return (
            <div class="flex-center gap-2">
              <NPopover disabled={!rowData.paymentDate}>
                {{
                  trigger: () => (
                    <NTag type={paidStatus?.description} size="small" bordered={false}>
                      {paidStatus?.name}
                    </NTag>
                  ),
                  default: () => (
                    <div class="flex-col gap-2">
                      <div class="text-gray-400">
                        <div>Payment time</div>
                        <div class="text-primary font-medium">{rowData.paymentDate}</div>
                      </div>
                      <div class="text-gray-400">
                        <div>Payment amount</div>
                        <div class="text-primary font-medium">{amount}</div>
                      </div>
                    </div>
                  )
                }}
              </NPopover>

              <NButton size="small" quaternary onClick={() => handleChangePaid(rowData)}>
                {{
                  icon: () => <SvgIcon icon="solar:pen-2-linear" />
                }}
              </NButton>
            </div>
          );
        }
      },
      {
        title: 'Update Time',
        key: 'updateTime',
        align: 'center',
        width: 180,
        render(rowData) {
          return dayjs(rowData.updateTime).format(TimeFormat.CN_TIME_24_NO_TIMEZONE);
        }
      },
      {
        key: 'operate',
        width: 100,
        render(rowData) {
          return (
            <div class="flex justify-end gap-2">
              <ButtonIcon
                text
                type="warning"
                quaternary={false}
                tooltipPlacement="top-end"
                tooltipContent="Void Invoice"
                icon="solar:file-corrupted-linear"
                disabled={rowData.deprecate}
                onClick={() => handleVoid(rowData.id)}
              />
              <ButtonIcon
                text
                type="primary"
                quaternary={false}
                tooltipPlacement="top-end"
                tooltipContent="Regenerate Invoice"
                icon="solar:file-check-linear"
                disabled={!rowData.deprecate}
                onClick={() => handleRegenerate(rowData.id)}
              />
              <ButtonIcon
                text
                quaternary={false}
                tooltipPlacement="top-end"
                tooltipContent="Download PDF"
                icon="solar:download-linear"
                onClick={() => handleDownload(rowData.id)}
              />
            </div>
          );
        }
      }
    ];
  }
});

async function handleChangePaid(invoice: Api.Invoice.InvoiceHistory) {
  selectedInvoice.value = invoice;
  const dialog = window.$dialog?.create({
    title: '',
    content() {
      return (
        <div>
          <NFormItem label="Payment Amount">
            <NInputNumber
              value={selectedInvoice.value!.paymentAmount}
              onUpdateValue={v => (selectedInvoice.value!.paymentAmount = v)}
              min={0}
              placeholder="Payment Amount"
            >
              {{
                prefix: () => <span>$</span>
              }}
            </NInputNumber>
          </NFormItem>
          <NFormItem label="Payment Time">
            <NDatePicker
              type="date"
              format="yyyy-MM-dd"
              placeholder="Payment Time"
              formatted-value={selectedInvoice.value!.paymentDate}
              onUpdateFormattedValue={v => (selectedInvoice.value!.paymentDate = v)}
            ></NDatePicker>
          </NFormItem>
        </div>
      );
    },
    positiveText: 'Confirm',
    negativeText: 'Cancel',
    async onPositiveClick() {
      dialog && (dialog.loading = true);
      if (!selectedInvoice.value) return;

      const { error: updatePaidErr } = await fetchUpdatePaidStatus(selectedInvoice.value);
      if (!updatePaidErr) {
        window.$message?.success('Update Successfully.');
        getData();
      }
      dialog && (dialog.loading = false);
      dialog?.destroy();
    }
  });
}

async function handleRegenerate(id: number) {
  const d = window.$dialog?.info({
    title: 'Regenerate Invoice',
    content: 'Are you sure you want to regenerate this invoice?',
    positiveText: 'confirm',
    negativeText: 'cancel',
    async onPositiveClick() {
      d && (d.loading = true);
      routerPushByKey('tiksage-dashboard_invoice-create', {
        params: {
          id: `${props.shopId}`
        },
        query: {
          invoiceId: `${id}`
        }
      });
      d && (d.loading = false);
    }
  });
}

async function handleVoid(id: number) {
  const d = window.$dialog?.warning({
    title: 'Void Invoice',
    content() {
      return 'Are you sure you want to void this invoice? this action cannot be undone!';
    },
    positiveText: 'confirm',
    negativeText: 'cancel',
    async onPositiveClick() {
      d && (d.loading = true);
      const { error } = await fetchUpdateInvoiceVoided(id);
      if (!error) {
        window.$message?.success('Invoice voided successfully');
        getData();
      }
      d && (d.loading = false);
    }
  });
}

async function handleDownload(id: number) {
  const { data: url, error } = await fetchDownloadInvoicePDF(id);
  if (!error) {
    window.open(VITE_INVOICE_URL + url?.fileName);
  }
}

async function initPaidStatusDic() {
  const opts = await getDictionaryByCodeType<number>('invoice_paid', false, false);

  paidStatusDic.value = opts!;
}

function initData() {
  initPaidStatusDic();
}

initData();

watch(
  () => show.value,
  newVal => {
    if (!newVal || !props.shopId) return;
    updateSearchParams({
      shopId: props.shopId,
      current: 1,
      size: 10
    });
    getData();
  }
);
</script>

<template>
  <NDrawer v-model:show="show" width="1200px">
    <NDrawerContent title="Invoice History" closable>
      <NDataTable
        class="h-full"
        remote
        flex-height
        :data="data"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
      ></NDataTable>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
