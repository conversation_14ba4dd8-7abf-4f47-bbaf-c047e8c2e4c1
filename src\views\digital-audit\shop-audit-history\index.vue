<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-12-08 19:24:49
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-12-10 14:30:07
 * @FilePath: \tiksage-frontend\src\views\shop-audit\history\index.vue
 * @Description: history.vue
-->
<script setup lang="tsx">
import { onMounted, onUnmounted, ref } from 'vue';
import { useIntervalFn, useToggle } from '@vueuse/core';
import { NAvatar, NButton, NFlex, NPopconfirm, NText } from 'naive-ui';
import dayjs from 'dayjs';
import { fetchDeleteById, fetchGetShopAuditReportHistory } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import { getFallbackImage } from '@/utils/fake-image';
import ButtonIcon from '@/components/custom/button-icon.vue';
import { TaskStatus, TimeFormat } from '@/enum';
import TaskProgress from '@/views/analysis/list/modules/task-progress.vue';
import SvgIcon from '@/components/custom/svg-icon.vue';
import Report from '../shop-audit/modules/report.vue';

const currentTaskId = ref<number>();
const [show, toggleShow] = useToggle(false);

const { columns, data, getData, loading, pagination } = useTable({
  apiFn: fetchGetShopAuditReportHistory,
  apiParams: {
    current: 1,
    size: 10
  },
  columns() {
    return [
      {
        key: 'index',
        width: 80,
        align: 'center',
        title: ''
      },
      {
        key: 'shopName',
        title: 'Shop Info',
        width: '300',
        render(rowData) {
          const { shopName, avatar } = rowData;
          return (
            <>
              {shopName ? (
                <NFlex align="center">
                  <NAvatar src={avatar || '1'} fallbackSrc={getFallbackImage(50, 50)} />
                  <NText>{shopName}</NText>
                </NFlex>
              ) : (
                '-'
              )}
            </>
          );
        }
      },
      {
        key: 'createTime',
        align: 'center',
        width: 300,
        title: 'Audit Date',
        render(rowData) {
          return dayjs.tz(rowData.createTime, 'Etc/Gmt+8').format(TimeFormat.US_TIME_24);
        }
      },
      {
        key: 'totalScore',
        align: 'center',
        title: 'Shop Score',
        render(rowData) {
          return rowData.totalScore || '-';
        }
      },
      {
        key: 'grabTaskStatus',
        title: 'Status',
        align: 'center',
        fixed: 'right',
        width: '150',
        render(rowData) {
          return <TaskProgress taskStatus={rowData.grabTaskStatus} />;
        }
      },
      {
        key: 'operate',
        title: '',
        render(rowData) {
          const isDisabled = ![TaskStatus.NEW, TaskStatus.RUNNING, TaskStatus.FAIL, TaskStatus.SUCCESS].includes(
            rowData.grabTaskStatus
          );
          return (
            <NFlex justify="end" wrap={false} size={16}>
              <ButtonIcon
                disabled={isDisabled}
                icon="solar:eye-linear"
                text
                quaternary={false}
                onClick={() => handleShowReport(rowData.id)}
              />
              <NPopconfirm
                onPositiveClick={async () => {
                  await handleDeleteReport(rowData.id);
                  getData();
                }}
              >
                {{
                  trigger: () => (
                    <NButton text size="small" ghost type="error">
                      {{
                        icon: () => <SvgIcon class="" icon="solar:trash-bin-2-linear" />
                      }}
                    </NButton>
                  ),
                  default: () => <>Are you sure to delete this task? This operation cannot be undone.</>
                }}
              </NPopconfirm>
            </NFlex>
          );
        }
      }
    ];
  }
});

function handleShowReport(taskId: number) {
  currentTaskId.value = taskId;
  toggleShow(true);
}

async function handleDeleteReport(taskId: number) {
  const { error } = await fetchDeleteById(taskId);
  if (!error) {
    window.$message?.success('Remove successfully.');
    getData();
  }
}

const { routerPushByKey } = useRouterPush();
function handleBackTo() {
  routerPushByKey('digital-audit_shop-audit');
}

const { pause, resume } = useIntervalFn(() => {
  getData();
}, 1000 * 30);

onMounted(() => {
  resume();
});

onUnmounted(() => {
  pause();
});
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="card-wrapper" :bordered="false" title="Audit History">
      <template #header-extra>
        <NButton strong secondary @click="handleBackTo">
          <template #icon>
            <icon-solar:add-circle-linear class="text-icon" />
          </template>
          Create Task
        </NButton>
      </template>
    </NCard>
    <NCard class="min-h-400px flex-1 card-wrapper" content-class="flex-col gap-4" :bordered="false">
      <div class="flex justify-end">
        <ButtonRefresh :callback="getData" />
      </div>
      <NDataTable
        class="h-full"
        :loading="loading"
        flex-height
        :data="data"
        :columns="columns"
        :pagination="pagination"
        remote
      ></NDataTable>
    </NCard>
    <NDrawer v-model:show="show" width="1200px">
      <NDrawerContent class="bg-#F7FAFC" closable>
        <Report v-if="currentTaskId" :task-id="currentTaskId" />
      </NDrawerContent>
    </NDrawer>
  </NFlex>
</template>

<style scoped></style>
