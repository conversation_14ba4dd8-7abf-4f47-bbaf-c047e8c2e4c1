<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-12-06 13:20:20
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-12-10 11:46:20
 * @FilePath: \tiksage-frontend\src\views\shop-audit\create\modules\introduction.vue
 * @Description: introduction
-->
<script setup lang="ts">
import img1 from '@/assets/imgs/shop-audit-1.png';
import img2 from '@/assets/imgs/shop-audit-2.png';
import img3 from '@/assets/imgs/shop-audit-3.png';
const steps = [
  {
    img: img1,
    title: 'Enter Shop Information',
    info: 'Input the TikTok shop you want to analyze, and our system will generate a detailed performance diagnosis, laying the groundwork for optimized growth.'
  },
  {
    img: img2,
    title: 'Smart Diagnosis & Scoring',
    info: "Evaluate your shop's key metrics—sales performance, content quality, and creator collaboration. Understand strengths and uncover improvement areas to drive data-driven optimizations."
  },
  {
    img: img3,
    title: 'Tailored Recommendations',
    info: 'Get actionable insights and personalized strategies to refine your shop content, strengthen creator partnerships, and boost sales in the competitive TikTok e-commerce market.'
  }
];
</script>

<template>
  <NCard :bordered="false">
    <NFlex class="h-full max-h-600px" vertical justify="space-around" :size="16" :wrap="false">
      <!-- <NH1 class="py-8 text-center font-bold">Precise Scoring, Targeted Optimization, Elevating Shop Performance</NH1> -->
      <NFlex class="" justify="space-around" :wrap="false">
        <div
          v-for="item in steps"
          :key="item.title"
          class="relative w-81 flex flex-col rounded-xl bg-white bg-clip-border text-gray-700 shadow-xl"
        >
          <div
            class="relative mx-4 h-40 overflow-hidden rounded-xl bg-primary-500 bg-clip-border shadow-lg shadow-primary-500/40 -mt-6"
          >
            <NImage class="absolute" :height="160" object-fit="cover" preview-disabled :src="item.img"></NImage>
          </div>
          <div class="p-6">
            <h5
              class="mb-2 block text-xl text-blue-gray-900 font-semibold leading-snug tracking-normal font-sans antialiased"
            >
              {{ item.title }}
            </h5>
            <p class="block text-wrap text-base text-inherit font-light leading-relaxed font-sans antialiased">
              {{ item.info }}
            </p>
          </div>
          <!--
 <div class="p-6 pt-0">
            <button
              data-ripple-light="true"
              type="button"
              class="select-none rounded-lg bg-blue-500 py-3 px-6 text-center align-middle font-sans text-xs font-bold uppercase text-white shadow-md shadow-blue-500/20 transition-all hover:shadow-lg hover:shadow-blue-500/40 focus:opacity-[0.85] focus:shadow-none active:opacity-[0.85] active:shadow-none disabled:pointer-events-none disabled:opacity-50 disabled:shadow-none"
            >
              Read More
            </button>
          </div>
-->
        </div>
      </NFlex>
    </NFlex>
  </NCard>
</template>

<style scoped></style>
