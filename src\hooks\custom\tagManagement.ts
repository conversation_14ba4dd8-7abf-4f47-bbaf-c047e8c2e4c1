import { computed, ref, watch } from 'vue';
import { creatorTagColorOptions } from '@/constants/colors';
import { fetchCreateTag, fetchDeleteTag, fetchUpdateTag } from '@/service/api';
import { useTagStore } from '@/store/modules/tag';

type Tag = Api.Tag.Tag;

export function useTagManagement(defaultSelectedTags?: number[]) {
  const tagStore = useTagStore();

  // 本地状态
  const tagList = computed(() => tagStore.tagList);
  const filteredTags = ref<Tag[]>([]);
  const selectedTags = ref<Tag[]>([]);
  const editingTag = ref<Tag | null>(null);
  const searchValue = ref('');

  // 检查是否存在同名标签
  const hasExistingTag = computed(() => {
    if (!searchValue.value) return false;
    const normalizedSearchValue = searchValue.value.trim().toLowerCase();
    return tagStore.tagList.some(tag => tag.name.toLowerCase() === normalizedSearchValue);
  });

  // 获取选中的标签ID列表
  const selectedTagIds = computed(() => selectedTags.value.map(tag => tag.id));

  // 初始化
  async function init() {
    await tagStore.initTagList();
    filteredTags.value = tagStore.tagList;

    if (defaultSelectedTags?.length) {
      selectedTags.value = defaultSelectedTags
        .map(tagId => tagStore.tagList.find(t => t.id === tagId))
        .filter((tag): tag is Tag => tag !== undefined);
    } else {
      selectedTags.value = [];
    }
  }

  // 搜索标签
  function handleSearch(value: string) {
    searchValue.value = value;
    if (!value) {
      filteredTags.value = tagStore.tagList;
      return;
    }

    const normalizedValue = value.toLowerCase();
    filteredTags.value = tagStore.tagList.filter(tag => tag.name.toLowerCase().includes(normalizedValue));
  }

  // 创建新标签
  async function createTag(name: string) {
    if (!name) return null;

    const normalizedName = name.trim();
    if (hasExistingTag.value) {
      const existingTag = tagStore.tagList.find(tag => tag.name.toLowerCase() === normalizedName.toLowerCase());
      return { exists: true, tag: existingTag };
    }

    try {
      const randomColor = creatorTagColorOptions[Math.floor(Math.random() * creatorTagColorOptions.length)].value;

      const { data: newTag, error: createTagErr } = await fetchCreateTag({ name: normalizedName, color: randomColor });
      if (createTagErr) return null;

      // 添加到全局store
      tagStore.addTag(newTag);

      // 更新本地过滤列表
      filteredTags.value = tagStore.tagList;

      return { exists: false, tag: newTag };
    } catch (error) {
      console.error('Failed to create tag:', error);
      return null;
    }
  }

  // 更新标签
  async function updateTag(tagId: number, updates: { name?: string; color?: string }) {
    try {
      const tag = tagStore.tagList.find(t => t.id === tagId);
      if (!tag) return false;

      // 检查是否有实际变化
      const hasNameChange = updates.name !== undefined && updates.name !== tag.name;
      const hasColorChange = updates.color !== undefined && updates.color !== tag.color;

      // 如果没有变化，直接返回true
      if (!hasNameChange && !hasColorChange) {
        return true;
      }

      const updatedTagParams: Api.Tag.Tag = {
        ...tag,
        ...updates
      };

      const { data: updatedTag, error: updateTagErr } = await fetchUpdateTag(updatedTagParams);
      if (updateTagErr) return false;

      // 更新全局store
      tagStore.updateTagInList(updatedTag);

      // 更新本地filteredTags
      const filteredIndex = filteredTags.value.findIndex(t => t.id === tagId);
      if (filteredIndex !== -1) {
        filteredTags.value[filteredIndex] = updatedTag;
      }

      // 更新选中标签
      const selectedIndex = selectedTags.value.findIndex(t => t.id === tagId);
      if (selectedIndex !== -1) {
        selectedTags.value[selectedIndex] = updatedTag;
      }

      return true;
    } catch (error) {
      console.error('Failed to update tag:', error);
      return false;
    }
  }

  // 选择/取消选择标签
  function toggleTag(tag: Tag) {
    const index = selectedTags.value.findIndex(t => t.id === tag.id);
    if (index === -1) {
      selectedTags.value.push(tag);
    } else {
      selectedTags.value.splice(index, 1);
    }
    return selectedTags.value;
  }

  // 移除标签
  function removeTag(tagId: number) {
    selectedTags.value = selectedTags.value.filter(t => t.id !== tagId);
  }

  // 从列表中删除标签
  async function deleteTag(tagId: number) {
    try {
      const { error } = await fetchDeleteTag(tagId);
      if (error) return false;

      // 从全局store中移除
      tagStore.removeTagFromList(tagId);

      // 从filteredTags中移除
      const filteredIndex = filteredTags.value.findIndex(t => t.id === tagId);
      if (filteredIndex !== -1) {
        filteredTags.value.splice(filteredIndex, 1);
      }

      // 从selectedTags中移除
      removeTag(tagId);

      return true;
    } catch (error) {
      console.error('Failed to delete tag:', error);
      return false;
    }
  }

  // 初始化
  init();

  watch(
    () => tagList.value,
    newVal => {
      filteredTags.value = newVal;
    },
    {
      immediate: true
    }
  );

  return {
    // 状态
    tagList,
    selectedTags,
    editingTag,
    searchValue,
    filteredTags,
    hasExistingTag,
    selectedTagIds,

    // 方法
    init,
    handleSearch,
    createTag,
    updateTag,
    toggleTag,
    removeTag,
    deleteTag
  };
}
