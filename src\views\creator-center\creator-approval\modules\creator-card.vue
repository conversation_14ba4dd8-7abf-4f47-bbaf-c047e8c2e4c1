<script setup lang="tsx">
import { computed, ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { NFormItem, NGi, NGrid, NInput } from 'naive-ui';
import dayjs from 'dayjs';
import { delay } from 'lodash-es';
import { fetchApproveCreator, fetchRejectCreator } from '@/service/api';
import { useAuth } from '@/hooks/business/auth';
import { getFallbackImage } from '@/utils/fake-image';
import { TimeFormat } from '@/enum';
import Phrases from './phrases.vue';

enum Status {
  PENDING,
  APPROVALED,
  REJECTED
}

// 1:email  2:phone  3:whatsapp  4:message  5:otherContact
enum ContactType {
  EMAIL = 1,
  PHONE,
  WHATS_APP,
  MESSAGE,
  OTHER
}

const productAvatarPrefix = import.meta.env.VITE_PRODUCT_AVATAR_URL;

interface Emits {
  (e: 'check', checkValue: boolean, creatorId: string): void;
  (e: 'submit'): void;
}

const emit = defineEmits<Emits>();

interface Props {
  data: Api.CreatorManage.CreatorApprovalInfo;
  canInvite: boolean;
  currentTab: Status;
}

const props = defineProps<Props>();

const { hasAuth } = useAuth();

const [isFlipped, toggleIsFlipped] = useToggle(false);

const avatarUrl = computed(() => {
  return `${import.meta.env.VITE_CREATOR_AVATAR_URL}${props.data.creatorAvatarLocal}`;
});

const createTime = computed(() => {
  return dayjs.tz(props.data.createTime, 'Etc/Gmt+8').format(TimeFormat.US_DATE_TIMEZONE) || '';
});

const contactInfo = computed(() => {
  let res: { icon: string; value: string };
  switch (props.data.mainContactType) {
    case ContactType.EMAIL:
      res = {
        icon: 'tabler:mail',
        value: props.data.email
      };
      break;
    case ContactType.PHONE:
      res = {
        icon: 'tabler:phone',
        value: props.data.phone
      };
      break;
    case ContactType.WHATS_APP:
      res = {
        icon: 'tabler:brand-whatsapp',
        value: props.data.whatsapp
      };
      break;
    case ContactType.MESSAGE:
      res = {
        icon: 'tabler:message',
        value: props.data.message
      };
      break;
    case ContactType.OTHER:
      res = {
        icon: 'material-symbols:contact-support-outline-rounded',
        value: props.data.otherContact
      };
      break;
    default:
      res = {
        icon: 'tabler:message-off',
        value: 'No primary contact'
      };
  }
  return res;
});

const indicators = computed(() => {
  return [
    {
      title: 'Slotting fee',
      prefix: '$',
      value: props.data.slottingFee,
      description: '',
      average: true,
      decimals: 2
    },
    {
      title: 'Commission',
      prefix: '',
      value: props.data.commission,
      suffix: '%',
      description: '',
      average: false,
      decimals: 2
    },
    {
      title: 'Followers',
      prefix: '',
      value: props.data.followers,
      description: '',
      average: true,
      decimals: 2
    },
    {
      title: 'Monthly Rev',
      prefix: '$',
      value: props.data.monthlyRev,
      description: '',
      average: true,
      decimals: 2
    },
    {
      title: 'Video GPM',
      prefix: '$',
      value: props.data.videoGpm,
      description: '',
      average: true,
      decimals: 2
    },
    {
      title: 'Shoppable video',
      prefix: '',
      value: props.data.shoppableVideo,
      description: '',
      average: true,
      decimals: 2
    }
  ];
});

function handleCheckCreator(value: boolean) {
  emit('check', value, props.data.creatorName);
}

function handleLinkTo() {
  window.open(props.data.ttAccountLink, '_blank');
}

const [approvalLoading, toggleApprovalLoading] = useToggle(false);

// creator-manage:approver
async function handleApproveCreator() {
  toggleApprovalLoading(true);
  const { error } = await fetchApproveCreator(props.data.id);
  if (!error) {
    window.$message?.success('Approval Successful!');
    emit('submit');
  }
  delay(() => {
    toggleApprovalLoading(false);
  }, 500);
}

const rejectionNotes = ref<string>('');
const [rejectLoading, toggleRejectLoading] = useToggle(false);
const phrasesRef = ref<typeof Phrases>();

function handleHistoryChecked(value: string) {
  rejectionNotes.value += value;
}

function clearRejectionNotes() {
  rejectionNotes.value = '';
}

function handleRejectCreator() {
  window.$dialog?.create({
    title: 'Rejection Notes',
    showIcon: false,
    loading: rejectLoading.value,
    style: 'width:600px',
    content() {
      return (
        <NGrid cols={2}>
          <NGi>
            <NFormItem label="History">
              <Phrases ref={phrasesRef} onCheck={handleHistoryChecked} />
            </NFormItem>
          </NGi>
          <NGi>
            <NFormItem label="Notes">
              <NInput
                value={rejectionNotes.value}
                type="textarea"
                clearable
                resizable={false}
                class="h-300px max-h-300px"
                placeholder="Please state the reason for rejection. (Optional)"
                onUpdate:value={v => (rejectionNotes.value = v)}
              />
            </NFormItem>
          </NGi>
        </NGrid>
      );
    },
    positiveText: 'Reject',
    async onPositiveClick() {
      toggleRejectLoading(true);

      const params = {
        id: props.data.id,
        notes: rejectionNotes.value
      };
      const { error } = await fetchRejectCreator(params);

      if (!error) {
        if (phrasesRef.value) {
          phrasesRef.value.addPhrase(rejectionNotes.value);
        }
        window.$message?.success('Rejection Successful!');
        emit('submit');
      }

      clearRejectionNotes();

      delay(() => {
        toggleRejectLoading(false);
      }, 500);
    },
    negativeText: 'Cancel',
    onNegativeClick() {
      clearRejectionNotes();
    },
    onClose() {
      clearRejectionNotes();
    },
    onEsc() {
      clearRejectionNotes();
    }
  });
}
</script>

<template>
  <div
    class="card group border-1 rounded-xl to-[#ffffff] bg-gradient-to-b shadow"
    :class="{
      'from-primary-50': currentTab === Status.PENDING,
      'from-[#F2FFF0]': currentTab === Status.APPROVALED,
      'from-error-50': currentTab === Status.REJECTED
    }"
    @click="toggleIsFlipped()"
  >
    <div class="card-inner" :style="{ transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)' }">
      <div class="card-front">
        <div class="relative h-100px flex-col-center">
          <NFlex class="absolute top-16px text-xs text-gray">
            <span>{{ createTime }}</span>
          </NFlex>
          <div class="absolute bottom--37px h-75px w-75px border-4px border-white rounded-full">
            <NImage
              class="rounded-full"
              :width="75"
              :height="75"
              preview-disabled
              :src="avatarUrl"
              :fallback-src="getFallbackImage(75, 75)"
            />
          </div>
          <ButtonIcon
            v-if="hasAuth('creator-approval:operator') && props.data.tags"
            class="absolute left-8px top-8px"
            style="color: #9ca3af"
            :quaternary="false"
            text
            icon="solar:hashtag-square-bold-duotone"
            :tooltip-content="props.data.tags"
            tooltip-placement="top"
          />
          <ButtonIcon
            class="absolute right-8px top-8px"
            :quaternary="false"
            text
            icon="logos:tiktok-icon"
            @click.stop="handleLinkTo"
          />
          <NCheckbox
            v-if="canInvite"
            class="absolute left-16px top-16px"
            size="large"
            @change.stop="handleCheckCreator"
          ></NCheckbox>
        </div>
        <div class="relative m-t-40px w-full flex-col-center gap-8px">
          <NFlex class="h-67px w-full" vertical justify="center" align="center" :size="16" :wrap="false">
            <NText class="text-xl">{{ props.data.creatorName }}</NText>
            <div v-if="hasAuth('creator-approval:operator')" class="flex items-center justify-center gap-3px text-gray">
              <SvgIcon :icon="contactInfo.icon" />
              <span>: {{ contactInfo.value }}</span>
              <ButtonCopy v-if="contactInfo.icon !== 'tabler:message-off'" :copy="contactInfo.value" />
            </div>
          </NFlex>
          <NGrid class="w-full rounded-xl bg-white pb-8px pt-8px shadow" y-gap="16" :cols="2">
            <NGi v-for="indicator in indicators.slice(2)" :key="indicator.title">
              <NFlex vertical justify="center" align="center">
                <!--
 <AverageCountTo
                  class="font-bold"
                  :end-value="indicator.value"
                  :decimals="indicator.decimals"
                  :prefix="indicator.prefix"
                  :average="indicator.average"
                  :suffix="indicator?.suffix"
                />
-->
                <span class="font-bold">
                  {{ indicator.value }}
                </span>
                <NFlex>
                  <NText class="text-gray">{{ indicator.title }}</NText>
                  <Tip v-if="indicator.description !== ''" :description="indicator.description" />
                </NFlex>
              </NFlex>
            </NGi>
          </NGrid>
          <NFlex
            v-if="currentTab === Status.PENDING && hasAuth('creator-approval:approver')"
            class="absolute bottom-1px h-80px w-full translate-y-3 p-8px opacity-0 transition-all group-hover:(translate-y-0 opacity-100)"
            justify="center"
            align="center"
            :size="16"
          >
            <div class="absolute bottom-0px h-full w-90% bg-white blur"></div>
            <NButton size="small" @click.stop="handleRejectCreator">Reject</NButton>
            <NButton size="small" :loading="approvalLoading" type="primary" @click.stop="handleApproveCreator">
              Approve
            </NButton>
          </NFlex>
          <NFlex
            v-if="currentTab === Status.REJECTED"
            vertical
            class="absolute bottom-1px h-full w-full translate-y-3 rounded-xl bg-error-100 p-8px opacity-0 shadow transition-all group-hover:(translate-y-0 opacity-100)"
            :size="16"
          >
            <span class="text font-bold">Reasons for Rejection:</span>
            <NEllipsis
              :line-clamp="6"
              :tooltip="{
                contentStyle: 'max-width:400px'
              }"
            >
              {{ props.data.notes }}
            </NEllipsis>
          </NFlex>
        </div>
      </div>
      <div class="card-back">
        <NText class="w-full p-t-16px text-center text-xl">Initial Collaboration</NText>
        <NGrid class="w-full rounded-xl bg-white pb-8px pt-8px shadow" y-gap="16" :cols="2">
          <NGi v-for="indicator in indicators.slice(0, 2)" :key="indicator.title">
            <NFlex vertical justify="center" align="center">
              <!--
 <AverageCountTo
                class="font-bold"
                :end-value="indicator.value"
                :decimals="indicator.decimals"
                :prefix="indicator.prefix"
                :average="indicator.average"
                :suffix="indicator?.suffix"
              />
-->
              <span class="font-bold">
                {{ indicator.value }}
              </span>
              <NFlex>
                <NText class="text-gray">{{ indicator.title }}</NText>
                <Tip v-if="indicator.description !== ''" :description="indicator.description" />
              </NFlex>
            </NFlex>
          </NGi>
        </NGrid>
        <NScrollbar style="max-height: 200px">
          <NFlex class="divide-y" vertical :wrap="false" :size="0">
            <div
              v-for="product in props.data.productList"
              :key="product.productId"
              class="flex items-center gap-16px p-8px"
            >
              <NImage
                class="flex-shrink-0"
                width="50px"
                height="50px"
                preview-disabled
                :src="productAvatarPrefix + product.productAvatarLocal"
                :fallback-src="getFallbackImage(50, 50)"
              />
              <NEllipsis
                :tooltip="{
                  contentStyle: 'max-width:400px;'
                }"
              >
                {{ product.productName }}
              </NEllipsis>
            </div>
          </NFlex>
        </NScrollbar>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.card {
  // width: 300px;
  height: 362px;
  box-sizing: border-box;
  backdrop-filter: blur(6px);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  user-select: none;
  perspective: 1000px;
  .card-inner {
    width: 100%;
    height: 100%;
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.999s;
    .card-front,
    .card-back {
      position: absolute;
      width: 100%;
      height: 100%;
      backface-visibility: hidden;
    }
    .card-front {
      display: flex;
      flex-direction: column;
      padding: 8px;
      border-radius: 10px;
      transform: rotateY(0deg);
    }
    .card-back {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 8px;
      // align-items: center;
      border-radius: 10px;
      // justify-content: center;
      transform: rotateY(180deg);
    }
  }

  &:hover {
    border: 1px solid black;
    box-shadow: 12px 17px 51px rgba(0, 0, 0, 0.22);
  }

  &:active {
    transform: scale(0.9) translateZ(0);
  }
}
</style>
