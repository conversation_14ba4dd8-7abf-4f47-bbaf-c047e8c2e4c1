<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { pick } from 'lodash-es';
import { fetchGetCampaignsOptionsByWeeklyReport, fetchGetWeeklyReportByCampaignId } from '@/service/api';
import { NumeralFormat } from '@/enum';
import { renderCell } from './shared';

interface Emits {
  (e: 'update', key: string, value: any): void;
}

const emit = defineEmits<Emits>();

interface Props {
  canSave?: boolean;
  isShow?: boolean;
  reportData?: Api.WeeklyReport.WeeklyReportData;
}

const props = defineProps<Props>();

const toolbarConfig = ref({
  excludeKeys: ['uploadImage']
});

const keyArr: Array<keyof Api.WeeklyReport.tableData> = [
  'cycleTitle',
  'adCost',
  'adGmv',
  'adPurchases',
  'adRoas',
  'adCpa',
  'adAov',
  'adCvr',
  'adAddsToCart',
  'adCheckoutsInitiated',
  'adImpressions',
  'adClicks',
  'adAddsToCartRate',
  'adCheckoutsInitiatedRate',
  'adCtr',
  'adCpc',
  'maxAdCost',
  'maxAdGmv',
  'maxAdPurchases',
  'maxAdRoas',
  'maxAdCpa',
  'maxAdAov'
];

const totalData = computed(() => {
  const res: Api.WeeklyReport.tableData[] = [];
  if (!props.reportData) return res;
  const { lastMonthData, thisMonthData, lastWeekData, thisWeekData } = props.reportData;

  if (lastMonthData) res.push(pick(lastMonthData, keyArr));
  if (thisMonthData) res.push(pick(thisMonthData, keyArr));
  if (lastWeekData) res.push(pick(lastWeekData, keyArr));
  if (thisWeekData) res.push(pick(thisWeekData, keyArr));

  return res;
});

const totalColumns = ref<any[]>([
  {
    key: 'cycleTitle',
    title: 'Date',
    align: 'center',
    width: 200
  },
  {
    key: 'adCost',
    title: 'Cost',
    align: 'center',
    unit: NumeralFormat.Real_Dollar,
    width: 120
  },
  {
    key: 'adGmv',
    title: 'GMV',
    align: 'center',
    unit: NumeralFormat.Real_Dollar,
    width: 120
  },
  {
    key: 'adPurchases',
    title: 'Purchases',
    align: 'center',
    width: 120
  },
  {
    key: 'adRoas',
    title: 'ROAS',
    align: 'center',
    width: 120
  },
  {
    key: 'adCpa',
    title: 'CPA',
    align: 'center',
    unit: NumeralFormat.Real_Dollar,
    width: 120
  },
  {
    key: 'adAov',
    title: 'AOV',
    align: 'center',
    unit: NumeralFormat.Real_Dollar,
    width: 120
  },
  {
    key: 'adCvr',
    title: 'CVR',
    align: 'center',
    unit: NumeralFormat.Percent,
    width: 120
  },
  {
    key: 'adAddsToCart',
    title: 'Adds to cart',
    align: 'center',
    width: 120
  },
  {
    key: 'adCheckoutsInitiated',
    title: 'Checkouts initiated',
    align: 'center',
    width: 120
  },
  {
    key: 'adImpressions',
    title: 'Impressions',
    align: 'center',
    width: 120
  },
  {
    key: 'adClicks',
    title: 'Clicks',
    align: 'center',
    width: 120
  },
  {
    key: 'adAddsToCartRate',
    title: 'Adds to cart rate(%)',
    align: 'center',
    unit: NumeralFormat.Percent,
    width: 150
  },
  {
    key: 'adCheckoutsInitiatedRate',
    title: 'Checkouts initiated rate(%)',
    align: 'center',
    unit: NumeralFormat.Percent,
    width: 150
  },
  {
    key: 'adCtr',
    title: 'CTR',
    align: 'center',
    unit: NumeralFormat.Percent,
    width: 120
  },
  {
    key: 'adCpc',
    title: 'CPC',
    align: 'center',
    unit: NumeralFormat.Real_Dollar,
    width: 120
  },
  {
    key: 'maxAdCost',
    title: 'Cost',
    align: 'center',
    unit: NumeralFormat.Real_Dollar,
    width: 120
  },
  {
    key: 'maxAdGmv',
    title: 'GMV',
    align: 'center',
    unit: NumeralFormat.Real_Dollar,
    width: 120
  },
  {
    key: 'maxAdPurchases',
    title: 'Purchases',
    align: 'center',
    width: 120
  },
  {
    key: 'maxAdRoas',
    title: 'ROAS',
    align: 'center',
    width: 120
  },
  {
    key: 'maxAdCpa',
    title: 'CPA',
    align: 'center',
    unit: NumeralFormat.Real_Dollar,
    width: 120
  },
  {
    key: 'maxAdAov',
    title: 'AOV',
    align: 'center',
    unit: NumeralFormat.Real_Dollar,
    width: 120
  }
]);

const [settingShow, toggleSettingShow] = useToggle(false);
type ColumnOptionItem = {
  key: string;
  title: string;
  disabled: boolean;
};

const checkedColumns = ref<string[]>([]);

const stashedCheckColumns = ref<string[]>([]);

const columnsChecksOptions = computed<ColumnOptionItem[]>(() => {
  return totalColumns.value.map(col => {
    const isDisabled = checkedColumns.value.length >= 8 && !checkedColumns.value.includes(col.key);
    return {
      key: col.key,
      title: col.title,
      disabled: col.key === 'cycleTitle' || isDisabled
    };
  });
});

const [campaignShow, toggleCampaignShow] = useToggle(false);
const [campaignGetLoading, toggleCampaignGetLoading] = useToggle(false);

const originalCheckedCampaigns = computed<string[]>(() => {
  return props.reportData?.selectCampaignIdList || [];
});
const checkedCampaigns = ref<string[]>([]);
const campaignsOptions = ref<ColumnOptionItem[]>([]);

const filteredColumns = computed(() => {
  return totalColumns.value.filter(col => stashedCheckColumns.value.includes(col.key));
});

const tableScrollx = computed(() => {
  return filteredColumns.value.reduce((p, c) => p + (Number(c.width) || 0), 0);
});

const gmvMaxColumns = computed(() => {
  const keys = ['cycleTitle', 'maxAdCost', 'maxAdGmv', 'maxAdPurchases', 'maxAdRoas', 'maxAdCpa', 'maxAdAov'];
  return totalColumns.value.filter(col => keys.includes(col.key));
});

const gmvMaxScrollx = computed(() => {
  return gmvMaxColumns.value.reduce((p, c) => p + (Number(c.width) || 0), 0);
});

function createDefaultCheckColumns() {
  return [
    'cycleTitle',
    'adCost',
    'adGmv',
    'adPurchases',
    'adRoas',
    'adCheckoutsInitiated',
    'adImpressions',
    'adClicks'
  ];
}

function handleEditColumns() {
  checkedColumns.value = [...stashedCheckColumns.value];
  toggleSettingShow(true);
}

function handleMetricsConfirm() {
  stashedCheckColumns.value = [...checkedColumns.value];
  toggleSettingShow(false);
}

function handleMetricsCancel() {
  toggleSettingShow(false);
  checkedColumns.value = [...stashedCheckColumns.value];
}

function handleEditCampaigns() {
  if (props.reportData!.selectCampaignIdList.length === 0) {
    checkedCampaigns.value = campaignsOptions.value.map(item => item.key);
  } else {
    checkedCampaigns.value = originalCheckedCampaigns.value;
  }
  toggleCampaignShow(true);
}

async function handleCampaignConfirm() {
  if (checkedCampaigns.value.length === 0) {
    window.$message?.error('Please select at least one campaign!');
    return;
  }

  toggleCampaignGetLoading(true);

  const params = {
    shopId: props.reportData!.shopId,
    week: props.reportData!.week,
    campaignsList: checkedCampaigns.value
  };

  const { data: weeklyReportData, error: weeklyReportErr } = await fetchGetWeeklyReportByCampaignId(params);
  if (!weeklyReportErr) {
    emit('update', 'selectCampaignIdList', checkedCampaigns.value);
    emit('update', 'lastMonthData', weeklyReportData.lastMonthData);
    emit('update', 'lastWeekData', weeklyReportData.lastWeekData);
    emit('update', 'thisMonthData', weeklyReportData.thisMonthData);
    emit('update', 'thisWeekData', weeklyReportData.thisWeekData);
    window.$message?.success('Update Successfully.');
  }

  toggleCampaignGetLoading(false);
  toggleCampaignShow(false);
}

function handleCampaignCancel() {
  toggleCampaignShow(false);
  checkedCampaigns.value = originalCheckedCampaigns.value;
}

function handleUpdateHtml(v: string, key: string) {
  emit('update', key, v);
}

function initCheckedColumns() {
  let checkColumns = [];

  checkColumns = createDefaultCheckColumns();

  checkedColumns.value = checkColumns;
  stashedCheckColumns.value = checkColumns;
}

async function initCampaignsOptions() {
  const { data, error } = await fetchGetCampaignsOptionsByWeeklyReport(props.reportData!.shopId);

  if (!error) {
    campaignsOptions.value = data.map(item => ({
      key: item.campaignId,
      title: item.campaignName,
      disabled: false
    }));
  }
}

watch(
  () => props.reportData,
  async newVal => {
    if (newVal?.reportId) {
      initCheckedColumns();
      initCampaignsOptions();
    }
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div class="flex-col gap-4">
    <NFormItem label-style="font-weight: 500;font-size: 16px;" label="3. Ads update">
      <div v-if="isShow" class="html-content">
        <div class="html-reset" v-html="reportData?.adsHtml || ''"></div>
      </div>
      <WangEditor
        v-else
        :value-html="reportData?.adsHtml || ''"
        :toolbar-config="toolbarConfig"
        @update:value-html="v => handleUpdateHtml(v, 'adsHtml')"
      ></WangEditor>
    </NFormItem>
    <template v-if="!isShow || reportData?.showAds">
      <div class="flex-y-center justify-end gap-2">
        <template v-if="!isShow">
          <NSwitch :value="reportData?.showAds" @update:value="v => emit('update', 'showAds', v)">
            <template #checked>Show</template>
            <template #unchecked>Hide</template>
          </NSwitch>
          <NButton :disabled="!canSave" secondary @click="handleEditCampaigns">
            <template #icon>
              <icon-solar:pen-2-linear />
            </template>
            Campaigns
          </NButton>
        </template>
        <NButton secondary @click="handleEditColumns">
          <template #icon>
            <icon-solar:pen-2-linear />
          </template>
          Metrics
        </NButton>
      </div>
      <NDataTable
        :scroll-x="tableScrollx"
        striped
        :single-line="false"
        :columns="filteredColumns"
        :data="totalData"
        :render-cell="renderCell"
      ></NDataTable>
      <NDataTable
        v-if="reportData?.showGmvMaxAds"
        :scroll-x="gmvMaxScrollx"
        striped
        :single-line="false"
        :columns="gmvMaxColumns"
        :data="totalData"
        :render-cell="renderCell"
      ></NDataTable>
    </template>

    <div v-if="isShow" class="html-content">
      <div class="html-reset" v-html="reportData?.adsHtmlBottom || ''"></div>
    </div>
    <WangEditor
      v-else
      :value-html="reportData?.adsHtmlBottom || ''"
      :toolbar-config="toolbarConfig"
      @update:value-html="v => handleUpdateHtml(v, 'adsHtmlBottom')"
    ></WangEditor>

    <NModal v-model:show="settingShow" class="min-w-800px" preset="dialog" title="Metrics">
      <NCheckboxGroup v-model:value="checkedColumns" class="w-full">
        <NGrid :cols="3" :x-gap="16" :y-gap="16">
          <NGi v-for="item in columnsChecksOptions" :key="item.key">
            <NCheckbox :value="item.key" :label="item.title" :disabled="item.disabled" />
          </NGi>
        </NGrid>
      </NCheckboxGroup>
      <template #action>
        <div class="flex justify-end gap-4">
          <NButton @click="handleMetricsCancel">Cancel</NButton>
          <NButton type="primary" @click="handleMetricsConfirm">Confirm</NButton>
        </div>
      </template>
    </NModal>

    <NModal
      v-model:show="campaignShow"
      class="min-w-800px"
      content-class="flex-col gap-4"
      preset="dialog"
      title="Campaigns"
    >
      <div class="flex-y-center gap-4">
        <span class="text-warning">Choose at least one campaign!</span>
        <NButton size="small" secondary @click="checkedCampaigns = campaignsOptions.map(item => item.key)">
          Selected All
        </NButton>
        <NButton size="small" secondary @click="checkedCampaigns = []">Clear All</NButton>
      </div>
      <NCheckboxGroup v-model:value="checkedCampaigns" class="w-full">
        <NGrid :cols="3" :x-gap="16" :y-gap="16">
          <NGi v-for="item in campaignsOptions" :key="item.key">
            <NCheckbox :value="item.key" :label="item.title" :disabled="item.disabled" />
          </NGi>
        </NGrid>
      </NCheckboxGroup>
      <template #action>
        <div class="flex justify-end gap-4">
          <NButton @click="handleCampaignCancel">Cancel</NButton>
          <NButton type="primary" :loading="campaignGetLoading" @click="handleCampaignConfirm">Confirm</NButton>
        </div>
      </template>
    </NModal>
  </div>
</template>

<style scoped lang="scss"></style>
