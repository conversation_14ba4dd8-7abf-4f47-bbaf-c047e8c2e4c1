<script setup lang="tsx">
import { watch } from 'vue';
import { NEllipsis, NImage } from 'naive-ui';
import { fetchGetProductTopListOnTikSageDashboard } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { getFallbackImage } from '@/utils/fake-image';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { NumeralFormat } from '@/enum';

const { VITE_PRODUCT_AVATAR_URL } = import.meta.env;

interface Props {
  dateRange: string[] | undefined;
}
const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const { columns, data, loading, updateSearchParams, getData } = useTable({
  immediate: false,
  apiFn: fetchGetProductTopListOnTikSageDashboard,
  apiParams: {
    current: 1,
    size: 20
  },
  columns() {
    return [
      {
        key: 'index',
        title: 'Rank',
        align: 'center',
        width: 60,
        render(rowData) {
          const iconClass = {
            'text-3xl': true,
            'text-yellow-500': rowData.index === 1,
            'text-gray-400': rowData.index === 2,
            'text-amber-700': rowData.index === 3
          };

          return (
            <div class="flex-center">
              {rowData.index <= 3 ? (
                <SvgIcon icon="solar:crown-line-bold-duotone" class={iconClass} />
              ) : (
                <span class="text-base">{rowData.index}</span>
              )}
            </div>
          );
        }
      },
      {
        key: 'productId',
        title: 'Product',
        width: 300,
        render(rowData) {
          const avatarUrl = VITE_PRODUCT_AVATAR_URL + rowData.productAvatarLocal;
          return (
            <div class="flex items-center gap-4">
              <div class="h-12 w-12 flex-shrink-0 rounded-sm">
                <NImage src={avatarUrl} fallbackSrc={getFallbackImage(60, 60)} />
              </div>
              <div class="">
                <NEllipsis lineClamp={2} tooltip={{ width: 400 }}>
                  {rowData.productName}
                </NEllipsis>
              </div>
            </div>
          );
        }
      },
      {
        key: 'gmv',
        title: 'Product GMV',
        align: 'center',
        width: 400,
        render(rowData) {
          return rowData.gmv ? numberFormat(rowData.gmv, NumeralFormat.Real_Dollar) : '-';
        }
      }
    ];
  }
});

watch(
  () => props.dateRange,
  newVal => {
    if (!newVal) return;
    const [startDateStr, endDateStr, perviousStartDateStr, perviousEndDateStr] = newVal;
    updateSearchParams({
      startDateStr,
      endDateStr,
      perviousStartDateStr,
      perviousEndDateStr
    });
    getData();
  },
  {
    immediate: true
  }
);
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" title="Product Ranking">
    <NDataTable remote :loading="loading" :bordered="false" :data="data" :columns="columns" />
  </NCard>
</template>

<style scoped></style>
