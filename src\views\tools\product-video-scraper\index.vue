<script setup lang="tsx">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { NButton } from 'naive-ui';
import { fetchDownloadVideoScraperTaskExcel } from '@/service/api';
import { downloadFile } from '@/utils/download';

const [show, toggleShow] = useToggle(false);
const [success, toggleSuccess] = useToggle(false);
const [loading, toggleLoading] = useToggle(false);

const realBlob = ref();

const keyword = ref('');

async function handleSearch() {
  toggleSuccess(false);

  if (!keyword.value.trim()) return;

  toggleLoading(true);
  toggleShow(true);

  const { data, error } = await fetchDownloadVideoScraperTaskExcel(keyword.value);

  if (!error) {
    toggleSuccess(true);
    realBlob.value = data;
    downloadFile(data, 'xlsx', 'Video Link');
  }

  toggleLoading(false);
}

function handleExport() {
  if (!realBlob.value) return;
  downloadFile(realBlob.value, 'xlsx', 'Video Link');
}
</script>

<template>
  <div class="flex-col gap-4">
    <NCard
      class="h-full min-h-400px card-wrapper"
      content-class="flex-col gap-4"
      :bordered="false"
      title="Product Video Scraper"
    >
      <template #header-extra>
        <ButtonBack />
      </template>

      <!-- 搜索区域 - 优化间距和视觉效果 -->
      <div class="my-12 flex-col-center gap-6">
        <div class="mb-2 text-center">
          <h2 class="mb-2 text-xl text-gray-700 font-medium">Find TikTok Videos for Your Products</h2>
          <p class="text-gray-500">Automatically scrape 50 TikTok video related links</p>
        </div>

        <NInputGroup class="max-w-600px shadow-xl">
          <NInput
            v-model:value="keyword"
            size="large"
            placeholder="Type in the product name or keyword"
            @keydown.enter="handleSearch"
          >
            <template #prefix>
              <icon-solar:magnifer-linear />
            </template>
          </NInput>
          <NButton size="large" :loading="loading" strong secondary type="primary" @click="handleSearch">
            Start Search
          </NButton>
        </NInputGroup>
      </div>

      <!-- 结果区域 - 添加过渡效果和更好的空状态 -->
      <div class="flex flex-1 gap-4">
        <!--
 <NScrollbar v-if="show" class="flex-col flex-1">
          <div class="mb-4 flex items-center justify-between">
            <div class="text-gray-700">
              <span class="font-medium">Search Results</span>
              <span class="ml-2 text-sm text-gray-500">for "{{ keyword }}"</span>
            </div>
            <NButton strong secondary>
              <template #icon>
                <icon-vscode-icons:file-type-excel2 />
              </template>
              Export Excel
            </NButton>
          </div>
          <NDataTable class="flex-1" :columns="columns as any" :bordered="false" :single-line="false"></NDataTable>
        </NScrollbar>
-->

        <div v-show="show" class="flex-center flex-1">
          <NEmpty
            :description="
              loading
                ? 'Searching for related videos, please wait...'
                : success
                  ? 'Successfully retrieved 50 related TikTok video links, ready for export'
                  : 'No related videos found, please try another keyword'
            "
          >
            <template #icon>
              <icon-svg-spinners:3-dots-fade v-if="loading" class="text-primary" />
              <icon-line-md:circle-to-confirm-circle-transition v-else-if="success" class="text-success" />
              <icon-line-md:alert v-else class="text-warning" />
            </template>
            <template #extra>
              <NButton v-show="success" strong secondary @click="handleExport">
                <template #icon>
                  <icon-vscode-icons:file-type-excel2 />
                </template>
                Export Excel
              </NButton>
            </template>
          </NEmpty>
        </div>
      </div>
    </NCard>
  </div>
</template>

<style scoped></style>
