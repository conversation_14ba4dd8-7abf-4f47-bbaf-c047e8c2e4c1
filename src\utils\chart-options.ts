/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-09-23 10:28:26
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-14 11:23:36
 * @FilePath: \tiksage-frontend\src\utils\chart-options.ts
 * @Description:
 */

import { isNil } from 'lodash-es';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from './../enum/index';

const { numberFormat } = useNumberFormat();

export function optionsHOC<
  T extends Visactor.VChart.ILineChartSpec | Visactor.VChart.IAreaChartSpec | Visactor.VChart.IBarChartSpec
>(baseOptions: T, customOptions: T, visible: boolean = true) {
  function initSeriesAndAxes(opt: T) {
    if (isNil(opt.series)) {
      opt.series = [];
    }
    if (!opt.axes) {
      opt.axes = [];
    }
    return opt;
  }
  let resOpt = baseOptions;

  const { type } = resOpt;
  const { data, xField, yField } = customOptions;

  if (Array.isArray(data)) {
    resOpt = initSeriesAndAxes(resOpt);

    for (const value of data) {
      const { values, id } = value as Visactor.VChart.IDataValues;
      /* eslint-disable no-continue */
      if (!values.length) continue;
      switch (id) {
        case 'numberY':
          resOpt.series &&
            (resOpt.series as T[]).push({
              type,
              id: 'numberSeries',
              dataId: 'numberY',
              seriesField: 'type',
              stack: false,
              tooltip: {
                activeType: 'dimension',
                dimension: {
                  title: {
                    valueTimeFormat: xField === 'time' ? '%b %d, %Y' : undefined
                  },
                  content: {
                    key: v => {
                      return v ? (v?.type as string) : '';
                    },
                    // keyFormatter: '{type}',
                    value: v => {
                      return v ? numberFormat(v[yField as string], NumeralFormat.Number) : v;
                    }
                  }
                }
              }
            } as T);
          resOpt.axes!.push({
            orient: 'left',
            id: 'left_numberY',
            visible,
            seriesId: ['numberSeries'],
            label: {
              formatMethod: v => {
                return numberFormat(v, NumeralFormat.Number);
              }
            },
            sync: {
              axisId: 'left_dollarY',
              tickAlign: true,
              zeroAlign: true
            }
          });
          break;
        case 'dollarY':
          resOpt.series &&
            (resOpt.series as T[]).push({
              type,
              id: 'dollarSeries',
              dataId: 'dollarY',
              seriesField: 'type',
              stack: false,
              tooltip: {
                activeType: 'dimension',
                dimension: {
                  title: {
                    valueTimeFormat: xField === 'time' ? '%b %d, %Y' : undefined
                  },
                  content: {
                    key: v => {
                      return v ? (v?.type as string) : '';
                    },
                    // keyFormatter: '{type}',
                    value: v => {
                      return v ? numberFormat(v[yField as string], NumeralFormat.Dollar) : v;
                    }
                  }
                }
              }
            } as T);
          resOpt.axes?.push({
            orient: 'left',
            id: 'left_dollarY',
            visible,
            seriesId: ['dollarSeries'],
            label: {
              formatMethod: v => {
                return numberFormat(v, NumeralFormat.Dollar);
              }
              // formatter: '{label:$~s}'
            },
            sync: {
              axisId: 'left_numberY',
              tickAlign: true,
              zeroAlign: true
            }
          });
          break;
        case 'percentY':
          resOpt.series &&
            (resOpt.series as T[]).push({
              type,
              id: 'percentSeries',
              dataId: 'percentY',
              seriesField: 'type',
              stack: false,
              tooltip: {
                activeType: 'dimension',
                dimension: {
                  title: {
                    valueTimeFormat: xField === 'time' ? '%b %d, %Y' : undefined
                  },
                  content: {
                    key: v => {
                      return v ? (v?.type as string) : '';
                    },
                    // keyFormatter: '{type}',
                    value: v => {
                      return v ? numberFormat(v[yField as string], NumeralFormat.Percent) : v;
                    }
                  }
                }
              }
            } as T);
          resOpt.axes?.push({
            orient: 'right',
            id: 'right_percentY',
            visible,
            seriesId: ['percentSeries'],
            label: {
              formatter: '{label:.2%}'
            },
            sync: {
              axisId: 'left_dollarY',
              tickAlign: true,
              zeroAlign: true
            }
          });
          break;
        default:
      }
    }
  }

  return resOpt;
}

export function handleChartPoint(show: boolean) {
  const noPoint = {
    point: {
      visible: false
    }
  };
  const showPoint = {
    point: {
      visible: true,
      state: {
        hover: {
          scaleX: 1.2,
          scaleY: 1.2
        }
      },
      style: {
        innerBorder: {
          distance: 1,
          lineWidth: 1
        },
        fillOpacity: 0.5
      }
    }
  };
  return show ? showPoint : noPoint;
}

// eslint-disable-next-line max-params
export function initPieIndicator(totalVal: number, text = 'Total', unit = NumeralFormat.Dollar) {
  return {
    visible: true,
    trigger: 'none',
    limitRatio: 0.5,
    title: {
      visible: true,
      space: 8,
      style: {
        text,
        fontSize: 14,
        fill: '#9ca3af'
      }
    },
    content: {
      visible: true,
      style: {
        fontSize: 16,
        fill: '#000',
        fontWeight: 'bold',
        text: () => {
          return numberFormat(totalVal, unit);
        }
      }
    }
  };
}

// eslint-disable-next-line max-params
export function initPieLegend(
  data: any[],
  total: number,
  sliceNumber = 5,
  unit = NumeralFormat.Dollar,
  lengendWidth = '50%'
) {
  return {
    visible: true,
    orient: 'right',
    data: (legendDatum: any) => {
      return legendDatum.slice(0, sliceNumber).map((d: any) => {
        const val = data.find(v => v.type === d.label)?.value || 0;
        const value = `${numberFormat(val, unit)}(${numberFormat(val / total, NumeralFormat.Percent)})`;
        return {
          ...d,
          value
        };
      });
    },
    item: {
      width: lengendWidth,
      spaceRow: 8,
      autoEllipsisStrategy: 'valueFirst',
      shape: {
        style: {
          symbolType: 'circle'
        }
      },
      label: {
        style: {
          fontSize: 14,
          fill: '#9ca3af'
        }
      },
      value: {
        alignRight: true,
        style: {
          textAlign: 'right',
          fontSize: 14,
          fill: '#000',
          fontWeight: 'bold',
          ellipsis: false
        }
      }
    },
    autoPage: false
  };
}

// eslint-disable-next-line max-params
export function formatSeriesAndAxes(
  options: Visactor.VChart.IChartSpec,
  dataIds: (string | number)[],
  orient: 'left' | 'right',
  unit: NumeralFormat,
  color?: string,
  sync?: boolean
) {
  const { axes, series, type } = options;
  if (!axes) {
    options.axes = [];
  }
  if (!series) {
    options.series = [];
  }

  const newSeriesList: Visactor.VChart.ISeriesSpec[] = dataIds.map(d => {
    return {
      type,
      id: `${d}Series`,
      dataId: d,
      tooltip: {
        activeType: 'dimension',
        dimension: {
          content: {
            key: v => {
              return v ? (v?.type as string) : '';
            },
            value: datum => {
              return numberFormat(datum?.y, unit);
            }
          }
        }
      }
    };
  });
  const newAxes: Visactor.VChart.ICartesianLinearAxisSpec = {
    id: `${dataIds.join('-')}Axes`,
    type: 'linear',
    orient,
    seriesId: newSeriesList.map(s => s.id) as string[],
    label: {
      style: {
        fill: color || undefined
      },
      formatMethod: (v: any) => {
        return numberFormat(v, unit);
      }
    },
    sync: {
      axisId: '',
      tickAlign: true
    }
  };
  if (sync) {
    const firstLeftAxis = axes?.find((a: any) => a.orient === 'left');
    if (firstLeftAxis) {
      newAxes.sync = {
        axisId: firstLeftAxis.id as string,
        tickAlign: true
      };
    }
  }

  options.series = [...(series || []), ...newSeriesList];
  options.axes = [...(axes || []), newAxes];
  return options;
}
