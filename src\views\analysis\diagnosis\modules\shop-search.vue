<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-06 10:25:57
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-08-16 16:59:42
 * @FilePath: \tiksage-frontend\src\views\analysis\modules\shop-search.vue
 * @Description: shop-search
-->
<script setup lang="ts">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { debounce, isNil } from 'lodash-es';
import { fetchQueryShopInfomation } from '@/service/api';

interface Emits {
  (e: 'change', value: Api.Analysis.ShopInfo | null): void;
}

const emit = defineEmits<Emits>();

const inputValue = ref();

const [loading, toggleLoading] = useToggle(false);

const showShopInfo = ref(false);

const status = ref<NaiveUI.FormValidationStatus | undefined>(undefined);

const shopInfo = ref<Api.Analysis.ShopInfo>();

const debounceWatch = debounce(async val => {
  shopInfo.value = undefined;
  if (isNil(val) || val === '') {
    showShopInfo.value = false;
    return;
  }
  showShopInfo.value = true;
  toggleLoading(true);
  // fetch api
  const { data, error } = await fetchQueryShopInfomation(val);
  if (error) {
    loading.value = false;
    showShopInfo.value = false;
    return;
  }
  shopInfo.value = data;
  toggleLoading(false);
  if (data?.shopName && data.ifFind) {
    emit('change', shopInfo.value);
  } else {
    emit('change', null);
  }
}, 1000);

// watch(() => inputValue.value, debounceWatch);
</script>

<template>
  <NFlex class="w-full" align="center" :wrap="false">
    <NInput
      v-model:value="inputValue"
      class="w-40%"
      :loading="loading"
      :status="status"
      autosize
      placeholder="Shop Name"
      clearable
      @keyup.enter="debounceWatch(inputValue)"
      @blur="debounceWatch(inputValue)"
    >
      <template #prefix>
        <SvgIcon icon="tabler:search" />
      </template>
    </NInput>
    <NFlex v-show="showShopInfo" align="center" :wrap="false">
      <NSkeleton v-if="loading" width="38px" :sharp="false" size="medium" />
      <NAvatar v-else-if="shopInfo?.ifFind" :src="shopInfo?.avatar" />
      <icon-tabler:circle-x-filled v-else class="text-error" />
      <NSkeleton v-if="loading" width="100px" :sharp="false" size="medium" />
      <NText v-else :class="shopInfo?.ifFind ? '' : 'text-error'">{{ shopInfo?.shopName || 'Not found' }}</NText>
      <icon-tabler:circle-check-filled v-if="shopInfo?.ifFind" class="text-success" />
    </NFlex>
  </NFlex>
</template>

<style scoped></style>
