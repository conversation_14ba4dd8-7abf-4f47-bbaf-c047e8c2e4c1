export const followerAgesOptions = [
  {
    label: '18 - 24',
    value: '18-24'
  },
  {
    label: '25 - 34',
    value: '25-34'
  },
  {
    label: '35 - 44',
    value: '35-44'
  },
  {
    label: '45 - 54',
    value: '45-54'
  },
  {
    label: '55+',
    value: '55+'
  }
];

export const followersGenderOptions = [
  {
    label: 'All',
    value: 'All'
  },
  {
    label: 'Majority Female',
    value: 'Female'
  },
  {
    label: 'Majority Male',
    value: 'Male'
  }
];

export const creatorGenderOptions = [
  {
    label: 'Female',
    value: 'Female'
  },
  {
    label: 'Male',
    value: 'Male'
  }
];

export const gmvOptions = [
  {
    label: '$0 - $100',
    value: '0,100'
  },
  {
    label: '$100 - $1K',
    value: '100,1000'
  },
  {
    label: '$1K - $10K',
    value: '1000,10000'
  },
  {
    label: '$10K +',
    value: '10000,'
  }
];
export const unitsSoldOptions = [
  {
    label: '0 - 10',
    value: '0,10'
  },
  {
    label: '10 - 100',
    value: '10,100'
  },
  {
    label: '100 - 1K',
    value: '100,1000'
  },
  {
    label: '1K +',
    value: '1000,'
  }
];

export const followersCountOptions = [
  {
    label: '0 - 100K',
    value: '0,100000'
  },
  {
    label: '100K - 500k',
    value: '100000,500000'
  },
  {
    label: '500k - 1M',
    value: '500000,1000000'
  },
  {
    label: '1M - 5M',
    value: '1000000,5000000'
  },
  {
    label: '5M - 10M',
    value: '5000000,10000000'
  },
  {
    label: '10M+',
    value: '10000000,'
  }
];

export const followersMark = {
  0: '0',
  10: '10K',
  19: '100K',
  28: '1M',
  37: '10M',
  46: '100M+'
};

// eslint-disable
export const formatData = (value: number, base: number = 10, isLocal: boolean = true) => {
  // eslint-enable
  let strNum = 0;
  const rank = 10;
  if (value <= 10) strNum = value * base;
  if (value > 10 && value <= 19) strNum = (value - 9) * base * rank ** 1;
  if (value > 19 && value <= 28) strNum = (value - 18) * base * rank ** 2;
  if (value > 28 && value <= 37) strNum = (value - 27) * base * rank ** 3;
  if (value > 37 && value <= 46) strNum = (value - 36) * base * rank ** 4;
  return isLocal ? strNum.toLocaleString() : strNum;
};

export const viewsOptions = [
  {
    label: '0 - 100',
    value: '0,100'
  },
  {
    label: '100 - 1K',
    value: '100,1000'
  },
  {
    label: '1K - 10K',
    value: '1000,10000'
  },
  {
    label: '10K - 100K',
    value: '10000,100000'
  },
  {
    label: '100K - 1M',
    value: '100000,1000000'
  },
  {
    label: '1M - 10M',
    value: '1000000,10000000'
  },
  {
    label: '10M - 100M',
    value: '10000000,100000000'
  },
  {
    label: '100M+',
    value: '100000000,'
  }
];

export const avgViewsMark = {
  0: '0',
  10: '100',
  19: '1K',
  28: '10K',
  37: '100K+'
};
// export const formatData = (value: number, isLocal: boolean = true) => {
//   let strNum = 0;
//   if (value <= 10) strNum = value * 10;
//   if (value > 10 && value <= 19) strNum = (value - 9) * 100;
//   if (value > 19 && value <= 28) strNum = (value - 18) * 1000;
//   if (value > 28) strNum = (value - 27) * 10000;
//   return isLocal ? strNum.toLocaleString() : strNum;
// };
