<script setup lang="ts">
import { computed, ref } from 'vue';
import { useToggle } from '@vueuse/core';
import type { SelectOption } from 'naive-ui';
import { NForm, NFormItem } from 'naive-ui';
import dayjs from 'dayjs';
import { fetchGetShopOptions, fetchUpdateDashboardCookie, fetchUpdateDashboardData } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

function createDefaultDashboardCookieParams() {
  return {
    shopId: null,
    sellerCenterCookie: null,
    affiliateCenterCookie: null
  };
}

const dashboardCookieParams = ref<Api.SystemManage.DashboardCookieParams>(createDefaultDashboardCookieParams());
const shopOptions = ref<SelectOption[]>([]);
const [showCookieModal, toggleShowCookieModal] = useToggle(false);

const { formRef: cookieFormRef, validate: cookieValidate } = useNaiveForm();

type CookieRuleKey = Extract<keyof Api.SystemManage.DashboardCookieParams, 'shopId'>;

const cookieRules = computed<Record<CookieRuleKey, App.Global.FormRule[]>>(() => {
  const { defaultRequiredRule } = useFormRules();

  return {
    shopId: [defaultRequiredRule]
  };
});

async function initShopOptions() {
  const { data: shopOpts, error: getShopOptsError } = await fetchGetShopOptions();
  if (getShopOptsError) return;
  shopOptions.value = shopOpts.map(item => ({
    label: item.shopName,
    value: item.shopId
  }));
}

function handleShowCookieModal() {
  toggleShowCookieModal();
}

async function handleUpdateCookie() {
  await cookieValidate();
  // request
  const { data: cookieResultData, error: updateCookieError } = await fetchUpdateDashboardCookie(
    dashboardCookieParams.value
  );
  if (updateCookieError) return;
  if (cookieResultData.affiliateUpdated) {
    window.$message?.success('Affiliate Center Cookie Update Successfully.');
  } else {
    window.$message?.warning('Affiliate Center Cookie Update Failed.');
  }
  if (cookieResultData.sellerUpdated) {
    window.$message?.success('Seller Center Cookie Update Successfully.');
  } else {
    window.$message?.warning('Seller Center Cookie Update Failed.');
  }
  toggleShowCookieModal();
}

function handleCookieModalCancel() {
  toggleShowCookieModal();
  dashboardCookieParams.value = createDefaultDashboardCookieParams();
}

/** Data Modal */

function createDefaultDashboardDataParams() {
  return {
    shopId: null,
    startDate: null,
    endDate: null
  };
}

const dashboardDataParams = ref<Api.SystemManage.DashboardDataParams>(createDefaultDashboardDataParams());
const [showUpdateDataModal, toggleShowUpdateDataModal] = useToggle(false);
const { formRef: dataFormRef, validate: dataValidate } = useNaiveForm();

type DataRuleKey = keyof Api.SystemManage.DashboardDataParams;

const dataRules = computed<Record<DataRuleKey, App.Global.FormRule[]>>(() => {
  const { defaultRequiredRule } = useFormRules();

  return {
    shopId: [defaultRequiredRule],
    startDate: [defaultRequiredRule],
    endDate: [defaultRequiredRule]
  };
});

function isDateDisabled(current: number, phase: 'start' | 'end', value: [number, number] | null) {
  // 如果日期大于当前日期，则禁用
  // 如果日期区间大于30天，则禁用
  const today = dayjs().startOf('day');
  const currentDate = dayjs(current);

  // 禁用大于今天的日期
  if (currentDate.isAfter(today)) {
    return true;
  }

  // 如果已经选择了开始日期，检查结束日期选择是否会超过30天
  if (phase === 'end' && value && value[0]) {
    const startDate = dayjs(value[0]);
    if (currentDate.diff(startDate, 'day') > 30) {
      return true;
    }
  }

  // 如果已经选择了结束日期，检查开始日期选择是否会超过30天
  if (phase === 'start' && value && value[1]) {
    const endDate = dayjs(value[1]);
    if (endDate.diff(currentDate, 'day') > 30) {
      return true;
    }
  }

  return false;
}

function handleUpdateDate(value: [string, string] | null) {
  dashboardDataParams.value.startDate = value?.[0];
  dashboardDataParams.value.endDate = value?.[1];
}

function handleShowUpdateDataModal() {
  toggleShowUpdateDataModal();
}

function handleDataModalCancel() {
  toggleShowUpdateDataModal();
  dashboardDataParams.value = createDefaultDashboardDataParams();
}

async function handleUpdateData() {
  await dataValidate();
  // request
  const { error: updateDataError } = await fetchUpdateDashboardData(dashboardDataParams.value);
  if (updateDataError) return;
  window.$message?.success('Update Successfully.');
  toggleShowUpdateDataModal();
}

function handleDownloadDescriptionDocument() {
  window.open('https://product.tiksage.com/images/guides/authorization_guide.docx', '_blank');
}

initShopOptions();
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" title="Dashboard Management">
    <template #header-extra>
      <NButton strong secondary @click="handleDownloadDescriptionDocument">
        <template #icon>
          <icon-solar:download-linear />
        </template>
        Description Document
      </NButton>
    </template>
    <div class="flex gap-4">
      <NButton @click="handleShowCookieModal">Update Cookie</NButton>
      <NButton @click="handleShowUpdateDataModal">Update Data</NButton>
    </div>

    <NModal v-model:show="showCookieModal" preset="dialog">
      <template #header>
        <span>Update Cookie</span>
      </template>
      <NForm ref="cookieFormRef" :rules="cookieRules" :model="dashboardCookieParams">
        <NFormItem label="Shop Name" path="shopId">
          <NSelect
            v-model:value="dashboardCookieParams.shopId"
            :options="shopOptions"
            placeholder="Select Shop"
          ></NSelect>
        </NFormItem>
        <NFormItem label="Seller Center Cookie" path="sellerCenterCookie">
          <NInput
            v-model:value="dashboardCookieParams.sellerCenterCookie"
            placeholder="Enter Seller Center Cookie"
          ></NInput>
        </NFormItem>
        <NFormItem label="Affiliate Center Cookie" path="affiliateCenterCookie">
          <NInput
            v-model:value="dashboardCookieParams.affiliateCenterCookie"
            placeholder="Enter Affiliate Center Cookie"
          ></NInput>
        </NFormItem>
        <div class="flex justify-end gap-4">
          <NButton @click="handleCookieModalCancel">Cancel</NButton>
          <NButton type="primary" @click="handleUpdateCookie">Update</NButton>
        </div>
      </NForm>
    </NModal>

    <NModal v-model:show="showUpdateDataModal" preset="dialog">
      <template #header>
        <span>Update Data</span>
      </template>
      <NForm ref="dataFormRef" :rules="dataRules" :model="dashboardDataParams">
        <NFormItem label="Shop Name" path="shopId">
          <NSelect
            v-model:value="dashboardDataParams.shopId"
            :options="shopOptions"
            placeholder="Select Shop"
          ></NSelect>
        </NFormItem>
        <NFormItem label="Date" path="startDate">
          <NDatePicker type="daterange" :is-date-disabled="isDateDisabled" @update:formatted-value="handleUpdateDate" />
        </NFormItem>
        <div class="flex justify-end gap-4">
          <NButton @click="handleDataModalCancel">Cancel</NButton>
          <NButton type="primary" @click="handleUpdateData">Update</NButton>
        </div>
      </NForm>
    </NModal>
  </NCard>
</template>

<style scoped></style>
