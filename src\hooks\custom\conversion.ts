/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-11 21:16:21
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-07-14 15:01:32
 * @FilePath: \tiksage-frontend\src\hooks\custom\conversion.ts
 * @Description: useConversion
 */

import { computed } from 'vue';
import { divide, floor, isNaN } from 'lodash-es';

export function useConversion(current: number, pervious: number) {
  const trend = computed(() => {
    if (current === pervious)
      return {
        icon: '- ',
        type: 'info'
      };
    else if (current > pervious)
      return {
        icon: '↑',
        type: 'success'
      };
    return {
      icon: '↓',
      type: 'error'
    };
  });

  const conversion = computed(() => {
    const num = floor(divide(Math.abs(current - pervious), pervious === 0 ? 1 : pervious), 4);
    return isNaN(num) ? 0 : num;
  });

  return {
    trend,
    conversion
  };
}
