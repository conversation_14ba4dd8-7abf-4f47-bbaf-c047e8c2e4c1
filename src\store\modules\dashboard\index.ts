/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-11 15:03:31
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-10 11:04:24
 * @FilePath: \tiksage-frontend\src\store\modules\dashboard\index.ts
 * @Description: dashboard store
 */
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { defineStore } from 'pinia';
import dayjs from 'dayjs';
import { fetchGetAdsMetrics, fetchGetDashboardData } from '@/service/api';
import { SetupStoreId } from '@/enum';

interface Option extends Api.Auth.IndicatorValue {
  label: string;
  value: string;
}
type Options = Option[][];

export const useDashboardStore = defineStore(SetupStoreId.Dashboard, () => {
  const dashboardData = ref<Api.Dashboard.DashboardData>({
    currentRangeArr: [],
    currentTotalData: {},
    previousTotalData: {}
  });
  const [loading, toggleLoading] = useToggle(true);

  async function getDashboardData(query: Api.Dashboard.DashboardSearchParams) {
    if (!query.shopIdsArr?.length) return;
    toggleLoading(true);
    const { data, error } = await fetchGetDashboardData(query);
    // Access ads data
    const { data: adsData, error: adsErr } = await fetchGetAdsMetrics({
      startDateStr: query.startDateStr as string,
      endDateStr: query.endDateStr as string,
      shopId: query.shopIdsArr[0]
    });

    if (!error && !adsErr) {
      // Merge data in data and adsData
      dashboardData.value = {
        currentRangeArr: data.currentRangeArr.map(item => {
          const adsItem = adsData.currentRangeArr.find(i => i.belongDate === item.belongDate) ?? {};
          return {
            ...item,
            ...adsItem
          };
        }),
        currentTotalData: { ...data.currentTotalData, ...adsData.currentTotalData },
        previousTotalData: { ...data.previousTotalData, ...adsData.previousTotalData }
      };

      toggleLoading(false);
    }
    toggleLoading(false);
  }

  function getUsefulData(displayValue: string | string[], options: Options) {
    let dvValue: string[] = [];
    if (typeof displayValue === 'string') dvValue = [displayValue];
    else dvValue = displayValue;
    const result = dvValue.map(v => {
      let option: Option | undefined;
      for (const opt of options) {
        const idx = opt.findIndex(o => o.title === v);
        if (idx !== -1) option = opt[idx];
      }
      if (!option) return null;
      const { previousTotalData, currentTotalData, currentRangeArr } = dashboardData.value;
      const series: number[] = [];
      const date: string[] = [];
      currentRangeArr?.forEach(r => {
        series.push(r[option.key] as number);
        date.push(r.belongDate as string);
      });
      return {
        option,
        currentData: currentTotalData![option.key],
        previousData: previousTotalData![option.key],
        series,
        date
      };
    });
    return result;
  }

  function initIndicatorData(displayValue: string | string[], options: Options) {
    let dvValue: string[] = [];
    if (typeof displayValue === 'string') dvValue = [displayValue];
    else dvValue = displayValue;
    const result = dvValue.map(v => {
      let option: Option | undefined;
      for (const opt of options) {
        const idx = opt.findIndex(o => o.title === v);
        if (idx !== -1) {
          option = opt[idx];
          break;
        }
      }
      if (!option) return null;
      const { previousTotalData, currentTotalData, currentRangeArr } = dashboardData.value;
      const values: any[] = [];
      currentRangeArr?.forEach(r => {
        values.push({
          time: dayjs(r.belongDate).unix(),
          type: option.title,
          y: r[option.key]
        });
      });
      return {
        option,
        currentData: currentTotalData![option.key],
        previousData: previousTotalData![option.key],
        values,
        fieldValues: option.fieldsArr ? getIndicatorRangeValues(option.fieldsArr) : undefined
      };
    });
    return result;
  }

  function getIndicatorRangeValues(fieldArr: Api.Auth.Field[]) {
    const { currentRangeArr } = dashboardData.value;

    const result: { [key in string]?: any } = {};

    fieldArr.forEach(f => {
      const values: any[] = [];
      currentRangeArr?.forEach(r => {
        values.push({
          time: dayjs(r.belongDate).unix(),
          type: f.title,
          y: r[f.key]
        });
      });
      result[f.title] = values;
    });

    return result;
  }

  return {
    loading,
    dashboardData,
    getDashboardData,
    getUsefulData,
    initIndicatorData
  };
});
