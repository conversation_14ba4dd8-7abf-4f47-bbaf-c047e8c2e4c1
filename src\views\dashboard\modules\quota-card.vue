<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-04 11:39:34
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-17 15:35:35
 * @FilePath: \tiksage-frontend\src\views\home\modules\quota-card.vue
 * @Description: quota-card
-->
<script setup lang="tsx">
import { computed, ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { NButton, NFlex, NText, NTransfer } from 'naive-ui';
import { useDashboardStore } from '@/store/modules/dashboard';
import { useBrandBreakDownStore } from '@/store/modules/brand-breakdown';
import { useIndicator } from '@/hooks/custom/indicator';
import QuotaCardItem2 from './quota-card-item2.vue';

interface Props {
  type?: 'default' | 'brand';
  model: {
    title: string;
    allIndicatorKey: Api.Auth.AllIndicatorKey[];
    userIndicatorKey: Api.Auth.userIndicatorKey;
    description?: string;
  };
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default'
});

// data
const { displayValue, checkedValue, options, updateCheckedValue, resetCheckedValue } = useIndicator(
  props.model.allIndicatorKey,
  props.model.userIndicatorKey,
  props.type
);

// modal
const [modalVisible, toggleVisible] = useToggle(false);

const handleTransferUpdate = (value: (string | number)[]) => {
  checkedValue.value = value as string[];
};

const handleCancelSelect = () => {
  toggleVisible();
  resetCheckedValue();
};

const loading = ref(false);

const submitUpdate = () => {
  loading.value = true;

  toggleVisible();
  updateCheckedValue();
  setTimeout(() => {
    loading.value = false;
  }, 1000);
};

const dashboardStore = useDashboardStore();
const brandStore = useBrandBreakDownStore();
// concat item data
const itemData = computed(() => {
  if (props.type === 'brand') {
    return brandStore.initIndicatorData(displayValue.value, options.value);
  }
  return dashboardStore.initIndicatorData(displayValue.value, options.value);
});
</script>

<template>
  <NFlex vertical :size="16">
    <NCard :bordered="false" class="card-wrapper">
      <template #header>
        <NFlex align="center">
          <NText>{{ props.model.title }}</NText>
          <Tip v-if="props.model.description" :description="props.model.description" />
        </NFlex>
      </template>
      <template #header-extra>
        <ButtonIcon
          icon="solar:pen-2-linear"
          tooltip-content="Select Metrics"
          tooltip-placement="top"
          @click="toggleVisible()"
        />
      </template>
    </NCard>
    <NGrid v-if="loading" :x-gap="16" :y-gap="16" :cols="2">
      <NGi v-for="(_, index) in itemData" :key="index">
        <NSkeleton height="148px" />
      </NGi>
    </NGrid>
    <NGrid v-else :cols="2" :x-gap="16" :y-gap="16">
      <NGi v-for="item in itemData" :key="item?.option.key">
        <QuotaCardItem2 :model="(item as any)" />
      </NGi>
    </NGrid>
    <NModal
      v-model:show="modalVisible"
      preset="dialog"
      title="Select Metrics"
      :closable="false"
      :mask-closable="false"
      :close-on-esc="false"
    >
      <NFlex vertical>
        <NTransfer
          :options="options[0]"
          :value="(checkedValue as string[])"
          :on-update:value="handleTransferUpdate"
        ></NTransfer>
      </NFlex>
      <template #action>
        <NFlex>
          <NButton size="small" @click="handleCancelSelect">Cancel</NButton>
          <NButton size="small" type="primary" @click="submitUpdate">Confirm</NButton>
        </NFlex>
      </template>
    </NModal>
  </NFlex>
</template>

<style scoped></style>
