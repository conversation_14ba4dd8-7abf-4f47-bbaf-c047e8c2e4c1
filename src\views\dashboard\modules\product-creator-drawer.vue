<script setup lang="tsx">
import { ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { NAvatar } from 'naive-ui';
import { delay } from 'lodash-es';
import { fetchGetProductCreators } from '@/service/api';
import { LinkToCreator } from '@/utils/tiktok-link';
import { getFallbackImage } from '@/utils/fake-image';

const { VITE_CREATOR_AVATAR_URL } = import.meta.env;

interface Props {
  params: Api.Dashboard.ProductCreatorSearchParams;
}
const props = defineProps<Props>();

const show = defineModel('show', {
  type: Boolean,
  required: true
});

type DataModel = Api.Dashboard.ProductCreator;

const [loading, toggleLoading] = useToggle(false);
const data = ref<DataModel[]>();
const columns = ref<NaiveUI.DataTableColumns<DataModel>>([
  {
    key: 'creatorId',
    title: '',
    render(_, rowIndex) {
      return (
        <div class="flex items-center gap-2">
          <span class="font-bold">{rowIndex + 1}</span>
        </div>
      );
    }
  },
  {
    key: 'creatorId',
    title: 'Creator',
    render(rowData) {
      const url = `${VITE_CREATOR_AVATAR_URL}${rowData.avatarLocal}`;
      return (
        <div
          class="flex items-center gap-2 hover:(cursor-pointer text-primary)"
          onClick={() => handleLinkCreator(rowData.creatorId)}
        >
          <NAvatar src={url} fallbackSrc={getFallbackImage(50, 50)}></NAvatar>
          <div class="flex-col">
            <span class="font-bold">{rowData.nickname}</span>
            <span>@{rowData.creatorId || '-'}</span>
          </div>
        </div>
      );
    }
  },
  {
    key: 'gmv',
    title: 'GMV',
    align: 'center',
    render(rowData) {
      return <span>${rowData.gmv}</span>;
    }
  },
  {
    key: 'unitSold',
    title: 'Units sold',
    align: 'center'
  }
]);

function handleLinkCreator(id: any) {
  LinkToCreator(id);
}

async function initData() {
  toggleLoading(true);
  const { data: res, error } = await fetchGetProductCreators(props.params);
  if (!error) {
    data.value = res;
  }
  toggleLoading(false);
}

watch(
  () => show.value,
  newVal => {
    if (newVal) {
      initData();
    } else {
      delay(() => {
        data.value = [];
      }, 200);
    }
  },
  {
    immediate: true
  }
);
</script>

<template>
  <NDrawer v-model:show="show" width="600">
    <NDrawerContent closable>
      <NDataTable
        class="h-full min-h-400px"
        :bordered="false"
        :loading="loading"
        :columns="columns"
        :data="data"
      ></NDataTable>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
