<script setup lang="ts">
import type { ButtonProps, PopconfirmProps } from 'naive-ui';

interface Props extends /* @vue-ignore */ PopconfirmProps {
  icon: string;
  confirmText: string;
  buttonProps?: ButtonProps;
}

defineProps<Props>();
</script>

<template>
  <NPopconfirm>
    <template #trigger>
      <NButton v-bind="buttonProps">
        <template #icon>
          <SvgIcon :icon="icon" />
        </template>
      </NButton>
    </template>
    <div>
      {{ confirmText }}
    </div>
  </NPopconfirm>
</template>

<style lang="scss" scoped></style>
