<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-09-04 10:19:25
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-08 14:34:37
 * @FilePath: \tiksage-frontend\src\views\operational-data\dashboard\index.vue
 * @Description: operational-data dashboard
-->
<script setup lang="tsx">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import dayjs from 'dayjs';
import { fetchOperationalDashboardData } from '@/service/api/operational-data';
import { useTable } from '@/hooks/common/table';
import ButtonIcon from '@/components/custom/button-icon.vue';
import { TimeFormat } from '@/enum';
import DataOperateDrawer from './modules/data-operate-drawer.vue';
import DataDetailDrawer from './modules/data-detail.drawer.vue';

const rowData = ref<Api.OperationalData.MonthlyData | null>(null);
const [detailVisible, toggleDetailVisible] = useToggle(false);

function handleView(data: Api.OperationalData.MonthlyData) {
  rowData.value = data;
  toggleDetailVisible(true);
}

const [createVisible, toggleCreateVisible] = useToggle(false);

const { columns, data, getData, loading, mobilePagination } = useTable({
  apiFn: fetchOperationalDashboardData,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10
  },
  columns: () => [
    {
      key: 'index',
      title: 'Date',
      width: 120,
      align: 'center',
      fixed: 'left',
      render(row) {
        return dayjs().year(row.year).month(row.month).format(TimeFormat.US_DATE_NO_DAY);
      }
    },
    { key: 'clients', title: 'Clients', width: 120, align: 'center' },
    { key: 'grossProfitFormatted', title: 'Gross Profit', width: 120, align: 'center' },
    { key: 'incomeFormatted', title: 'Income', width: 120, align: 'center' },
    { key: 'expenditureFormatted', title: 'Expenditure', width: 120, align: 'center' },
    { key: 'gmvFormatted', title: 'GMV', width: 120, align: 'center' },
    { key: 'orderNum', title: 'Order', width: 120, align: 'center' },
    { key: 'affiliates', title: 'Affiliates', width: 120, align: 'center' },
    { key: 'videos', title: 'Videos', width: 120, align: 'center' },
    { key: 'livestreams', title: 'Livestreams', width: 120, align: 'center' },
    { key: 'advertisingFormatted', title: 'Advertising', width: 120, align: 'center' },
    {
      key: 'operate',
      title: '',
      align: 'center',
      width: 100,
      fixed: 'right',
      render: row => {
        return (
          <div class="flex-center gap-8px">
            <ButtonIcon
              icon="tabler:pencil"
              tooltipContent="Detail"
              tooltipPlacement="top"
              // @ts-ignore
              onClick={() => handleView(row)}
            />
          </div>
        );
      }
    }
  ]
});
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <NCard title="OP Dashboard" :bordered="false" class="card-wrapper sm:flex-1-hidden">
      <template #header-extra>
        <ButtonIcon
          icon="ic-round-plus"
          tooltip-content="Add"
          tooltip-placement="top"
          @click="toggleCreateVisible(true)"
        />
      </template>
      <NDataTable
        :bordered="false"
        :columns="columns"
        :data="data"
        size="small"
        flex-height
        :scroll-x="1420"
        :loading="loading"
        remote
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <DataDetailDrawer v-model:visible="detailVisible" :row-data="rowData" />
      <DataOperateDrawer v-model:visible="createVisible" operate-type="add" @submitted="getData" />
    </NCard>
  </div>
</template>

<style scoped></style>
