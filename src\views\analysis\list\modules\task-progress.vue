<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-21 11:43:22
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jar<PERSON>@axwuxi.com
 * @LastEditTime: 2024-12-10 14:26:54
 * @FilePath: \tiksage-frontend\src\views\analysis\list\modules\task-progress.vue
 * @Description: task-progress
-->
<script setup lang="ts">
import { ref, watch } from 'vue';
import { TaskStatus } from '@/enum';

interface Props {
  taskStatus: TaskStatus;
}

type Status = 'default' | 'success' | 'error' | 'warning' | 'info';

const props = defineProps<Props>();

const isProcessing = ref<boolean>(false);
const percentage = ref<number>(0);
const progressStatus = ref<Status>('default');

const description = ref<string>('');

watch(
  () => props.taskStatus,
  newTaskStatus => {
    switch (newTaskStatus) {
      case TaskStatus.NEW:
        progressStatus.value = 'default';
        percentage.value = 0;
        isProcessing.value = false;
        description.value = 'Waiting to start...';
        break;
      case TaskStatus.SUCCESS:
        progressStatus.value = 'success';
        percentage.value = 100;
        isProcessing.value = false;
        description.value = 'Completed!';
        break;
      case TaskStatus.RUNNING:
      case TaskStatus.FAIL:
        progressStatus.value = 'info';
        isProcessing.value = true;
        // percentage.value = calculateProgressPercentage(rowData.createTime, new Date());
        percentage.value = 50;
        description.value = 'Processing, please wait...';
        break;
      case TaskStatus.CATEGORY_NOT_FIND:
        progressStatus.value = 'warning';
        percentage.value = 100;
        isProcessing.value = false;
        description.value = 'The category was not found.';
        break;
      case TaskStatus.CATEGORY_NOT_SUPPORT:
        progressStatus.value = 'warning';
        percentage.value = 100;
        isProcessing.value = false;
        description.value = 'This category is not supported.';
        break;
      // progressStatus.value = 'warning';
      // percentage.value = 30;
      // isProcessing.value = true;
      // description.value = 'Retrying operation...';
      // break;
      default:
        progressStatus.value = 'error';
        percentage.value = 100;
        isProcessing.value = false;
        description.value = 'The task went wrong.';
    }
  },
  { immediate: true }
);
</script>

<template>
  <NTooltip>
    <template #trigger>
      <NProgress
        type="line"
        :status="progressStatus"
        :processing="isProcessing"
        :percentage="percentage"
        indicator-placement="outside"
      >
        <div class="text flex-center">
          <icon-tabler:player-play-filled v-if="progressStatus === 'default'" class="text-coolgray" />
          <icon-tabler:circle-check-filled v-if="progressStatus === 'success'" class="text-success" />
          <icon-tabler:loader-2 v-if="progressStatus === 'info'" class="animate-spin text-primary" />
          <icon-tabler:circle-x-filled v-if="progressStatus === 'error'" class="text-error" />
          <icon-tabler:alert-circle-filled v-if="progressStatus === 'warning'" class="text-warning" />
        </div>
      </NProgress>
    </template>
    {{ description }}
  </NTooltip>
</template>

<style scoped></style>
