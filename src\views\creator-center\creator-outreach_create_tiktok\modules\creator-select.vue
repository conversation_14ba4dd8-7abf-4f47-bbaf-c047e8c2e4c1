<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { NAvatar, NEllipsis, NTag } from 'naive-ui';
import { concat } from 'lodash-es';
import { getFallbackImage } from '@/utils/fake-image';

const { VITE_CREATOR_AVATAR_URL } = import.meta.env;

enum Tab {
  MANUAL = 'manual',
  SELECTED = 'selected'
}

interface Props {
  creatorData: Api.CreatorNetwork.FindCreator[];
  showManual?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showManual: true
});

const model = defineModel<string[]>('value', {
  default: []
});

const manualModel = defineModel<string[]>('manual', {
  default: [],
  required: false
});

const selectedModel = defineModel<string[]>('selected', {
  default: [],
  required: false
});

const tabValue = ref(Tab.MANUAL);

const inputValue = ref('');

const tabs = computed(() => {
  const res = [
    {
      label: `Selected Creators (${selectedModel.value.length})`,
      name: Tab.SELECTED
    }
  ];
  if (props.showManual) {
    res.unshift({
      label: `Manual Input (${manualModel.value.length})`,
      name: Tab.MANUAL
    });
  }
  return res;
});

function parseCreatorIds() {
  if (!inputValue.value) {
    manualModel.value = [];
    return;
  }

  // Parses the input string, supports multiple separators (line breaks, commas, semicolons, spaces, etc.)
  const ids = inputValue.value
    .split(/[\n,;\s]+/) // Use regular expressions to split multiple separators
    .map(id => id.trim())
    .filter(id => id); // Filter empty strings

  manualModel.value = ids;
  model.value = concat(selectedModel.value, manualModel.value);
}

const columns: NaiveUI.DataTableColumns<Api.CreatorNetwork.FindCreator> = [
  {
    key: 'id',
    title: 'Creator',
    width: 250,
    render(rowData) {
      const avatarUrl = `${VITE_CREATOR_AVATAR_URL}${rowData.avatarLocal}`;
      return (
        <div class="flex-y-center gap-2">
          <div class="flex-center flex-shrink-0">
            <NAvatar round src={avatarUrl} fallbackSrc={getFallbackImage(50, 50)} />
          </div>
          <div class="flex-col gap-2">
            <NEllipsis class="font-bold" lineClamp={1}>
              {rowData.nickname || '-'}
            </NEllipsis>
            <span class="text-coolgray">@{rowData.id || '-'}</span>
          </div>
        </div>
      );
    }
  },

  {
    key: 'categoryJson',
    title: 'Category',
    align: 'center',
    width: 200,
    render(rowData) {
      if (!rowData.categoryJson) return '-';
      const categoryList = JSON.parse(rowData.categoryJson);
      if (!categoryList.length) return '-';
      return (
        <div class="flex flex-wrap gap-2">
          {categoryList.map((item: string) => {
            return (
              <NTag bordered={false} size="small">
                {item}
              </NTag>
            );
          })}
        </div>
      );
    }
  },
  {
    key: 'follower',
    title: 'Followers',
    align: 'center',
    width: 120
  },
  {
    key: 'gmv',
    title: 'GMV',
    align: 'center',
    width: 120
  },
  {
    key: 'unitsSold',
    title: 'Units Sold',
    align: 'center',
    width: 120
  },
  {
    key: 'avgVideoViews',
    title: 'Avg. video views',
    align: 'center',
    width: 150
  },
  {
    key: 'engagementRate',
    title: 'Engagement rate',
    align: 'center',
    width: 150
  }
];

watch(
  () => props.creatorData,
  newVal => {
    if (newVal) {
      selectedModel.value = newVal.map(item => item.id);
      model.value = concat(selectedModel.value, manualModel.value);
      tabValue.value = Tab.SELECTED;
    }
  }
);

watch(
  () => manualModel.value,
  () => {
    inputValue.value = manualModel.value.join('\n');
  },
  {
    once: true
  }
);

watch(
  () => inputValue.value,
  () => {
    parseCreatorIds();
  }
);
</script>

<template>
  <div class="w-60% flex-col gap-2">
    <NTabs v-model:value="tabValue" type="segment" animated>
      <NTab v-for="tab in tabs" :key="tab.name" :name="tab.name" :tab="tab.label"></NTab>
    </NTabs>
    <div v-show="tabValue === Tab.MANUAL" class="w-full">
      <NInput
        v-model:value="inputValue"
        class="h-400px"
        type="textarea"
        placeholder="You can directly paste the creatorId here. Separate multiple IDs with commas, spaces, or new lines."
      ></NInput>
    </div>
    <NDataTable
      v-show="tabValue === Tab.SELECTED"
      class="h-400px"
      :scroll-x="1110"
      flex-height
      :columns="columns"
      :data="props.creatorData"
    ></NDataTable>
  </div>
</template>

<style scoped></style>
