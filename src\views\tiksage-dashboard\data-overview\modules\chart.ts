import { theme } from '@/constants/visactor-vchart';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';

const { numberFormat } = useNumberFormat();

interface TransformConfig<T> {
  data: T[];
  selected: string[];
  options: CommonType.Option[];
  xField?: string;
}
export function transformData<T>({ data, selected, options, xField = 'date' }: TransformConfig<T>) {
  return data.flatMap(item => {
    return selected.map(metric => ({
      type: options.find(i => i.value === metric)?.label,
      value: item[metric as keyof T],
      x: item[xField as keyof T]
    }));
  });
}

export const pieDefaultSpec: Visactor.VChart.IPieChartSpec = {
  type: 'pie',
  data: {
    values: []
  },
  outerRadius: 0.8,
  innerRadius: 0.5,
  layoutRadius: layoutRect => {
    const { width, height } = layoutRect;

    return Math.min(width, height) / 2.2;
  },
  padAngle: 0.6,
  valueField: 'value',
  categoryField: 'type',
  pie: {
    style: {
      cornerRadius: 10
    },
    state: {
      hover: {
        outerRadius: 0.85,
        stroke: '#000',
        lineWidth: 1
      },
      selected: {
        outerRadius: 0.85,
        stroke: '#000',
        lineWidth: 1
      }
    }
  },
  legends: {
    visible: true,
    orient: 'bottom'
  },
  label: {
    visible: true
  },
  tooltip: {
    mark: {
      title: {
        visible: false
      },
      content: [
        {
          key: v => {
            return v ? (v.type as string) : '';
          },
          value: datum => {
            return `${datum && numberFormat(datum.value, NumeralFormat.Real_Dollar)} (${datum && datum._percent_}%)`; // eslint-disable-line no-underscore-dangle
          }
        }
      ]
    }
  },
  emptyPlaceholder: {
    showEmptyCircle: true,
    emptyCircle: {
      style: {
        innerRadius: 0.5,
        fill: '#F4F5F5'
      }
    }
  },
  ...theme
};

export const barDefaultSpec: Visactor.VChart.IBarChartSpec = {
  type: 'bar',
  data: [
    {
      id: 'bar',
      values: []
    }
  ],
  seriesField: 'type',
  stack: false,
  bar: {
    style: {
      cornerRadius: 5
    }
  },
  barMinWidth: 20,
  barMaxWidth: 50,
  legends: [{ visible: true, position: 'middle', orient: 'top' }]
};

export const lineAreaDefaultSpec: Visactor.VChart.IAreaChartSpec = {
  type: 'area',
  padding: 0,
  data: [],
  xField: 'date',
  yField: 'value',
  seriesField: 'type',
  // stack: false,
  axes: [
    {
      orient: 'bottom',
      label: {
        formatter: '%b %d'
      }
    }
  ],
  area: {
    style: {
      fill: {
        gradient: 'linear',
        x0: 0.5,
        y0: 0,
        x1: 0.5,
        y1: 1,
        stops: [
          {
            offset: 0,
            opacity: 1
          },
          {
            offset: 1,
            opacity: 0
          }
        ]
      }
    }
  },
  legends: [{ visible: true, position: 'middle', orient: 'top' }]
};
