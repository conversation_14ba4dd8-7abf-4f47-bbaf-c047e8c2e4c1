import { request } from '../request';

export function fetchGetInvoiceHistory(params: Api.Invoice.InvoiceHistoryParams) {
  return request<Api.Invoice.InvoiceHistoryResponse>({
    url: '/invoice/getInvoiceList',
    method: 'get',
    params
  });
}

export function fetchGetInvoiceDetail(id: number) {
  return request<Api.Invoice.InvoiceCreator>({
    url: '/invoice/getInvoiceDetail',
    method: 'get',
    params: {
      id
    }
  });
}

export function fetchCreateInvoice(data: Api.Invoice.InvoiceCreator) {
  return request<number>({
    url: '/invoice/createInvoice',
    method: 'post',
    data
  });
}

export function fetchCreateContact(data: Api.Invoice.ContactorCreateParams) {
  return request({
    url: '/invoice/addContact',
    method: 'post',
    data
  });
}

export function fetchGetContactList(shopId: number) {
  return request<Api.Invoice.Contactor[]>({
    url: '/invoice/getContactList',
    method: 'get',
    params: {
      shopId
    }
  });
}

export function fetchGetDefaultInvoiceInfo(shopId: number) {
  return request<Api.Invoice.defaultInvoiceInfo>({
    url: '/invoice/selectInvoiceNoAndDefaultContact',
    method: 'get',
    params: {
      shopId
    }
  });
}

export function fetchUpdateInvoiceVoided(id: number) {
  return request({
    url: '/invoice/setInvoiceDeprecate',
    method: 'put',
    params: {
      id
    }
  });
}

export function fetchDownloadInvoicePDF(invoiceId: number) {
  return request<{ fileName: string }>({
    url: '/invoice/downloadInvoicePdf',
    method: 'get',
    params: {
      invoiceId
    }
  });
}

export function fetchUpdatePaidStatus(data: Api.Invoice.InvoiceHistory) {
  return request({
    url: '/invoice/setPaymentInfo',
    method: 'put',
    data
  });
}
