<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-29 10:52:44
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jar<PERSON>@axwuxi.com
 * @LastEditTime: 2024-09-24 09:56:50
 * @FilePath: \tiksage-frontend\src\views\manage\log-analysis\modules\page-distribution.vue
 * @Description: page-distribution
-->
<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  data: Api.SystemManage.Frequencies[];
}

const props = defineProps<Props>();

const chartOptions = computed(() => {
  const values: any = [];
  props.data.forEach(d => {
    values.push({
      type: d.pageName,
      // type: local.route[d.pageName] || 'Undefined',
      value: d.count
    });
  });

  const result: Visactor.VChart.IPieChartSpec = {
    type: 'pie',
    data: {
      values
    },
    categoryField: 'type',
    valueField: 'value'
  };
  return result;
});
</script>

<template>
  <NCard class="h-full card-wrapper" content-class="flex-center" :border="false" title="Page Navigation Frequencies">
    <VDoughnutChart :chart-options="chartOptions" />
  </NCard>
</template>

<style scoped></style>
