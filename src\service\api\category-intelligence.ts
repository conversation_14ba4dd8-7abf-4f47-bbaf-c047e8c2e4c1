/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-29 16:06:01
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-10-10 09:23:32
 * @FilePath: \tiksage-frontend\src\service\api\category-intelligence.ts
 * @Description: CategoryIntelligence Api
 */
import { request } from '../request';

export function fetchMonthList(data: Api.CategoryIntelligence.ShopTypeParams) {
  return request<string[]>({
    url: '/el-query/listQueryMonth',
    method: 'post',
    data
  });
}

// get overview  month format: yyyy-MM
export function fetchGetOverview(data: Api.CategoryIntelligence.OverviewSearchParams) {
  return request<Api.CategoryIntelligence.OverviewResponse>({
    url: '/el-query/queryOverview',
    method: 'get',
    params: data
  });
}

// tree map data
export function fetchShopMonData(data: Api.CategoryIntelligence.ShopMonParams) {
  return request<Api.CategoryIntelligence.ShopMonResponse>({
    url: '/el-query/listShopsMonData',
    method: 'post',
    data
  });
}

// fetchProductTop10
export function fetchProductTop10(data: Api.CategoryIntelligence.TopParams) {
  if (data?.shopIds?.[0] === -1) {
    data.shopIds = [];
  }
  return request<Api.CategoryIntelligence.ProductTop10Response>({
    url: '/el-query/listProductsTop',
    method: 'post',
    data
  });
}

export function fetchCreatorTop10(data: Api.CategoryIntelligence.TopParams) {
  if (data?.shopIds?.[0] === -1) {
    data.shopIds = [];
  }
  return request<Api.CategoryIntelligence.CreatorTop10Response>({
    url: '/el-query/listCreatorsTop',
    method: 'post',
    data
  });
}

export function fetchCreatorSyncDate(month: string) {
  return request<Api.CategoryIntelligence.CreatorSyncDateResponse>({
    url: '/el-query/queryCreatorSyncDate',
    method: 'get',
    params: { month }
  });
}

// fetchShopsByCategory
export function fetchShopsByCategory(data: Api.CategoryIntelligence.ShopCategorySearchParams) {
  return request<Api.CategoryIntelligence.ShopsByCategoryResponse>({
    url: '/el-query/listShopsByCategroy',
    method: 'post',
    data
  });
}

export function fetchProductPrice(data: Api.CategoryIntelligence.ProductPriceSearchParams) {
  return request<Api.CategoryIntelligence.ProductPriceResponse>({
    url: '/el-query/listPriceDistribution',
    method: 'post',
    data
  });
}

export function fetchGetIndiactorTrendData(shopType: number) {
  return request<Api.CategoryIntelligence.IndiactorTrendResponse>({
    url: '/el-query/listMonthData',
    method: 'get',
    params: { shopType }
  });
}

export function fetchGetVideoLiveListByPage(data: Api.CategoryIntelligence.VideoLiveSearchParams) {
  return request<Api.CategoryIntelligence.VideoLiveResponse>({
    url: '/el-query/listVideoLiveTop',
    method: 'post',
    data
  });
}

export function fetchGetSuCategoryOptions(shopType: number) {
  return request<string[]>({
    url: '/el-query/listSubCategorys',
    method: 'post',
    data: { shopType }
  });
}

export function fetchGetThirdCategoryOptions(shopType: number, subCategory: string) {
  return request<string[]>({
    url: '/el-query/listThirdCategorys',
    method: 'post',
    data: { shopType, subCategory }
  });
}

export function fetchGetCoreData(params: Api.CategoryIntelligence.CoreDataParams) {
  return request<Api.CategoryIntelligence.CoreDataResponse>({
    url: '/el-query/queryCategoryData',
    method: 'get',
    params
  });
}
