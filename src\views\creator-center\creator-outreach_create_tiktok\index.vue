<script setup lang="tsx">
import { computed, onBeforeUnmount, onMounted, onUnmounted, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { NButton, NFormItem, NInput } from 'naive-ui';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash-es';
import type { RouteKey } from '@elegant-router/types';
import {
  fetchCreateMessageTemplate,
  fetchGetFindCreatorListByIds,
  fetchGetMessageTemplateList,
  fetchRunTaskByTikTok
} from '@/service/api';
import { useUserStore } from '@/store/modules/user';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useRouterPush } from '@/hooks/common/router';
import { localStg } from '@/utils/storage';
import TikTokProduct from './modules/tiktok-product.vue';
import CreatorSelect from './modules/creator-select.vue';
import MessageTemplateDrawer from './modules/message-template-drawer.vue';

type Model = Api.CreatorNetwork.RunTaskByTikTokParams & {
  manualCreatorList: string[];
  selectedCreatorList: string[];
};

const { routerPushByKey } = useRouterPush();
const needCached = ref(true);
const stepCurrent = ref(1);
const steps = [
  {
    title: 'Creators Data'
  },
  {
    title: 'Task Properties'
  },
  {
    title: 'Task Content'
  },
  {
    title: 'finish'
  }
];

const selectOutreachMethod = ref('batch_messages');

const selectOutreachMethodOptions = [
  {
    label: 'Batch Messages',
    value: 'batch_messages'
  }
];

const shopOptions = ref<CommonType.Option<number>[]>([]);

const templateName = ref<string>('');
const messageTemplateList = ref<Api.CreatorNetwork.MessageTemplate[]>([]);
const messageDrawerShow = ref(false);

const selectedCreatorData = ref<Api.CreatorNetwork.FindCreator[]>([]);

const model = ref<Model>(createDefaultModel());

const { formRef, validate } = useNaiveForm();

const rules = computed(() => {
  const { defaultRequiredRule } = useFormRules();
  let res = {};
  switch (stepCurrent.value) {
    case 1:
      res = {
        shopId: [defaultRequiredRule],
        creatorList: [defaultRequiredRule]
      };
      break;
    case 2:
      res = {
        taskName: [defaultRequiredRule]
      };
      break;
    case 3:
      res = {
        title: [defaultRequiredRule],
        content: [defaultRequiredRule],
        productIds: [defaultRequiredRule]
      };
      break;
    default:
      break;
  }
  return res;
});

const showSaveTemplate = computed(() => {
  return !isEmpty(model.value.title) && !isEmpty(model.value.content);
});

const [taskLoading, toggleTaskLoading] = useToggle(false);

async function handleNext() {
  await validate();

  switch (stepCurrent.value) {
    case 3: {
      toggleTaskLoading(true);
      const { error } = await fetchRunTaskByTikTok(model.value);
      toggleTaskLoading(false);
      if (error) {
        return;
      }
      break;
    }
    case 1: {
      const shopName = shopOptions.value.find(item => item.value === model.value.shopId)?.label;
      const timeStr = dayjs().format('MMDD');
      model.value.taskName = `${shopName}-${model.value.creatorList.length}-${timeStr}`;
      break;
    }
    default:
      break;
  }
  stepCurrent.value += 1;
}

function handleBack() {
  window.$dialog?.warning({
    title: 'Warning',
    content: 'Going back will clear all task data you have entered. Are you sure you want to leave?',
    positiveText: 'Leave',
    negativeText: 'Stay',
    onPositiveClick: () => {
      needCached.value = false;
      routerPushByKey('creator-center_creator-outreach_find-creator');
    }
  });
}

const userStore = useUserStore();
const dictionaryStore = useDictionaryStore();

const messageModuleOptions = ref<Api.Dictionary.DictionaryItem<number>[]>([]);
async function initMessageModuleOptions() {
  const messageModuleOpts = await dictionaryStore.getDictionaryByCodeType<number>('message_module');
  if (!messageModuleOpts) return;
  messageModuleOptions.value = messageModuleOpts;
  model.value.messageModule = messageModuleOpts[0].code;
}

async function initShopOptions() {
  const userShops = await userStore.getUserShop();
  shopOptions.value =
    userShops?.map(item => {
      return {
        label: item.shopName,
        value: item.shopId
      };
    }) || [];
}

function createDefaultModel(): Model {
  return {
    shopId: null,
    taskName: '',
    title: '',
    content: '',
    creatorList: [],
    productIds: [],
    products: [],
    messageModule: 2,
    manualCreatorList: [],
    selectedCreatorList: []
  };
}

function handleLinkTo(route: RouteKey) {
  needCached.value = false;
  routerPushByKey(route);
}

function handleSaveTemplate() {
  const dialog = window.$dialog?.create({
    title: 'Save as template',
    content() {
      return (
        <NFormItem label="Template Name">
          <NInput onUpdate:value={v => (templateName.value = v)} />
        </NFormItem>
      );
    },
    positiveText: 'Save',
    negativeText: 'Cancel',
    async onPositiveClick() {
      dialog && (dialog.loading = true);
      const { error: createMsgErr } = await fetchCreateMessageTemplate({
        name: templateName.value,
        title: model.value.title,
        content: model.value.content
      });
      if (!createMsgErr) {
        window.$message?.success('Save Successfully.');
        initMessageTemplateOptions();
        dialog?.destroy();
      }
      dialog && (dialog.loading = false);
    }
  });
}

async function initMessageTemplateOptions() {
  const { data: msgList, error: msgListErr } = await fetchGetMessageTemplateList();
  if (!msgListErr) {
    messageTemplateList.value = msgList;
  }
}

function handleShowMsgDrawer() {
  messageDrawerShow.value = true;
}

function handleSelectMessageTemplate(id: number) {
  const msg = messageTemplateList.value.find(item => item.id === id);
  if (msg) {
    model.value.title = msg.title;
    model.value.content = msg.content;
  }
}

function handleReBackTask() {
  const json = localStg.get('cachedTiktokMessageData');
  if (!json) return;
  const { step, ...rest } = JSON.parse(json);
  model.value = rest;
  stepCurrent.value = step;
}

async function initSelectedCreatorData() {
  const ids = localStg.get('findCreatorSelectedIds') || [];
  const { data: selectedCreators, error: selectedCreatorErr } = await fetchGetFindCreatorListByIds(ids || []);
  if (!selectedCreatorErr) {
    selectedCreatorData.value = selectedCreators;
  }
}

function initData() {
  initSelectedCreatorData();
  initShopOptions();
  initMessageModuleOptions();
  initMessageTemplateOptions();
}

initData();

watch(
  () => messageDrawerShow.value,
  newVal => {
    if (!newVal) {
      initMessageTemplateOptions();
    }
  }
);

// 添加捕获F5刷新的功能
function handleBeforeUnload(e?: BeforeUnloadEvent) {
  if (!needCached.value) {
    localStg.remove('cachedTiktokMessageData');
    localStg.remove('findCreatorSelectedIds');
    return;
  }

  // 保存当前状态到localStorage
  localStg.set('cachedTiktokMessageData', JSON.stringify({ ...model.value, step: stepCurrent.value }));

  // 显示确认对话框
  e?.preventDefault();
}

onMounted(() => {
  setTimeout(() => {
    handleReBackTask();
  });
  // 添加beforeunload事件监听器
  window.addEventListener('beforeunload', handleBeforeUnload);
});

onBeforeUnmount(() => {
  handleBeforeUnload();
});

onUnmounted(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload);
});

watch(
  [() => model.value.manualCreatorList, () => model.value.selectedCreatorList],
  () => {
    model.value.creatorList = [...model.value.manualCreatorList, ...model.value.selectedCreatorList];
  },
  {
    immediate: true
  }
);
</script>

<template>
  <div class="flex-col gap-4">
    <NCard class="h-full min-h-400px card-wrapper" segmented :bordered="false" title="Create Outreach-Task">
      <template #header-extra>
        <ButtonBack :back-callback="handleBack" />
      </template>
      <NSteps :current="stepCurrent">
        <NStep v-for="s in steps" :key="s.title" :title="s.title"></NStep>
      </NSteps>
      <NForm ref="formRef" :model="model" :rules="rules">
        <div v-show="stepCurrent === 1" class="form-wrapper">
          <NFormItem label="Shop" path="shopId">
            <NSelect v-model:value="model.shopId" class="max-w-400px" :options="shopOptions"></NSelect>
          </NFormItem>
          <NFormItem label="Creator Data" path="creatorList">
            <CreatorSelect
              v-model:manual="model.manualCreatorList"
              v-model:selected="model.selectedCreatorList"
              :creator-data="selectedCreatorData"
            />
          </NFormItem>
        </div>
        <div v-show="stepCurrent === 2" class="form-wrapper">
          <NFormItem label="Select Outreach Method" path="">
            <NRadioGroup v-model:value="selectOutreachMethod">
              <NRadio v-for="item in selectOutreachMethodOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </NRadio>
            </NRadioGroup>
          </NFormItem>
          <NFormItem label="Task Name" path="taskName">
            <NInput v-model:value="model.taskName" class="max-w-400px" />
            <template #feedback>
              <span class="text-xs text-coolgray">Default format: Shop Name-Creator Count-Date (MMDD)</span>
            </template>
          </NFormItem>
        </div>
        <div v-show="stepCurrent === 3" class="form-wrapper">
          <NGrid class="my-4" :cols="2" :x-gap="16">
            <NGi
              v-for="module in messageModuleOptions"
              :key="module.code"
              class="h-130px flex-col gap-4 rounded bg-#F8F8F8 p-4 hover:cursor-pointer"
              @click="model.messageModule = module.code"
            >
              <div class="flex gap-4">
                <NRadio :checked="model.messageModule === module.code"></NRadio>
                <span>{{ module.name }}</span>
              </div>
              <span>{{ module.description }}</span>
            </NGi>
          </NGrid>
          <div class="flex justify-end">
            <NSelect
              class="max-w-200px"
              placeholder="Please select a template."
              :options="messageTemplateList"
              value-field="id"
              label-field="name"
              @update:value="handleSelectMessageTemplate"
            >
              <template #header>
                <div class="flex justify-end">
                  <NButton size="tiny" quaternary @click="handleShowMsgDrawer">
                    <template #icon>
                      <icon-solar:settings-linear />
                    </template>
                  </NButton>
                </div>
              </template>
            </NSelect>
          </div>
          <NFormItem label="Title" path="title">
            <NInput v-model:value="model.title" show-count :maxlength="50" placeholder="" />
          </NFormItem>
          <NFormItem label="Message" path="content">
            <NInput
              v-model:value="model.content"
              class="h-250px"
              type="textarea"
              placeholder=""
              show-count
              :maxlength="500"
            />
          </NFormItem>
          <div class="flex justify-end">
            <NButton v-show="showSaveTemplate" secondary @click="handleSaveTemplate">Save as template</NButton>
          </div>
          <NFormItem label="Product card" path="productIds">
            <TikTokProduct
              v-model:value="model.productIds"
              v-model:products="model.products"
              :shop-id="model.shopId"
              class="h-full"
            />
          </NFormItem>
        </div>
      </NForm>
      <div v-show="stepCurrent === 4" class="h-full flex-center">
        <div class="flex-col-center gap-4">
          <icon-solar:check-circle-bold-duotone class="text-5xl text-green-5" />
          <span class="text-base">
            Successfully Created an Outreach Task for {{ model.creatorList.length }} Creator(s).
          </span>
          <div class="flex gap-4">
            <!-- <NButton strong secondary> Create Another Outreach Task with the Same Creator(s) </NButton> -->
            <NButton
              strong
              secondary
              type="primary"
              @click="handleLinkTo('creator-center_creator-outreach_history_tiktok')"
            >
              Back to OutreachTask List
            </NButton>
          </div>
        </div>
      </div>
      <template #action>
        <div v-if="stepCurrent < 4" class="flex-y-center justify-end gap-4">
          <span v-if="stepCurrent === 1" class="text-coolgray">
            Total {{ model.creatorList.length }} creator{{ model.creatorList.length !== 1 ? 's' : '' }} selected
          </span>
          <NButton v-if="stepCurrent > 1" @click="stepCurrent--">Previous</NButton>
          <NButton type="primary" :loading="taskLoading" @click="handleNext">
            {{ stepCurrent === 3 ? 'Save & Run Task' : 'Next' }}
          </NButton>
        </div>
      </template>
    </NCard>
    <MessageTemplateDrawer v-model:show="messageDrawerShow" />
  </div>
</template>

<style scoped lang="scss">
.form-wrapper {
  @apply mt-8;
}
</style>
