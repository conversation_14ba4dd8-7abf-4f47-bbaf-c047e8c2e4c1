<script setup lang="tsx">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { NButton, NFormItem, NInput } from 'naive-ui';
import { isNil } from 'lodash-es';
import {
  fetchDeleteCampaignTemplate,
  fetchGetCampaignTemplateDetail,
  fetchGetCampaignTemplateList,
  fetchSaveCampaignTemplate
} from '@/service/api';

const templateOptions = ref<CommonType.Option<number>[]>([]);

// interface Props {
//   data: Api.WeeklyReport.CampaignData[];
// }

// const props = defineProps<Props>();

const model = defineModel<Api.WeeklyReport.CampaignData[]>('data', {
  required: true
});

const [listLoading, toggleListLoading] = useToggle(false);
const newTemplateName = ref('');

function handleSaveTemplate() {
  const dialog = window.$dialog?.create({
    title: 'Save as template',
    content() {
      return (
        <NFormItem label="Template Name">
          <NInput onUpdate:value={v => (newTemplateName.value = v)} />
        </NFormItem>
      );
    },
    positiveText: 'Save',
    negativeText: 'Cancel',
    async onPositiveClick() {
      dialog && (dialog.loading = true);
      const { error: createMsgErr } = await fetchSaveCampaignTemplate({
        templateName: newTemplateName.value,
        campaignList: model.value
      });
      if (!createMsgErr) {
        window.$message?.success('Save Successfully.');
        initData();
        dialog?.destroy();
      }
      dialog && (dialog.loading = false);
    }
  });
}

function handleApplyTemplate(templateId: number) {
  const dialog = window.$dialog?.info({
    title: 'Apply Template',
    content: 'Are you sure you want to apply this template? It will cover the entire table content!',
    positiveText: 'confirm',
    negativeText: 'cancel',
    async onPositiveClick() {
      dialog && (dialog.loading = true);
      const { data: detailData, error: applyErr } = await fetchGetCampaignTemplateDetail(templateId);
      if (!applyErr) {
        model.value = detailData;
        window.$message?.success('Apply Successfully.');
        dialog?.destroy();
      }
      dialog && (dialog.loading = false);
    }
  });
}

async function handleDeleteTemplate(templateId?: number) {
  if (isNil(templateId)) return;
  const { error: delErr } = await fetchDeleteCampaignTemplate(templateId);
  if (!delErr) {
    window.$message?.success('Delete Successfully.');
    initData();
  }
}

async function initData() {
  toggleListLoading(true);
  const { data: opt, error: optErr } = await fetchGetCampaignTemplateList();
  if (!optErr) {
    templateOptions.value = opt.map(item => ({
      label: item.templateName,
      value: item.templateId
    }));
  }
  toggleListLoading(false);
}

initData();
</script>

<template>
  <NPopover trigger="click">
    <template #trigger>
      <NButton secondary>template</NButton>
    </template>
    <template #header>
      <div class="flex-center">
        <NButton size="small" secondary type="primary" @click="handleSaveTemplate">Save as template</NButton>
      </div>
    </template>
    <NScrollbar style="max-height: 300px" class="w-200px">
      <NEmpty v-if="!templateOptions.length" description="No templates available." />
      <NSpin v-else :show="listLoading">
        <NList hoverable clickable>
          <NListItem
            v-for="option in templateOptions"
            :key="option.value"
            @click="handleApplyTemplate(option.value as number)"
          >
            <div class="flex-y-center justify-between">
              <NEllipsis :line-clamp="1">{{ option.label }}</NEllipsis>
              <NButton size="tiny" type="error" quaternary @click.stop="handleDeleteTemplate(option.value)">
                <template #icon>
                  <icon-solar:trash-bin-minimalistic-linear />
                </template>
              </NButton>
            </div>
          </NListItem>
        </NList>
      </NSpin>
    </NScrollbar>
  </NPopover>
</template>

<style scoped></style>
