<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-11-04 09:24:51
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-04 11:04:35
 * @FilePath: \tiksage-frontend\src\views\video-manage\upload-list\modules\video-card.vue
 * @Description: video-card
-->
<script setup lang="ts">
import { computed } from 'vue';
import dayjs from 'dayjs';
import { TimeFormat } from '@/enum';

interface Props {
  deletable?: boolean;
  data: Api.VideoManage.VideoCard;
}

interface Emites {
  (e: 'view', id: number): void;
  (e: 'delete', id: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  deletable: false
});

const emit = defineEmits<Emites>();

const videoSrc = computed(() => {
  const res = `${import.meta.env.VITE_VIDEO_URL}${props.data.localVideo}`;
  return res;
});

function handleView() {
  emit('view', props.data.id);
}
function handleDelete() {
  emit('delete', props.data.id);
}
</script>

<template>
  <div id="mask-layer" class="relative h-full">
    <div
      class="absolute left-0 top-0 z-999 hidden h-full w-full flex-center gap-16px rounded bg-dark-300/50 opacity-0 transition-opacity"
    >
      <icon-solar:eye-linear
        class="text-3xl text-gray hover:transform-scale-90 hover:cursor-pointer"
        @click="handleView"
      />
      <icon-solar:trash-bin-2-linear
        v-if="deletable"
        class="text-3xl text-gray hover:transform-scale-90 hover:cursor-pointer"
        @click="handleDelete"
      />
    </div>
    <NCard size="small" embedded>
      <template #cover>
        <div class="flex-center bg-dark">
          <Vidstack :src="videoSrc" :show-control="false" />
        </div>
      </template>
      <NFlex vertical>
        <NText>@{{ data.creatorName }}</NText>
        <NText class="self-end text-end text-xs">
          {{ dayjs(data.createTime).format(TimeFormat.US_TIME_12_NO_YEAR) }}
        </NText>
      </NFlex>
    </NCard>
  </div>
</template>

<style scoped lang="scss">
#mask-layer:hover > div:nth-child(1) {
  display: flex;
  opacity: 1;
}
</style>
