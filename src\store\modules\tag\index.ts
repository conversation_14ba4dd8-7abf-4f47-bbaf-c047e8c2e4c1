import { ref } from 'vue';
import { defineStore } from 'pinia';
import { fetchGetTagList } from '@/service/api';
import { SetupStoreId } from '@/enum';

type Tag = Api.Tag.Tag;

export const useTagStore = defineStore(SetupStoreId.Tag, () => {
  const tagList = ref<Tag[]>([]);

  // 初始化标签列表
  async function initTagList() {
    // 仅当tagList为空时初始化
    if (tagList.value.length === 0) {
      const { data: tagListRes, error: tagListErr } = await fetchGetTagList();
      if (!tagListErr) {
        tagList.value = tagListRes;
      } else {
        tagList.value = [];
      }
    }
    return tagList.value;
  }

  // 添加新标签到列表
  function addTag(tag: Tag) {
    const existingIndex = tagList.value.findIndex(t => t.id === tag.id);
    if (existingIndex === -1) {
      tagList.value.push(tag);
    }
  }

  // 更新标签
  function updateTagInList(updatedTag: Tag) {
    const index = tagList.value.findIndex(t => t.id === updatedTag.id);
    if (index !== -1) {
      tagList.value[index] = updatedTag;
    }
  }

  // 从列表中删除标签
  function removeTagFromList(tagId: number) {
    const index = tagList.value.findIndex(t => t.id === tagId);
    if (index !== -1) {
      tagList.value.splice(index, 1);
    }
  }

  return {
    tagList,
    initTagList,
    addTag,
    updateTagInList,
    removeTagFromList
  };
});
