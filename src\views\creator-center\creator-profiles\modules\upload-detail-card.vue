<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { useEventBus, useIntervalFn, useToggle } from '@vueuse/core';
import type { UploadCustomRequestOptions, UploadFileInfo } from 'naive-ui';
import { NAlert } from 'naive-ui';
import dayjs from 'dayjs';
import { isNil } from 'lodash-es';
import { fetchGetRunningTask, fetchReRunScript, fetchUploadCreatorCenter } from '@/service/api';
import TaskProgress from '@/views/analysis/list/modules/task-progress.vue';
import { TaskStatus, TimeFormat } from '@/enum';

const bus = useEventBus<string>('refresh-task');

function handleVideoLimit(data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) {
  if (!data.file.type?.includes('sheet')) {
    window.$message?.warning('Upload a Excel in xlsx format.');
    return false;
  }

  const fileSize = data.file.file?.size;
  if (fileSize && fileSize > 1024 * 1024 * 100) {
    window.$message?.warning("The file you're trying to upload is too large. Maximum allowed size is 100MB.");
    return false;
  }

  return true;
}

function handleDownloadTemplate() {
  window.location.href = 'http://product.tiksage.com/images/aic-creator/template/creator_center_template.xlsx';
}

const fileList = ref<UploadFileInfo[]>([]);

const [isRunningTask, toggleRunningTask] = useToggle(false);

const [isRunningTaskError, toggleRunningTaskError] = useToggle(false);

const runningTask = ref<Api.CreatorCenter.RunningTask>();

const { resume, pause } = useIntervalFn(
  () => {
    getRunningTask();
    bus.emit('refresh-task');
  },
  1000 * 5,
  {
    immediate: false,
    immediateCallback: true
  }
);

async function getRunningTask() {
  const { data, error } = await fetchGetRunningTask();
  if (!error) {
    if (!isNil(data.taskId)) {
      runningTask.value = data;
      if ([11, 12, 13].includes(data.grabTaskStatus)) {
        toggleRunningTaskError(true);
      }
      toggleRunningTask(true);
      return;
    }
  }
  pause();
  toggleRunningTaskError(false);
  toggleRunningTask(false);
  fileList.value = [];
}

const customRequest = async ({ file, onFinish, onError }: UploadCustomRequestOptions) => {
  const { error } = await fetchUploadCreatorCenter({ file: file.file as File });
  if (error) {
    onError();
  } else {
    resume();
  }
  onFinish();
};

const progressIndicators = computed(() => [
  {
    key: 'total',
    label: 'Total',
    value: runningTask.value?.total || 0
  },
  {
    key: 'pending',
    label: 'Pending',
    value: runningTask.value?.pending || 0
  },
  {
    key: 'success',
    label: 'Success',
    value: runningTask.value?.success || 0
  },
  {
    key: 'failed',
    label: 'Failed',
    value: runningTask.value?.fail || 0
  }
]);

const progressTime = computed(() => {
  if (!runningTask.value) return '';
  const time = dayjs(runningTask.value.createTime).tz('Etc/GMT+8').format(TimeFormat.US_TIME_24);
  return `Created On: ${time}`;
});

const [isReRunScript, toggleReRunScript] = useToggle(false);

async function handleReRunScript() {
  if (!runningTask.value?.taskId) return;
  toggleReRunScript(true);
  const { error } = await fetchReRunScript(runningTask.value.taskId);
  if (!error) {
    window.$message?.success('Re-run Successfully.');
    resume();
  }
  toggleReRunScript(false);
}

onMounted(() => {
  resume();
});

onUnmounted(() => {
  pause();
});
</script>

<template>
  <div class="flex-col gap-16px">
    <NCard class="card-wrapper" :bordered="false" title="Upload Creator">
      <NUpload
        ref="uploadRef"
        v-model:file-list="fileList"
        :disabled="isRunningTask"
        directory-dnd
        :max="1"
        :custom-request="customRequest"
        @before-upload="handleVideoLimit"
      >
        <NUploadDragger>
          <NFlex class="h-200px" vertical justify="center" align="center" :size="16">
            <SvgIcon class="text-4xl text-primary" icon="solar:cloud-upload-bold-duotone" />
            <NText>Drag & drop your files here or choose files.</NText>
            <NText>
              *100 MB max file size. (
              <NButton text type="primary" @click.stop="handleDownloadTemplate">Template</NButton>
              )*
            </NText>
          </NFlex>
        </NUploadDragger>
      </NUpload>
    </NCard>
    <NAlert v-show="isRunningTaskError" type="warning">
      The system has detected that the data fetching for some creators is restricted and requires manual CAPTCHA
      verification.
      <br />
      Please go to the TikTok backend to complete the verification. Once done, the system will automatically continue
      fetching the data.
    </NAlert>
    <NCard class="card-wrapper" :bordered="false" title="Creator Data Processing Progress">
      <template #header-extra>
        <span v-if="isRunningTask" class="text-gray">{{ progressTime }}</span>
      </template>
      <div class="h-60px">
        <NGrid v-if="isRunningTask" class="h-full" item-responsive responsive="screen">
          <NGi span="8" class="flex-y-center gap-32px">
            <NStatistic v-for="item in progressIndicators" :key="item.key" :label="item.label" :value="item.value" />
          </NGi>
          <NGi span="8" class="flex-center">
            <TaskProgress :task-status="runningTask?.grabTaskStatus || TaskStatus.NEW" />
          </NGi>
          <NGi span="8" class="flex items-center justify-end">
            <NPopconfirm v-if="isRunningTaskError" @positive-click="handleReRunScript">
              <template #trigger>
                <NButton :loading="isReRunScript" type="primary">Re-run Script</NButton>
              </template>
              Are you sure you want to re-execute this task?
            </NPopconfirm>
          </NGi>
        </NGrid>
        <div v-else class="h-full w-full flex-center bg-layout">
          <span class="text text-gray">
            No tasks are currently running. Please upload an xlsx file to start a new task.
          </span>
        </div>
      </div>
    </NCard>
  </div>
</template>

<style scoped></style>
