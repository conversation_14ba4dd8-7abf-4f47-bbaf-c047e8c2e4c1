<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-14 09:37:39
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-01 14:49:41
 * @FilePath: \tiksage-frontend\src\views\dashboard\modules\today-contributing-creators-card.vue
 * @Description: today-contributing-creators-card
-->
<script setup lang="tsx">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { NAvatar, NDataTable, NEllipsis, NFlex } from 'naive-ui';
import { isNumber } from 'lodash-es';
import { fetchTodayCreatorList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { dateFormat } from '@/utils/date';
import { LinkToCreator } from '@/utils/tiktok-link';
import { getFallbackImage } from '@/utils/fake-image';
import SvgIcon from '@/components/custom/svg-icon.vue';
import Tip from '@/components/custom/tip.vue';
import { NumeralFormat } from '@/enum';
import ButtonCopy from '@/components/custom/button-copy.vue';

interface Props {
  updateTime?: number;
  topData: Api.Dashboard.TodayCreator[];
  shopId: number;
}

const props = defineProps<Props>();

const prefixUrl = ref(import.meta.env.VITE_CREATOR_AVATAR_URL);

const { numberFormat } = useNumberFormat();

const { data, columns, pagination, updateSearchParams, getData, loading } = useTable({
  immediate: false,
  apiFn: fetchTodayCreatorList,
  apiParams: {
    current: 1,
    size: 10,
    shopId: props.shopId
  },
  columns() {
    return [
      {
        key: 'nickname',
        fixed: 'left',
        align: 'center',
        width: 50,
        render(_rowData, index) {
          if (index === 0) {
            return (
              <NFlex class="w-100%" justify="center">
                <SvgIcon style="width:25px;height:25px" icon="twemoji:1st-place-medal" />
              </NFlex>
            );
          } else if (index === 1) {
            return (
              <NFlex class="w-100%" justify="center">
                <SvgIcon style="width:25px;height:25px" icon="twemoji:2nd-place-medal" />
              </NFlex>
            );
          } else if (index === 2) {
            return (
              <NFlex class="w-100%" justify="center">
                <SvgIcon style="width:25px;height:25px" icon="twemoji:3rd-place-medal" />
              </NFlex>
            );
          }
          return <span>{index + 1}</span>;
        }
      },
      {
        key: 'creatorId',
        title: 'Creator',
        fixed: 'left',
        width: 250,
        render: rowData => {
          return (
            <div
              class="flex flex-nowrap items-center gap-2 hover:(cursor-pointer text-primary)"
              onClick={() => handleLinkCreator(rowData.creatorId)}
            >
              <NAvatar
                class="flex-shrink-0"
                src={`${prefixUrl.value}${rowData.avatarLocal}`}
                fallbackSrc={getFallbackImage(50, 50)}
              />
              <NEllipsis>{rowData.nickname}</NEllipsis>
              <ButtonCopy copy={rowData.nickname} />
            </div>
          );
        }
      },
      {
        key: 'affiliateGmv',
        title() {
          return (
            <NFlex align="center" justify="center" wrap={false}>
              <span>GMV</span>
              <Tip description="The total amount of paid orders (including returns and refunds) attributed from this affiliate within 14 days of clicking product links in their content (including LIVEs, shoppable videos, and showcase)." />
            </NFlex>
          );
        },
        align: 'center',
        width: 150,
        sorter: 'default',
        sortOrder: 'descend'
      },
      {
        key: 'affiliateFollowers',
        title() {
          return (
            <NFlex align="center" justify="center" wrap={false}>
              <span>Followers</span>
              <Tip description="The current number of followers the affiliate has." />
            </NFlex>
          );
        },
        align: 'center',
        width: 150
      },
      {
        key: 'affiliateGmvLive',
        title() {
          return (
            <NFlex align="center" justify="center" wrap={false}>
              <span>LIVE GMV</span>
              <Tip description="The total amount of orders placed within 24 hours of viewing the LIVE, including returns and refunds." />
            </NFlex>
          );
        },
        align: 'center',
        width: 150
      },
      {
        key: 'affiliateGmvShoppableVideo',
        title() {
          return (
            <NFlex align="center" justify="center" wrap={false}>
              <span>Shoppable video GMV</span>
              <Tip description="The total amount of paid orders (including returns and refunds) attributed from this affiliate within 14 days of clicking product links in their content (including LIVEs, shoppable videos, and showcase)." />
            </NFlex>
          );
        },
        align: 'center',
        width: 150
      },
      {
        key: 'affiliateGmvShowcase',
        title() {
          return (
            <NFlex align="center" justify="center" wrap={false}>
              <span>Showcase GMV</span>
              <Tip description="The total amount of paid orders (including returns and refunds) attributed from creator's Showcase during the selected period." />
            </NFlex>
          );
        },
        align: 'center',
        width: 150
      },
      {
        key: 'avgOrderValue',
        title() {
          return (
            <NFlex align="center" justify="center" wrap={false}>
              <span>Avg. order value</span>
              <Tip description="The average amount paid by buyers for each order." />
            </NFlex>
          );
        },
        align: 'center',
        width: 150
      },
      {
        key: 'estCommission',
        title() {
          return (
            <NFlex align="center" justify="center" wrap={false}>
              <span>Est. commission</span>
              <Tip description="Total estimated commission paid to creators, including COD (paid and unpaid) and non-COD orders, and refunds." />
            </NFlex>
          );
        },
        align: 'center',
        width: 150
      }
      // {
      //   key: 'affiliateLives',
      //   title() {
      //     return (
      //       <NFlex align="center" justify="center" wrap={false}>
      //         <span>Affiliate LIVEs</span>
      //         <Tip description="The total number of affiliate LIVEs that feature the seller's product." />
      //       </NFlex>
      //     );
      //   },
      //   align: 'center',
      //   width: 150
      // },
      // {
      //   key: 'affiliateGmvShoppableVideo',
      //   title() {
      //     return (
      //       <NFlex align="center" justify="center" wrap={false}>
      //         <span>Affiliate shoppable videos</span>
      //         <Tip description="The total number of affiliate shoppable videos that feature the seller's product." />
      //       </NFlex>
      //     );
      //   },
    ];
  }
});

const simpleColumns: NaiveUI.TableColumn<Api.Dashboard.TodayCreator>[] = [
  {
    key: 'creatorId',
    title: 'Creator',
    fixed: 'left',
    width: 150,
    render: rowData => {
      return (
        <div
          class="flex flex-nowrap items-center gap-2 hover:(cursor-pointer text-primary)"
          onClick={() => handleLinkCreator(rowData.creatorId)}
        >
          <NAvatar
            class="flex-shrink-0"
            src={`${prefixUrl.value}${rowData.avatarLocal}`}
            fallbackSrc={getFallbackImage(50, 50)}
          />
          <NEllipsis lineClamp={1}>{rowData.nickname}</NEllipsis>
        </div>
      );
    }
  },
  {
    key: 'affiliateGmv',
    title: 'GMV',
    align: 'center',
    width: 110,
    sorter: 'default',
    sortOrder: 'descend'
  },
  {
    key: 'affiliateFollowers',
    title: 'Followers',
    align: 'center',
    width: 110
  },
  {
    key: 'estCommission',
    title: 'Est. commission',
    align: 'center',
    width: 130
  }
];

function handleLinkCreator(id: any) {
  LinkToCreator(id);
}

type Keys = keyof Api.Dashboard.TodayCreator;

const dollarKeys: Keys[] = [
  'affiliateGmv',
  'affiliateGmvLive',
  'affiliateGmvShoppableVideo',
  'affiliateGmvShowcase',
  'avgOrderValue',
  'estCommission'
];

const percentKeys: Keys[] = [];

function renderCell(value: any, _rowData: any, column: any) {
  if (!isNumber(value)) return value;
  if (dollarKeys.length && dollarKeys.includes(column.key)) {
    return numberFormat(value, NumeralFormat.Dollar);
  }
  if (percentKeys.length && percentKeys.includes(column.key)) {
    return numberFormat(value, NumeralFormat.Percent);
  } else if (typeof value === 'number') {
    return numberFormat(value, NumeralFormat.Number);
  }
  return value || 0;
}

const [drawShow, toggleDrawShow] = useToggle(false);

function handleSeeMore() {
  toggleDrawShow(true);
}

function handleEnterDrawer() {
  updateSearchParams({ shopId: props.shopId });
  getData();
}
</script>

<template>
  <NCard class="card-wrapper" content-class="flex" :bordered="false" title="Contributing Creators">
    <template #header-extra>
      <NButton icon-placement="right" quaternary @click="handleSeeMore">
        <template #icon>
          <icon-solar:alt-arrow-right-bold-duotone />
        </template>
        See More
      </NButton>
    </template>
    <NDataTable
      class="flex-1"
      flex-height
      size="large"
      remote
      :bordered="false"
      :columns="simpleColumns"
      :data="topData.slice(0, 5)"
      :render-cell="renderCell"
    >
      <template #empty>
        <NEmpty class="m-auto" description="No Orders"></NEmpty>
      </template>
    </NDataTable>
    <NDrawer v-model:show="drawShow" width="80%" @after-enter="handleEnterDrawer">
      <NDrawerContent closable>
        <template #header>
          <NFlex>
            <span>More Data</span>
            <NText class="text-sm text-gray">Last updated: {{ dateFormat(updateTime) }}</NText>
          </NFlex>
        </template>
        <NDataTable
          remote
          class="h-full"
          size="small"
          :bordered="false"
          :loading="loading"
          :pagination="pagination"
          :columns="columns"
          :data="data"
          flex-height
          :scroll-x="1700"
          :render-cell="renderCell"
        >
          <template #empty>
            <NEmpty class="m-auto" description="No Orders"></NEmpty>
          </template>
        </NDataTable>
      </NDrawerContent>
    </NDrawer>
  </NCard>
</template>

<style scoped></style>
