<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-05 14:31:30
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-25 16:07:24
 * @FilePath: \tiksage-frontend\src\views\home\modules\creator-sales-card.vue
 * @Description: creator-sales-card
-->
<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { NAvatar, NEllipsis, NFlex } from 'naive-ui';
import { fetchDownoloadCreatorsTopData, fetchGetCreatorsTopList } from '@/service/api';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { downloadFile } from '@/utils/download';
import { LinkToCreator } from '@/utils/tiktok-link';
import { getFallbackImage } from '@/utils/fake-image';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { NumeralFormat } from '@/enum';
import Tip from '@/components/custom/tip.vue';

interface Props {
  params: Api.Dashboard.DashboardTopSearchParams;
}

const { numberFormat } = useNumberFormat();

const props = defineProps<Props>();

const tableData = ref<Api.Dashboard.CreatorsTop[]>([]);

const initData = async (params: Api.Dashboard.DashboardTopSearchParams) => {
  const { data, error } = await fetchGetCreatorsTopList({ ...params, topNum: 10 });
  if (error) return;
  tableData.value = data.creatorTopArr;
};

const columns = computed<NaiveUI.TableColumn<Api.Dashboard.CreatorsTop>[]>(() => [
  {
    key: 'nickname',
    fixed: 'left',
    align: 'center',
    width: 50,
    render(_rowData, index) {
      if (index === 0) {
        return (
          <NFlex class="w-100%" justify="center">
            <SvgIcon style="width:25px;height:25px" icon="twemoji:1st-place-medal" />
          </NFlex>
        );
      } else if (index === 1) {
        return (
          <NFlex class="w-100%" justify="center">
            <SvgIcon style="width:25px;height:25px" icon="twemoji:2nd-place-medal" />
          </NFlex>
        );
      } else if (index === 2) {
        return (
          <NFlex class="w-100%" justify="center">
            <SvgIcon style="width:25px;height:25px" icon="twemoji:3rd-place-medal" />
          </NFlex>
        );
      }
      return <span>{index + 1}</span>;
    }
  },
  {
    key: 'creatorId',
    title: 'Creator',
    fixed: 'left',
    width: 250,
    render: rowData => {
      return (
        <div
          class="flex flex-nowrap items-center gap-2 hover:(cursor-pointer text-primary)"
          onClick={() => handleLinkCreator(rowData.creatorId)}
        >
          <NAvatar
            src={`${import.meta.env.VITE_CREATOR_AVATAR_URL}${rowData.avatar}`}
            fallbackSrc={getFallbackImage(50, 50)}
          />
          <NEllipsis style={'max-width:180px'}>{rowData.nickname}</NEllipsis>
        </div>
      );
    }
  },
  {
    key: 'affiliateGmv',
    title() {
      return (
        <NFlex align="center" justify="center" wrap={false}>
          <span>GMV</span>
          <Tip description="This figure represents the total revenue from paid orders (including returns and refunds) that can be attributed to this creator within 14 days of users clicking product links in their content, specifically within the context of the current shop's partnerships and collaborations. This includes sales generated from LIVEs, shoppable videos, and showcases." />
        </NFlex>
      );
    },
    align: 'center',
    width: 150,
    sorter: 'default',
    sortOrder: 'descend'
  },
  {
    key: 'affiliateFollowers',
    title() {
      return (
        <NFlex align="center" justify="center" wrap={false}>
          <span>Followers</span>
          <Tip description="The current number of followers the affiliate has." />
        </NFlex>
      );
    },
    align: 'center',
    width: 150
  },
  {
    key: 'affiliateGmvShoppableVideo',
    title() {
      return (
        <NFlex align="center" justify="center" wrap={false}>
          <span>Shoppable video GMV</span>
          <Tip description="The total amount of paid orders (including returns and refunds) attributed from this affiliate within 14 days of clicking product links in their content (including LIVEs, shoppable videos, and showcase)." />
        </NFlex>
      );
    },
    align: 'center',
    width: 150
  },
  {
    key: 'affiliateGmvLive',
    title() {
      return (
        <NFlex align="center" justify="center" wrap={false}>
          <span>LIVE GMV</span>
          <Tip description="The total amount of orders placed within 24 hours of viewing the LIVE, including returns and refunds." />
        </NFlex>
      );
    },
    align: 'center',
    width: 150
  },
  {
    key: 'affiliateGmvShowcase',
    title() {
      return (
        <NFlex align="center" justify="center" wrap={false}>
          <span>Showcase GMV</span>
          <Tip description="The total amount of paid orders (including returns and refunds) attributed from creator's Showcase during the selected period." />
        </NFlex>
      );
    },
    align: 'center',
    width: 150
  },
  {
    key: 'avgOrderValue',
    title() {
      return (
        <NFlex align="center" justify="center" wrap={false}>
          <span>Avg. order value</span>
          <Tip description="The average amount paid by buyers for each order." />
        </NFlex>
      );
    },
    align: 'center',
    width: 150
  },
  {
    key: 'estCommission',
    title() {
      return (
        <NFlex align="center" justify="center" wrap={false}>
          <span>Est. commission</span>
          <Tip description="Total estimated commission paid to creators, including COD (paid and unpaid) and non-COD orders, and refunds." />
        </NFlex>
      );
    },
    align: 'center',
    width: 150
  },
  {
    key: 'affiliateLives',
    title() {
      return (
        <NFlex align="center" justify="center" wrap={false}>
          <span>Affiliate LIVEs</span>
          <Tip description="The total number of affiliate LIVEs that feature the seller's product." />
        </NFlex>
      );
    },
    align: 'center',
    width: 150
  },
  {
    key: 'affiliateShoppableVideos',
    title() {
      return (
        <NFlex align="center" justify="center" wrap={false}>
          <span>Affiliate shoppable videos</span>
          <Tip description="The total number of affiliate shoppable videos that feature the seller's product." />
        </NFlex>
      );
    },
    align: 'center',
    width: 200
  }
  // {
  //   key: 'ctr',
  //   title: 'CTR',
  //   align: 'right',
  //   minWidth: 70
  //   // width: 60
  // },
  // {
  //   key: 'buyers',
  //   title: 'Buyers',
  //   align: 'right',
  //   minWidth: 70
  //   // width: 60
  // }
  // {
  //   key: 'affiliateLives',
  //   title: 'Affiliate LIVEs',
  //   align: 'right'
  //   // width: 100
  // }
]);

function handleLinkCreator(id: any) {
  LinkToCreator(id);
}

type DollarKey = keyof Pick<
  Api.Dashboard.CreatorsTop,
  | 'affiliateGmv'
  | 'affiliateGmvLive'
  | 'affiliateGmvShoppableVideo'
  | 'affiliateGmvShowcase'
  | 'avgOrderValue'
  | 'estCommission'
>;

const dollarKeys: DollarKey[] = [
  'affiliateGmv',
  'affiliateGmvLive',
  'affiliateGmvShoppableVideo',
  'affiliateGmvShowcase',
  'avgOrderValue',
  'estCommission'
];
const renderCell = (value: any, _rowData: any, column: any) => {
  const isDollar = dollarKeys.includes(column.key);
  return numberFormat(value, isDollar ? NumeralFormat.Dollar : NumeralFormat.Number);
};

const tableRef = ref();

async function downloadCsv() {
  const { data, error } = await fetchDownoloadCreatorsTopData({ ...props.params, topNum: 10 });
  if (error) return;
  downloadFile(data, 'xlsx', 'Top 10 Best-Selling Creators');
}
watch(
  () => props,
  newProps => {
    initData(newProps.params);
  },
  {
    immediate: true
  }
);
</script>

<template>
  <NCard :bordered="false" title="Top 10 Best-Selling Creators">
    <template #header-extra>
      <ButtonIcon icon="solar:download-linear" tooltip-content="Export" tooltip-placement="top" @click="downloadCsv" />
    </template>
    <NDataTable
      ref="tableRef"
      :bordered="false"
      size="small"
      :render-cell="renderCell"
      :columns="columns"
      :data="tableData"
      scroll-x="1700"
    ></NDataTable>
  </NCard>
</template>

<style scoped></style>
