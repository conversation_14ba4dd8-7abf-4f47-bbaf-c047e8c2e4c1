<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-28 10:37:08
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-11-08 16:34:04
 * @FilePath: \tiksage-frontend\src\views\video-manage\list\index.vue
 * @Description: upload-list
-->
<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import dayjs from 'dayjs';
import { delay } from 'lodash-es';
import { fetchGetVideoApprovalList } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import VideoCard from '@/views/video-manage/upload-list/modules/video-card.vue';
import TimeVerticalBar from '@/views/video-manage/upload-list/modules/time-vertical-bar.vue';
import { TimeFormat } from '@/enum';
import Search from '@/views/video-manage/upload-list/modules/search.vue';
import VideoInfoDrawer from '@/views/video-manage/upload-list/modules/video-info-drawer.vue';

enum Status {
  PENDING,
  APPROVALED,
  REJECTED
}

const authStore = useAuthStore();
const [infoShow, toggleInfoShow] = useToggle(false);
const [loading, toggleLoading] = useToggle(false);

const showData = ref<Api.VideoManage.VideoApprovalListResponse[]>();
const videoId = ref<number>();

const searchParams = ref<Api.VideoManage.VideoApprovalListSearchParams>({
  status: Status.PENDING
});

const emptyContent = computed(() => {
  const description: any = {
    0: 'No videos awaiting review.',
    1: 'No videos have been marked as approved.',
    2: 'No videos have been marked as rejected.'
  };
  return description[searchParams.value.status];
});

async function initData() {
  toggleLoading(true);
  const { data, error } = await fetchGetVideoApprovalList({
    ...searchParams.value,
    client: Number(authStore.userInfo.id)
  });
  if (!error) {
    showData.value = data;
  }
  delay(() => {
    toggleLoading(false);
  }, 500);
}

function handleShowInfo(id: number) {
  videoId.value = id;
  toggleInfoShow(true);
}

function handleSearchChange(value: { creatorName?: string; product?: string }) {
  searchParams.value = { ...searchParams.value, ...value };
}

async function handleApproveSubmit(type?: 'next') {
  await initData();
  nextTick(() => {
    if (type === 'next') {
      if (showData.value && showData.value[0]?.listVideoApproval) {
        videoId.value = showData.value[0].listVideoApproval[0].id;
      } else {
        toggleInfoShow(false);
        window.$message?.info('All videos have been reviewed.');
      }
    }
  });
}

watch(
  () => searchParams.value,
  () => {
    initData();
  },
  {
    deep: true,
    immediate: true
  }
);
</script>

<template>
  <NFlex vertical :size="16">
    <NCard
      class="min-h-400px flex-1 card-wrapper"
      content-class="flex-col gap-16px"
      :bordered="false"
      title="Video List"
    >
      <template #header-extra>
        <Search @change="handleSearchChange" />
      </template>
      <NTabs v-model:value="searchParams.status">
        <NTab label="Pending Review" :name="Status.PENDING"></NTab>
        <NTab label="Approved" :name="Status.APPROVALED"></NTab>
        <NTab label="Rejected" :name="Status.REJECTED"></NTab>
      </NTabs>
      <NSpin class="h-full" content-class="h-full flex-col" :show="loading">
        <NEmpty v-if="!showData?.length" class="m-auto">{{ emptyContent }}</NEmpty>
        <template v-else>
          <TimeVerticalBar v-for="monthData in showData" :key="monthData.month">
            <template #title>
              <span>{{ dayjs(monthData.month).format(TimeFormat.US_DATE_NO_DAY) }}</span>
            </template>
            <NGrid :cols="6" :x-gap="16" :y-gap="16" item-responsive responsive="screen">
              <NGi v-for="item in monthData.listVideoApproval" :key="item.id">
                <VideoCard class="h-182px" :data="item" @view="handleShowInfo" />
              </NGi>
            </NGrid>
          </TimeVerticalBar>
        </template>
      </NSpin>
    </NCard>
    <VideoInfoDrawer :id="videoId" v-model:show="infoShow" role="approver" @submit="handleApproveSubmit" />
  </NFlex>
</template>

<style scoped></style>
