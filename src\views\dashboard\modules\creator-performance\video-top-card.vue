<script setup lang="ts">
import { ref, watch } from 'vue';
import dayjs from 'dayjs';
import { fetchGetVideoData } from '@/service/api';
import VideoInfoItem from './video-info-item.vue';

interface Props {
  searchParams: Api.CreatorPerformance.CreatorPerformanceSearchParams;
  lastDay?: string;
}

const props = defineProps<Props>();

const videoData = ref<Api.CreatorPerformance.VideoData[]>([]);

async function initData(params: Api.CreatorPerformance.CreatorPerformanceSearchParams) {
  const { data, error } = await fetchGetVideoData(params);
  if (!error) {
    videoData.value = data;
  }
}

function getTitle(startDateStr: string, endDateStr: string) {
  if (!startDateStr || !endDateStr) return '';

  const startDate = dayjs(startDateStr);
  let endDate = dayjs(endDateStr);

  // 如果结束日期大于今天，则使用今天作为结束日期
  const last = props.lastDay ? dayjs(props.lastDay) : dayjs().startOf('day');
  if (endDate.isAfter(last)) {
    endDate = last;
  }

  // 检查日期范围是否在同一周内
  if (startDate.week() === endDate.week() && startDate.year() === endDate.year()) {
    // 周格式：Week 19th 2025 5.4-5.10
    const weekNumber = startDate.week();
    const weekSuffix = getOrdinalSuffix(weekNumber);
    const year = startDate.year();
    const startDay = startDate.format('M.D');
    const endDay = endDate.format('M.D');

    return `Week ${weekNumber}${weekSuffix} ${year} ${startDay}-${endDay}`;
  }
  // 检查日期范围是否在同一月内
  else if (startDate.month() === endDate.month() && startDate.year() === endDate.year()) {
    // 月格式：Jan 2025 5.1-5.30
    const month = startDate.format('MMM');
    const year = startDate.year();
    const startDay = startDate.format('M.D');
    const endDay = endDate.format('M.D');

    return `${month} ${year} ${startDay}-${endDay}`;
  }
  // 其他情况，只显示日期范围
  const startFormatted = startDate.format('M.D');
  const endFormatted = endDate.format('M.D');
  return `${startFormatted}-${endFormatted}`;
}

// 辅助函数：获取序数后缀（1st, 2nd, 3rd, 4th等）
function getOrdinalSuffix(num: number): string {
  const j = num % 10;
  const k = num % 100;

  if (j === 1 && k !== 11) {
    return 'st';
  }
  if (j === 2 && k !== 12) {
    return 'nd';
  }
  if (j === 3 && k !== 13) {
    return 'rd';
  }
  return 'th';
}

function handleFormatItemList(data: (Api.CreatorPerformance.ShopsVideo | null)[]) {
  if (data.length < 5) {
    const diff = 5 - data.length;
    for (let i = 0; i < diff; i += 1) {
      data.push(null);
    }
  }
  return data;
}

watch(
  () => props.searchParams,
  newVal => {
    initData(newVal);
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" title="Top 5 Best-Performing Videos">
    <NGrid :cols="3" :x-gap="16" :y-gap="16">
      <NGi v-for="item in videoData" :key="item.startDate">
        <div class="flex-col-center border-1 border-gray-100 rounded-lg px-2 py-4 shadow-lg">
          <div class="mb-4">
            <span class="text-lg text-primary font-500">{{ getTitle(item.startDate, item.endDate) }}</span>
          </div>
          <div v-if="!item.data.length" class="h-430px w-full flex-center">
            <NEmpty>
              <template #icon>
                <icon-solar:videocamera-record-bold-duotone />
              </template>
              <template #default>No Video Available</template>
            </NEmpty>
          </div>
          <template v-else>
            <VideoInfoItem
              v-for="(video, idx) in handleFormatItemList(item.data)"
              :key="video?.videoId"
              class="w-full"
              :row-data="video"
              :shop-id="searchParams.shopIdsArr[0]"
              :index="idx"
            />
          </template>
        </div>
      </NGi>
    </NGrid>
  </NCard>
</template>

<style scoped></style>
