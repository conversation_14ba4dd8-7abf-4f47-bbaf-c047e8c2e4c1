/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'account',
    path: '/account',
    component: 'layout.base',
    meta: {
      title: 'account',
      i18nKey: 'route.account'
    },
    children: [
      {
        name: 'account_center',
        path: '/account/center',
        component: 'view.account_center',
        meta: {
          title: 'account_center',
          i18nKey: 'route.account_center'
        }
      }
    ]
  },
  {
    name: 'analysis',
    path: '/analysis',
    component: 'layout.base',
    meta: {
      title: 'analysis',
      i18nKey: 'route.analysis'
    },
    children: [
      {
        name: 'analysis_diagnosis',
        path: '/analysis/diagnosis',
        component: 'view.analysis_diagnosis',
        meta: {
          title: 'analysis_diagnosis',
          i18nKey: 'route.analysis_diagnosis'
        }
      },
      {
        name: 'analysis_list',
        path: '/analysis/list',
        component: 'view.analysis_list',
        meta: {
          title: 'analysis_list',
          i18nKey: 'route.analysis_list'
        }
      },
      {
        name: 'analysis_report',
        path: '/analysis/report/:id',
        component: 'view.analysis_report',
        meta: {
          title: 'analysis_report',
          i18nKey: 'route.analysis_report'
        }
      }
    ]
  },
  {
    name: 'category-analysis',
    path: '/category-analysis',
    component: 'layout.base',
    meta: {
      title: 'category-analysis',
      i18nKey: 'route.category-analysis'
    },
    children: [
      {
        name: 'category-analysis_category-intelligence',
        path: '/category-analysis/category-intelligence',
        component: 'view.category-analysis_category-intelligence',
        meta: {
          title: 'category-analysis_category-intelligence',
          i18nKey: 'route.category-analysis_category-intelligence'
        }
      },
      {
        name: 'category-analysis_category-leaders',
        path: '/category-analysis/category-leaders',
        component: 'view.category-analysis_category-leaders',
        meta: {
          title: 'category-analysis_category-leaders',
          i18nKey: 'route.category-analysis_category-leaders'
        }
      }
    ]
  },
  {
    name: 'creator-center',
    path: '/creator-center',
    component: 'layout.base',
    meta: {
      title: 'creator-center',
      i18nKey: 'route.creator-center'
    },
    children: [
      {
        name: 'creator-center_creator-approval',
        path: '/creator-center/creator-approval',
        component: 'view.creator-center_creator-approval',
        meta: {
          title: 'creator-center_creator-approval',
          i18nKey: 'route.creator-center_creator-approval'
        }
      },
      {
        name: 'creator-center_creator-approval-video-status',
        path: '/creator-center/creator-approval-video-status',
        component: 'view.creator-center_creator-approval-video-status',
        meta: {
          title: 'creator-center_creator-approval-video-status',
          i18nKey: 'route.creator-center_creator-approval-video-status'
        }
      },
      {
        name: 'creator-center_creator-database',
        path: '/creator-center/creator-database',
        component: 'view.creator-center_creator-database',
        meta: {
          title: 'creator-center_creator-database',
          i18nKey: 'route.creator-center_creator-database'
        }
      },
      {
        name: 'creator-center_creator-outreach',
        path: '/creator-center/creator-outreach',
        component: 'view.creator-center_creator-outreach',
        meta: {
          title: 'creator-center_creator-outreach',
          i18nKey: 'route.creator-center_creator-outreach'
        },
        children: [
          {
            name: 'creator-center_creator-outreach_create',
            path: '/creator-center/creator-outreach/create',
            meta: {
              title: 'creator-center_creator-outreach_create',
              i18nKey: 'route.creator-center_creator-outreach_create'
            },
            children: [
              {
                name: 'creator-center_creator-outreach_create_email',
                path: '/creator-center/creator-outreach/create/email',
                component: 'view.creator-center_creator-outreach_create_email',
                meta: {
                  title: 'creator-center_creator-outreach_create_email',
                  i18nKey: 'route.creator-center_creator-outreach_create_email'
                }
              },
              {
                name: 'creator-center_creator-outreach_create_target-invitation',
                path: '/creator-center/creator-outreach/create/target-invitation',
                component: 'view.creator-center_creator-outreach_create_target-invitation',
                meta: {
                  title: 'creator-center_creator-outreach_create_target-invitation',
                  i18nKey: 'route.creator-center_creator-outreach_create_target-invitation'
                }
              },
              {
                name: 'creator-center_creator-outreach_create_tiktok',
                path: '/creator-center/creator-outreach/create/tiktok',
                component: 'view.creator-center_creator-outreach_create_tiktok',
                meta: {
                  title: 'creator-center_creator-outreach_create_tiktok',
                  i18nKey: 'route.creator-center_creator-outreach_create_tiktok'
                }
              }
            ]
          },
          {
            name: 'creator-center_creator-outreach_find-creator',
            path: '/creator-center/creator-outreach/find-creator',
            component: 'view.creator-center_creator-outreach_find-creator',
            meta: {
              title: 'creator-center_creator-outreach_find-creator',
              i18nKey: 'route.creator-center_creator-outreach_find-creator'
            }
          },
          {
            name: 'creator-center_creator-outreach_history',
            path: '/creator-center/creator-outreach/history',
            meta: {
              title: 'creator-center_creator-outreach_history',
              i18nKey: 'route.creator-center_creator-outreach_history'
            },
            children: [
              {
                name: 'creator-center_creator-outreach_history_email',
                path: '/creator-center/creator-outreach/history/email',
                component: 'view.creator-center_creator-outreach_history_email',
                meta: {
                  title: 'creator-center_creator-outreach_history_email',
                  i18nKey: 'route.creator-center_creator-outreach_history_email'
                }
              },
              {
                name: 'creator-center_creator-outreach_history_target-invitation',
                path: '/creator-center/creator-outreach/history/target-invitation',
                component: 'view.creator-center_creator-outreach_history_target-invitation',
                meta: {
                  title: 'creator-center_creator-outreach_history_target-invitation',
                  i18nKey: 'route.creator-center_creator-outreach_history_target-invitation'
                }
              },
              {
                name: 'creator-center_creator-outreach_history_tiktok',
                path: '/creator-center/creator-outreach/history/tiktok',
                component: 'view.creator-center_creator-outreach_history_tiktok',
                meta: {
                  title: 'creator-center_creator-outreach_history_tiktok',
                  i18nKey: 'route.creator-center_creator-outreach_history_tiktok'
                }
              }
            ]
          },
          {
            name: 'creator-center_creator-outreach_settings',
            path: '/creator-center/creator-outreach/settings',
            meta: {
              title: 'creator-center_creator-outreach_settings',
              i18nKey: 'route.creator-center_creator-outreach_settings'
            },
            children: [
              {
                name: 'creator-center_creator-outreach_settings_email',
                path: '/creator-center/creator-outreach/settings/email',
                component: 'view.creator-center_creator-outreach_settings_email',
                meta: {
                  title: 'creator-center_creator-outreach_settings_email',
                  i18nKey: 'route.creator-center_creator-outreach_settings_email'
                }
              }
            ]
          }
        ]
      },
      {
        name: 'creator-center_creator-profiles',
        path: '/creator-center/creator-profiles',
        component: 'view.creator-center_creator-profiles',
        meta: {
          title: 'creator-center_creator-profiles',
          i18nKey: 'route.creator-center_creator-profiles'
        }
      }
    ]
  },
  {
    name: 'dashboard',
    path: '/dashboard',
    component: 'layout.base',
    meta: {
      title: 'dashboard',
      i18nKey: 'route.dashboard'
    },
    children: [
      {
        name: 'dashboard_base',
        path: '/dashboard/base',
        component: 'view.dashboard_base',
        meta: {
          title: 'dashboard_base',
          i18nKey: 'route.dashboard_base'
        }
      },
      {
        name: 'dashboard_brand-breakdown',
        path: '/dashboard/brand-breakdown',
        component: 'view.dashboard_brand-breakdown',
        meta: {
          title: 'dashboard_brand-breakdown',
          i18nKey: 'route.dashboard_brand-breakdown'
        }
      },
      {
        name: 'dashboard_sub-brand',
        path: '/dashboard/sub-brand',
        component: 'view.dashboard_sub-brand',
        meta: {
          title: 'dashboard_sub-brand',
          i18nKey: 'route.dashboard_sub-brand'
        }
      },
      {
        name: 'dashboard_weekly-report',
        path: '/dashboard/weekly-report',
        component: 'view.dashboard_weekly-report',
        meta: {
          title: 'dashboard_weekly-report',
          i18nKey: 'route.dashboard_weekly-report'
        },
        children: [
          {
            name: 'dashboard_weekly-report_generate',
            path: '/dashboard/weekly-report/generate',
            component: 'view.dashboard_weekly-report_generate',
            meta: {
              title: 'dashboard_weekly-report_generate',
              i18nKey: 'route.dashboard_weekly-report_generate'
            }
          },
          {
            name: 'dashboard_weekly-report_preview',
            path: '/dashboard/weekly-report/preview',
            component: 'view.dashboard_weekly-report_preview',
            meta: {
              title: 'dashboard_weekly-report_preview',
              i18nKey: 'route.dashboard_weekly-report_preview'
            }
          }
        ]
      }
    ]
  },
  {
    name: 'detail',
    path: '/detail',
    component: 'layout.base',
    meta: {
      title: 'detail',
      i18nKey: 'route.detail'
    },
    children: [
      {
        name: 'detail_creator',
        path: '/detail/creator/:id',
        component: 'view.detail_creator',
        meta: {
          title: 'detail_creator',
          i18nKey: 'route.detail_creator'
        }
      },
      {
        name: 'detail_video',
        path: '/detail/video/:id',
        component: 'view.detail_video',
        meta: {
          title: 'detail_video',
          i18nKey: 'route.detail_video'
        }
      }
    ]
  },
  {
    name: 'digital-audit',
    path: '/digital-audit',
    component: 'layout.base',
    meta: {
      title: 'digital-audit',
      i18nKey: 'route.digital-audit'
    },
    children: [
      {
        name: 'digital-audit_shop-audit',
        path: '/digital-audit/shop-audit',
        component: 'view.digital-audit_shop-audit',
        meta: {
          title: 'digital-audit_shop-audit',
          i18nKey: 'route.digital-audit_shop-audit'
        }
      },
      {
        name: 'digital-audit_shop-audit-history',
        path: '/digital-audit/shop-audit-history',
        component: 'view.digital-audit_shop-audit-history',
        meta: {
          title: 'digital-audit_shop-audit-history',
          i18nKey: 'route.digital-audit_shop-audit-history'
        }
      }
    ]
  },
  {
    name: 'google-auth',
    path: '/google-auth',
    component: 'layout.base$view.google-auth',
    meta: {
      title: 'google-auth',
      i18nKey: 'route.google-auth',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'manage',
    path: '/manage',
    component: 'layout.base',
    meta: {
      title: 'manage',
      i18nKey: 'route.manage'
    },
    children: [
      {
        name: 'manage_common',
        path: '/manage/common',
        component: 'view.manage_common',
        meta: {
          title: 'manage_common',
          i18nKey: 'route.manage_common'
        }
      },
      {
        name: 'manage_dictionary',
        path: '/manage/dictionary',
        component: 'view.manage_dictionary',
        meta: {
          title: 'manage_dictionary',
          i18nKey: 'route.manage_dictionary'
        }
      },
      {
        name: 'manage_log-analysis',
        path: '/manage/log-analysis',
        component: 'view.manage_log-analysis',
        meta: {
          title: 'manage_log-analysis',
          i18nKey: 'route.manage_log-analysis'
        }
      },
      {
        name: 'manage_menu',
        path: '/manage/menu',
        component: 'view.manage_menu',
        meta: {
          title: 'manage_menu',
          i18nKey: 'route.manage_menu'
        }
      },
      {
        name: 'manage_role',
        path: '/manage/role',
        component: 'view.manage_role',
        meta: {
          title: 'manage_role',
          i18nKey: 'route.manage_role'
        }
      },
      {
        name: 'manage_shop',
        path: '/manage/shop',
        component: 'view.manage_shop',
        meta: {
          title: 'manage_shop',
          i18nKey: 'route.manage_shop'
        }
      },
      {
        name: 'manage_user',
        path: '/manage/user',
        component: 'view.manage_user',
        meta: {
          title: 'manage_user',
          i18nKey: 'route.manage_user'
        }
      },
      {
        name: 'manage_user-detail',
        path: '/manage/user-detail/:id',
        component: 'view.manage_user-detail',
        meta: {
          title: 'manage_user-detail',
          i18nKey: 'route.manage_user-detail'
        }
      }
    ]
  },
  {
    name: 'operational-data',
    path: '/operational-data',
    component: 'layout.base',
    meta: {
      title: 'operational-data',
      i18nKey: 'route.operational-data'
    },
    children: [
      {
        name: 'operational-data_dashboard',
        path: '/operational-data/dashboard',
        component: 'view.operational-data_dashboard',
        meta: {
          title: 'operational-data_dashboard',
          i18nKey: 'route.operational-data_dashboard'
        }
      }
    ]
  },
  {
    name: 'return-refund',
    path: '/return-refund',
    component: 'layout.base$view.return-refund',
    meta: {
      title: 'return-refund',
      i18nKey: 'route.return-refund'
    }
  },
  {
    name: 'sales-analytics',
    path: '/sales-analytics/:id',
    component: 'layout.base$view.sales-analytics',
    props: true,
    meta: {
      title: 'sales-analytics',
      i18nKey: 'route.sales-analytics'
    }
  },
  {
    name: 'tiksage-dashboard',
    path: '/tiksage-dashboard',
    component: 'layout.base',
    meta: {
      title: 'tiksage-dashboard',
      i18nKey: 'route.tiksage-dashboard'
    },
    children: [
      {
        name: 'tiksage-dashboard_data-overview',
        path: '/tiksage-dashboard/data-overview',
        component: 'view.tiksage-dashboard_data-overview',
        meta: {
          title: 'tiksage-dashboard_data-overview',
          i18nKey: 'route.tiksage-dashboard_data-overview'
        }
      },
      {
        name: 'tiksage-dashboard_invoice-create',
        path: '/tiksage-dashboard/invoice-create/:id',
        component: 'view.tiksage-dashboard_invoice-create',
        meta: {
          title: 'tiksage-dashboard_invoice-create',
          i18nKey: 'route.tiksage-dashboard_invoice-create'
        }
      },
      {
        name: 'tiksage-dashboard_shop-commission',
        path: '/tiksage-dashboard/shop-commission',
        component: 'view.tiksage-dashboard_shop-commission',
        meta: {
          title: 'tiksage-dashboard_shop-commission',
          i18nKey: 'route.tiksage-dashboard_shop-commission'
        }
      }
    ]
  },
  {
    name: 'tools',
    path: '/tools',
    component: 'layout.base',
    meta: {
      title: 'tools',
      i18nKey: 'route.tools'
    },
    children: [
      {
        name: 'tools_creator-export',
        path: '/tools/creator-export',
        component: 'view.tools_creator-export',
        meta: {
          title: 'tools_creator-export',
          i18nKey: 'route.tools_creator-export'
        }
      },
      {
        name: 'tools_index',
        path: '/tools/index',
        component: 'view.tools_index',
        meta: {
          title: 'tools_index',
          i18nKey: 'route.tools_index'
        }
      },
      {
        name: 'tools_product-video-scraper',
        path: '/tools/product-video-scraper',
        component: 'view.tools_product-video-scraper',
        meta: {
          title: 'tools_product-video-scraper',
          i18nKey: 'route.tools_product-video-scraper'
        }
      },
      {
        name: 'tools_time-convert',
        path: '/tools/time-convert',
        component: 'view.tools_time-convert',
        meta: {
          title: 'tools_time-convert',
          i18nKey: 'route.tools_time-convert'
        }
      },
      {
        name: 'tools_video-download',
        path: '/tools/video-download',
        component: 'view.tools_video-download',
        meta: {
          title: 'tools_video-download',
          i18nKey: 'route.tools_video-download'
        }
      }
    ]
  },
  {
    name: 'video-manage',
    path: '/video-manage',
    component: 'layout.base',
    meta: {
      title: 'video-manage',
      i18nKey: 'route.video-manage'
    },
    children: [
      {
        name: 'video-manage_approval-list',
        path: '/video-manage/approval-list',
        component: 'view.video-manage_approval-list',
        meta: {
          title: 'video-manage_approval-list',
          i18nKey: 'route.video-manage_approval-list'
        }
      },
      {
        name: 'video-manage_product-list',
        path: '/video-manage/product-list',
        component: 'view.video-manage_product-list',
        meta: {
          title: 'video-manage_product-list',
          i18nKey: 'route.video-manage_product-list'
        }
      },
      {
        name: 'video-manage_upload-list',
        path: '/video-manage/upload-list',
        component: 'view.video-manage_upload-list',
        meta: {
          title: 'video-manage_upload-list',
          i18nKey: 'route.video-manage_upload-list'
        }
      }
    ]
  }
];
