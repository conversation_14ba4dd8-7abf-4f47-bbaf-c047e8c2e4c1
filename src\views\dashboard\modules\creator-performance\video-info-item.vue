<script setup lang="tsx">
import { computed, nextTick, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import dayjs from 'dayjs';
import { divide } from 'lodash-es';
import { useVChart } from '@/hooks/common/vchart';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { handleChartPoint } from '@/utils/chart-options';
import { getFallbackImage } from '@/utils/fake-image';
import { NumeralFormat, TimeFormat } from '@/enum';
import VideoHistoryModal from './video-history-modal.vue';

const { VITE_TODAY_VIDEO_AVATAR_URL } = import.meta.env;

interface Props {
  shopId: number;
  rowData: Api.CreatorPerformance.ShopsVideo | null;
  index: number;
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const coverImgUrl = computed(() => {
  return `${VITE_TODAY_VIDEO_AVATAR_URL}${props.rowData?.videoAvatarLocal}`;
});

// const avatarUrl = computed(() => {
//   return `${VITE_CREATOR_AVATAR_URL}${props.rowData?.avatarLocal}`;
// });

const videoUrl = computed(() => {
  return `https://www.tiktok.com/@${props.rowData?.creatorHandle}/video/${props.rowData?.videoId}`;
});

const creatorUrl = computed(() => {
  return `https://www.tiktok.com/@${props.rowData?.creatorHandle}`;
});

const publishTime = computed(() => {
  if (!props.rowData?.publishTime) return '-';
  return dayjs.unix(props.rowData?.publishTime).format(TimeFormat.US_DATE);
});

const gmvTotal = computed(() => {
  if (!props.rowData?.allGmv) return '-';
  return numberFormat(props.rowData?.allGmv, NumeralFormat.Dollar);
});

const adGmvRatio = computed(() => {
  if (!props.rowData) return '-%';

  const { adsGmv, allGmv } = props.rowData;
  if (adsGmv > allGmv) return '100%';
  return numberFormat(divide(adsGmv, allGmv), NumeralFormat.Percent);
});

const creatorHandle = computed(() => {
  return `@${props.rowData?.creatorHandle || '-'}`;
});

const defaultSpec: Visactor.VChart.IAreaChartSpec = {
  type: 'area',
  seriesField: 'type',
  padding: [2, 0, 2, 0],
  data: {
    values: []
  },
  xField: 'x',
  yField: 'y',
  color: ['#a855f7'],
  point: {
    visible: false
  },
  line: {
    style: {
      curveType: 'monotone'
    }
  },
  axes: [
    {
      orient: 'bottom',
      visible: false
    },
    {
      orient: 'left',
      visible: false
    }
  ],
  tooltip: {
    mark: {
      visible: false
    },
    dimension: {
      title: {
        visible: true,
        value: datum => {
          return dayjs(datum?.x).format(TimeFormat.US_DATE);
        }
      },
      content: {
        key: v => {
          return v ? (v?.type as string) : '';
        },
        value: datum => {
          return numberFormat(datum?.y, NumeralFormat.Dollar);
        }
      }
    }
  },
  area: {
    style: {
      fill: {
        gradient: 'linear',
        x0: 0.5,
        y0: 0,
        x1: 0.5,
        y1: 1,
        stops: [
          {
            offset: 0,
            opacity: 1
          },
          {
            offset: 1,
            opacity: 0
          }
        ]
      }
    }
  }
};

const { domRef: areaRef, updateSpec } = useVChart(() => defaultSpec);

const [modelShow, toggleModelShow] = useToggle(false);

function handleLinkToTiktok(url: string) {
  window.open(url, '_blank');
}

function showMore() {
  toggleModelShow(true);
}

watch(
  () => props.rowData,
  async newVal => {
    await nextTick();
    const values = newVal?.historyPerformance?.map(i => ({ x: i.belongDate, type: 'Total GMV', y: i.gmv })) || [];
    setTimeout(() => {
      updateSpec(oldOpts => {
        return {
          ...oldOpts,
          ...handleChartPoint(values.length === 1),
          data: { values }
        };
      });
    });
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div>
    <NGrid class="mb-4 w-full" :x-gap="2" item-responsive responsive="self">
      <NGi span="3">
        <div class="h-full flex-center">
          <span v-if="index === 0" class="text-primary">
            <SvgIcon style="width: 25px; height: 25px" icon="twemoji:1st-place-medal" />
          </span>
          <span v-else-if="index === 1" class="text-primary">
            <SvgIcon style="width: 25px; height: 25px" icon="twemoji:2nd-place-medal" />
          </span>
          <span v-else-if="index === 2" class="text-primary">
            <SvgIcon style="width: 25px; height: 25px" icon="twemoji:3rd-place-medal" />
          </span>
          <span v-else class="text-base text-primary">{{ index + 1 }}</span>
        </div>
      </NGi>
      <template v-if="rowData">
        <NGi span="16">
          <div class="flex gap-2">
            <div
              class="relative h-75px w-40px flex-shrink-0 hover:cursor-pointer"
              @click="handleLinkToTiktok(videoUrl)"
            >
              <NImage
                class="h-full w-full rounded"
                object-fit="cover"
                :src="coverImgUrl"
                :fallback-src="getFallbackImage(40, 75)"
              />
              <div class="absolute inset-0 flex-center rounded bg-black/20">
                <SvgIcon icon="solar:play-circle-bold" class="text-lg text-white" />
              </div>
              <div
                v-if="rowData.ad"
                class="absolute right-[-6px] top-[-6px] rounded bg-#D31C67 p-0.5 text-xs text-white"
              >
                Ad
              </div>
            </div>

            <div class="flex-col flex-nowrap">
              <NEllipsis :line-clamp="1" class="text-lg font-bold" :tooltip="{ contentStyle: 'max-width:400px' }">
                {{ rowData.videoName }}
              </NEllipsis>
              <div class="flex-y-center flex-nowrap gap-4 text-xs text-#6C6C6D font-bold">
                <span class="flex-shrink-0">GMV: {{ gmvTotal }}</span>
                <span>{{ adGmvRatio }} by Ads</span>
              </div>
              <NEllipsis
                class="cursor-pointer text-xs text-coolgray"
                :line-clamp="1"
                @click="handleLinkToTiktok(creatorUrl)"
              >
                {{ creatorHandle }}
              </NEllipsis>
              <span class="text-xs text-coolgray">Post time: {{ publishTime }}</span>
            </div>
          </div>
        </NGi>
        <NGi span="5" class="group m-auto h-30px w-full hover:cursor-pointer">
          <div ref="areaRef" class="h-30px w-full"></div>
          <NButton
            text
            class="w-full text-xs text-coolgray opacity-0 transition-opacity duration-300 group-hover:opacity-100"
            @click="showMore"
          >
            Details
          </NButton>
        </NGi>
      </template>
      <template v-else>
        <NGi span="21">
          <div class="h-70px w-full flex gap-2">
            <div class="relative h-70px w-40px flex-center flex-shrink-0 rounded bg-primary-100">
              <SvgIcon icon="fluent:video-off-16-filled" class="text-lg text-primary" />
            </div>
            <div class="flex-center text-base text-coolgray">No videos on the list yet</div>
          </div>
        </NGi>
      </template>
    </NGrid>
    <VideoHistoryModal
      v-model:show="modelShow"
      :video-id="rowData?.videoId"
      :shop-id="shopId"
      :show-ads="rowData?.ad"
    />
  </div>
</template>

<style scoped></style>
