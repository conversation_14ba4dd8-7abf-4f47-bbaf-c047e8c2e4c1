<script setup lang="ts">
interface Props {
  title: string;
  value: string;
  showPercent: boolean;
  percent?: string;
  vertical?: boolean;
  size?: 'small' | 'medium' | 'large';
}

withDefaults(defineProps<Props>(), {
  vertical: false,
  size: 'medium'
});
</script>

<template>
  <div class="h-full flex-col gap-8px rounded-xl bg-primary-100 p-8px" :class="{ 'flex-col': vertical }">
    <div class="text-xs text-coolgray">{{ title }}</div>
    <div class="nowrap-hidden" :class="{ 'flex-col gap-0': vertical, 'flex items-center gap-8px': !vertical }">
      <div class="text-xl font-bold">{{ value }}</div>
      <CycleRatio v-if="showPercent && percent" :size="size" :percent="percent" text />
    </div>
  </div>
</template>

<style scoped></style>
