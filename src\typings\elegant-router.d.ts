/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

declare module "@elegant-router/types" {
  type ElegantConstRoute = import('@elegant-router/vue').ElegantConstRoute;

  /**
   * route layout
   */
  export type RouteLayout = "base" | "blank";

  /**
   * route map
   */
  export type RouteMap = {
    "root": "/";
    "not-found": "/:pathMatch(.*)*";
    "403": "/403";
    "404": "/404";
    "500": "/500";
    "account": "/account";
    "account_center": "/account/center";
    "analysis": "/analysis";
    "analysis_diagnosis": "/analysis/diagnosis";
    "analysis_list": "/analysis/list";
    "analysis_report": "/analysis/report/:id";
    "category-analysis": "/category-analysis";
    "category-analysis_category-intelligence": "/category-analysis/category-intelligence";
    "category-analysis_category-leaders": "/category-analysis/category-leaders";
    "creator-center": "/creator-center";
    "creator-center_creator-approval": "/creator-center/creator-approval";
    "creator-center_creator-approval-video-status": "/creator-center/creator-approval-video-status";
    "creator-center_creator-database": "/creator-center/creator-database";
    "creator-center_creator-outreach": "/creator-center/creator-outreach";
    "creator-center_creator-outreach_create": "/creator-center/creator-outreach/create";
    "creator-center_creator-outreach_create_email": "/creator-center/creator-outreach/create/email";
    "creator-center_creator-outreach_create_target-invitation": "/creator-center/creator-outreach/create/target-invitation";
    "creator-center_creator-outreach_create_tiktok": "/creator-center/creator-outreach/create/tiktok";
    "creator-center_creator-outreach_find-creator": "/creator-center/creator-outreach/find-creator";
    "creator-center_creator-outreach_history": "/creator-center/creator-outreach/history";
    "creator-center_creator-outreach_history_email": "/creator-center/creator-outreach/history/email";
    "creator-center_creator-outreach_history_target-invitation": "/creator-center/creator-outreach/history/target-invitation";
    "creator-center_creator-outreach_history_tiktok": "/creator-center/creator-outreach/history/tiktok";
    "creator-center_creator-outreach_settings": "/creator-center/creator-outreach/settings";
    "creator-center_creator-outreach_settings_email": "/creator-center/creator-outreach/settings/email";
    "creator-center_creator-profiles": "/creator-center/creator-profiles";
    "dashboard": "/dashboard";
    "dashboard_base": "/dashboard/base";
    "dashboard_brand-breakdown": "/dashboard/brand-breakdown";
    "dashboard_sub-brand": "/dashboard/sub-brand";
    "dashboard_weekly-report": "/dashboard/weekly-report";
    "dashboard_weekly-report_generate": "/dashboard/weekly-report/generate";
    "dashboard_weekly-report_preview": "/dashboard/weekly-report/preview";
    "detail": "/detail";
    "detail_creator": "/detail/creator/:id";
    "detail_video": "/detail/video/:id";
    "digital-audit": "/digital-audit";
    "digital-audit_shop-audit": "/digital-audit/shop-audit";
    "digital-audit_shop-audit-history": "/digital-audit/shop-audit-history";
    "google-auth": "/google-auth";
    "iframe-page": "/iframe-page/:url";
    "login": "/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?";
    "manage": "/manage";
    "manage_common": "/manage/common";
    "manage_dictionary": "/manage/dictionary";
    "manage_log-analysis": "/manage/log-analysis";
    "manage_menu": "/manage/menu";
    "manage_role": "/manage/role";
    "manage_shop": "/manage/shop";
    "manage_user": "/manage/user";
    "manage_user-detail": "/manage/user-detail/:id";
    "operational-data": "/operational-data";
    "operational-data_dashboard": "/operational-data/dashboard";
    "return-refund": "/return-refund";
    "sales-analytics": "/sales-analytics/:id";
    "tiksage-dashboard": "/tiksage-dashboard";
    "tiksage-dashboard_data-overview": "/tiksage-dashboard/data-overview";
    "tiksage-dashboard_invoice-create": "/tiksage-dashboard/invoice-create/:id";
    "tiksage-dashboard_shop-commission": "/tiksage-dashboard/shop-commission";
    "tools": "/tools";
    "tools_creator-export": "/tools/creator-export";
    "tools_index": "/tools/index";
    "tools_product-video-scraper": "/tools/product-video-scraper";
    "tools_time-convert": "/tools/time-convert";
    "tools_video-download": "/tools/video-download";
    "video-manage": "/video-manage";
    "video-manage_approval-list": "/video-manage/approval-list";
    "video-manage_product-list": "/video-manage/product-list";
    "video-manage_upload-list": "/video-manage/upload-list";
  };

  /**
   * route key
   */
  export type RouteKey = keyof RouteMap;

  /**
   * route path
   */
  export type RoutePath = RouteMap[RouteKey];

  /**
   * custom route key
   */
  export type CustomRouteKey = Extract<
    RouteKey,
    | "root"
    | "not-found"
  >;

  /**
   * the generated route key
   */
  export type GeneratedRouteKey = Exclude<RouteKey, CustomRouteKey>;

  /**
   * the first level route key, which contain the layout of the route
   */
  export type FirstLevelRouteKey = Extract<
    RouteKey,
    | "403"
    | "404"
    | "500"
    | "account"
    | "analysis"
    | "category-analysis"
    | "creator-center"
    | "dashboard"
    | "detail"
    | "digital-audit"
    | "google-auth"
    | "iframe-page"
    | "login"
    | "manage"
    | "operational-data"
    | "return-refund"
    | "sales-analytics"
    | "tiksage-dashboard"
    | "tools"
    | "video-manage"
  >;

  /**
   * the custom first level route key
   */
  export type CustomFirstLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
  >;

  /**
   * the last level route key, which has the page file
   */
  export type LastLevelRouteKey = Extract<
    RouteKey,
    | "403"
    | "404"
    | "500"
    | "google-auth"
    | "iframe-page"
    | "login"
    | "account_center"
    | "analysis_diagnosis"
    | "analysis_list"
    | "analysis_report"
    | "category-analysis_category-intelligence"
    | "category-analysis_category-leaders"
    | "creator-center_creator-approval-video-status"
    | "creator-center_creator-approval"
    | "creator-center_creator-database"
    | "creator-center_creator-outreach"
    | "creator-center_creator-outreach_create_email"
    | "creator-center_creator-outreach_create_target-invitation"
    | "creator-center_creator-outreach_create_tiktok"
    | "creator-center_creator-outreach_find-creator"
    | "creator-center_creator-outreach_history_email"
    | "creator-center_creator-outreach_history_target-invitation"
    | "creator-center_creator-outreach_history_tiktok"
    | "creator-center_creator-outreach_settings_email"
    | "creator-center_creator-profiles"
    | "dashboard_base"
    | "dashboard_brand-breakdown"
    | "dashboard_sub-brand"
    | "dashboard_weekly-report_generate"
    | "dashboard_weekly-report_preview"
    | "detail_creator"
    | "detail_video"
    | "digital-audit_shop-audit-history"
    | "digital-audit_shop-audit"
    | "manage_common"
    | "manage_dictionary"
    | "manage_log-analysis"
    | "manage_menu"
    | "manage_role"
    | "manage_shop"
    | "manage_user-detail"
    | "manage_user"
    | "operational-data_dashboard"
    | "return-refund"
    | "sales-analytics"
    | "tiksage-dashboard_data-overview"
    | "tiksage-dashboard_invoice-create"
    | "tiksage-dashboard_shop-commission"
    | "tools_creator-export"
    | "tools_index"
    | "tools_product-video-scraper"
    | "tools_time-convert"
    | "tools_video-download"
    | "video-manage_approval-list"
    | "video-manage_product-list"
    | "video-manage_upload-list"
  >;

  /**
   * the custom last level route key
   */
  export type CustomLastLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
  >;

  /**
   * the single level route key
   */
  export type SingleLevelRouteKey = FirstLevelRouteKey & LastLevelRouteKey;

  /**
   * the custom single level route key
   */
  export type CustomSingleLevelRouteKey = CustomFirstLevelRouteKey & CustomLastLevelRouteKey;

  /**
   * the first level route key, but not the single level
  */
  export type FirstLevelRouteNotSingleKey = Exclude<FirstLevelRouteKey, SingleLevelRouteKey>;

  /**
   * the custom first level route key, but not the single level
   */
  export type CustomFirstLevelRouteNotSingleKey = Exclude<CustomFirstLevelRouteKey, CustomSingleLevelRouteKey>;

  /**
   * the center level route key
   */
  export type CenterLevelRouteKey = Exclude<GeneratedRouteKey, FirstLevelRouteKey | LastLevelRouteKey>;

  /**
   * the custom center level route key
   */
  export type CustomCenterLevelRouteKey = Exclude<CustomRouteKey, CustomFirstLevelRouteKey | CustomLastLevelRouteKey>;

  /**
   * the center level route key
   */
  type GetChildRouteKey<K extends RouteKey, T extends RouteKey = RouteKey> = T extends `${K}_${infer R}`
    ? R extends `${string}_${string}`
      ? never
      : T
    : never;

  /**
   * the single level route
   */
  type SingleLevelRoute<K extends SingleLevelRouteKey = SingleLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}$view.${K}`;
      }
    : never;

  /**
   * the last level route
   */
  type LastLevelRoute<K extends GeneratedRouteKey> = K extends LastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `view.${K}`;
      }
    : never;
  
  /**
   * the center level route
   */
  type CenterLevelRoute<K extends GeneratedRouteKey> = K extends CenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the multi level route
   */
  type MultiLevelRoute<K extends FirstLevelRouteNotSingleKey = FirstLevelRouteNotSingleKey> = K extends string
    ? ElegantConstRoute & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}`;
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;
  
  /**
   * the custom first level route
   */
  type CustomSingleLevelRoute<K extends CustomFirstLevelRouteKey = CustomFirstLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `layout.${RouteLayout}$view.${LastLevelRouteKey}`;
      }
    : never;

  /**
   * the custom last level route
   */
  type CustomLastLevelRoute<K extends CustomRouteKey> = K extends CustomLastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `view.${LastLevelRouteKey}`;
      }
    : never;

  /**
   * the custom center level route
   */
  type CustomCenterLevelRoute<K extends CustomRouteKey> = K extends CustomCenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the custom multi level route
   */
  type CustomMultiLevelRoute<K extends CustomFirstLevelRouteNotSingleKey = CustomFirstLevelRouteNotSingleKey> =
    K extends string
      ? ElegantConstRoute & {
          name: K;
          path: RouteMap[K];
          component: `layout.${RouteLayout}`;
          children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
        }
      : never;

  /**
   * the custom route
   */
  type CustomRoute = CustomSingleLevelRoute | CustomMultiLevelRoute;

  /**
   * the generated route
   */
  type GeneratedRoute = SingleLevelRoute | MultiLevelRoute;

  /**
   * the elegant route
   */
  type ElegantRoute = GeneratedRoute | CustomRoute;
}
