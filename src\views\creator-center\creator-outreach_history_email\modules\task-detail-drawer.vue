<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { NEllipsis, NTag } from 'naive-ui';
import dayjs from 'dayjs';
import { isNil } from 'lodash-es';
import { fetchGetEmailTaskDetail, fetchGetEmailTaskSenderList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { TimeFormat } from '@/enum';
import TextExpandable from './text-expandable.vue';

interface Props {
  taskId: number | null;
  taskStatusOptions: Api.Dictionary.DictionaryItem<number>[];
  taskSenderStatusOptions: Api.Dictionary.DictionaryItem<number>[];
}

const props = defineProps<Props>();

const visible = defineModel('visible', {
  type: Boolean,
  default: false
});

const taskDetail = ref<Api.CreatorNetwork.TaskDetail | null>(null);

const taskIndicators = computed(() => {
  return [
    {
      title: 'Total Recipients',
      value: taskDetail.value?.totalCount || 0,
      color: '#000'
    },
    {
      title: 'Sending',
      value: taskDetail.value?.sendingCount || 0,
      color: '#000'
    },
    {
      title: 'Total Sent',
      value: taskDetail.value?.sentCount || 0,
      color: '#000'
    },
    {
      title: 'Successful',
      value: taskDetail.value?.successCount || 0,
      color: '#52c41a'
    },
    {
      title: 'Failed',
      value: taskDetail.value?.failCount || 0,
      color: '#ff3d33'
    },
    {
      title: 'Open Rate',
      value: `${(taskDetail.value?.readRatio || 0) * 100}%`,
      color: '#a855f7'
    }
  ];
});

const statusObj = computed(() => {
  const attr = props.taskStatusOptions.find(item => item.code === taskDetail.value?.status);

  return {
    ...attr,
    description: JSON.parse(attr?.description)
  };
});

const createTime = computed(() => {
  return dayjs(taskDetail.value?.createTime).tz('Etc/GMT+8').format(TimeFormat.US_TIME_24) || '-';
});

const { data, pagination, loading, columns, searchParams, updateSearchParams, getData } = useTable({
  immediate: false,
  apiFn: fetchGetEmailTaskSenderList,
  apiParams: {
    current: 1,
    size: 10,
    taskId: props.taskId
  },
  columns() {
    return [
      {
        title: 'Email',
        key: 'email',
        width: 250,
        ellipsis: {
          tooltip: true
        }
      },
      {
        title: 'Creator',
        key: 'creatorId',
        align: 'center',
        width: 200,
        render(rowData) {
          return (
            <div class="max-w-full w-full flex items-center gap-16px">
              <div class="w-170px flex-col flex-1">
                <NEllipsis tooltip={{ contentStyle: 'max-width:400px' }} class="font-bold">
                  {rowData.creatorName || '-'}
                </NEllipsis>
                <NEllipsis tooltip={{ contentStyle: 'max-width:400px' }} class="text-coolgray">
                  @{rowData.creatorId || '-'}
                </NEllipsis>
              </div>
            </div>
          );
        }
      },
      {
        title: 'Status',
        key: 'status',
        align: 'center',
        width: 200,
        render(rowData: any) {
          const taskSenderStatus = props.taskSenderStatusOptions.find(item => item.code === rowData.status);
          return (
            <NTag type={taskSenderStatus?.description} bordered={false} size="small">
              {taskSenderStatus?.name}
            </NTag>
          );
        }
      },
      {
        title: 'Error Message',
        key: 'errorMessage',
        align: 'center',
        width: 200,
        ellipsis: {
          tooltip: {
            contentStyle: 'max-width:400px'
          }
        },
        render(rowData) {
          return rowData.errorMessage || '-';
        }
      }
    ];
  }
});

const [taskLoading, toggleTaskLoading] = useToggle(false);

async function getTaskDetail(id: number) {
  toggleTaskLoading(true);
  const { data: taskDetailData, error } = await fetchGetEmailTaskDetail(id);
  if (!error) {
    taskDetail.value = taskDetailData;
    updateSearchParams({
      taskId: id
    });
    getData();
    toggleTaskLoading(false);
  }
}

watch(
  () => searchParams.status,
  () => {
    updateSearchParams({
      current: 1,
      size: 10
    });
    getData();
  }
);

watch(
  () => visible.value,
  async newVal => {
    if (newVal) {
      if (!isNil(props.taskId)) {
        getTaskDetail(props.taskId);
      }
    }
  }
);
</script>

<template>
  <NDrawer v-model:show="visible" :width="1200">
    <NDrawerContent class="bg-#F7FAFC" body-content-class="flex-col gap-16px" closable>
      <NSpin v-if="taskLoading" class="m-auto"></NSpin>
      <template v-else>
        <NCard class="card-wrapper" :bordered="false" title="Task Detail"></NCard>
        <NGrid item-responsive responsive="screen" :x-gap="16" :y-gap="16">
          <NGi span="12">
            <NCard
              class="h-full card-wrapper"
              segmented
              content-class="flex-col justify-center"
              :bordered="false"
              title="Info"
            >
              <NDescriptions label-class="text-coolgray" content-class="" :columns="1" label-placement="left">
                <NDescriptionsItem label="Task Name">
                  <span>{{ taskDetail?.taskName || '-' }}</span>
                </NDescriptionsItem>
                <NDescriptionsItem label="Created Time">
                  <span>{{ createTime }}</span>
                </NDescriptionsItem>
                <NDescriptionsItem label="Task Status">
                  <NTag :type="statusObj?.description.buttonType" :bordered="false" size="small">
                    {{ statusObj?.name }}
                  </NTag>
                </NDescriptionsItem>
              </NDescriptions>
            </NCard>
          </NGi>
          <NGi span="12">
            <NGrid :cols="3" :x-gap="16" :y-gap="16">
              <NGi v-for="indicator in taskIndicators" :key="indicator.title">
                <div class="flex-col rounded-8px bg-white px-16px py-24px">
                  <span class="text-coolgray">{{ indicator.title }}</span>
                  <span class="text-xl font-bold" :style="{ color: indicator.color }">{{ indicator.value }}</span>
                </div>
              </NGi>
            </NGrid>
          </NGi>
        </NGrid>
        <NCard class="card-wrapper" segmented :bordered="false" title="Email Content">
          <div class="bg">
            <NDescriptions :columns="1" label-class="font-bold text-base" label-placement="left">
              <NDescriptionsItem label="Subject">
                <span>{{ taskDetail?.title || '-' }}</span>
              </NDescriptionsItem>
              <NDescriptionsItem label="Content">
                <div class="p-4">
                  <TextExpandable :content="taskDetail?.content || '-'" :max-lines="1" />
                </div>
              </NDescriptionsItem>
            </NDescriptions>
          </div>
        </NCard>
        <NCard
          class="min-h-400px flex-1 card-wrapper"
          content-class="flex-col gap-16px"
          :bordered="false"
          segmented
          title="Recipient List"
        >
          <div>
            <NGrid :cols="5" :x-gap="16" :y-gap="16" item-responsive>
              <NGi offset="4" span="1">
                <NSelect
                  v-model:value="searchParams.status"
                  placeholder="Open Status"
                  clearable
                  :options="taskSenderStatusOptions"
                  value-field="code"
                  label-field="name"
                />
              </NGi>
            </NGrid>
          </div>
          <NDataTable
            class="flex-1"
            remote
            :loading="loading"
            :bordered="false"
            :columns="columns"
            :data="data"
            :pagination="pagination"
          >
            <template #empty>
              <NEmpty description="Start by importing or selecting creators to populate the list." />
            </template>
          </NDataTable>
        </NCard>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
