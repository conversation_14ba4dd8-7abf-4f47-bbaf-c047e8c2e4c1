<script setup lang="ts">
import type { InputProps } from 'naive-ui';

interface Props {
  inputProps?: InputProps;
}

defineProps<Props>();

const countryCodeOptions = [{ label: 'US +1', value: 'US#1' }];

const country = defineModel<string>('country', {
  required: false,
  default: 'US#1'
});

const value = defineModel<string>('value', {
  required: false
});
</script>

<template>
  <NInput v-model:value="value" v-bind="inputProps">
    <template #prefix>
      <NSelect
        v-model:value="country"
        class="w-100px"
        :options="countryCodeOptions"
        :bordered="false"
        :consistent-menu-width="false"
      ></NSelect>
    </template>
  </NInput>
</template>

<style scoped></style>
