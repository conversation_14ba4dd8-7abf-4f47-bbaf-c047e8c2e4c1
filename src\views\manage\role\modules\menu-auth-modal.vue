<script setup lang="ts">
import { computed, shallowRef, watch } from 'vue';
import { flatMap, groupBy, map } from 'lodash-es';
import { fetchGetButtonTree, fetchGetRoleMenuById, fetchGetRoutes, fetchUpdateRoleMenu } from '@/service/api';
import { $t } from '@/locales';

defineOptions({
  name: 'MenuAuthModal'
});

interface Props {
  /** the roleId */
  roleId: number;
}

const props = defineProps<Props>();

const visible = defineModel<boolean>('visible', {
  default: false
});

function closeModal() {
  visible.value = false;
}

const title = computed(() => $t('common.edit') + $t('page.manage.role.menuAuth'));

const tree = shallowRef<Api.SystemManage.MenuTree[]>([]);

async function getMenuTree() {
  const { error, data } = await fetchGetRoutes();

  if (!error) {
    tree.value = data as any;
  }
}

const menuChecks = shallowRef<number[]>([]);
const buttonChecks = shallowRef<string[]>([]);

async function getChecks() {
  // request
  const { data, error } = await fetchGetRoleMenuById(props.roleId);
  if (error) return;
  if (data.menuButtonList) {
    menuChecks.value = data.menuButtonList.map(v => v.menuId);
    buttonChecks.value = flatMap(data.menuButtonList, 'buttonCodeList');
  }
}

const buttonTree = shallowRef<Api.SystemManage.ButtonTree[]>([]);

// only show buttonTree when menu is checked
const showButtonTree = computed(() => {
  return buttonTree.value.filter(v => menuChecks.value.includes(v.menuId));
});

async function getButtonTree() {
  // request
  const { data, error } = await fetchGetButtonTree();
  if (error) return;
  buttonTree.value = data;
}

// Recursively collect all menuIds
function getAllMenuIdList(treeArr: Api.SystemManage.MenuTree[] | null): number[] {
  if (!treeArr) return [];
  return treeArr.flatMap(m => [m.id, ...(m.children ? getAllMenuIdList(m.children) : [])]);
}

function getMenuButtonList() {
  const checksButtons = buttonTree.value.filter(v => buttonChecks.value.includes(v.buttonCode));
  const groupButtons = groupBy(checksButtons, 'menuId');

  // 剔除 tree 中不存在的 menuChecks
  const allMenuIds = getAllMenuIdList(tree.value);
  const filteredMenuChecks = menuChecks.value.filter(i => allMenuIds.includes(i));

  const menuButtons = map(filteredMenuChecks, menuId => {
    return {
      menuId,
      buttonCodeList: map(groupButtons?.[menuId], button => button.buttonCode) || []
    };
  });

  return menuButtons;
}

async function handleSubmit() {
  // request
  const params: Api.SystemManage.RoleMenuParams = {
    roleId: props.roleId,
    menuButtonList: getMenuButtonList() || []
  };

  const { error } = await fetchUpdateRoleMenu(params);
  if (error) return;

  window.$message?.success?.($t('common.modifySuccess'));

  closeModal();
}

function init() {
  getMenuTree();
  getButtonTree();
  getChecks();
}

watch(visible, (val: boolean) => {
  if (val) {
    init();
  }
});
</script>

<template>
  <NModal v-model:show="visible" :title="title" preset="card" class="w-480px">
    <NFlex :wrap="false">
      <NTree
        v-model:checked-keys="menuChecks"
        :data="tree"
        key-field="id"
        label-field="menuName"
        checkable
        expand-on-click
        virtual-scroll
        block-line
        :selectable="false"
        class="h-280px"
      />
      <NTree
        v-model:checked-keys="buttonChecks"
        :data="showButtonTree"
        key-field="buttonCode"
        label-field="buttonDesc"
        checkable
        expand-on-click
        virtual-scroll
        block-line
        :selectable="false"
        class="h-280px"
      />
    </NFlex>
    <template #footer>
      <NSpace justify="end">
        <NButton size="small" class="mt-16px" @click="closeModal">
          {{ $t('common.cancel') }}
        </NButton>
        <NButton type="primary" size="small" class="mt-16px" @click="handleSubmit">
          {{ $t('common.confirm') }}
        </NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<style scoped></style>
