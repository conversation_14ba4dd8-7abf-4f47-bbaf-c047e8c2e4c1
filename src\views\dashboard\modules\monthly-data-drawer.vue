<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { computedAsync, useToggle } from '@vueuse/core';
import { isNil } from 'lodash-es';
import { fetchExportMonthlyDataByExcel, fetchGetMonthlyData, fetchGetShopDataById } from '@/service/api';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { downloadFile } from '@/utils/download';
import eventBus from '@/utils/event-bus';
import { NumeralFormat } from '@/enum';

interface Props {
  shopId?: number;
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const title = computedAsync(async () => {
  let str = 'TikTok data update';
  if (props.shopId) {
    const { data: shopData, error: shopErr } = await fetchGetShopDataById(props.shopId);
    if (!shopErr) str = `${str} for ${shopData.shopName}`;
  }
  return str;
}, 'TikTok data update');

const show = ref<boolean>(false);

const [loading, toggleLoading] = useToggle(false);
const [exportLoading, toggleExportLoading] = useToggle(false);

const columns = ref<NaiveUI.DataTableColumns<Api.Dashboard.MonthlyData>>([
  {
    key: 'monthStr',
    align: 'center',
    title: 'Monthly',
    width: 100
  },
  {
    key: 'videoProductImpressions',
    align: 'center',
    title: 'Video',
    children: [
      {
        key: 'videoProductImpressions',
        align: 'center',
        title: 'Product Impressions',
        width: 160
      },
      {
        key: 'videoProductClicks',
        align: 'center',
        title: 'Product Clicks',
        width: 160
      },
      {
        key: 'videoViews',
        align: 'center',
        title: 'Views',
        width: 160
      },
      {
        key: 'videoComments',
        align: 'center',
        title: 'Comments',
        width: 160
      },
      {
        key: 'videoShares',
        align: 'center',
        title: 'Shares',
        width: 160
      },
      {
        key: 'videoLikes',
        align: 'center',
        title: 'Likes',
        width: 160
      }
    ]
  },
  {
    key: 'liveProductImpressions',
    align: 'center',
    title: 'Live',
    children: [
      {
        key: 'liveProductImpressions',
        align: 'center',
        title: 'Product Impressions',
        width: 160
      },
      {
        key: 'liveViewers',
        align: 'center',
        title: 'Viewers',
        width: 160
      },
      {
        key: 'liveProductClicks',
        align: 'center',
        title: 'Product Clicks',
        width: 160
      },
      {
        key: 'liveViews',
        align: 'center',
        title: 'Views',
        width: 160
      },
      {
        key: 'liveComments',
        align: 'center',
        title: 'Comments',
        width: 160
      },
      {
        key: 'liveShares',
        align: 'center',
        title: 'Shares',
        width: 160
      },
      {
        key: 'liveLikes',
        align: 'center',
        title: 'Likes',
        width: 160
      }
    ]
  },
  {
    key: 'productCardViews',
    align: 'center',
    title: 'Product Card',
    children: [
      {
        key: 'productCardViews',
        align: 'center',
        title: 'Views',
        width: 160
      },
      {
        key: 'productCardClicks',
        align: 'center',
        title: 'Clicks',
        width: 160
      },
      {
        key: 'productCardViewers',
        align: 'center',
        title: 'Viewers',
        width: 160
      }
    ]
  }
]);

const tableData = ref<Api.Dashboard.MonthlyData[]>([]);

const calculateTotalWidth = (cols: any[]): number => {
  return cols.reduce((total, col) => {
    if (col.children && col.children.length) {
      return total + calculateTotalWidth(col.children);
    }
    return total + (Number(col.width) || 0);
  }, 0);
};

const scrollX = computed(() => {
  return calculateTotalWidth(columns.value);
});

function renderCell(value: any) {
  if (typeof value === 'number') {
    return numberFormat(value, NumeralFormat.Real_Number);
  }
  return value;
}

async function handleExport() {
  if (isNil(props.shopId)) return;
  toggleExportLoading(true);
  const { data: exportData, error: exportErr } = await fetchExportMonthlyDataByExcel(props.shopId);
  if (exportErr) return;
  downloadFile(exportData, 'xlsx', title.value);
  toggleExportLoading(false);
}

async function initData() {
  if (isNil(props.shopId)) return;
  toggleLoading(true);
  const { data: monthlyData, error } = await fetchGetMonthlyData(props.shopId);
  if (!error) {
    tableData.value = monthlyData;
  }
  toggleLoading(false);
}

onMounted(() => {
  eventBus.on('monthly-data-drawer', ({ type, data }) => {
    if (type === 'show') {
      show.value = data;
    }
  });
});

onUnmounted(() => {
  eventBus.off('monthly-data-drawer');
});

watch(
  () => show.value,
  newVal => {
    newVal && initData();
  }
);
</script>

<template>
  <NDrawer v-model:show="show" width="1200">
    <NDrawerContent :title="title" closable>
      <div class="mb-4 flex justify-end">
        <NButton quaternary :loading="exportLoading" @click="handleExport">
          <template #icon>
            <icon-solar:download-linear />
          </template>
          Export Excel
        </NButton>
      </div>

      <NDataTable
        :scroll-x="scrollX"
        :loading="loading"
        :bordered="false"
        :single-line="false"
        :columns="columns"
        :data="tableData"
        :render-cell="renderCell"
      ></NDataTable>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
