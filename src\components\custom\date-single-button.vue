<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-29 16:15:27
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-09-05 11:00:31
 * @FilePath: \tiksage-frontend\src\components\custom\date-single-button.vue
 * @Description: date-single-button
-->
<script setup lang="tsx">
import { reactive, ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { NDatePicker } from 'naive-ui';
import dayjs from 'dayjs';
import type { IsDateDisabled } from 'naive-ui/es/date-picker/src/interface';
import { TimeFormat } from '@/enum';

type Model = string;

interface Props {
  type:
    | 'date'
    | 'datetime'
    | 'daterange'
    | 'datetimerange'
    | 'month'
    | 'monthrange'
    | 'year'
    | 'yearrange'
    | 'quarter'
    | 'quarterrange'
    | 'week';
  isDateDisabled: IsDateDisabled;
}

const props = defineProps<Props>();

const model = defineModel<Model>('value', { required: true });

const [dropdownShow, toggleDropdownShow] = useToggle(false);

const dateLabel = ref(dayjs(model.value).format(TimeFormat.US_DATE_NO_DAY));

const onSelectDate = (value: number, formattedValue: string) => {
  model.value = formattedValue;
  dateLabel.value = `${dayjs(value).format(TimeFormat.US_DATE_NO_DAY)}`;
  toggleDropdownShow();
};

const renderDateComponent = () => {
  return (
    <NDatePicker
      panel
      type={props.type}
      valueFormat="yyyy-MM"
      actions={['confirm']}
      defaultFormattedValue={model.value}
      isDateDisabled={props.isDateDisabled}
      onConfirm={onSelectDate}
    ></NDatePicker>
  );
};

const cycleOptions = reactive([
  {
    key: 'header',
    type: 'render',
    render: renderDateComponent
  }
]);
</script>

<template>
  <NDropdown :show="dropdownShow" :options="cycleOptions" @clickoutside="toggleDropdownShow()">
    <NButton secondary @click="toggleDropdownShow()">
      <template #icon>
        <SvgIcon icon="ic:baseline-calendar-month"></SvgIcon>
      </template>
      {{ dateLabel }}
    </NButton>
  </NDropdown>
</template>

<style scoped></style>
