<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import dayjs from 'dayjs';
import { fetchGetMonthlyDataByCreatorPerformance } from '@/service/api';
import { useVChart } from '@/hooks/common/vchart';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat, TimeFormat } from '@/enum';

interface Props {
  searchParams: Api.CreatorPerformance.CreatorPerformanceSearchParams;
}

const props = defineProps<Props>();

const year = computed(() => {
  return dayjs().year();
});

const options = [
  {
    label: 'Creator Confirmed',
    value: 'creatorOutreached'
  },
  {
    label: 'Free Sample',
    value: 'freeSample'
  },
  {
    label: 'Video Posted',
    value: 'videoPost'
  }
];

const { numberFormat } = useNumberFormat();

const chartData = ref<Api.CreatorPerformance.CreatorPerformanceMonthlyResponse>([]);

const defaultSpec = ref<Visactor.VChart.IBarChartSpec>({
  type: 'bar',
  data: [],
  xField: ['x', 'type'],
  seriesField: 'type',
  yField: 'y',
  bar: {
    style: {
      cornerRadius: 5
    }
  },
  stack: false,
  tooltip: {
    mark: {
      visible: false
    },
    dimension: {
      title: {
        visible: true,
        value: datum => {
          return dayjs(datum?.x).format(TimeFormat.US_DATE_NO_DAY);
        }
      },
      content: {
        key: v => {
          return v ? (v?.type as string) : '';
        },
        value: datum => {
          return numberFormat(datum?.y, NumeralFormat.Real_Number);
        }
      }
    }
  },
  axes: [
    {
      orient: 'bottom',
      label: {
        formatMethod(text: any) {
          return dayjs(text).format('MMM');
        }
      }
    }
  ],
  barMinWidth: 20,
  barMaxWidth: 50,
  legends: [{ visible: true, position: 'middle', orient: 'top' }]
});

const { domRef: barRef, updateSpec } = useVChart(() => defaultSpec.value as any);

async function initData(shopIds: number[], brandList?: string[] | null) {
  const { data, error } = await fetchGetMonthlyDataByCreatorPerformance(shopIds, brandList);
  if (!error) {
    chartData.value = data;
    handleUpdateData();
  }
}

function handleUpdateData() {
  let values: any[] = [];
  options.forEach(o => {
    values = values.concat(
      chartData.value.map(d => {
        return {
          x: d.yearMonth,
          type: o.label,
          y: d[o.value as keyof Api.CreatorPerformance.CreatorPerformanceMonthly]
        };
      })
    );
  });
  updateSpec(opts => {
    return { ...opts, data: [{ values }] };
  });
}

watch(
  () => props.searchParams.shopIdsArr,
  newVal => {
    initData(newVal, props.searchParams.brandList);
  },
  {
    immediate: true
  }
);
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" :title="`Monthly Performance (${year})`">
    <div ref="barRef" class="h-400px"></div>
  </NCard>
</template>

<style scoped></style>
