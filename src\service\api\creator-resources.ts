/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-20 16:07:03
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-23 17:44:57
 * @FilePath: \tiksage-frontend\src\service\api\creator-resources.ts
 * @Description: creator resources API
 */
import { request } from '../request';

// get creator list
export function fetchGetCreatorsList(params: Api.CreatorResources.CreatorSearchParams) {
  return request<Api.CreatorResources.CreatorList>({
    url: '/creator/pageListCreators',
    method: 'post',
    data: params
  });
}

// get category tree
export function fetchGetCategoryTree(data = { sellId: 5 }) {
  return request<Api.CreatorResources.CategoryTree[]>({
    timeout: 1000 * 30,
    url: '/tiktokShop/product/getGlobalCategoriesTree',
    method: 'post',
    data
  });
}

export function fetchFuzzySearchCreator(keyword: string, limitNum: number = 5) {
  return request<Api.CreatorResources.FuzzySearchCreatorResponse>({
    url: '/creator/findCreators',
    method: 'post',
    data: { keyword, limitNum }
  });
}

export function fetchLastSyncTime() {
  return request<{ lastSyncDate: string }>({
    url: '/creator/queryLastSyncDate',
    method: 'get'
  });
}
