<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-24 15:01:32
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-10-31 13:11:08
 * @FilePath: \tiksage-frontend\src\views\home\modules\shop-list.vue
 * @Description: shop list
-->
<script setup lang="tsx">
import { ref } from 'vue';
import { NAvatar, NEllipsis, NFlex, NImage, NText } from 'naive-ui';
import type { DataTableCreateSummary } from 'naive-ui';
import { add } from 'lodash-es';
import { fetchDownoloadShopsData, fetchGetDashboardShopList } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { useRouterPush } from '@/hooks/common/router';
import { downloadFile } from '@/utils/download';
import { LinkToProduct } from '@/utils/tiktok-link';
import { getFallbackImage } from '@/utils/fake-image';
import { NumeralFormat } from '@/enum';

interface Props {
  params: Pick<Api.Dashboard.DashboardSearchParams, 'shopIdsArr' | 'startDateStr' | 'endDateStr'>;
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const { routerPushByKey } = useRouterPush();
const onShopSet = () => {
  routerPushByKey('account_center');
};

const { columns, data, loading, mobilePagination } = useTable({
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    shopIdsArr: props.params.shopIdsArr,
    startDateStr: props.params.startDateStr || '',
    endDateStr: props.params.endDateStr || ''
  },
  apiFn: fetchGetDashboardShopList,
  columns: () => [
    {
      key: 'shopId',
      title: 'Shop',
      fixed: 'left',
      width: 250,
      render: rowData => (
        <NFlex align="center">
          <NAvatar
            src={`${import.meta.env.VITE_SHOP_AVATAR_URL}${rowData.avatar}`}
            fallbackSrc={getFallbackImage(50, 50)}
          ></NAvatar>
          <NFlex vertical>
            <NEllipsis style={'max-width:180px'}>{rowData.shopName}</NEllipsis>
            <NEllipsis style={'max-width:180px'}>@{rowData.shopCode || '-'}</NEllipsis>
          </NFlex>
        </NFlex>
      )
    },
    {
      key: 'activeProductNum',
      title: 'Top selling products',
      align: 'center',
      fixed: 'left',
      width: 200,
      render: rowData => {
        // let topProducts = rowData.topSellingProductImgList.length ? rowData.topSellingProductImgList:
        return (
          <NFlex class="w-100%" justify="center">
            {rowData.topSellingProductImgList.length
              ? rowData.topSellingProductImgList.map(tsp => {
                  return (
                    <div
                      class="flex flex-nowrap items-center gap-2 hover:(cursor-pointer text-primary)"
                      onClick={() => handleLinkProduct(tsp.productId)}
                    >
                      <NImage
                        previewDisabled
                        width={50}
                        height={50}
                        src={`${import.meta.env.VITE_PRODUCT_AVATAR_URL}${tsp.productAvatarLocal}`}
                        fallbackSrc={getFallbackImage(50, 50)}
                      ></NImage>
                    </div>
                  );
                })
              : '-'}
          </NFlex>
        );
      }
    },
    {
      key: 'gmv',
      title: 'GMV',
      align: 'center',
      width: 110
      // sorter: 'default',
      // defaultSortOrder:'descend'
      // sortOrder: sortKeyMapOrderRef.value.gmv || false
    },
    {
      key: 'visitors',
      title: 'Visitors',
      align: 'center',
      width: 110
      // sorter: 'default',
      // sortOrder: sortKeyMapOrderRef.value.visitors || false
    },
    {
      key: 'buyers',
      title: 'Buyers',
      align: 'center',
      width: 110
      // sorter: 'default',
      // sortOrder: sortKeyMapOrderRef.value.buyers || false
    },
    {
      key: 'videos',
      title: 'Videos',
      align: 'center',
      width: 110
      // sorter: 'default',
      // sortOrder: sortKeyMapOrderRef.value.videos || false
    },
    {
      key: 'lives',
      title: 'Live streamings',
      align: 'center',
      width: 110
      // sorter: 'default',
      // sortOrder: sortKeyMapOrderRef.value.lives || false
    }
  ]
});

function handleLinkProduct(id: any) {
  LinkToProduct(id);
}

function getValue(rowData: Api.Dashboard.DashboardShop[], key: keyof Api.Dashboard.DashboardShop, isDollar: boolean) {
  const val = rowData.reduce((p, c) => {
    return add(p, c[key] as number);
  }, 0);
  return numberFormat(val, isDollar ? NumeralFormat.Dollar : NumeralFormat.Number);
}

const summary: DataTableCreateSummary<Api.Dashboard.DashboardShop> = rowData => {
  return {
    shopId: {
      value: <span class="font-bold">Total {rowData.length} Shops</span>
    },
    gmv: {
      value: <span class="font-bold">{getValue(rowData, 'gmv', true)}</span>
    },
    visitors: {
      value: <span class="font-bold">{getValue(rowData, 'visitors', false)}</span>
    },
    buyers: {
      value: <span class="font-bold">{getValue(rowData, 'buyers', false)}</span>
    },
    videos: {
      value: <span class="font-bold">{getValue(rowData, 'videos', false)}</span>
    },
    lives: {
      value: <span class="font-bold">{getValue(rowData, 'lives', false)}</span>
    }
  };
};

type DollarKey = keyof Pick<Api.Dashboard.DashboardShop, 'gmv'>;

const dollarKeys: DollarKey[] = ['gmv'];

const renderCell = (value: any, _rowData: any, column: any) => {
  const isDollar = dollarKeys.includes(column.key);
  return numberFormat(value, isDollar ? NumeralFormat.Dollar : NumeralFormat.Number);
};

const tableRef = ref();

async function downloadCsv() {
  const { data: downloadData, error } = await fetchDownoloadShopsData(props.params);
  if (error) return;
  downloadFile(downloadData, 'xlsx', 'Shop Performance');
}
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <template #header>
      <NText>Shop Performance</NText>
    </template>
    <template #header-extra>
      <NButtonGroup>
        <ButtonIcon icon="solar:settings-linear" tooltip-content="Set" tooltip-placement="top" @click="onShopSet" />
        <ButtonIcon
          icon="solar:download-linear"
          tooltip-content="Export"
          tooltip-placement="top"
          @click="downloadCsv"
        />
      </NButtonGroup>
    </template>
    <NDataTable
      ref="tableRef"
      :bordered="false"
      size="small"
      :loading="loading"
      remote
      :render-cell="renderCell"
      :columns="columns"
      :data="data"
      :row-key="row => row.id"
      :summary="summary"
      :pagination="mobilePagination"
      scroll-x="1110"
    />
  </NCard>
</template>

<style scoped></style>
