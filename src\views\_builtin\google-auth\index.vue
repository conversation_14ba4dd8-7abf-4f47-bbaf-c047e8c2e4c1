<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { delay } from 'lodash-es';
import { fetchGoogleOauth2Callback } from '@/service/api';
import { useRouterPush } from '@/hooks/common/router';

const { query } = useRoute();

const { routerPushByKey } = useRouterPush();

const code = ref<string | null>(query.code as string | null);
const scope = ref<string | null>(query.scope as string | null);
watch(
  () => code.value,
  async () => {
    if (code.value) {
      const { origin } = window.location;
      const { error } = await fetchGoogleOauth2Callback(origin, code.value, scope.value || '');
      // for test
      // const { error } = await fetchGoogleOauth2Callback('http://127.0.0.1:9527', code.value, scope.value || '');
      if (!error) {
        delay(() => {
          routerPush<PERSON>y<PERSON>ey('creator-center_creator-outreach_settings_email');
        }, 1000);
        window.$message?.success(`Authorization successful`);
      }
    }
  },
  { immediate: true }
);
</script>

<template>
  <div class="flex-center">
    <NSpin :show="true" description="Loading..."></NSpin>
  </div>
</template>

<style scoped></style>
