<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-11-04 16:30:04
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jar<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-12 17:32:37
 * @FilePath: \tiksage-frontend\src\views\return-refund\modules\Reasons-proportion-card.vue
 * @Description: Reasons-proportion-card
-->
<script setup lang="ts">
import { computed } from 'vue';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';

interface Props {
  data: { type: string; value: number }[];
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const chartOptions = computed<Visactor.VChart.IPieChartSpec>(() => {
  if (!props.data.length) {
    return {
      type: 'pie',
      data: [
        {
          id: 'numberY',
          values: props.data || []
        }
      ],
      categoryField: 'type',
      valueField: 'value'
    };
  }

  const total = props.data.reduce((acc, cur) => acc + cur.value, 0);

  const res: Visactor.VChart.IPieChartSpec = {
    type: 'pie',
    data: [
      {
        id: 'numberY',
        values: props.data || []
      }
    ],
    categoryField: 'type',
    valueField: 'value',
    label: {
      visible: false
    },
    legends: {
      visible: true,
      type: 'discrete',
      orient: 'right',
      pager: {
        space: 10
      },
      data: legendData => {
        return legendData.map(d => {
          let value: any = props.data.find((v: any) => v.type === d.label)?.value;
          value = `${numberFormat(value, NumeralFormat.Number)}(${numberFormat(value / total, NumeralFormat.Percent)})`;
          return {
            ...d,
            value
          };
        });
      },
      item: {
        width: '50%',
        spaceRow: 8,
        autoEllipsisStrategy: 'valueFirst',
        shape: {
          style: {
            symbolType: 'circle'
          }
        },
        label: {
          style: {
            fontSize: 14,
            fill: '#9ca3af'
          }
        },
        value: {
          alignRight: true,
          style: {
            textAlign: 'right',
            fontSize: 14,
            fill: '#000',
            fontWeight: 'bold',
            ellipsis: false
          }
        }
      }
    }
  };

  return res;
});

function initData() {}

initData();
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" title="Returns/Refunds Reasons Proportion">
    <VDoughnutChart style="height: 300px" :chart-options="chartOptions" />
  </NCard>
</template>

<style scoped></style>
