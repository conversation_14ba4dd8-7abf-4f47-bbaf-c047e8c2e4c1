<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-09-09 14:46:36
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-08 16:55:57
 * @FilePath: \tiksage-frontend\src\views\dashboard\jazwares\modules\sub-brand-product-card.vue
 * @Description: sub-brand-product-card
-->
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { delay, sortBy } from 'lodash-es';

interface Props {
  data?: Api.DashboardJazwares.SubBrandMetricResponse;
}

const props = defineProps<Props>();

type impressionType = Extract<
  keyof Api.DashboardJazwares.SubBrandMetric,
  'shopTabListingImpressions' | 'liveImpressions' | 'videoImpressions' | 'productCardImpressions'
>;

const impressionOptions: CommonType.Option<impressionType>[] = [
  {
    label: 'Shop tab listing impressions',
    value: 'shopTabListingImpressions',
    description: `Each time a link to your listing appears in the shop tab, it's counted as one impression. Users can tap links to visit your listings page directly.`
  },
  {
    label: 'LIVE impressions',
    value: 'liveImpressions',
    description:
      'Each time a product link appears in a LIVE, it counts as 1 impression. Viewers can click these links to visit your product listings.'
  },
  {
    label: 'Video impressions',
    value: 'videoImpressions',
    description:
      'Each time a product link appears in the shoppable video, it counts as 1 impression. Viewers can tap these links to visit your product listings.'
  },
  {
    label: 'Product card impressions',
    value: 'productCardImpressions',
    description: 'The number of impressions the product card received during the selected period.'
  }
];

const [loading, toggleLoading] = useToggle(false);

const impressionValue = ref<impressionType>('shopTabListingImpressions');

const chartData = ref<Visactor.VChart.IPieChartSpec>();

function initData() {
  const result: Visactor.VChart.IPieChartSpec = {
    type: 'pie',
    data: {
      values: [] as any[]
    },
    categoryField: 'type',
    valueField: 'value'
  };

  if (props.data && props.data.length) {
    const sortData = sortBy(props.data, [impressionValue.value]);
    for (const ele of sortData) {
      const value = {
        type: ele.subBrand,
        value: ele[impressionValue.value],
        unit: '$'
      };
      result.data && (result.data as any).values.push(value);
    }
  }

  chartData.value = result;
}

watch(
  () => impressionValue.value,
  () => {
    toggleLoading(true);
    initData();
    delay(() => {
      toggleLoading(false);
    }, 500);
  },
  {
    immediate: true
  }
);
</script>

<template>
  <NCard class="h-full card-wrapper" content-class="min-h-400px" :bordered="false" title="Brand Product Impressions">
    <template #header-extra>
      <SelectDesc
        v-model:value="impressionValue"
        class="w-100px"
        :options="impressionOptions"
        :consistent-menu-width="false"
      ></SelectDesc>
    </template>
    <NSpin v-if="loading"></NSpin>
    <VDoughnutChart v-else :chart-options="chartData" />
  </NCard>
</template>

<style scoped></style>
