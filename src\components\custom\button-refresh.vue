<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-12-06 10:35:45
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-12-06 11:07:23
 * @FilePath: \tiksage-frontend\src\components\custom\button-refresh.vue
 * @Description: button-refresh
-->
<script setup lang="ts">
import { useToggle } from '@vueuse/core';
import { delay } from 'lodash-es';
import ButtonIcon from './button-icon.vue';

interface Props {
  callback: () => void;
}

const props = defineProps<Props>();
const [loading, toggleLoading] = useToggle(false);

async function handleCopy() {
  if (loading.value) return;

  toggleLoading(true);
  try {
    await props.callback();
  } catch (error) {
    console.log(error);
    delay(() => {
      toggleLoading(false);
    }, 500);
  }
  delay(() => {
    toggleLoading(false);
  }, 500);
}
</script>

<template>
  <ButtonIcon
    class="h-auto"
    :rotate="loading"
    icon="ant-design:sync-outlined"
    :quaternary="false"
    text
    tooltip-content="Refresh"
    tooltip-placement="top"
    @click.stop="handleCopy"
  ></ButtonIcon>
</template>

<style scoped></style>
