<script setup lang="ts">
import { reactive } from 'vue';
import { useToggle } from '@vueuse/core';
import { useLoadingBar } from 'naive-ui';
import { delay } from 'lodash-es';
import { COLORS } from '@/constants/colors';
import { fetchVideoDownload } from '@/service/api';
import { downloadFile } from '@/utils/download';
import step1 from '@/assets/imgs/step1.png';
import step2 from '@/assets/imgs/step2.png';
import { PlatformType } from '@/enum';

const selectOptions = [
  { label: 'TikTok', value: PlatformType.TIKTOK },
  { label: 'DouYin', value: PlatformType.DOUYIN }
];

const descriptions = [
  {
    key: 'Step 1',
    title: 'Find Video',
    detail: 'Open the TikTok video you want to download, click the "Share" button and then click "Copy Link".',
    img: step1,
    color: COLORS[0],
    class: `shadow-[${COLORS[0]}]`
  },
  {
    key: 'Step 2',
    title: 'Download Video',
    detail: `Paste the link in the text field provided on the page. The system will instantly recognize whether it's from the "TikTok" platform or "DouYin" (the Chinese version of TikTok). Simply click the "Download" button and wait for your watermark-free video.`,
    img: step2,
    color: COLORS[1],
    class: `shadow-[${COLORS[1]}]`
  }
  // {
  //   title: 'Step3',
  //   detail: 'Click the "Download" button.',
  //   img:'/src/assets/imgs/step1.png',
  //   color: COLORS[2],
  //   class: `shadow-[${COLORS[2]}]`
  // }
];

const loadingBar = useLoadingBar();

const model = reactive({
  platform: PlatformType.TIKTOK,
  url: ''
});

function extractAndDeterminePlatform(text: string): PlatformType {
  // Extract the regular expression of link from the text
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  const urls = text.match(urlRegex);

  // Define the regular expression of TIKTOK and Douyin
  const tiktokRegex = /https?:\/\/(www\.)?tiktok\.com\/@\w+\/video\/\d+/;
  const douyinRegex = /https?:\/\/v\.douyin\.com\/\w+/;

  // If you find the link, judge which platform it is
  if (urls) {
    for (const url of urls) {
      if (tiktokRegex.test(url)) {
        return PlatformType.TIKTOK;
      } else if (douyinRegex.test(url)) {
        return PlatformType.DOUYIN;
      }
    }
  }

  return PlatformType.TIKTOK; // If not found a link, return default
}

const handleUpdate = (value: string) => {
  model.platform = extractAndDeterminePlatform(value);
  model.url = value;
};

const [loading, toggleLoading] = useToggle(false);
const handleDownload = async () => {
  toggleLoading(true);
  loadingBar.start();
  // fetch api
  const { data, error } = await fetchVideoDownload(model);
  if (!error) {
    downloadFile(data, 'mp4', 'Video');
  }
  delay(() => {
    toggleLoading(false);
    loadingBar.finish();
  }, 500);
};
</script>

<template>
  <NFlex class="h-100% min-h-500px xl:hidden" vertical :size="16">
    <NCard
      class="flex-1 card-wrapper"
      content-class="flex-col-center"
      :bordered="false"
      title="Watermark-Free Short Video Download"
    >
      <template #header-extra>
        <ButtonBack />
      </template>
      <NFlex vertical justify="space-around" align="center" class="h-full w-full flex-1 gap-16px">
        <NFlex align="center" class="min-h-100px w-100% flex-basis-30% xl:w-60%">
          <NInputGroup>
            <NSelect
              v-model:value="model.platform"
              size="large"
              :consistent-menu-width="false"
              style="width: 120px"
              :options="selectOptions"
            ></NSelect>
            <NInput
              :value="model.url"
              size="large"
              placeholder="Paste link here"
              clearable
              @update:value="handleUpdate"
              @keydown.enter="handleDownload"
            ></NInput>
            <NButton size="large" type="primary" :loading="loading" @click="handleDownload">Download</NButton>
          </NInputGroup>
        </NFlex>
        <NTabs
          class="flex-basis-70%"
          default-value="Step 1"
          justify-content="space-evenly"
          type="line"
          animated
          trigger="click"
        >
          <NTabPane
            v-for="desc in descriptions"
            :key="desc.key"
            class="flex-wra flex items-center justify-center"
            :name="desc.key"
            :tab="desc.key"
          >
            <img class="hidden xl:block" width="800" height="600" :src="desc.img" />
            <div class="w-300px">
              <NH1>{{ desc.title }}</NH1>
              <span>{{ desc.detail }}</span>
            </div>
          </NTabPane>
        </NTabs>
      </NFlex>
    </NCard>
  </NFlex>
</template>

<style scoped></style>
