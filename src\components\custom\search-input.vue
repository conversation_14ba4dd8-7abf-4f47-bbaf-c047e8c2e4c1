<script setup lang="ts">
import type { ExtractPropTypes } from 'vue';
import { NInput } from 'naive-ui';

type InputType = ExtractPropTypes<typeof NInput>;
type Props = /* @vue-ignore */ InputType;
defineProps<Props>();
</script>

<template>
  <NInput style="max-width: 200px" clearable v-bind="$attrs">
    <template #prefix>
      <icon-tabler:search class="text-gray" />
    </template>
  </NInput>
</template>

<style scoped></style>
