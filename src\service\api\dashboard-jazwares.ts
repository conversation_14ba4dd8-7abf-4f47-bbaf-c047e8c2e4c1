/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-09-09 15:35:31
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-09-11 15:47:46
 * @FilePath: \tiksage-frontend\src\service\api\dashboard-jazwares.ts
 * @Description: dashboard-jazwares api
 */
import { request } from '../request';

export function fetchShopImpressionsOptions() {
  return request<string[]>({
    url: '/shop/impressions',
    method: 'get'
  });
}

export function fetchGetSubBrandShopOptions(data: Api.DashboardJazwares.BaseSearchParams) {
  return request<string[]>({
    url: '/shop/listBrands',
    method: 'post',
    data
  });
}

export function fetchGetSubBrandShopMetricData(data: Api.DashboardJazwares.BaseSearchParams) {
  return request<Api.DashboardJazwares.SubBrandMetricResponse>({
    url: '/shop/productImpressionsBySubBrand',
    method: 'post',
    data
  });
}

export function fetchGetProductTopListByBrand(data: Api.DashboardJazwares.DashboardTopSearchParams) {
  return request<Api.Dashboard.ProductTopResponse>({
    url: '/shop/listProductsTopDataByBrand',
    method: 'post',
    data
  });
}

export function fetchGetShopList(data: Api.DashboardJazwares.BaseSearchParams) {
  return request<Api.DashboardJazwares.DashboardShop[]>({
    url: '/shop/listSubBrandShopsData',
    method: 'post',
    data
  });
}
