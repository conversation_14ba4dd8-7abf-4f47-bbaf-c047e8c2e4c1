<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-14 11:17:36
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-12-05 10:53:46
 * @FilePath: \tiksage-frontend\src\components\custom\date-button.vue
 * @Description: date-button
-->
<script setup lang="ts">
import { ref, watch } from 'vue';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { isNil } from 'lodash-es';
import { dateFormat } from '@/utils/date';
import { TimeFormat } from '@/enum';

defineOptions({
  name: 'ButtonDate',
  inheritAttrs: false
});

interface Props {
  defaultValue: number;
  startTime?: string;
  endTime?: string;
  showUpdate?: boolean;
  showQuickButton?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  defaultValue: -1,
  // startTime: '0',
  // endTime: dayjs().toISOString(),
  showUpdate: false,
  showQuickButton: true
});

interface Emits {
  (e: 'update:value', value: Model): void;
  (e: 'update:unix', value: [number, number] | null): void;
  (e: 'update:timestamp', value: [number, number] | null): void;
}

const emit = defineEmits<Emits>();

type Model = [string, string] | null;

const model = ref(getDateRange(props.endTime, props.defaultValue));

const radios = [
  {
    title: 'Current month',
    value: -1,
    focus: false
  },
  {
    title: 'Last 7 days',
    value: 6,
    focus: true
  },
  {
    title: 'Last 28 days',
    value: 27,
    focus: false
  }
];

const activeRadio = ref(props.defaultValue);

function getDateRange(endTime: string | undefined, days: number): Model {
  if (isNil(endTime)) return null;
  const last: Dayjs | null = dayjs(endTime);
  let start;

  if (days === -1) {
    // current Month
    start = last.startOf('month');
  } else if (days === -2) {
    start = last.subtract(2, 'month').startOf('month');
  } else {
    start = last.subtract(days, 'day');
  }
  return [isNil(start) ? '' : start.format(TimeFormat.CN_DATE), isNil(last) ? '' : last.format(TimeFormat.CN_DATE)];
}

function handleQuickSelect(value: number) {
  activeRadio.value = value;
  model.value = getDateRange(props.endTime, value);
}

function dateDisabled(ts: number) {
  let end;
  let start;

  if (props.startTime) {
    start = dayjs(props.startTime).valueOf();
  } else {
    start = dayjs(0).valueOf();
  }

  if (props.endTime) {
    end = dayjs(props.endTime).valueOf();
  } else {
    end = dayjs().valueOf();
  }

  return ts < start || ts > end;
}

function handleChangeFormattedValue(value: [string, string]) {
  model.value = value;
  activeRadio.value = 0;
}

watch(
  model,
  newVal => {
    emit('update:value', newVal);

    if (newVal) {
      const start = dayjs.tz(newVal[0], 'Etc/GMT+8');
      const end = dayjs.tz(newVal[1], 'Etc/GMT+8').endOf('day');

      emit('update:timestamp', [start.valueOf(), end.valueOf()]);
      emit('update:unix', [start.unix(), end.unix()]);
    } else {
      emit('update:timestamp', null);
      emit('update:unix', null);
    }
  },
  {
    deep: true,
    immediate: true
  }
);
</script>

<template>
  <div>
    <NButtonGroup v-if="showQuickButton">
      <NButton
        v-for="radio in radios"
        :key="radio.title"
        strong
        secondary
        :type="activeRadio === radio.value ? 'primary' : 'default'"
        @click="handleQuickSelect(radio.value)"
      >
        {{ radio.title }}
      </NButton>
      <NDatePicker
        clearable
        :formatted-value="model"
        value-format="yyyy-MM-dd"
        class="cus-border w-280px"
        type="daterange"
        format="MMM dd, yyyy"
        input-readonly
        :is-date-disabled="dateDisabled"
        :actions="null"
        close-on-select
        v-bind="$attrs"
        @update-formatted-value="handleChangeFormattedValue"
      >
        <template #footer>
          <NText v-if="showUpdate" type="error">Last Updated: {{ dateFormat(props.endTime) }}</NText>
        </template>
      </NDatePicker>
    </NButtonGroup>
    <NDatePicker
      v-else
      clearable
      :formatted-value="model"
      value-format="yyyy-MM-dd"
      class="w-280px"
      type="daterange"
      format="MMM dd, yyyy"
      :is-date-disabled="dateDisabled"
      :actions="null"
      close-on-select
      v-bind="$attrs"
      @update-formatted-value="handleChangeFormattedValue"
    >
      <template #footer>
        <NText v-if="showUpdate" type="error">Last Updated: {{ dateFormat(props.endTime) }}</NText>
      </template>
    </NDatePicker>
  </div>
</template>

<style scoped>
:deep(.cus-border .n-input) {
  border-radius: 0 6px 6px 0 !important;
}
</style>
