<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-12-09 09:40:30
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-12-10 13:27:58
 * @FilePath: \tiksage-frontend\src\views\shop-audit\create\modules\top-shop-card.vue
 * @Description: top-shop-card
-->
<script setup lang="ts">
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { getFallbackImage } from '@/utils/fake-image';
import { NumeralFormat } from '@/enum';

const { VITE_SHOP_AUDIT_AVATAR_URL } = import.meta.env;

interface Props {
  index: number;
  data: Api.ShopAudit.SaShopTotalPO;
}
defineProps<Props>();

const { numberFormat } = useNumberFormat();
</script>

<template>
  <div
    class="relative w-64 overflow-hidden border rounded-3xl bg-white px-9 py-7 shadow-[0px_0px_15px_rgba(0,0,0,0.09)]"
  >
    <div class="absolute h-24 w-24 rounded-full bg-primary-400 -right-5 -top-7">
      <p class="absolute bottom-6 left-7 text-2xl text-white">0{{ index + 1 }}</p>
    </div>
    <div class="mt-4xl flex-col gap-16px">
      <div class="h-60px w-60px flex-center border rounded-full fill-primary-500">
        <NImage
          class="rounded-full"
          :src="`${VITE_SHOP_AUDIT_AVATAR_URL}${data?.avatarLocal}`"
          :fallback-src="getFallbackImage(60, 60)"
        />
      </div>
      <span class="text-xl font-600">{{ data?.shopName }}</span>
      <NDescriptions
        class="mt-16px"
        :columns="1"
        label-placement="left"
        label-class="text-gray mr-8px"
        label-style="vertical-align:middle"
      >
        <NDescriptionsItem label="Category">
          <NTag :bordered="false" type="primary">{{ data?.category }}</NTag>
        </NDescriptionsItem>
        <NDescriptionsItem label="Total GMV">
          <span class="font-bold">{{ numberFormat(data?.gmv, NumeralFormat.Dollar) }}</span>
        </NDescriptionsItem>
      </NDescriptions>
    </div>
  </div>
</template>

<style scoped></style>
