<script setup lang="ts">
import { watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { assign } from 'lodash-es';
import { fetchGetSellersContribution } from '@/service/api';
import { useVChart } from '@/hooks/common/vchart';
import { initPieIndicator, initPieLegend } from '@/utils/chart-options';
import { pieDefaultSpec } from './chart';

interface Props {
  dateRange: string[] | undefined;
}
const props = defineProps<Props>();

const [showInvoicePie, toggleShowInvoicePie] = useToggle(true);

function cusSpec(spec: Visactor.VChart.IPieChartSpec): Visactor.VChart.IPieChartSpec {
  const cus: Visactor.VChart.IPieChartSpec = {
    type: 'pie',
    categoryField: 'type',
    valueField: 'value',
    label: {
      visible: false
    },
    legends: {
      visible: true,
      orient: 'right'
    }
  };
  return assign({}, spec, cus);
}

const { domRef: gmvRef, updateSpec: updateGmvSpec } = useVChart(() => cusSpec(pieDefaultSpec));
const { domRef: invoiceConRef, updateSpec: updateInvoiceSpec } = useVChart(() => cusSpec(pieDefaultSpec));

function formatGmvPieData(data: Api.TikSageDashboard.ShopGmvPie[]) {
  if (!data) return [];
  const gmvData = data.map(item => {
    return {
      value: item.gmv,
      type: item.shopName
    };
  });
  return gmvData;
}

function formatInvoicePieData(data: Api.TikSageDashboard.ShopCommissionPie[]) {
  if (!data.length) return [];
  const invoiceData = data.map(item => {
    return {
      value: item.commission,
      type: item.shopName
    };
  });
  return invoiceData;
}

async function initPieData(params: Api.TikSageDashboard.DateParams) {
  const { data, error } = await fetchGetSellersContribution(params);
  if (!error) {
    const { shopGmvPie, shopCommissionPie } = data;
    if (shopGmvPie) {
      const gmvData = formatGmvPieData(shopGmvPie);
      const total = gmvData.reduce((pre, cur) => {
        return pre + cur.value;
      }, 0);
      updateGmvSpec(opts => {
        const res = assign({}, opts, {
          data: [
            {
              id: 'gmv',
              values: gmvData as any
            }
          ],
          indicator: initPieIndicator(total),
          legends: initPieLegend(gmvData, total)
        });
        return res;
      });
    }

    if (shopCommissionPie) {
      toggleShowInvoicePie(true);
      const invoiceData = formatInvoicePieData(shopCommissionPie);
      const total = invoiceData.reduce((pre, cur) => {
        return pre + cur.value;
      }, 0);
      updateInvoiceSpec(opts => {
        return assign({}, opts, {
          data: [
            {
              id: 'invoice',
              values: invoiceData as any
            }
          ],
          indicator: initPieIndicator(total),
          legends: initPieLegend(invoiceData, total)
        });
      });
    } else {
      toggleShowInvoicePie(false);
    }
  }
}

watch(
  () => props.dateRange,
  newVal => {
    if (!newVal) return;
    const [startDateStr, endDateStr] = newVal;
    initPieData({
      startDateStr,
      endDateStr
    });
  },
  {
    immediate: true
  }
);
</script>

<template>
  <NGrid :cols="2" :x-gap="16" :y-gap="16">
    <NGi>
      <NCard class="card-wrapper" :bordered="false" title="GMV Contribution">
        <div ref="gmvRef" class="h-300px"></div>
      </NCard>
    </NGi>
    <NGi>
      <NCard
        class="card-wrapper"
        content-class="flex-center min-h-320px"
        :bordered="false"
        title="Current Est. Commission Contribution"
      >
        <NEmpty
          v-if="!showInvoicePie"
          class="m-auto"
          description="Available for monthly or yearly period only. "
        ></NEmpty>
        <div v-else ref="invoiceConRef" class="h-300px w-full"></div>
      </NCard>
    </NGi>
  </NGrid>
</template>

<style scoped></style>
