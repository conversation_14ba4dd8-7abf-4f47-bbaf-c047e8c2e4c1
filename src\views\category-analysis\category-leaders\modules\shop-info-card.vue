<script setup lang="ts">
import { computed } from 'vue';
import { getFallbackImage } from '@/utils/fake-image';

const { VITE_SHOP_LEADER_AVATAR_URL } = import.meta.env;

interface Props {
  data: Api.CategoryLeaders.TopGmvShop | Api.CategoryLeaders.TopGmvShopListResponse;
  idx: number;
  moreInfo?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  moreInfo: false
});

const avatarUrl = computed(() => {
  return `${VITE_SHOP_LEADER_AVATAR_URL}${props.data.shopAvatarLocal}`;
});

const shopIndicators = computed(() => {
  return [
    {
      title: 'Total GMV',
      prefix: '$',
      value: props.data.shopOverviewData.totalGmv,
      description: '',
      average: true,
      decimals: 2,
      suffix: ''
    },
    {
      title: 'Total Units sold',
      prefix: '',
      value: props.data.shopOverviewData.totalSold,
      description: '',
      average: true,
      decimals: 2,
      suffix: ''
    }
  ];
});

const moreIndicators = computed(() => {
  return [
    {
      title: 'Positive rating rate',
      prefix: '',
      value: props.data.shopOverviewData.positiveRatingRate,
      description: '',
      average: true,
      decimals: 2,
      suffix: ''
    },
    {
      title: 'Quality Score',
      prefix: '',
      value: props.data.shopOverviewData.qualityScore,
      description: '',
      average: true,
      decimals: 2,
      suffix: ''
    },
    {
      title: 'Responds within 24h',
      prefix: '',
      value: props.data.shopOverviewData.respondsWithin24h,
      description: '',
      average: true,
      decimals: 2,
      suffix: ''
    },
    {
      title: 'Ships within 48h',
      prefix: '',
      value: props.data.shopOverviewData.shipsWithin48h,
      description: '',
      average: true,
      decimals: 2,
      suffix: ''
    }
  ];
});
</script>

<template>
  <NCard :bordered="false" content-class="relative" size="small">
    <div class="absolute right-[10px] top-[10px]">
      <icon-solar:crown-line-bold-duotone
        v-if="idx <= 3"
        class="text-3xl"
        :class="{
          'text-#FFD700': idx === 1,
          'text-#C0C0C0': idx === 2,
          'text-#B87333': idx === 3
        }"
      />
      <div v-else class="flex items-center pt-6px">
        <icon-tabler:number class="text-2xl text-primary" />
        <span class="text-base text-primary font-bold">{{ idx }}</span>
      </div>
    </div>
    <NThing>
      <template #avatar>
        <div class="h-60px w-60px overflow-hidden border-1px rounded-full">
          <NImage preview-disabled :src="avatarUrl" :fallback-src="getFallbackImage(60, 60)" />
        </div>
      </template>
      <template #header>
        <div class="flex items-center gap-16px pr-25px">
          <NEllipsis
            style="max-width: 100%"
            class="text-xl font-bold"
            :tooltip="{ contentStyle: 'max-width:400px' }"
            :line-clamp="1"
          >
            {{ data.shopName || '-' }}
          </NEllipsis>
          <template v-if="moreInfo">
            <icon-twemoji:flag-united-states v-if="moreInfo" class="text-xl" />
            <ButtonIcon
              v-if="data.shopOverviewData.sshop"
              text
              :quaternary="false"
              type="primary"
              icon="tabler:circle-letter-s"
              tooltip-content="Products Fulfilled by TikTok Shops"
              tooltip-placement="top"
            />
          </template>
        </div>
      </template>
      <template #description>
        <div>
          <NTag type="primary" :bordered="false">{{ data.category }}</NTag>
          <div class="mt-4px flex items-center justify-between gap-16px">
            <div class="flex items-center gap-16px">
              <div class="h-36px flex items-center gap-8px">
                <NRate readonly allow-half :value="Number(data.shopOverviewData.rate) || 0" />
                <NTag type="warning" :bordered="false" round>{{ data.shopOverviewData.rate || 0 }} / 5</NTag>
                <ButtonIcon
                  v-if="!moreInfo && data.shopOverviewData.sshop"
                  text
                  :quaternary="false"
                  type="primary"
                  icon="tabler:circle-letter-s"
                  tooltip-content="Products Fulfilled by TikTok Shops"
                  tooltip-placement="top"
                />
              </div>
              <NTooltip v-if="moreInfo" trigger="hover" placement="top">
                <template #trigger>
                  <NTag type="primary" :bordered="false">
                    <template #icon>
                      <icon-tabler:tags class="text-icon text-primary" />
                    </template>
                    {{ data.shopOverviewData.totalProducts || 0 }}
                  </NTag>
                </template>
                SPUs on sale
              </NTooltip>
            </div>
          </div>
        </div>
      </template>
      <template #footer>
        <NGrid class="divide-x" y-gap="16" :cols="moreInfo ? 4 : 2">
          <NGi v-for="indicator in moreInfo ? moreIndicators : shopIndicators" :key="indicator.title">
            <NFlex vertical justify="center" align="center" :size="0">
              <span class="font-bold">{{ indicator.value || '-' }}</span>
              <NFlex>
                <NText class="text-gray">{{ indicator.title }}</NText>
                <Tip v-if="indicator.description !== ''" :description="indicator.description" />
              </NFlex>
            </NFlex>
          </NGi>
        </NGrid>
      </template>
    </NThing>
  </NCard>
</template>

<style scoped></style>
