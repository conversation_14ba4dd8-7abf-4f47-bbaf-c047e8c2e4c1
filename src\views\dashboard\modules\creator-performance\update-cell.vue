<script setup lang="ts">
import { nextTick, ref } from 'vue';
import { useToggle } from '@vueuse/core';

interface Props {
  value?: string | null;
  onlyNumber?: boolean;
  inputProps?: NaiveUI.InputProps;
  inputNumberProps?: NaiveUI.InputNumberProps;
  updateFn: (value?: string | null) => void;
}

const props = withDefaults(defineProps<Props>(), {
  value: null,
  inputProps: () => ({}),
  inputNumberProps: () => ({}),
  onlyNumber: false
});

const inputRef = ref<HTMLElement>();
const editValue = ref<any>();
const [isEdit, toggleIsEdit] = useToggle(false);
const [loading, toggleLoading] = useToggle(false);

function handleToggleUpdate() {
  toggleIsEdit(true);
  editValue.value = props.value;
  nextTick(() => {
    inputRef.value?.focus();
  });
}

async function handleUpdateValue() {
  toggleLoading(true);
  try {
    await props.updateFn(editValue.value);
    toggleIsEdit(false);
  } catch {
  } finally {
    toggleLoading(false);
  }
}

function handleCancelUpdateValue() {
  toggleIsEdit(false);
  editValue.value = props.value;
}
</script>

<template>
  <div class="flex-center gap-2">
    <template v-if="!isEdit">
      <NEllipsis :line-clamp="1">{{ value }}</NEllipsis>
      <NButton type="default" quaternary @click="handleToggleUpdate">
        <template #icon>
          <icon-solar:pen-2-linear />
        </template>
      </NButton>
    </template>
    <template v-else>
      <NInputNumber
        v-if="onlyNumber"
        ref="inputRef"
        v-model:value="editValue"
        placeholder=""
        autofocus
        :loading="loading"
        :min="1"
        :precision="0"
        v-bind="inputNumberProps"
        @blur="handleCancelUpdateValue"
        @keydown.enter="handleUpdateValue"
      />
      <NInput
        v-else
        ref="inputRef"
        v-model:value="editValue"
        placeholder=""
        autofocus
        :loading="loading"
        v-bind="inputProps"
        @blur="handleCancelUpdateValue"
        @keydown.enter="handleUpdateValue"
      ></NInput>
    </template>
  </div>
</template>

<style scoped></style>
