<script setup lang="ts"></script>

<template>
  <NFlex vertical :wrap="false">
    <NFlex align="center" class="relative">
      <svg class="absolute left--5px text-gray" width="14" height="14">
        <circle cx="7" cy="7" r="5" stroke="#9ca3af" stroke-width="2" fill="none" />
      </svg>
      <div class="m-l-16px text-xl">
        <slot name="title"></slot>
      </div>
    </NFlex>
    <div class="w-full border-l-3px border-l-gray-200 p-b-16px p-l-16px p-t-16px">
      <slot></slot>
    </div>
  </NFlex>
</template>

<style scoped></style>
