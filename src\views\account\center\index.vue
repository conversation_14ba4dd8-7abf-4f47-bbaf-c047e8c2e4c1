<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-25 13:57:33
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-12-03 13:11:40
 * @FilePath: \tiksage-frontend\src\views\my-account\index.vue
 * @Description: my-account
-->
<script setup lang="tsx">
import { onMounted, reactive } from 'vue';
import { NForm, NFormItem, NInput, NText } from 'naive-ui';
import dayjs from 'dayjs';
import { fetchChangePassword, fetchDeleteOwnerShop } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useUserStore } from '@/store/modules/user';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { getFallbackImage } from '@/utils/fake-image';
import callbackImg from '@/assets/svg-icon/avatar.svg';
import { TimeFormat } from '@/enum';

const { VITE_SHOP_AVATAR_URL } = import.meta.env;

const userStore = useUserStore();

const { userInfo } = useAuthStore();

const onAddShop = () => {
  window.$dialog?.create({
    title: 'Notice',
    content() {
      return (
        <NText>
          We apologize for the inconvenience, as manual addition of shops for management is currently not supported.
          Rest assured, we have noted your request and will contact you shortly to assist with your issue. We hope you
          have a wonderful day.
        </NText>
      );
    },
    positiveText: 'Continue',
    negativeText: 'Cancel'
  });
};
// delete button
const onDelete = (id: number) => {
  window.$dialog?.warning({
    title: 'waring',
    content: 'This operation will delete shop. The operation is irreversible. Are you sure you want to do this?',
    positiveText: 'Confirm',
    negativeText: 'More Think',
    onPositiveClick: async () => {
      const { error } = await fetchDeleteOwnerShop(id);
      if (!error) {
        window.$message?.success('delete success!');
        userStore.getUserShop();
      }
    }
  });
};

const { formRef, validate } = useNaiveForm();
const { defaultRequiredRule, formRules } = useFormRules();

type FormModel = {
  oldPassword: string;
  newPassword: string;
};
type RuleKey = keyof FormModel;

const rules: Record<RuleKey, App.Global.FormRule | App.Global.FormRule[]> = {
  oldPassword: defaultRequiredRule,
  newPassword: formRules.registerPwd
};

const creatModel = () => {
  return {
    oldPassword: '',
    newPassword: ''
  };
};

const formModel = reactive<FormModel>(creatModel());

const handleResetPassword = () => {
  window.$dialog?.create({
    title: 'Change your password',
    content() {
      return (
        <div class="m-40px">
          <NForm ref={formRef} model={formModel} rules={rules}>
            <NFormItem label="Account ID">
              <NText>{userInfo.email}</NText>
            </NFormItem>
            <NFormItem label="Current Password" path="currentPassword">
              <NInput
                value={formModel.oldPassword}
                onUpdate:value={v => (formModel.oldPassword = v)}
                type="password"
                placeholder="Enter current password"
                show-password-on="mousedown"
              />
            </NFormItem>
            <NFormItem label="New Password" path="newPassword">
              <NInput
                value={formModel.newPassword}
                onUpdate:value={v => (formModel.newPassword = v)}
                type="password"
                placeholder="Enter new password"
                show-password-on="mousedown"
              />
            </NFormItem>
          </NForm>
        </div>
      );
    },
    positiveText: 'Confirm',
    onPositiveClick: async () => {
      await validate();
      const { error } = await fetchChangePassword(formModel);
      if (error) return false;
      return true;
    },
    negativeText: 'Cancel',
    onAfterLeave: () => {
      Object.assign(formModel, creatModel());
    }
  });
};

onMounted(() => {
  userStore.getUserShop();
});
</script>

<template>
  <NFlex vertical :size="16">
    <NCard :bordered="false" class="card-wrapper">
      <NFlex align="center">
        <div class="relative">
          <NImage
            width="100"
            height="100"
            class="rounded-50%"
            :src="callbackImg"
            :fallback-src="getFallbackImage(100, 100)"
          />
          <!--
 <NButton circle color="#165DFF" class="absolute bottom-5% right-5% border-2px border-white border-solid">
            <template #icon>
              <SvgIcon icon="ic:baseline-add-a-photo" />
            </template>
          </NButton>
-->
        </div>
        <NDescriptions label-placement="left" :columns="2" class="flex-1">
          <NDescriptionsItem label="Name">{{ userInfo.userName || '-' }}</NDescriptionsItem>
          <NDescriptionsItem label="Email">
            {{ userInfo.email || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="Account ID">{{ userInfo.email || '-' }}</NDescriptionsItem>
          <NDescriptionsItem label="Registration Time">
            {{ dayjs(userInfo.createTime).format(TimeFormat.US_DATE) || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="Password" label-style="height:30px; line-height:30px">
            <NButton size="small" quaternary @click="handleResetPassword">
              <template #icon>
                <SvgIcon icon="tabler:pencil-cog" />
              </template>
            </NButton>
          </NDescriptionsItem>
        </NDescriptions>
      </NFlex>
    </NCard>
    <NCard title="My Shops" :bordered="false" class="flex-1-hidden card-wrapper" content-class="h-full overflow-auto">
      <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
        <NGi v-for="item in userStore.userShops" :key="item.id" span="24 s:12 m:12 l:8">
          <NCard hoverable>
            <NThing content-indented>
              <template #avatar>
                <NAvatar size="large" round :src="`${VITE_SHOP_AVATAR_URL}${item.avatarLocal}`" />
              </template>
              <template #header>
                <NText>{{ item.shopName || '-' }}</NText>
              </template>
              <template #header-extra>
                <NButton quaternary @click="onDelete(item.shopId)">
                  <template #icon>
                    <SvgIcon icon="ic:baseline-delete-outline" />
                  </template>
                </NButton>
              </template>
              <template #description>
                <NText>@{{ item.shopCode || '-' }}</NText>
              </template>
              <NDescriptions size="small" label-placement="left" :column="1" label-align="right">
                <NDescriptionsItem label="Platform"><NTag>TikTok</NTag></NDescriptionsItem>
                <NDescriptionsItem label="Addition time">
                  {{ item.createTime && dayjs(item.createTime).format(TimeFormat.US_DATE) }}
                </NDescriptionsItem>
              </NDescriptions>
            </NThing>
          </NCard>
        </NGi>
        <NGi span="24 s:12 m:12 l:8">
          <NCard hoverable embedded content-style="padding:0" class="h-full min-h-165px">
            <NFlex justify="center" align="center" class="h-full" @click="onAddShop">
              <NText>Add Shop</NText>
              <SvgIcon icon="ic:baseline-add" class="text-size-5" />
            </NFlex>
          </NCard>
        </NGi>
      </NGrid>
    </NCard>
  </NFlex>
</template>

<style scoped></style>
