<script setup lang="ts">
import { ref, watch } from 'vue';

interface Emits {
  (e: 'check', value: string): void;
}

const emit = defineEmits<Emits>();

type Phrases = string[];
// 已记住的短语列表，最多保存 10 条
const phrases = ref<Phrases>(loadPhrasesFromLocalStorage());

// 添加快捷短语
function addPhrase(value: string) {
  const newPhrase = value.trim();
  if (!newPhrase || phrases.value.includes(newPhrase)) {
    return; // 忽略空短语或重复短语
  }

  // 添加新短语
  phrases.value.push(newPhrase);

  // 如果超过 10 条，移除最久的短语
  if (phrases.value.length > 10) {
    phrases.value.shift();
  }

  // 更新本地存储
  savePhrasesToLocalStorage(phrases.value);
}

// 加载本地存储的短语
function loadPhrasesFromLocalStorage() {
  const savedPhrases = JSON.parse(localStorage.getItem('phrases') || '[]');
  return savedPhrases || [];
}

// 保存短语到本地存储
function savePhrasesToLocalStorage(value: Phrases) {
  localStorage.setItem('phrases', JSON.stringify(value));
}

// 监听 `phrases` 变化，自动保存到本地存储
watch(phrases, () => {
  savePhrasesToLocalStorage(phrases.value);
});

function handleCheck(value: string) {
  emit('check', value);
}

defineExpose({ addPhrase });
</script>

<template>
  <NFlex :size="16">
    <NTag
      v-for="phrase in phrases"
      :key="phrase"
      :color="{ color: '#FAFAFC' }"
      checkable
      @update:checked="handleCheck(phrase)"
    >
      <NEllipsis style="max-width: 100px" :tooltip="{ maxWidth: 400 }">{{ phrase }}</NEllipsis>
    </NTag>
  </NFlex>
</template>

<style scoped></style>
