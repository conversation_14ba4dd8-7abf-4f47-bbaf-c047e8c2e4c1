import { request } from '../request';

export function fetchUploadCreatorCenter(data: { file: File }) {
  return request({
    url: '/aic-creator/importCreators',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    method: 'post',
    data
  });
}

export function fetchGetRunningTask() {
  return request<Api.CreatorCenter.RunningTask>({
    url: '/aic-creator/queryRunningTask',
    method: 'get'
  });
}

export function fetchReRunScript(taskId: number) {
  return request({
    url: '/aic-creator/runTask',
    method: 'post',
    data: {
      taskId
    }
  });
}

export function fetchGetCreatorCenterTaskList(data: Api.Common.CommonSearchParams) {
  return request<Api.CreatorCenter.CreatorCenterTaskListResponse>({
    url: '/aic-creator/pageListTasks',
    method: 'post',
    data
  });
}

export function fetchGetRaceOptions() {
  return request<string[]>({
    url: '/aic-creator/queryRaceSelection',
    method: 'get'
  });
}

export function fetchGetLanguageOptions() {
  return request<string[]>({
    url: '/aic-creator/queryLanguageSelection',
    method: 'get'
  });
}

export function fetchGetProductCategoryOptions() {
  return request<string[]>({
    url: '/aic-creator/queryProductCategorySelection',
    method: 'get'
  });
}

export function fetchGetCreatorCategoryOptions() {
  return request<string[]>({
    url: '/aic-creator/queryCreatorCategorySelection',
    method: 'get'
  });
}

export function fetchGetClientOptions() {
  return request<string[]>({
    url: '/aic-creator/queryClientSelection',
    method: 'get'
  });
}

export function fetchGetCreatorCenterList(data: Api.CreatorCenter.CreatorCenterSearchParams) {
  return request<Api.CreatorCenter.CreatorCenterListResponse>({
    url: '/aic-creator/pageListCreators',
    method: 'post',
    data
  });
}

export function fetchUpdateCreatorEvaluated(data: Api.CreatorCenter.CreatorUpdateEvaluatedParams) {
  return request({
    url: '/aic-creator/updateEvaluatedType',
    method: 'post',
    data
  });
}

export function fetchDownloadTaskFailedData(taskId: number) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/aic-creator/exportTaskFailData',
    method: 'post',
    data: { taskId }
  });
}

export function fetchDownloadCreatorData(data: Api.CreatorCenter.CreatorCenterSearchParams) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/aic-creator/exportCreators',
    method: 'post',
    data
  });
}
