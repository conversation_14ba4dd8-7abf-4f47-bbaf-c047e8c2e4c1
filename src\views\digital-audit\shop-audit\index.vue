<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-12-06 11:14:54
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-12-10 17:27:27
 * @FilePath: \tiksage-frontend\src\views\shop-audit\create\index.vue
 * @Description: shop-audit-createe
-->

<script setup lang="ts">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { delay } from 'lodash-es';
import { fetchSubmitShopAuditReport } from '@/service/api';
import { useRouterPush } from '@/hooks/common/router';
import ButtonCreate from './modules/button-create.vue';
import Introduction from './modules/introduction.vue';
import Report from './modules/report.vue';

const [showReport, toggleShowReport] = useToggle(false);

const shopName = ref<string>();
const taskId = ref<number>(0);

const [loading, toggleLoading] = useToggle(false);

async function handleSubmit() {
  if (!shopName.value) return;

  toggleLoading(true);

  if (showReport.value) {
    window.$message?.warning(
      'A report is currently being generated. Please try again later or refresh the page to retry.'
    );
    delay(() => {
      toggleLoading(false);
    }, 1000);
    return;
  }

  const { data, error } = await fetchSubmitShopAuditReport(shopName.value);

  if (!error) {
    window.$message?.success('Submission successful.');
    taskId.value = data;

    toggleShowReport(true);
  }

  delay(() => {
    toggleLoading(false);
  }, 1000);
}

const { routerPushByKey } = useRouterPush();
function handleLinkToHistory() {
  routerPushByKey('digital-audit_shop-audit-history');
}
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="bg-transparent" :bordered="false" title="TikTok Shop Audit">
      <template #header-extra>
        <NButton strong secondary @click="handleLinkToHistory">
          <template #icon>
            <icon-solar:history-bold-duotone class="text-icon" />
          </template>
          History
        </NButton>
      </template>
      <NFlex class="py-8" :wrap="false" justify="center" align="center">
        <div class="w-400px">
          <NInput v-model:value="shopName" clearable size="large" placeholder="Shop Name"></NInput>
        </div>
        <ButtonCreate :loading="loading" @click="handleSubmit">Submit</ButtonCreate>
      </NFlex>
    </NCard>
    <Introduction v-if="!showReport" class="min-h-400px flex-1 bg-transparent" />
    <Report v-else :task-id="taskId" />
  </NFlex>
</template>

<style scoped></style>
