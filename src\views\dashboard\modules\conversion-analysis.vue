<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { assign } from 'lodash-es';
import { fetchGetConversionAnalysisData } from '@/service/api';
import { useVChart } from '@/hooks/common/vchart';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { initPieIndicator, initPieLegend } from '@/utils/chart-options';
import { NumeralFormat } from '@/enum';
import { pieDefaultSpec } from '@/views/tiksage-dashboard/data-overview/modules/chart';

interface Props {
  params: Api.Dashboard.DashboardTopSearchParams;
}

type ConversionItem = {
  key: keyof Api.Dashboard.ConversionAnalysisResponse;
  indiactorColor: string;
  color: string;
  name: string;
  desc: string;
  width: number;
  unit?: NumeralFormat;
  value: number;
  adValue: number;
  adDesc: string;
  indiactors: {
    key: keyof Api.Dashboard.IndiactorData;
    name: string;
    value: number;
  }[][];
};

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const originalData = ref<Api.Dashboard.ConversionAnalysisResponse>();

const conversionList = computed(() => {
  let res: ConversionItem[] = [
    {
      key: 'impressionData',
      indiactorColor: '#DCE8FF',
      color: '#3779FE',
      name: 'Impression',
      desc: `
Impression

Live:
The Live direct GMV is the total amount paid for orders placed directly from Live streams, including returns and refunds.

Video:
The shoppable video direct GMV is the total amount paid for orders placed directly from shoppable videos, including returns and refunds.

Product Card:
The product card direct GMV is the total amount paid for orders placed directly from product cards, including returns and refunds.
      `,
      width: 250,
      value: 0,
      adValue: 0,
      adDesc: 'Number of times your Ads were shown.',
      indiactors: [
        [
          {
            key: 'video',
            name: 'Video',
            value: 0
          },
          {
            key: 'live',
            name: 'Live',
            value: 0
          },
          {
            key: 'productCard',
            name: 'Product Card',
            value: 0
          }
        ]
      ]
    },
    {
      key: 'engagementData',
      indiactorColor: '#E8F0FF',
      color: '#3ACCFE',
      name: 'Engagement',
      width: 210,
      value: 0,
      desc: `
Engagement

Live:
Total number of times the live shared, likes received, commented during the live posted period.

Video:
Total number of times the video shared, likes received, commented during the live posted period.
      `,
      adValue: 0,
      adDesc: 'Number of likes, shares, comments of your ads during the ads impression',
      indiactors: [
        [
          {
            key: 'video',
            name: 'Video',
            value: 0
          },
          {
            key: 'live',
            name: 'Live',
            value: 0
          }
        ],
        [
          {
            key: 'likes',
            name: 'Likes',
            value: 0
          },
          {
            key: 'shares',
            name: 'Shares',
            value: 0
          },
          {
            key: 'comments',
            name: 'Comments',
            value: 0
          }
        ]
      ]
    },
    {
      key: 'clickData',
      indiactorColor: '#F3F7FF',
      color: '#00E5FF',
      name: 'Clicks',
      width: 170,
      value: 0,
      desc: `
Clicks

Live:
Number of product clicks during the Live posted during the selected period.

Video:
Number of product clicks during the video posted during the selected period.

Product Card:
Number of product card clicks during the selected period.`,
      adValue: 0,
      adDesc: 'Number of clicks from your ads to a specified destination.',
      indiactors: [
        [
          {
            key: 'video',
            name: 'Video',
            value: 0
          },
          {
            key: 'live',
            name: 'Live',
            value: 0
          },
          {
            key: 'productCard',
            name: 'Product Card',
            value: 0
          }
        ]
      ]
    },
    {
      key: 'itemsSoldData',
      indiactorColor: '#F5F8FF',
      color: '#6FF1FF',
      name: 'Items sold',
      width: 130,
      value: 0,
      desc: `
Items sold

Live:
The total number of individual items sold from lives.

Video:
The total number of individual items sold from all videos.

Product card:
The total number of individual items sold from all product cards.`,
      adValue: 0,
      adDesc: 'The number of Shop items sold attributed to your ads.',
      indiactors: [
        [
          {
            key: 'video',
            name: 'Video',
            value: 0
          },
          {
            key: 'live',
            name: 'Live',
            value: 0
          },
          {
            key: 'productCard',
            name: 'Product Card',
            value: 0
          }
        ]
      ]
    },
    {
      key: 'gmvData',
      indiactorColor: '#F5F8FF',
      color: '#6FF1FF',
      name: 'GMV',
      width: 90,
      unit: NumeralFormat.Dollar,
      value: 0,
      desc: `
GMV

Live:
The total amount paid for orders from live, including returns and refunds.

Video:
The total amount paid for orders placed directly from the shoppable video, including returns and refunds.

Product Card:
The total amount paid for orders placed directly from the product card, including returns and refunds.`,
      adValue: 0,
      adDesc:
        "Gross revenue of Shop orders attributed to your ads. It's the amount people pay, less any sales taxes, plus any product discounts provided to them by Shop platform.",
      indiactors: [
        [
          {
            key: 'video',
            name: 'Video',
            value: 0
          },
          {
            key: 'live',
            name: 'Live',
            value: 0
          },
          {
            key: 'productCard',
            name: 'Product Card',
            value: 0
          }
        ]
      ]
    }
  ];

  if (originalData.value) {
    res = res.map(i => {
      const data = originalData.value![i.key];

      if (!data) return i;

      return {
        ...i,
        value: data.total,
        adValue: data.ads,
        indiactors: i.indiactors.map(row => {
          return row.map(indiactor => {
            return {
              ...indiactor,
              value: data[indiactor.key]
            };
          });
        })
      };
    });
  }
  return res;
});

const { domRef: impressionChartRef, updateSpec: updateImpressionSpec } = useVChart(() =>
  cusSpec(pieDefaultSpec, NumeralFormat.Real_Number)
);
const { domRef: gmvChartRef, updateSpec: updateGMVSpec } = useVChart(() => cusSpec(pieDefaultSpec));

function cusSpec(
  spec: Visactor.VChart.IPieChartSpec,
  tooltipUnit = NumeralFormat.Real_Dollar
): Visactor.VChart.IPieChartSpec {
  const cus: Visactor.VChart.IPieChartSpec = {
    type: 'pie',
    padding: 0,
    outerRadius: 1,
    innerRadius: 0.7,
    color: ['#3779FE', '#3ACCFE', '#6FF1FF'],
    categoryField: 'type',
    valueField: 'value',
    pie: {
      style: {
        cornerRadius: 10
      },
      state: {
        hover: {
          outerRadius: 1.05,
          stroke: '#000',
          lineWidth: 1
        },
        selected: {
          outerRadius: 1.05,
          stroke: '#000',
          lineWidth: 1
        }
      }
    },
    label: {
      visible: false
    },
    legends: {
      visible: true,
      orient: 'right'
    },
    tooltip: {
      mark: {
        title: {
          visible: false
        },
        content: [
          {
            key: v => {
              return v ? (v.type as string) : '';
            },
            value: datum => {
              return `${datum && numberFormat(datum.value, tooltipUnit)} (${datum && datum._percent_}%)`; // eslint-disable-line no-underscore-dangle
            }
          }
        ]
      }
    }
  };
  return assign({}, spec, cus);
}

function initPieData() {
  updateImpressionSpec(oldOpts => {
    const indiactors = conversionList.value.find(item => item.key === 'impressionData')?.indiactors[0];
    const values =
      indiactors?.map(indiactor => {
        return {
          type: indiactor.name,
          value: indiactor.value
        };
      }) || [];
    const total = values.reduce((acc, cur) => acc + cur.value, 0);

    const result = assign({}, oldOpts, {
      data: [{ id: 'impression', values }],
      indicator: initPieIndicator(total, 'Impression', NumeralFormat.Number),
      legends: initPieLegend(values, total, 10, NumeralFormat.Number, '60%')
    });

    return result;
  });

  updateGMVSpec(oldOpts => {
    const indiactors = conversionList.value.find(item => item.key === 'gmvData')?.indiactors[0];

    const values =
      indiactors?.map(indiactor => {
        return {
          type: indiactor.name,
          value: indiactor.value
        };
      }) || [];
    const total = values.reduce((acc, cur) => acc + cur.value, 0);

    const result = assign({}, oldOpts, {
      data: [{ id: 'gmv', values }],
      indicator: initPieIndicator(total, 'GMV', NumeralFormat.Dollar),
      legends: initPieLegend(values, total, 10, NumeralFormat.Dollar, '60%')
    });

    return result;
  });

  // updateGMV2Spec(oldOpts => {
  //   return { ...oldOpts, data: { values: [{ type: 'GMV', value: 0 }] } };
  // });
}

function realNumber(num: number | undefined, unit: NumeralFormat = NumeralFormat.Number) {
  return numberFormat(num, unit);
}

async function initData(params: Api.Dashboard.DashboardTopSearchParams) {
  const searhParams: Api.Dashboard.ConversionAnalysisSearchParams = {
    shopId: params.shopIdsArr![0],
    startDateStr: params.startDateStr,
    endDateStr: params.endDateStr
  };
  const { data, error } = await fetchGetConversionAnalysisData(searhParams);
  if (!error) {
    originalData.value = data;
  }
}

watch(
  () => props.params,
  newVal => {
    const { shopIdsArr, startDateStr, endDateStr } = newVal;
    if (!shopIdsArr?.length) return;
    if (!startDateStr || !endDateStr) return;

    initData(newVal);
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => conversionList.value,
  () => {
    initPieData();
  }
);
</script>

<template>
  <NCard class="card-wrapper" :border="false">
    <NGrid :cols="3" :x-gap="16" :y-gap="16">
      <NGi :span="2">
        <div v-for="item in conversionList" :key="item.name" class="mb-4 flex justify-between">
          <div class="h-75px w-150px flex-col-center rounded-lg" :style="{ backgroundColor: item.indiactorColor }">
            <div class="flex-y-center gap-2">
              <span class="font-bold">Ads {{ item.name }}</span>
              <Tip v-if="item.adDesc" :description="item.adDesc" />
            </div>
            <span>{{ realNumber(item.adValue, item.unit) }}</span>
          </div>
          <div class="flex-col gap-2">
            <div class="trapezium-container" :style="{ width: `${item.width}px`, backgroundColor: item.color }">
              <div class="flex-col-center">
                <div class="flex-y-center gap-2 text-base">
                  <span>{{ item.name }}</span>
                  <Tip v-if="item.desc" :description="item.desc" />
                </div>
                <span>{{ realNumber(item.value, item.unit) }}</span>
              </div>
            </div>
          </div>
          <div class="indiactor-container h-75px w-270px" :style="{ backgroundColor: item.indiactorColor }">
            <div class="h-full w-14px flex-center">
              <icon-material-symbols:equal-rounded />
            </div>
            <div class="flex-col justify-center gap-2">
              <div v-for="(row, rowIdx) in item.indiactors" :key="rowIdx" class="flex-y-center gap-2">
                <template v-for="(indiactor, indiactorIdx) in row" :key="indiactor.key">
                  <div class="flex-col-center text-xs">
                    <span class="font-bold">{{ indiactor.name }}</span>

                    <span class="font-500">{{ realNumber(indiactor.value, item.unit) }}</span>
                  </div>
                  <icon-material-symbols:add-2-rounded v-if="indiactorIdx !== row.length - 1" />
                </template>
              </div>
            </div>
          </div>
        </div>
      </NGi>
      <NGi :span="1">
        <div class="h-full flex-col">
          <div ref="impressionChartRef" class="h-227px w-full"></div>
          <div ref="gmvChartRef" class="h-227px w-full"></div>
        </div>
      </NGi>
    </NGrid>
  </NCard>
</template>

<style lang="scss" scoped>
.indiactor-container {
  @apply relative flex gap-2 rounded-lg pl-4;

  &::before {
    content: '';
    position: absolute;
    top: calc(50% - 2px);
    left: -165px;
    width: 165px;
    border-style: dashed;
    border-top-width: 2px;
    border-top-color: var(--n-border-color);
  }
}

.trapezium-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 270px;
  height: 75px;
  color: #fff;
  font-weight: bold;
  background-color: #3662ec;
  z-index: 1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -20px;
    width: 30px;
    height: inherit;
    background-color: inherit;
    transform: skewX(15deg);
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
    z-index: -1;
  }
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: -20px;
    width: 30px;
    height: inherit;
    background-color: inherit;
    transform: skewX(-15deg);
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    z-index: -1;
  }
}
</style>
