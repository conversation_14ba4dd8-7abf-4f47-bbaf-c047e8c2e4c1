<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-17 14:20:38
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jar<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-18 14:25:26
 * @FilePath: \tiksage-frontend\src\components\custom\button-copy.vue
 * @Description: button-copy
-->
<script setup lang="ts">
import ButtonIcon from './button-icon.vue';

interface Props {
  copy: string;
}

const props = defineProps<Props>();

async function handleCopy() {
  try {
    await navigator.clipboard.writeText(props.copy);
    window.$message?.success('Copy success.');
  } catch (_e) {
    console.log(_e);
    window.$message?.error('Copy failure.');
  }
}
</script>

<template>
  <ButtonIcon
    class="h-auto"
    style="color: #9ca3af"
    :quaternary="false"
    text
    icon="tabler:copy"
    tooltip-content="Copy"
    tooltip-placement="top"
    @click.stop="handleCopy"
  ></ButtonIcon>
</template>

<style scoped></style>
