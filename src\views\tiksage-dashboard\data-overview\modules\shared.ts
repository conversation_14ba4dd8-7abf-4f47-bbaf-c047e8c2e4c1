import { readonly, ref } from 'vue';
import { fetchGetOverviewDataOnTikSageDashboard } from '@/service/api';

// 创建一个共享的状态
const realData = ref<Api.TikSageDashboard.OverviewDataResponse>();

export function useOverviewData() {
  // 获取数据的方法
  async function fetchData(params: Api.TikSageDashboard.DateParams) {
    const { data, error } = await fetchGetOverviewDataOnTikSageDashboard(params);
    if (!error) {
      realData.value = data;
    }
    return { data, error };
  }

  // 返回只读的数据引用和方法
  return {
    realData: readonly(realData),
    fetchData
  };
}
