<script setup lang="ts">
import { computed } from 'vue';
import { getFallbackImage } from '@/utils/fake-image';

const { VITE_SHOP_LEADER_AVATAR_URL } = import.meta.env;

interface Props {
  data: Api.CategoryLeaders.LeaderShopProduct;
  isExport?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isExport: false
});

const avatarUrl = computed(() => {
  return `${VITE_SHOP_LEADER_AVATAR_URL}${props.data?.productAvatarLocal}`;
});

const truncatedProductName = computed(() => {
  if (!props.data?.productName) {
    return '-';
  }
  if (props.data.productName.length > 55) {
    return `${props.data.productName.slice(0, 55)}...`;
  }
  return props.data.productName;
});

const shopIndicators = computed(() => {
  return [
    {
      title: 'GMV',
      prefix: '$',
      value: props.data?.monthlyGmv || '$0',
      showPercent: true,
      percent: props.data?.monthlyGmvGrowth || '0%',
      description: '',
      average: true,
      decimals: 2,
      suffix: ''
    },
    {
      title: 'Units Sold',
      prefix: '',
      value: props.data?.monthlySold || '0',
      showPercent: true,
      percent: props.data?.monthlySoldGrowth || '0%',
      description: '',
      average: true,
      decimals: 2,
      suffix: ''
    }
  ];
});
</script>

<template>
  <NCard class="" :bordered="false" size="small" content-class="relative max-w-full overflow-hidden">
    <div class="absolute right-0 top-0 h-58px w-58px overflow-hidden bg-transparent">
      <div
        class="absolute right-[-50%] top-[8px] w-[100px] rotate-45 border-y-1px border-primary text-center"
        style="transform-origin: center"
      >
        <span class="inline-block text-primary">BEST</span>
      </div>
    </div>
    <NThing class="">
      <template #avatar>
        <div class="h-60px w-60px overflow-hidden border-1 rounded-xl">
          <NImage preview-disabled :src="avatarUrl" :fallback-src="getFallbackImage(60, 60)" />
        </div>
      </template>
      <template #header>
        <div class="h-52px overflow-hidden">
          <NEllipsis
            v-if="!isExport"
            style="max-width: 100%"
            class="whitespace-pre-wrap pr-25px font-bold"
            :tooltip="{ contentStyle: 'max-width:400px' }"
            :line-clamp="2"
          >
            {{ data?.productName || '-' }}
          </NEllipsis>
          <div class="line-clamp-2 break-all pr-25px font-bold">
            {{ truncatedProductName }}
          </div>
        </div>
      </template>
      <template #description>
        <NDescriptions
          class="nowrap-hidden"
          label-class="text-coolgray"
          size="small"
          label-placement="left"
          :column="1"
        >
          <NDescriptionsItem class="p-0" label="Launch Date">
            {{ data?.launchDate || '-' }}
          </NDescriptionsItem>
          <NDescriptionsItem label="Price">
            {{ data?.price || '-' }}
          </NDescriptionsItem>
        </NDescriptions>
      </template>
      <NGrid class="mt-[-8px] divide-x" x-gap="0" :cols="2">
        <NGi v-for="indicator in shopIndicators" :key="indicator.title">
          <NFlex vertical justify="center" align="center" :size="0">
            <NFlex align="center" :size="16" :wrap="false">
              <span class="font-bold">{{ indicator.value }}</span>
              <CycleRatio v-if="indicator.showPercent" size="small" text :percent="indicator.percent" />
            </NFlex>
            <NFlex>
              <NText class="text-gray">{{ indicator.title }}</NText>
              <Tip v-if="indicator.description !== ''" :description="indicator.description" />
            </NFlex>
          </NFlex>
        </NGi>
      </NGrid>
    </NThing>
  </NCard>
</template>

<style scoped>
.n-descriptions .n-descriptions-table-content {
  padding: 0 !important;
}
</style>
