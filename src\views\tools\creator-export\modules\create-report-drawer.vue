<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-23 10:42:50
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-07 17:44:54
 * @FilePath: \tiksage-frontend\src\views\tools\creator-export\modules\create-report-drawer.vue
 * @Description: create-report-drawer
-->
<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import type { UploadFileInfo } from 'naive-ui';
import { fetchCreateCreatorsExportTask } from '@/service/api';

interface Emits {
  (e: 'submit'): void;
}

const emit = defineEmits<Emits>();

const show = defineModel('show', {
  type: Boolean,
  required: true
});

const tabValue = ref<number>(1);

const [uploadDisabled, toggleUploadDisabled] = useToggle(true);
const [keyInDisabled, toggleKeyInDisabled] = useToggle(true);

const uploadRef = ref();

const uploadFile = ref<UploadFileInfo[]>();

function handleBeforeUpload(data: { file: UploadFileInfo }) {
  if (data.file.file?.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
    window.$message?.error(`Only Excel files in '.xlsx' format can be uploaded. Please re-upload.`);
    return false;
  }
  return true;
}

const descriptionValue = ref<string>();

const inputValue = ref<string>();

const creatorIds = computed<string[]>(() => {
  if (!inputValue.value) return [];
  return matchCreatorIds(inputValue.value);
});

function matchCreatorIds(text: string) {
  const regex = /\b(?!.*\.\.)(?!.*\.$)[a-zA-Z0-9._]{2,24}\b/g;
  const matchs = text.match(regex);
  return matchs || ([] as string[]);
}

async function handleCreateTask(type: 'create' | 'next') {
  const params: Api.Tool.CreateTaskParams = {
    description: descriptionValue.value
  };
  if (tabValue.value === 1) {
    params.creatorIdListStr = JSON.stringify(creatorIds.value);
  } else {
    if (!uploadFile.value?.length) return;
    params.file = uploadFile.value[0].file as File;
  }
  const { error } = await fetchCreateCreatorsExportTask(params);
  if (error) return;
  window.$message?.success('Create Success.');
  emit('submit');
  if (type === 'create') {
    show.value = false;
  } else {
    tabValue.value === 1 && (inputValue.value = '');
    tabValue.value === 2 && (uploadFile.value = []);
  }
}

function handleDownloadTemplate() {
  window.location.href = 'https://product.tiksage.com/images/template/creator_template.xlsx';
}

function handleInputChange() {
  if (creatorIds.value.length > 100) {
    window.$message?.warning('Enter up to 100 creators, or choose to upload a file instead.');
  }
}

watch(
  () => creatorIds.value,
  newVal => {
    if (creatorIds.value.length <= 100) {
      toggleKeyInDisabled(!newVal.length);
    } else {
      toggleKeyInDisabled(true);
    }
  }
);

watch(
  () => uploadFile.value,
  newVal => {
    toggleUploadDisabled(Boolean(!newVal?.length));
  }
);

watch(
  () => show.value,
  newVal => {
    if (!newVal) {
      inputValue.value = '';
      descriptionValue.value = '';
      uploadFile.value = [];
    }
  }
);
</script>

<template>
  <NDrawer v-model:show="show" width="450px">
    <NDrawerContent title="Create Task">
      <NTabs v-model:value="tabValue" type="segment" animated>
        <NTabPane :name="1" tab="Key In">
          <NForm>
            <NFormItem label="Creator ID">
              <NInput
                v-model:value="inputValue"
                :resizable="false"
                class="h-300px max-h-300px"
                type="textarea"
                placeholder="Enter up to 100 creators, separated by spaces, commas, or line breaks, or upload a file for more."
                @change="handleInputChange"
              ></NInput>
            </NFormItem>
            <NFormItem label="Description">
              <NInput
                v-model:value="descriptionValue"
                :resizable="false"
                class="h-110px max-h-110px"
                type="textarea"
                maxlength="100"
                show-count
                placeholder="Enter a task-related description (optional)."
                @change="handleInputChange"
              ></NInput>
            </NFormItem>
            <NFlex justify="flex-end" :size="16">
              <NButton @click="show = false">Cancel</NButton>
              <NButton type="primary" :disabled="keyInDisabled" @click="handleCreateTask('create')">Create</NButton>
              <NButton type="primary" :disabled="keyInDisabled" @click="handleCreateTask('next')">
                Create & Next
              </NButton>
            </NFlex>
          </NForm>
        </NTabPane>
        <NTabPane :name="2" tab="Upload File">
          <NForm>
            <NFormItem label="Template Excel">
              <NButton text type="info" @click="handleDownloadTemplate">Download</NButton>
            </NFormItem>
            <NFormItem label="Import File">
              <NUpload
                ref="uploadRef"
                v-model:file-list="uploadFile"
                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                directory-dnd
                :max="1"
                @before-upload="handleBeforeUpload"
              >
                <NUploadDragger>
                  <NFlex class="h-150px" vertical justify="center" align="center" :size="16">
                    <SvgIcon class="text-4xl text-primary" icon="solar:cloud-upload-bold-duotone" />
                    <NText>Drag and drop your Excel spreadsheet to upload.</NText>
                  </NFlex>
                </NUploadDragger>
              </NUpload>
            </NFormItem>
            <NFormItem label="Description">
              <NInput
                v-model:value="descriptionValue"
                :resizable="false"
                class="h-110px max-h-110px"
                type="textarea"
                maxlength="100"
                show-count
                placeholder="Enter a task-related description (optional)."
                @change="handleInputChange"
              ></NInput>
            </NFormItem>
            <NFlex justify="flex-end" :size="16">
              <NButton @click="show = false">Cancel</NButton>
              <NButton type="primary" :disabled="uploadDisabled" @click="handleCreateTask('create')">Create</NButton>
              <NButton type="primary" :disabled="uploadDisabled" @click="handleCreateTask('next')">
                Create & Next
              </NButton>
            </NFlex>
          </NForm>
        </NTabPane>
      </NTabs>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
textarea {
  resize: none;
}
</style>
