import { request } from '../request';

export function fetchAddEmailTemplate(data: Api.CreatorNetwork.AddEmailTemplateParams) {
  return request({
    url: '/contactCreators/addTemplate',
    method: 'post',
    data
  });
}

export function fetchChangeEmailTemplate(data: Api.CreatorNetwork.AddEmailTemplateParams) {
  return request({
    url: '/contactCreators/modifyTemplate',
    method: 'put',
    data
  });
}

export function fetchGetEmailTemplateList(params: Api.CreatorNetwork.GetEmailTemplateListParams) {
  return request<Api.CreatorNetwork.GetEmailTemplateListResponse>({
    url: '/contactCreators/queryTemplatePage',
    method: 'get',
    params
  });
}

export function fetchDeleteEmailTemplate(id: number) {
  return request({
    url: '/contactCreators/deleteTemplate',
    method: 'delete',
    params: {
      id
    }
  });
}

export function fetchGetEmailTaskConfig() {
  return request<Api.CreatorNetwork.EmailTaskConfigResponse>({
    url: '/contactCreators/getEmailConfig',
    method: 'get'
  });
}

export function fetchUpdateEmailTaskConfig(data: Api.CreatorNetwork.EmailTaskConfigResponse) {
  return request({
    url: '/contactCreators/setEmailConfig',
    method: 'post',
    data
  });
}

export function fetchUploadCreatorDataByExcel(
  data: Api.CreatorNetwork.UploadCreatorDataByExcelParams,
  type: 'email' | 'tiktok'
) {
  const url =
    type === 'email' ? '/contactCreators/uploadCreatorExcel' : '/contactCreators/message/uploadMessageCreatorExcel';
  return request<Api.CreatorNetwork.UploadCreatorDataByExcelResponse>({
    url,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    method: 'post',
    data
  });
}

export function fetchDownloadErrorCreatorDataByExcel(data: Api.CreatorNetwork.CreatorInfo[]) {
  return request({
    url: '/contactCreators/exportUploadFailedData',
    responseType: 'blob',
    method: 'post',
    data
  });
}

export function fetchStartTask(data: Api.CreatorNetwork.StartTaskParams) {
  return request({
    url: '/contactCreators/sendEmail',
    method: 'post',
    data
  });
}

export function fetchStartTestEmail(data: Api.CreatorNetwork.StartTestEmailParams) {
  return request({
    url: '/contactCreators/testSend',
    method: 'post',
    data
  });
}

export function fetchGetEmailTemplateOptions() {
  return request<Api.CreatorNetwork.GetEmailTemplateOptions[]>({
    url: '/contactCreators/queryTemplateList',
    method: 'get'
  });
}

export function fetchGetTaskHistoryList(data: Api.CreatorNetwork.GetTaskHistoryListParams) {
  return request<Api.CreatorNetwork.GetTaskHistoryListResponse>({
    url: '/contactCreators/queryTaskList',
    method: 'post',
    data
  });
}

export function fetchGetEmailAccountList() {
  return request<Api.CreatorNetwork.EmailAccount[]>({
    url: '/contactCreators/queryEmailList',
    method: 'get'
  });
}

export function fetchDeleteEmailAccount(id: number) {
  return request({
    url: '/contactCreators/deleteEmail',
    method: 'delete',
    params: {
      id
    }
  });
}

export function fetchGetEmailTaskDetail(taskId: number) {
  return request<Api.CreatorNetwork.TaskDetail>({
    url: '/contactCreators/queryTaskDetail',
    method: 'get',
    params: {
      taskId
    }
  });
}

export function fetchGetEmailTaskSenderList(data: Api.CreatorNetwork.EmailTaskSenderListParams) {
  return request<Api.CreatorNetwork.EmailTaskSenderListResponse>({
    url: '/contactCreators/queryTaskDetailList',
    method: 'post',
    data
  });
}

export function fetchDeleteEmailTask(id: number) {
  return request({
    url: '/contactCreators/deleteTask',
    method: 'delete',
    params: {
      id
    }
  });
}

export function fetchStartEmailTask(id: number) {
  return request({
    url: '/contactCreators/startTask',
    method: 'put',
    params: {
      id
    }
  });
}

export function fetchPauseEmailTask(id: number) {
  return request({
    url: '/contactCreators/pauseTask',
    method: 'put',
    params: {
      id
    }
  });
}

export function fetchGetProductListByCard(data: Api.CreatorNetwork.ProductParams) {
  return request<Api.CreatorNetwork.ProductResponse>({
    url: '/contactCreators/message/getProductsPage',
    method: 'post',
    data
  });
}

export function fetchRunTaskByTikTok(data: Api.CreatorNetwork.RunTaskByTikTokParams) {
  return request({
    url: '/contactCreators/message/sendMessage',
    method: 'post',
    data
  });
}

export function fetchGetTaskHistoryByTikTok(data: Api.CreatorNetwork.TaskHistoryByTikTokParams) {
  return request<Api.CreatorNetwork.TaskHistoryByTikTokResponse>({
    url: '/contactCreators/message/queryMessageTaskList',
    method: 'post',
    data
  });
}

export function fetchStartTikTokTask(id: number) {
  return request({
    url: '/contactCreators/message/startTask',
    method: 'put',
    params: {
      id
    }
  });
}

export function fetchPauseTikTokTask(id: number) {
  return request({
    url: '/contactCreators/message/pauseTask',
    method: 'put',
    params: {
      id
    }
  });
}

export function fetchGetTaskInfoByTikTok(taskId: number) {
  return request<Api.CreatorNetwork.TaskDetailByTikTok>({
    url: '/contactCreators/message/queryMessageTaskDetail',
    method: 'get',
    params: {
      taskId
    }
  });
}

export function fetchGetTaskInfoCreatorsByTikTok(data: Api.CreatorNetwork.TaskInfoCreatorsByTikTokParams) {
  return request<Api.CreatorNetwork.TaskInfoCreatorsByTikTokResponse>({
    url: '/contactCreators/message/queryMessageTaskDetailList',
    method: 'post',
    data
  });
}

export function fetchGetFindCreatorList(data: Api.CreatorNetwork.FindCreatorParams) {
  return request<Api.CreatorNetwork.FindCreatorResponse>({
    url: '/creator/pageFindCreators',
    method: 'post',
    data
  });
}

export function fetchGetFindCreatorIdList(data: Api.CreatorNetwork.FindCreatorIdParams) {
  return request<number[]>({
    url: '/creator/findCreatorIdList',
    method: 'post',
    data
  });
}

export function fetchGetFindCreatorListByIds(creatorIdList: number[]) {
  return request<Api.CreatorNetwork.FindCreator[]>({
    url: '/creator/findCreatorsByIds',
    method: 'post',
    data: { creatorIdList }
  });
}

export function fetchCreateMessageTemplate(data: Api.CreatorNetwork.CreateMessageTemplateParams) {
  return request({
    url: '/contactCreators/message/addMessageTemplate',
    method: 'post',
    data
  });
}

export function fetchGetMessageTemplateList() {
  return request<Api.CreatorNetwork.MessageTemplate[]>({
    url: '/contactCreators/message/queryMessageTemplateList',
    method: 'get'
  });
}

export function fetchGetMessageTemplateListByPage(params: Api.Common.CommonSearchParams) {
  return request<Api.CreatorNetwork.MessageTemplateListByPageResponse>({
    url: '/contactCreators/message/queryMessageTemplatePage',
    method: 'get',
    params
  });
}

export function fetchUpdateMessageTemplate(data: Api.CreatorNetwork.MessageTemplate) {
  return request({
    url: '/contactCreators/message/modifyMessageTemplate',
    method: 'put',
    data
  });
}

export function fetchDeleteMessageTemplate(id: number) {
  return request({
    url: '/contactCreators/message/deleteMessageTemplate',
    method: 'delete',
    params: {
      id
    }
  });
}

export function fetchDownloadCreatorDataByFilter(data: Api.CreatorNetwork.FindCreatorParams) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/creator/exportCreators',
    method: 'post',
    data
  });
}
