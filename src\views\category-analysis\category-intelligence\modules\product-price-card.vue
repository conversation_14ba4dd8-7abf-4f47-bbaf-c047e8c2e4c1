<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-19 14:20:10
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-08 10:20:30
 * @FilePath: \tiksage-frontend\src\views\category-intelligence\modules\product-price-card.vue
 * @Description: Brand Store Product Price Distribution Card
-->
<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import type { DataTableColumn } from 'naive-ui';
import { delay, divide, isNil } from 'lodash-es';
import { fetchProductPrice } from '@/service/api';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';
import CategoryShopSelect from './category-shop-select.vue';

interface Props {
  shopType: number;
  month: string;
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const [loading, toggleLoading] = useToggle(false);

const tableData = ref<Api.CategoryIntelligence.PriceInfo[]>();

function formatPriceRange(low: number | undefined, up: number | undefined) {
  if (isNil(low)) {
    return `≤ ${up}`;
  } else if (isNil(up)) {
    return `> ${low}`;
  }
  return `${low} - ${up}`;
}

const columns: DataTableColumn<Api.CategoryIntelligence.PriceInfo>[] = [
  {
    key: 'up',
    title: 'Price Range($)',
    align: 'center',
    fixed: 'left',
    width: '140',
    render(rowData) {
      const { low, up } = rowData;
      return formatPriceRange(low, up);
    }
  },
  {
    key: 'spusNum',
    title: 'No.of Active SPUs',
    align: 'center',
    width: '140'
  },
  {
    key: 'shopsNum',
    title: 'No.of Shops for Sale',
    align: 'center',
    width: '160'
  },
  {
    key: 'sales',
    title: 'Gross Sales',
    align: 'center',
    width: '110'
  },
  {
    key: 'unitsSold',
    title: 'Units Sold',
    align: 'center',
    width: '110'
  },
  {
    key: 'contentNum',
    title: 'Content Volume',
    align: 'center',
    width: '140'
  }
];
// chart action start
type MetricKey = keyof Api.CategoryIntelligence.PriceInfo;

const metricValue = ref<MetricKey>('spusNum');

const metricOptions = reactive<CommonType.Option<MetricKey>[]>([
  {
    value: 'spusNum',
    label: 'No.of Active SPUs'
  },
  {
    value: 'shopsNum',
    label: 'No.of Shops for Sale'
  },
  {
    value: 'sales',
    label: 'Gross Sales'
  },
  {
    value: 'unitsSold',
    label: 'Units Sold'
  },
  {
    value: 'contentNum',
    label: 'Content Volume'
  }
]);

const chartData = ref<Visactor.VChart.IBarChartSpec>();
const [chartLoading, toggleChartLoading] = useToggle(false);

watch(
  () => metricValue.value,
  () => {
    if (!tableData.value) return;
    initChartData(tableData.value, metricValue.value);
  }
);
// chart action end

const initData = async (shopType: Props['shopType'], month: string, category: string, thirdCategory: string) => {
  const params = {
    shopType,
    month,
    category,
    thirdCategory
  };
  const { data, error } = await fetchProductPrice(params);
  if (error) return;
  tableData.value = data;
  // format chart data
  initChartData(data, metricValue.value);
  toggleLoading(false);
};

const dollarKeys: string[] = ['sales'];
const percentKeys: string[] = [];

const renderCell = (value: any, _rowData: any, column: any) => {
  if (dollarKeys.includes(column.key)) {
    return numberFormat(value, NumeralFormat.Dollar);
  }
  if (percentKeys.includes(column.key)) {
    return numberFormat(divide(Number(value), 100), NumeralFormat.Percent);
  } else if (typeof value === 'number') {
    return numberFormat(value, NumeralFormat.Number);
  }
  return value;
};

function initChartData(original: Api.CategoryIntelligence.ProductPriceResponse, metricKey: MetricKey) {
  toggleChartLoading(true);

  const data: Visactor.VChart.IDataValues[] = [
    {
      id: dollarKeys.includes(metricKey) ? 'dollarY' : 'numberY',
      values: []
    }
  ];
  for (const ele of original) {
    (data[0].values as any[]).push({
      x: formatPriceRange(ele.low, ele.up),
      type: metricOptions.find(m => m.value === metricValue.value)?.label,
      y: ele[metricKey]
    });
  }

  const result: Visactor.VChart.IBarChartSpec = {
    type: 'bar',
    data,
    xField: 'x',
    yField: 'y',
    axes: [
      {
        orient: 'left',
        label: {}
      }
    ]
  };
  chartData.value = result;

  delay(() => {
    toggleChartLoading(false);
  }, 500);
}

// eslint-disable-next-line max-params
function handleSelectChange(model: { category: string; thirdCategory: string; shopIds: number[] }) {
  toggleLoading(true);
  initData(props.shopType, props.month, model.category, model.thirdCategory);
}
</script>

<template>
  <NGrid x-gap="16" y-gap="16" item-responsive responsive="screen">
    <NGi span="16" class="h-full">
      <NCard
        :bordered="false"
        class="h-full card-wrapper"
        content-class="min-h-300px flex-center"
        title="Price Tier Overview"
      >
        <template #header-extra>
          <CategoryShopSelect
            :month="month"
            :shop-type="shopType"
            show-third-category
            @update:value="handleSelectChange"
          />
        </template>
        <NDataTable
          :bordered="false"
          size="small"
          :loading="loading"
          :data="tableData"
          :columns="columns"
          :scroll-x="800"
          :render-cell="renderCell"
        />
      </NCard>
    </NGi>
    <NGi span="8" class="h-full">
      <NCard
        class="h-full card-wrapper"
        content-class="flex-center min-h-320px"
        :bordered="false"
        title="Price Tier Comparison"
      >
        <template #header-extra>
          <NSelect
            v-model:value="metricValue"
            class="max-w-110px"
            :options="metricOptions"
            :consistent-menu-width="false"
          />
        </template>
        <NSpin v-if="chartLoading"></NSpin>
        <VBarChart v-else style="height: 300px" :chart-options="chartData" />
      </NCard>
    </NGi>
  </NGrid>
</template>

<style scoped></style>
