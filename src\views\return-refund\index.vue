<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import dayjs from 'dayjs';
import { fetchGetRRByDate } from '@/service/api';
import { useUserStore } from '@/store/modules/user';
import { NumeralFormat } from '@/enum';
import ShopInfoCard from '../dashboard/modules/shop-info-card.vue';
import OrdersProportionCard from './modules/orders-proportion-card.vue';
import ReasonProportionCard from './modules/reason-proportion-card.vue';
import OrderDetailCard from './modules/order-detail-card.vue';
import ProductDistributionCard from './modules/product-distribution-card.vue';

const growthMetrics = reactive<CommonType.Component.Metric<keyof Api.ReturnRefund.OverviewResponse>[]>([
  {
    key: 'returnTotalNum',
    title: 'R&R Orders',
    description: 'The total sales revenue for the current month.',
    value: 0,
    unit: NumeralFormat.Number,
    decimals: 2,
    icon: 'solar:document-bold-duotone',
    isConversion: false,
    prevValue: 0
  },
  {
    key: 'returnAndRefundNum',
    title: 'Return and Refund',
    description: '',
    value: 0,
    unit: NumeralFormat.Number,
    decimals: 2,
    icon: 'solar:repeat-bold-duotone',
    isConversion: false,
    prevValue: 0
  },
  {
    key: 'refundNum',
    title: 'Refund Only',
    description: '',
    value: 0,
    unit: NumeralFormat.Number,
    decimals: 2,
    icon: 'solar:card-recive-bold-duotone',
    isConversion: false,
    prevValue: 0
  },
  {
    key: 'returnAmount',
    title: 'R&R Order Amount',
    description:
      'This metric represents the cumulative monetary value of all return orders, including both refunds and returns with refunds, within the specified period.',
    value: 0,
    unit: NumeralFormat.Dollar,
    decimals: 2,
    icon: 'solar:dollar-minimalistic-bold-duotone',
    isConversion: false,
    prevValue: 0
  }
]);

const [loading, toggleLoading] = useToggle(false);

const searchParams = ref<Api.ReturnRefund.OverviewSearchParams>({
  startTimeStamp: 0,
  endTimeStamp: 0,
  shopId: 0
});

const ordersData = reactive<Pick<Api.ReturnRefund.OverviewResponse, 'affiliateTotalOrderNum' | 'returnTotalNum'>>({
  affiliateTotalOrderNum: 0,
  returnTotalNum: 0
});

const reasonsData = ref<{ type: string; value: number }[]>([]);

const returnProductData = ref<Api.ReturnRefund.ReturnProduct[]>([]);

// const orderSourceData = ref<Api.ReturnRefund.OrderSource[]>([]);

const userStore = useUserStore();
const shopOptions = ref<CommonType.Option<number>[]>([]);

// Optimize store options initialization function
async function initShopOptions() {
  try {
    // Show loading status
    toggleLoading(true);

    // Get user store data
    const shops = await userStore.getUserShop();

    // Check if there is data, if not, force refresh to get
    if (!shops || shops.length === 0) {
      await userStore.getUserShop(true); // Force refresh to obtain data
    }

    shopOptions.value = userStore.userShops
      .filter(item => item.isApiAuthorized)
      .map(item => ({ label: item.shopName, value: item.shopId }));

    // If a store is available, automatically select the first one
    if (shopOptions.value.length) {
      searchParams.value.shopId = shopOptions.value[0].value;
    } else {
      window.$message?.warning('No authorized store found');
    }
  } catch (error) {
    console.error('Initialization of the store option failed:', error);
    window.$message?.error('Failed to obtain store information');
  } finally {
    toggleLoading(false);
  }
}

onMounted(() => {
  initShopOptions();
});

async function initData(params: Api.ReturnRefund.OverviewSearchParams) {
  toggleLoading(true);
  const { data, error } = await fetchGetRRByDate(params);
  if (!error) {
    // total metrics
    growthMetrics.forEach(metric => {
      metric.value = (data[metric.key] as number) || 0;
    });

    // Returns/Refunds Orders Proportion
    ordersData.affiliateTotalOrderNum = data.affiliateTotalOrderNum || 0;
    ordersData.returnTotalNum = data.returnTotalNum || 0;

    // Returns/Refunds Resons Proportion
    reasonsData.value = data.returnReason.length ? data.returnReason.map(v => ({ type: v.reason, value: v.num })) : [];

    // Product Distribution in R&R Orders
    returnProductData.value = data.returnProduct || [];

    // Returns/Refunds Order Source
    // orderSourceData.value = data.orderSourceList || [];
  }

  toggleLoading(false);
}

function handleDateUpdate(value: [number, number] | null) {
  if (value) {
    searchParams.value.startTimeStamp = value[0];
    searchParams.value.endTimeStamp = value[1];
  }
}

onMounted(() => {
  initShopOptions();
});

watch(
  () => searchParams.value,
  newVal => {
    if (newVal.shopId && newVal.startTimeStamp && newVal.endTimeStamp) initData(newVal);
  },
  {
    deep: true
  }
);
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="card-wrapper" :bordered="false" title="Returns/Refunds Management">
      <template #header-extra>
        <NPopselect v-model:value="searchParams.shopId" :disabled="shopOptions.length <= 1" :options="shopOptions">
          <NButton quaternary>
            <div class="flex items-center justify-end">
              <ShopInfoCard :id="searchParams.shopId" />
              <icon-solar:alt-arrow-down-line-duotone v-if="shopOptions.length > 1" />
            </div>
          </NButton>
        </NPopselect>
      </template>
    </NCard>
    <NCard>
      <NFlex justify="flex-end">
        <ButtonDate
          :default-value="-1"
          :start-time="dayjs().year(2024).month(11).date(31).format()"
          :end-time="dayjs.tz(Date.now(), 'Etc/GMT+8').format()"
          @update:unix="handleDateUpdate"
        />
      </NFlex>
    </NCard>
    <NSpin v-if="loading" class="flex-1"></NSpin>
    <NFlex v-else vertical :size="16">
      <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
        <NGi v-for="metric in growthMetrics" :key="metric.key" span="6">
          <IndicatorCard :metric="metric" />
        </NGi>
      </NGrid>
      <NGrid :cols="2" :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
        <NGi>
          <OrdersProportionCard :data="ordersData" />
        </NGi>
        <NGi>
          <ReasonProportionCard :data="reasonsData" />
        </NGi>
      </NGrid>
      <ProductDistributionCard :data="returnProductData" />
      <!-- <OrderSourceCard :data="orderSourceData" /> -->
      <OrderDetailCard :params="searchParams" />
    </NFlex>
  </NFlex>
</template>

<style scoped></style>
