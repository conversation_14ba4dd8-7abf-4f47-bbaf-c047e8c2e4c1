<script lang="tsx" setup>
import { computed, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { NAvatar, NButton, NEllipsis, NGi, NGrid, NImage, NTag } from 'naive-ui';
import dayjs from 'dayjs';
import { isNumber, uniqBy } from 'lodash-es';
import {
  fetchExportProdcuctTrackingReport,
  fetchGetProductTrackingData,
  fetchGetProductsAnalysisByCreatorPerformance,
  fetchUpdateProductGoal,
  fetchUpdateProductShortName
} from '@/service/api';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { useAuth } from '@/hooks/business/auth';
import { LinkToCreator, LinkToProduct, LinkToVideo } from '@/utils/tiktok-link';
import { getFallbackImage } from '@/utils/fake-image';
import { downloadFile } from '@/utils/download';
import { getDateWeekOrMonth } from '@/utils/date';
import ButtonCopy from '@/components/custom/button-copy.vue';
import { NumeralFormat, TimeFormat } from '@/enum';
import SvgIcon from '@/components/custom/svg-icon.vue';
import UpdateCell from './update-cell.vue';

const { VITE_PRODUCT_AVATAR_URL, VITE_CREATOR_AVATAR_URL, VITE_TODAY_VIDEO_AVATAR_URL } = import.meta.env;

type TableData = Api.CreatorPerformance.ProductAnalysisData;

interface Props {
  searchParams: Api.CreatorPerformance.CreatorPerformanceSearchParams;
  shopOptions: CommonType.Option<number>[];
  subBrand?: boolean;
}

const props = defineProps<Props>();
const { numberFormat } = useNumberFormat();
const { hasAuth } = useAuth();

const tableSearchParams = ref<Api.CreatorPerformance.ProductsAnalysisSearchParams>(createDefaultSearchParams());
const period = computed(() => {
  return `${tableSearchParams.value.startDateStr}-${tableSearchParams.value.endDateStr}`;
});

// const subBrandOptions = ref<CommonType.Option<string>[]>([]);
const tableData = ref<TableData[]>([]);
const [loading, toggleLoading] = useToggle(false);
const columns = computed<NaiveUI.DataTableColumns<TableData>>(() => {
  const isOperator = hasAuth('creator-performance:operator');

  const arr: NaiveUI.DataTableColumns<TableData> = [
    {
      key: 'productId',
      title: 'Product',
      render(rowData) {
        const src = `${VITE_PRODUCT_AVATAR_URL}${rowData.productAvatarLocal}`;
        return (
          <div
            class="flex-y-center gap-2 hover:(cursor-pointer text-primary)"
            onClick={() => handleLink('product', rowData.productId)}
          >
            <div class="h-50px w-50px flex-shrink-0 overflow-hidden border rounded">
              <NImage src={src} fallbackSrc={getFallbackImage(50, 50)} />
            </div>
            <div class="flex-col">
              <NEllipsis line-clamp={1} tooltip={{ contentStyle: 'max-width:400px;' }}>
                {rowData.productName || '-'}
              </NEllipsis>
              <div class="flex-y-center">
                <NEllipsis class="text-coolgray" line-clamp={2} tooltip={{ contentStyle: 'max-width:400px;' }}>
                  PID:{rowData.productId}
                </NEllipsis>
                <ButtonCopy copy={rowData.productId} />
              </div>
            </div>
          </div>
        );
      }
    },
    {
      key: 'targetCount',
      title: 'Targeted Invitations',
      align: 'center'
    },
    {
      key: 'quantity',
      title: 'Free Sample Quantity',
      align: 'center'
    },
    {
      key: 'quantity',
      title: 'Sample Sent Rate',
      align: 'center',
      render(rowData) {
        const rate = numberFormat(rowData.quantity / rowData.targetCount || 0, NumeralFormat.Percent);
        return rate;
      }
    },
    {
      key: 'shopName',
      title: 'Status Tracking',
      width: 150,
      align: 'center',
      render(rowData) {
        return (
          <NButton text type="primary" onClick={() => handleShowModal(rowData.productId)}>
            View
          </NButton>
        );
      }
    }
  ];

  if (isOperator) {
    // Add after the array index is 2
    arr.splice(2, 0, {
      key: 'goal',
      title: 'Target Samples',
      align: 'center',
      render(rowData) {
        return (
          <UpdateCell
            onlyNumber
            value={rowData.goal}
            updateFn={v => handleUpdateGoal(rowData.productId, v as string)}
          />
        );
      }
    });
    arr.splice(1, 0, {
      key: 'productShortName',
      title: 'Product Short Name',
      align: 'center',
      render(rowData) {
        return (
          <UpdateCell
            inputProps={{ type: 'textarea', maxlength: 200, showCount: true }}
            value={rowData.productShortName}
            updateFn={v => handleUpdateProductShortName(rowData.productId, v as string)}
          />
        );
      }
    });
  }

  return arr;
});

const [modalShow, toggleModalShow] = useToggle(false);
const [tableLoading, toggleTableLoading] = useToggle(false);
const filterParams = ref({
  creatorId: null,
  status: null
});

const modalData = ref<Api.CreatorPerformance.ProductTrackingResponse>([]);
const showModalData = ref<Api.CreatorPerformance.ProductTrackingData[]>([]);

const creatorsOptions = computed(() => {
  const res = modalData.value.map(v => ({
    label: v.creatorId,
    value: v.creatorId,
    icon: v.creatorAvatarLocal
  }));
  return uniqBy(res, 'value');
});

const statusOptions = computed(() => {
  const res = modalData.value.map(v => ({
    label: v.status,
    value: v.status
  }));
  return uniqBy(res, 'value');
});

const modalColumns = ref<NaiveUI.DataTableColumns<Api.CreatorPerformance.ProductTrackingData>>([
  {
    key: 'creatorId',
    title: 'Creator',
    width: 200,
    render(rowData) {
      const avatarUrl = `${VITE_CREATOR_AVATAR_URL}${rowData.creatorAvatarLocal}`;
      const followers = rowData.followers || '-';
      return (
        <div
          class="flex-y-center gap-2 hover:(cursor-pointer text-primary)"
          onClick={() => handleLink('creator', rowData.creatorId)}
        >
          <NAvatar class="flex-shrink-0" src={avatarUrl} fallbackSrc={getFallbackImage(50, 50)} />
          <div class="flex-col">
            <NEllipsis line-clamp={1}>{rowData.creatorId}</NEllipsis>
            <span class="text-coolgray">Followers: {followers}</span>
          </div>
        </div>
      );
    }
  },
  {
    key: 'salesVolume',
    title: 'Sales Volume',
    align: 'center'
  },
  {
    key: 'gpm',
    title: 'GPM',
    align: 'center'
  },
  {
    key: 'videoId',
    title: 'Video Or Live',
    width: 300,
    titleAlign: 'center',
    render(rowData) {
      if (rowData.status.toLowerCase().includes('live')) {
        return <div class="flex-center">LIVE</div>;
      }

      if (rowData.status.toLowerCase().includes('video')) {
        const gmvTotal = numberFormat(rowData.gmv, NumeralFormat.Dollar);
        const itemsSold = numberFormat(rowData.itemsSold, NumeralFormat.Number);
        const postTime = dayjs.unix(rowData.postTimestamp).tz('Etc/GMT+8').format(TimeFormat.US_TIME_24);
        return (
          <div class="w-full flex gap-2">
            <div
              class="relative h-70px w-40px flex-shrink-0 hover:cursor-pointer"
              onClick={() => handleLink('video', rowData.creatorOecId, rowData.videoId)}
            >
              <NImage
                class="h-full w-full rounded"
                object-fit="cover"
                src={`${VITE_TODAY_VIDEO_AVATAR_URL}${rowData.videoAvatarLocal}`}
                fallbackSrc={getFallbackImage(40, 71)}
              />
              <div class="absolute inset-0 flex-center rounded bg-black/20">
                <SvgIcon icon="solar:play-circle-bold" class="text-lg text-white" />
              </div>
            </div>
            <div class="flex-col flex-nowrap items-start">
              <NEllipsis line-clamp={1} class="text-lg font-bold" tooltip={{ contentStyle: 'max-width:400px' }}>
                {rowData?.videoName || '-'}
              </NEllipsis>
              <NGrid cols={2}>
                <NGi>
                  <span class="flex-shrink-0 text-xs text-#6C6C6D font-bold">GMV: {gmvTotal}</span>
                </NGi>
                <NGi>
                  <span class="flex-shrink-0 text-xs text-#6C6C6D font-bold">Items Sold: {itemsSold}</span>
                </NGi>
              </NGrid>
              <span class="text-xs text-coolgray">Post: {postTime || '-'}</span>
            </div>
          </div>
        );
      }

      return <div class="flex-center">-</div>;
    }
  },

  {
    key: 'status',
    title: 'Status',
    align: 'center',
    width: 150,
    render(rowData) {
      return (
        <NTag size="small" bordered={false}>
          {rowData.status}
        </NTag>
      );
    }
  },
  {
    key: 'orderTime',
    title: 'Order Time',
    align: 'center',
    width: 250,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width:400px;'
      }
    },
    render(rowData) {
      return dayjs.unix(rowData.orderTimestamp).tz('Etc/GMT+8').format(TimeFormat.US_TIME_24);
    }
  }
]);

function handleLink(type: 'product' | 'video' | 'creator' | 'normal', id1?: string, id2?: string) {
  switch (type) {
    case 'product':
      LinkToProduct(id1);
      break;
    case 'creator': {
      LinkToCreator(id1);
      break;
    }
    case 'video':
      LinkToVideo(id1, id2);
      break;
    case 'normal':
      window.open(id1, '_blank');
      break;
    default:
  }
}

function createDefaultSearchParams(): Api.CreatorPerformance.ProductsAnalysisSearchParams {
  return {
    brand: '',
    shopIdsArr: [],
    startDateStr: '',
    endDateStr: ''
  };
}

async function handleShowModal(productId: string) {
  filterParams.value = {
    creatorId: null,
    status: null
  };

  toggleModalShow(true);
  toggleTableLoading(true);

  const { data: trackingData, error: trackingErr } = await fetchGetProductTrackingData({
    ...props.searchParams,
    productId
  });
  if (!trackingErr) {
    modalData.value = trackingData;
    showModalData.value = trackingData;
  }
  toggleTableLoading(false);
}

function renderCell(value: any, _rowData: any, column: any) {
  if (!isNumber(value)) return value;
  if (column.unit) {
    return numberFormat(value, column.unit);
  } else if (typeof value === 'number') {
    return numberFormat(value, NumeralFormat.Number);
  }
  return value;
}

async function handleUpdateGoal(productId: string, goal: string) {
  const query: Api.CreatorPerformance.ProductGoalParams = {
    goal,
    period: period.value,
    productId,
    shopId: tableSearchParams.value.shopIdsArr[0]
  };
  const { error: updateErr } = await fetchUpdateProductGoal(query);
  if (!updateErr) {
    window.$message?.success('Update Successfully.');
    initTableData(tableSearchParams.value);
  }
}

async function handleUpdateProductShortName(productId: string, shortName: string) {
  const query: Api.CreatorPerformance.ChangeShortNameParams = {
    productShortName: shortName,
    productId
  };
  const { error: updateErr } = await fetchUpdateProductShortName(query);
  if (!updateErr) {
    window.$message?.success('Update Successfully.');
    initTableData(tableSearchParams.value);
  }
}

async function handleExportProductTrackingReport() {
  const { shopIdsArr, startDateStr, endDateStr } = tableSearchParams.value;
  const { data: reportData, error: reportErr } = await fetchExportProdcuctTrackingReport({
    startDateStr,
    endDateStr,
    shopIdsArr
  });
  if (!reportErr) {
    const shopName = props.shopOptions.find(v => v.value === shopIdsArr[0])?.label;
    const date = getDateWeekOrMonth(startDateStr, endDateStr);
    downloadFile(reportData, 'xlsx', `${shopName} affiliate sample tracker-${date}`, false);
  }
}

// async function initSubBrandOptions() {
//   const { data: subBrandOpts, error: subBrandErr } = await fetchGetSubBrandShopOptions(tableSearchParams.value);

//   if (!subBrandErr) {
//     subBrandOptions.value = [{ label: 'All', value: '' }].concat(subBrandOpts.map(v => ({ label: v, value: v })));
//   }
// }

async function initTableData(params: Api.CreatorPerformance.CreatorPerformanceSearchParams) {
  toggleLoading(true);
  const { data, error } = await fetchGetProductsAnalysisByCreatorPerformance({ ...params, period: period.value });
  if (!error) {
    tableData.value = data;
  }
  toggleLoading(false);
}

watch(
  () => props,
  newVal => {
    if (!newVal) return;

    tableSearchParams.value = { ...newVal.searchParams, brand: '' };

    // if (newVal.subBrand) {
    //   initSubBrandOptions();
    // }
  },
  {
    immediate: true,
    deep: true
  }
);

watch(
  () => tableSearchParams.value,
  newVal => {
    if (!newVal) return;

    if (!newVal.shopIdsArr.length) return;

    if (!newVal.startDateStr || !newVal.endDateStr) return;

    initTableData(newVal);
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => filterParams.value,
  newVal => {
    if (!newVal) return;
    showModalData.value = modalData.value.filter(v => {
      if (newVal.creatorId && v.creatorId !== newVal.creatorId) return false;
      if (newVal.status && v.status !== newVal.status) return false;
      return true;
    });
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" title="Products Analysis">
    <template #header-extra>
      <div class="flex-y-center justify-end gap-2">
        <!--
 <NSelect
          v-if="subBrand"
          v-model:value="tableSearchParams.brand"
          class="w-150px"
          :consistent-menu-width="false"
          :options="subBrandOptions"
          placeholder="Sub Brand"
        ></NSelect>
-->
        <ButtonIcon
          icon="solar:download-linear"
          tooltip-content="Export"
          tooltip-placement="top"
          @click="handleExportProductTrackingReport"
        />
      </div>
    </template>
    <NDataTable
      class="h-400px"
      :loading="loading"
      :bordered="false"
      flex-height
      :data="tableData"
      :columns="columns"
    ></NDataTable>
    <NModal
      v-model:show="modalShow"
      class="min-w-1200px"
      content-class="flex-col gap-4"
      preset="dialog"
      title="Status Tracking"
      :show-icon="false"
      closable
    >
      <div class="flex-y-center gap-2">
        <NSelect
          v-model:value="filterParams.creatorId"
          class="w-200px"
          filterable
          :consistent-menu-width="false"
          :options="creatorsOptions"
          placeholder="Creator ID"
          clearable
        ></NSelect>
        <NSelect
          v-model:value="filterParams.status"
          class="w-200px"
          filterable
          :consistent-menu-width="false"
          :options="statusOptions"
          placeholder="Status"
          clearable
        ></NSelect>
      </div>
      <NDataTable
        class="h-600px"
        flex-height
        :data="showModalData"
        :columns="modalColumns"
        :loading="tableLoading"
        :render-cell="renderCell"
      ></NDataTable>
    </NModal>
  </NCard>
</template>
