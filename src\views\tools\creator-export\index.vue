<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-23 09:39:15
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-07 17:46:12
 * @FilePath: \tiksage-frontend\src\views\tools\creator-export\index.vue
 * @Description: creator-export
-->
<script setup lang="tsx">
import { onMounted, onUnmounted } from 'vue';
import { useIntervalFn, useToggle } from '@vueuse/core';
import { NFlex } from 'naive-ui';
import dayjs from 'dayjs';
import {
  fetchDeleteById,
  fetchDownloadRunCreatorsExportTaskExcel,
  fetchGetCreatorsExportTaskList,
  fetchRunCreatorsExportTask
} from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { downloadFile } from '@/utils/download';
import ButtonIcon from '@/components/custom/button-icon.vue';
import TaskProgress from '@/views/analysis/list/modules/task-progress.vue';
import { TaskStatus, TimeFormat } from '@/enum';
import CreateReportDrawer from './modules/create-report-drawer.vue';

const [createShow, toggleCreateShow] = useToggle(false);

const { data, getData, columns, pagination, loading } = useTable({
  immediate: false,
  apiFn: fetchGetCreatorsExportTaskList,
  apiParams: {
    current: 1,
    size: 10
  },
  columns() {
    return [
      {
        key: 'id',
        title: 'Task ID',
        align: 'center',
        width: 100
      },
      {
        key: 'createTime',
        title: 'Task Create Time',
        align: 'center',
        render(rowData) {
          return dayjs(rowData.createTime).format(TimeFormat.US_TIME_24);
        }
      },
      {
        key: 'description',
        title: 'Description',
        align: 'center',
        ellipsis: {
          tooltip: true
        }
      },
      {
        key: 'grabTaskStatus',
        title: 'Status',
        align: 'center',
        fixed: 'right',
        width: 150,
        render(rowData) {
          return <TaskProgress taskStatus={rowData.grabTaskStatus} />;
        }
      },
      {
        key: 'operate',
        title: 'Operation',
        align: 'center',
        fixed: 'right',
        width: 160,
        render(rowData) {
          return (
            /* eslint-disable no-nested-ternary */
            <NFlex align="center" size={0}>
              <ButtonIcon
                type="primary"
                icon="tabler:player-play"
                tooltipPlacement="top"
                tooltipContent="Run"
                disabled={rowData.grabTaskStatus === TaskStatus.RUNNING}
                onClick={() => {
                  handleRun(rowData.id);
                }}
              />

              <ButtonIcon
                type="error"
                icon="solar:trash-bin-2-linear"
                tooltipPlacement="top"
                tooltipContent="Delete"
                disabled={rowData.grabTaskStatus === TaskStatus.RUNNING}
                onClick={() => {
                  handleDelete(rowData.id);
                }}
              />

              {rowData.grabTaskStatus === TaskStatus.SUCCESS ? (
                <ButtonIcon
                  icon="solar:download-linear"
                  tooltipPlacement="top"
                  tooltipContent="Export"
                  onClick={() => {
                    handleExport(rowData.id);
                  }}
                />
              ) : (
                <div class="h-36px"></div>
              )}
            </NFlex>
            /* eslint-enable no-nested-ternary */
          );
        }
      }
    ];
  }
});

async function handleRun(id: number) {
  const { error } = await fetchRunCreatorsExportTask(id);
  if (error) return;
  window.$message?.success('Run Success.');
  getData();
}

async function handleDelete(id: number) {
  const { error } = await fetchDeleteById(id);
  if (error) return;
  window.$message?.success('Delete Success.');
  getData();
}

async function handleExport(id: number) {
  const { data: fileData, error } = await fetchDownloadRunCreatorsExportTaskExcel(id);
  if (error) return;
  downloadFile(fileData, 'xlsx', 'Creator Data Fetch');
}

const { pause, resume } = useIntervalFn(
  () => {
    getData();
  },
  1000 * 30,
  {
    immediateCallback: true
  }
);

onMounted(() => {
  resume();
});

onUnmounted(() => {
  pause();
});
</script>

<template>
  <NFlex vertical>
    <NCard class="h-full min-h-400px" title="Creator Export" content-class="flex-col">
      <template #header-extra>
        <div class="flex items-center justify-end gap-4">
          <NButton strong secondary @click="toggleCreateShow(true)">
            <template #icon>
              <icon-solar:add-circle-linear />
            </template>
            Create Task
          </NButton>
          <ButtonBack />
        </div>
      </template>
      <NDataTable
        remote
        class="flex-1"
        :bordered="false"
        size="small"
        flex-height
        :loading="loading"
        :columns="columns"
        :data="data"
        :pagination="pagination"
      />
    </NCard>
    <CreateReportDrawer v-model:show="createShow" @submit="getData()" />
  </NFlex>
</template>

<style scoped></style>
