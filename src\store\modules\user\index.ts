/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-15 16:39:04
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-07-18 01:17:32
 * @FilePath: \tiksage-frontend\src\store\modules\user\index.ts
 * @Description:
 */
import { ref } from 'vue';
import { defineStore } from 'pinia';
import { fetchGetOwnerShop } from '@/service/api';
import { SetupStoreId } from '@/enum';

export const useUserStore = defineStore(SetupStoreId.User, () => {
  const userShops = ref<Api.User.Shop[]>([]);
  const hasLoaded = ref(false);

  /**
   * Get user store data
   *
   * @param force Whether to force refresh data
   * @returns User store data
   */
  const getUserShop = async (force = false) => {
    // If it is loaded and does not force refresh, the existing data will be returned directly
    if (hasLoaded.value && !force) return userShops.value;

    try {
      const { data, error } = await fetchGetOwnerShop();

      if (!error && data) {
        userShops.value = data;
        hasLoaded.value = true;
      }

      return userShops.value;
    } catch (err) {
      console.error(err);
      return [];
    }
  };

  getUserShop();

  return {
    userShops,
    getUserShop,
    hasLoaded
  };
});
