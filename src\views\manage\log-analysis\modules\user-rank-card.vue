<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-29 15:46:17
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-09-03 14:20:05
 * @FilePath: \tiksage-frontend\src\views\manage\log-analysis\modules\user-rank-card.vue
 * @Description: user-rank-card
-->
<script setup lang="tsx">
import { reactive } from 'vue';
import type { PaginationProps } from 'naive-ui';

interface Props {
  data: Api.SystemManage.UserFrequency[];
}

defineProps<Props>();

const columns: NaiveUI.DataTableBaseColumn<Api.SystemManage.UserFrequency>[] = [
  {
    key: 'accountId',
    width: 180,
    fixed: 'left',
    title: 'Account ID'
  },
  {
    key: 'userName',
    width: 180,
    fixed: 'left',
    title: 'Account Name'
  },
  {
    key: 'loginCount',
    width: 180,
    title: 'Login Count'
  },
  {
    key: 'home',
    width: 180,
    title: 'Page-Dashboard',
    render(rowData) {
      return rowData.userPageCountList.find(p => p.pageName === 'home')?.count || 0;
    }
  },
  {
    key: 'creator-market',
    width: 180,
    title: 'Page-Creator Market',
    render(rowData) {
      return rowData.userPageCountList.find(p => p.pageName === 'creator-market')?.count || 0;
    }
  },
  {
    key: 'category-intelligence',
    width: 220,
    title: 'Page-Category Intelligence',
    render(rowData) {
      return rowData.userPageCountList.find(p => p.pageName === 'category-intelligence')?.count || 0;
    }
  },
  {
    key: 'analysis',
    width: 180,
    title: 'Page-Create Analysis',
    render(rowData) {
      return rowData.userPageCountList.find(p => p.pageName === 'analysis')?.count || 0;
    }
  },
  {
    key: 'analysis_list',
    width: 180,
    title: 'Page-Analyzer History',
    render(rowData) {
      return rowData.userPageCountList.find(p => p.pageName === 'analysis_list')?.count || 0;
    }
  },
  {
    key: 'analysis_report',
    width: 180,
    title: 'Page-Analyzer Report',
    render(rowData) {
      return rowData.userPageCountList.find(p => p.pageName === 'analysis_report')?.count || 0;
    }
  }
];

const pagination = reactive<PaginationProps>({
  showSizePicker: true,
  pageSizes: [10, 15, 20, 25, 30]
});
</script>

<template>
  <NCard class="card-wrapper" :border="false" title="Account Activity Ranking">
    <NDataTable :columns="columns" :data="data" scroll-x="1660" :pagination="pagination"></NDataTable>
  </NCard>
</template>

<style scoped></style>
