<script setup lang="ts">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import ShopInfoCard from './shop-info-card.vue';
import ShopDetailDrawer from './shop-detail-drawer.vue';
import IndicatorItem from './indicator-item.vue';
import ProductInfoCard from './product-info-card.vue';

interface Props {
  isExport?: boolean;
  data: Api.CategoryLeaders.TopGmvShopListResponse[];
}

defineProps<Props>();

type MonthIndicator = {
  title: string;
  key: keyof Api.CategoryLeaders.LeaderShopMonth;
  growthKey: keyof Api.CategoryLeaders.LeaderShopMonth;
  value: string;
};

const monthIndicators: MonthIndicator[] = [
  {
    title: 'GMV',
    key: 'gmv',
    growthKey: 'gmvGrowth',
    value: '$0'
  },
  {
    title: 'Units Sold',
    key: 'sold',
    growthKey: 'soldGrowth',
    value: '0'
  },
  {
    title: 'Average Price',
    key: 'price',
    growthKey: 'priceGrowth',
    value: '$0'
  },
  {
    title: 'Lives',
    key: 'lives',
    growthKey: 'livesGrowth',
    value: '0'
  },
  {
    title: 'Videos',
    key: 'videos',
    growthKey: 'videosGrowth',
    value: '0'
  },
  {
    title: 'Affiliates',
    key: 'affiliates',
    growthKey: 'affiliatesGrowth',
    value: '0'
  }
];

const [visible, toggleVisible] = useToggle(false);
const currentId = ref<number>();

const handleShowDetail = (id: number) => {
  currentId.value = id;
  toggleVisible(true);
};
</script>

<template>
  <NCard class="card-wrapper" content-class="flex-col " :bordered="false" title="Top 10 Shops by Monthly GMV">
    <!-- <NScrollbar content-class="divide-y" style="max-height: 1200px"> -->
    <div
      v-for="item in data"
      :key="item.id"
      class="flex-col gap-16px rounded-xl bg-white p-12px hover:(cursor-pointer bg-gray-100)"
      @click="handleShowDetail(item.id)"
    >
      <NGrid class="border rounded bg-white py-3px" cols="3" :x-gap="3" :y-gap="0">
        <NGi>
          <ShopInfoCard class="h-full" :idx="item.sort" :data="item" />
        </NGi>
        <NGi class="flex-center">
          <NGrid class="" :cols="3" x-gap="8" y-gap="8">
            <NGi v-for="column in monthIndicators" :key="column.key">
              <IndicatorItem
                style="background: #f3f4f6"
                :title="column.title"
                :value="item.shopMonthData?.[column.key] || column.value"
                show-percent
                :percent="item.shopMonthData?.[column.growthKey] || '0%'"
                size="small"
                vertical
              />
            </NGi>
          </NGrid>
        </NGi>
        <NGi>
          <ProductInfoCard class="h-full" :data="item.shopProductData" :is-export="isExport" />
        </NGi>
      </NGrid>
    </div>
    <!-- </NScrollbar> -->
    <ShopDetailDrawer :id="currentId" v-model:visible="visible" />
  </NCard>
</template>

<style scoped></style>
