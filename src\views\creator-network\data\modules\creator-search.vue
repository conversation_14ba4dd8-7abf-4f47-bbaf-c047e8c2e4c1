<script setup lang="ts"></script>

<template>
  <div class="flex-col gap-16px">
    <NGrid :x-gap="16" :y-gap="16" :cols="5" item-responsive responsive="self">
      <NGi span="2">
        <NInput clearable placeholder="Creator ID/Name">
          <template #prefix>
            <icon-tabler:search />
          </template>
        </NInput>
      </NGi>
      <NGi class="flex items-center" offset="1" span="2">
        <NCheckbox>Show Only Email-Accessible Creators</NCheckbox>
      </NGi>
    </NGrid>
    <NGrid :x-gap="16" :y-gap="16" :cols="5" item-responsive responsive="self">
      <NGi>
        <NSelect clearable placeholder="Category"></NSelect>
      </NGi>
      <NGi>
        <NSelect clearable placeholder="GMV (Past 30 Days)"></NSelect>
      </NGi>
      <NGi>
        <NSelect clearable placeholder="Followers"></NSelect>
      </NGi>
      <NGi>
        <NSelect clearable placeholder="Follower Gender"></NSelect>
      </NGi>
      <NGi>
        <NSelect clearable placeholder="Follower Age"></NSelect>
      </NGi>
    </NGrid>
  </div>
</template>

<style scoped></style>
