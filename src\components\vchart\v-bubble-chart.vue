<script setup lang="ts">
import { onBeforeUnmount, onMounted, onUpdated, ref } from 'vue';
import { merge } from 'lodash-es';
import { VChart } from '@visactor/vchart';
import { theme } from '@/constants/visactor-vchart';

interface Props {
  chartOptions?: Visactor.VChart.IBarChartSpec;
}
const props = defineProps<Props>();

let chart: Visactor.VChart.IVChart;

const chartContainer = ref<HTMLDivElement>();

function parseSpec(chartProps: Props) {
  const baseOptions: Visactor.VChart.IBarChartSpec = {
    type: 'bar',
    data: {
      values: []
    },
    stack: true,
    legends: [{ visible: true, position: 'middle', orient: 'top' }],
    ...theme
  };
  const { chartOptions } = chartProps;
  return chartOptions ? merge(baseOptions, chartProps.chartOptions) : baseOptions;
}

function createOrUpdateChart(chartProps: Props) {
  if (chartContainer.value && !chart) {
    chart = new VChart(parseSpec(chartProps), {
      dom: chartContainer.value
    });

    chart.renderSync();
    return true;
  } else if (chart) {
    chart.updateSpec(parseSpec(chartProps));
    chart.renderSync();

    return true;
  }
  return false;
}

onMounted(() => {
  createOrUpdateChart(props);
});

onUpdated(() => {
  createOrUpdateChart(props);
});

onBeforeUnmount(() => {
  if (chart) {
    chart.release();
  }
});
</script>

<template>
  <div ref="chartContainer" class="h-400px w-full"></div>
</template>

<style scoped></style>
