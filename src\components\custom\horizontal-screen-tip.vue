<script setup lang="ts"></script>

<template>
  <NFlex
    vertical
    :size="16"
    justify="center"
    align="center"
    class="pointer-events-none fixed z-9999 h-full w-full bg-dark/90 p-20px text-xl text-white"
  >
    <icon-material-symbols:screen-rotation-rounded class="text-40px" />
    <NText class="text-white">Rotate to landscape for better view.</NText>
  </NFlex>
</template>

<style scoped></style>
