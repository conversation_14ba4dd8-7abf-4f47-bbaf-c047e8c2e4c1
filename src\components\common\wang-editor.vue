<script setup lang="ts">
import '@wangeditor-next/editor/dist/css/style.css'; // 引入 css
import { computed, onBeforeUnmount, ref, shallowRef } from 'vue';
import { Editor, Toolbar } from '@wangeditor-next/editor-for-vue';
import type { IToolbarConfig } from '@wangeditor-next/editor';

interface Props {
  readonly?: boolean;
  toolbarConfig?: Partial<IToolbarConfig>;
}

const props = defineProps<Props>();

// Editor instance, must be used with shallowref
const editorRef = shallowRef();

// Content HTML
const valueHtml = defineModel('valueHtml', {
  type: String,
  required: true
});

const mode = ref('simple');

const defaultToolbarConfig: Partial<IToolbarConfig> = {
  excludeKeys: ['uploadImage'],
  insertKeys: {
    index: 99,
    keys: ['|', 'nickname']
  }
};

const realToolbarConfig = computed(() => {
  return props.toolbarConfig || defaultToolbarConfig;
});

const editorConfig = computed(() => {
  return {
    readOnly: props.readonly || false,
    placeholder: 'Please enter content...'
  };
});

// When the component is destroyed, the editor is also destroyed in time
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor === null) return;
  editor.destroy();
});

const handleCreated = (editor: any) => {
  editorRef.value = editor; // Record the Editor instance, important!
};
</script>

<template>
  <div class="border-1px rounded p-3px">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :default-config="realToolbarConfig"
      :mode="mode"
    />
    <Editor
      v-model="valueHtml"
      style="height: 400px; overflow-y: hidden"
      :default-config="editorConfig"
      :mode="mode"
      @on-created="handleCreated"
    />
  </div>
</template>
