<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import dayjs from 'dayjs';
import { fetchGetCreatorPerformanceData, fetchGetLastDayByCreatorPerformance } from '@/service/api';
import SingleDatePick from '@/views/tiksage-dashboard/data-overview/modules/single-date-pick.vue';
import { TimeFormat } from '@/enum';
import MetricCard from './metric-card.vue';
import MonthlyCard from './monthly-card.vue';
import OutreachedPerfCard from './outreached-perf-card.vue';
import VideoTopCard from './video-top-card.vue';
import ProductAnalysisCard from './product-analysis-card.vue';

interface Props {
  shopIdsArr: number[] | null | undefined;
  shopOptions: CommonType.Option<number>[];
  subBrand?: boolean;
  brandList?: string[] | null;
}

const props = withDefaults(defineProps<Props>(), {
  subBrand: false
});

const subBranOptions = ref<CommonType.Option<string>[]>([]);

const searchParams = ref<Api.CreatorPerformance.CreatorPerformanceSearchParams>(createDefaultSearchParams());

const [loading, toggleLoading] = useToggle(true);
const dataRange = ref<string[]>([]);

const updateStr = computed(() => {
  if (!dataRange.value) return '';
  return `Last updated: ${dayjs.tz(dataRange.value?.[1], 'Etc/GMT+8').format(TimeFormat.US_DATE_TIMEZONE)}`;
});

const metrics = ref([
  {
    key: 'creatorOutreached',
    title: 'Creator Confirmed',
    icon: 'solar:user-check-rounded-bold-duotone',
    value: 102,
    rating: 'Normal',
    color: '#2C7ACD',
    bgColor: '#D0FBFF',
    chartColor: '#D0FBFF',
    chartValue: []
  },
  {
    key: 'freeSample',
    title: 'Free Sample',
    icon: 'solar:gift-bold-duotone',
    value: 80,
    rating: 'Normal',
    color: '#FF8F1F',
    bgColor: '#F8DEBD',
    chartColor: '#FBEEDE',
    chartValue: []
  },
  {
    key: 'videoPost',
    title: 'Video Posted',
    icon: 'solar:videocamera-record-bold-duotone',
    value: 102,
    rating: 'Normal',
    color: '#F55FED',
    bgColor: '#FFE5FF',
    chartColor: '#FFE5FF',
    chartValue: []
  }
]);

const creatorOutereachedData = ref<Api.CreatorPerformance.CreatorLevel[]>([]);

function createDefaultSearchParams(): Api.CreatorPerformance.CreatorPerformanceSearchParams {
  return {
    shopIdsArr: props.shopIdsArr || [],
    brandList: props.brandList,
    startDateStr: '',
    endDateStr: ''
  };
}

function handleDateChange(value: string[]) {
  searchParams.value.startDateStr = value[0];
  searchParams.value.endDateStr = value[1];
  initData();
}

async function initDateRange() {
  toggleLoading(true);
  let lastDay;
  const { data, error } = await fetchGetLastDayByCreatorPerformance(searchParams.value);
  if (!error) {
    lastDay = dayjs(data.endDateStr).format(TimeFormat.CN_DATE);
  } else {
    lastDay = dayjs().format(TimeFormat.CN_DATE);
  }

  dataRange.value = [dayjs().startOf('year').format(TimeFormat.CN_DATE), lastDay];
  toggleLoading(false);
}

async function initData() {
  const { data, error } = await fetchGetCreatorPerformanceData(searchParams.value);

  if (!error) {
    creatorOutereachedData.value = data.creatorLevelDTOS;
    metrics.value = metrics.value.map(m => {
      m.value = data.creatorPerformanceTotalData[m.key as keyof Api.CreatorPerformance.CreatorPerformance] as number;
      m.chartValue = data.currentRangeData.map(d => {
        return {
          x: d.belongDate,
          type: m.title,
          y: d[m.key as keyof Api.CreatorPerformance.CreatorPerformanceDaily] as number
        };
      }) as any;
      return m;
    });
  }
}

watch(
  [() => props.shopIdsArr, () => props.brandList],
  ([newShopIdsArr, newBrandList]) => {
    // shopIdsArr does not exist or exits when it is empty
    if (!newShopIdsArr || newShopIdsArr.length === 0) return;

    // When subBrand is true, brandList does not exist or exits when it is empty
    // if (newSubBrand && (!newBrandList || newBrandList.length === 0)) return;

    searchParams.value.shopIdsArr = newShopIdsArr || [];
    searchParams.value.brandList = newBrandList;

    initDateRange();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div class="h-full flex-col gap-4">
    <template v-if="!loading">
      <NCard class="card-wrapper" :bordered="false">
        <template #header>
          <NFlex align="center">
            <span>Data Overview</span>
            <NText class="text-base text-gray">{{ updateStr }}</NText>
          </NFlex>
        </template>
        <template #header-extra>
          <SingleDatePick
            :default-date-types="['week', 'month']"
            default-seleceted-type="week"
            :undisable-date-range="dataRange"
            @change="handleDateChange"
          />
        </template>
      </NCard>
      <NGrid :cols="3" :x-gap="16" :y-gap="16">
        <NGi v-for="metric in metrics" :key="metric.title">
          <MetricCard :metric="metric" />
        </NGi>
      </NGrid>
      <ProductAnalysisCard
        :search-params="searchParams"
        :sub-brand-options="subBranOptions"
        :shop-options="shopOptions"
      />
      <MonthlyCard :search-params="searchParams" />
      <OutreachedPerfCard :data="creatorOutereachedData" />
      <VideoTopCard :search-params="searchParams" :last-day="dataRange?.[1]" />
    </template>
    <NSpin v-else class="m-auto"></NSpin>
  </div>
</template>

<style scoped></style>
