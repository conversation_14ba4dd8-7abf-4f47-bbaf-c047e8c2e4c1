<script setup lang="ts">
import { onMounted, ref, watchEffect } from 'vue';
import { useTimestamp } from '@vueuse/core';
import dayjs from 'dayjs';
import { TimeFormat } from '@/enum';
import WorldTime from './modules/world-time.vue';
import TimeCopy from './modules/time-copy.vue';

const timestamp = useTimestamp({ offset: 0 });

const options: CommonType.Option<string>[] = [
  {
    label: 'UTC',
    value: 'UTC'
  },
  {
    label: 'GMT',
    value: 'GMT'
  },
  { label: 'BeiJing', value: 'Asia/Shanghai' },
  {
    label: 'EST/EDT',
    value: 'America/New_York'
  },
  {
    label: 'CST/CDT',
    value: 'America/Chicago'
  },
  {
    label: 'MST/MDT',
    value: 'America/Denver'
  },
  {
    label: 'PST/PDT',
    value: 'America/Los_Angeles'
  },
  { label: 'Pacific/Kwajalein (UTC-12:00)', value: 'Etc/GMT+12' },
  { label: 'Pacific/Midway (UTC-11:00)', value: 'Etc/GMT+11' },
  { label: 'Pacific/Honolulu (UTC-10:00)', value: 'Etc/GMT+10' },
  { label: 'America/Anchorage (UTC-09:00)', value: 'Etc/GMT+9' },
  { label: 'America/Los_Angeles (UTC-08:00)', value: 'Etc/GMT+8' },
  { label: 'America/Denver (UTC-07:00)', value: 'Etc/GMT+7' },
  { label: 'America/Chicago (UTC-06:00)', value: 'Etc/GMT+6' },
  { label: 'America/New_York (UTC-05:00)', value: 'Etc/GMT+5' },
  { label: 'America/Halifax (UTC-04:00)', value: 'Etc/GMT+4' },
  { label: 'America/Argentina/Buenos_Aires (UTC-03:00)', value: 'Etc/GMT+3' },
  { label: 'Atlantic/South_Georgia (UTC-02:00)', value: 'Etc/GMT+2' },
  { label: 'Atlantic/Azores (UTC-01:00)', value: 'Etc/GMT+1' },
  { label: 'Europe/London (UTC+00:00)', value: 'Etc/GMT+0' },
  { label: 'Europe/Berlin (UTC+01:00)', value: 'Etc/GMT-1' },
  { label: 'Europe/Athens (UTC+02:00)', value: 'Etc/GMT-2' },
  { label: 'Asia/Riyadh (UTC+03:00)', value: 'Etc/GMT-3' },
  { label: 'Asia/Dubai (UTC+04:00)', value: 'Etc/GMT-4' },
  { label: 'Asia/Karachi (UTC+05:00)', value: 'Etc/GMT-5' },
  { label: 'Asia/Dhaka (UTC+06:00)', value: 'Etc/GMT-6' },
  { label: 'Asia/Bangkok (UTC+07:00)', value: 'Etc/GMT-7' },
  { label: 'Asia/Shanghai (UTC+08:00)', value: 'Etc/GMT-8' },
  { label: 'Asia/Tokyo (UTC+09:00)', value: 'Etc/GMT-9' },
  { label: 'Australia/Sydney (UTC+10:00)', value: 'Etc/GMT-10' },
  { label: 'Pacific/Noumea (UTC+11:00)', value: 'Etc/GMT-11' },
  { label: 'Pacific/Auckland (UTC+12:00)', value: 'Etc/GMT-12' }
];

const errorMessage = ref<string>();
const inputValue = ref<string>();
const timeString = ref<string>();
const sourceTimezone = ref<string>();
const targetTimezone = ref<string>();
const convertedTime = ref<string>();

function handleTimeStringChange(value: string) {
  timeString.value = value;
}

// Validate the date format (YYYY-MM-DD HH:mm:ss)
const isValidDateFormat = (dateString: string) => {
  const regex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
  return regex.test(dateString);
};

function convert() {
  errorMessage.value = '';
  convertedTime.value = '';
  try {
    if (!timeString.value || !sourceTimezone.value || !targetTimezone.value) {
      errorMessage.value = 'Enter the date and time and select a timezone';
      return;
    }

    // Validate date format
    if (!isValidDateFormat(timeString.value)) {
      errorMessage.value = 'Invalid date format. Use YYYY-MM-DD HH:mm:ss.';
      return;
    }

    const currentTime = dayjs.tz(timeString.value, sourceTimezone.value);

    convertedTime.value = currentTime?.tz(targetTimezone.value).format(TimeFormat.CN_TIME_24_NO_TIMEZONE);

    errorMessage.value = '';
  } catch (error) {
    console.error('Conversion failed:', error);
    errorMessage.value = 'Invalid input format or timezone selection';
  }
}

onMounted(() => {
  inputValue.value = dayjs().format(TimeFormat.CN_TIME_24_NO_TIMEZONE);
  handleTimeStringChange(inputValue.value);
});

watchEffect(() => {
  convert();
});
</script>

<template>
  <NFlex vertical :size="16">
    <NCard
      class="h-full min-h-400px card-wrapper"
      content-class="flex-col justify-around gap-16px"
      :bordered="false"
      title="Timezone Converter"
    >
      <template #header-extra>
        <ButtonBack />
      </template>
      <NFlex justify="space-around" align="center" :wrap="false">
        <WorldTime :copy="dayjs(timestamp).tz('Asia/Shanghai').format(TimeFormat.CN_TIME_24)">
          Asia/Shanghai: {{ dayjs(timestamp).tz('Asia/Shanghai').format(TimeFormat.CN_TIME_24) }}
        </WorldTime>
        <WorldTime :copy="dayjs(timestamp).tz('America/Los_Angeles').format(TimeFormat.CN_TIME_24)">
          America/Los_Angeles: {{ dayjs(timestamp).tz('America/Los_Angeles').format(TimeFormat.CN_TIME_24) }}
        </WorldTime>
      </NFlex>

      <NGrid class="divide-x-2" :cols="2">
        <NGi class="flex-col">
          <NH3 prefix="line">Set Time and Timezone</NH3>

          <div vertical class="min-w-200px w-50% self-center">
            <NFormItem label="Input time">
              <!-- <NDatePicker v-model:value="timeString" type="datetime" clearable /> -->
              <NInput
                v-model:value="inputValue"
                clearable
                placeholder="Enter time (YYYY-MM-DD HH:mm:ss)"
                @change="handleTimeStringChange"
              />
            </NFormItem>
            <NFormItem label="Source time zone">
              <NSelect v-model:value="sourceTimezone" :options="options" />
            </NFormItem>
            <NFormItem label="Target time zone">
              <NSelect v-model:value="targetTimezone" :options="options" />
            </NFormItem>
          </div>
        </NGi>
        <NGi class="h-full flex-col">
          <NH3 class="m-l-16px" prefix="line">Converting result</NH3>
          <NFlex class="flex-1" vertical justify="center" align="center">
            <TimeCopy v-if="convertedTime" :title="convertedTime" />
            <NText v-if="errorMessage" class="text-16px text-warning">{{ errorMessage }}</NText>
          </NFlex>
        </NGi>
      </NGrid>
    </NCard>
  </NFlex>
</template>

<style scoped></style>
