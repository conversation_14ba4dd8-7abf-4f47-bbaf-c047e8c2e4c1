/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-14 16:09:53
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-21 15:29:42
 * @FilePath: \tiksage-frontend\src\utils\date.ts
 * @Description: util for date
 */
import dayjs from 'dayjs';
import { isNil, isNumber } from 'lodash-es';
import { TimeFormat } from '@/enum';

/**
 * Calculate the time difference between the two dates and display it with a percentage
 *
 * @param startDateStr
 * @param endDateStr
 * @param minute Difference minute
 * @returns
 */
export function calculateProgressPercentage(startDateStr: Date, endDateStr: Date, minute: number = 15) {
  const startDate = dayjs(startDateStr);
  const endDate = dayjs(endDateStr);

  // Calculate the difference between two dates (milliseconds)
  const diffInMilliseconds = endDate.diff(startDate, 'millisecond');

  // Convert the difference to second
  const diffInSeconds = diffInMilliseconds / 1000;

  // 15 minutes to a second
  const fifteenMinutesInSeconds = minute * 60;

  if (diffInSeconds < fifteenMinutesInSeconds) {
    // Percentage of calculation progress
    const progressPercentage = (diffInSeconds / fifteenMinutesInSeconds) * 100;
    return Number(Math.min(progressPercentage, 99).toFixed(2));
  }
  // If the time difference is greater than or equal to 15 minutes, you can choose to return directly to the difference in seconds or perform other treatment
  return 99;
}

export function dateFormat(time?: any) {
  if (isNil(time) || time === '') return '';

  let res: any = time;
  // dayjs.extend(utc);
  // dayjs.extend(timezone);
  if (isNumber(time) && time.toString().length === 10) {
    res = dayjs.unix(time);
  }
  return dayjs(res).tz('Etc/GMT+8').format(TimeFormat.US_TIME_24);
}

export function getDateWeekOrMonth(startDate: string, endDate: string) {
  const start = dayjs(startDate);
  const end = dayjs(endDate);
  if (start.week() === end.week() && start.year() === end.year()) {
    return `Week-${start.week()}`;
  }
  if (start.month() === end.month() && start.year() === end.year()) {
    return start.format('MMM');
  }
  return start.format('MMM YYYY');
}
