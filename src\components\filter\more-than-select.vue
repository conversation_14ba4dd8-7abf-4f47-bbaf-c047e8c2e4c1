<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { divide } from 'lodash-es';

interface Props extends /* @vue-ignore */ NaiveUI.PopselectProps {
  unit?: string;
}

const props = defineProps<Props>();
const model = defineModel('range', {
  type: String
});

const popRef = ref<any>(null);
const range = ref<string | null>(null);
const displayRange = ref<string | null>(null);

// 判断是否为百分比单位
const isPercentUnit = computed(() => props.unit === '%');

// 监听model值的变化，如果值不在options中，则设置到range中
watch(
  () => model.value,
  newVal => {
    if (!newVal) {
      range.value = null;
      displayRange.value = null;
      return;
    }

    // 检查是否在预定义选项中
    const isPresetOption = props.options?.some(opt => opt.value === newVal);
    if (!isPresetOption) {
      // 如果不在预定义选项中，将值设置到range中
      range.value = newVal;
      // 如果是百分比，显示时需要乘以100
      displayRange.value = isPercentUnit.value ? String(Number(newVal) * 100) : newVal;
    } else {
      range.value = null;
      displayRange.value = null;
    }
  },
  { immediate: true }
);

// 处理输入过滤
function handleFilterNumber(value: string | null) {
  if (!value) {
    displayRange.value = null;
    range.value = null;
    return;
  }

  // 只允许输入数字
  const reg = /^\d*$/;

  if (reg.test(value)) {
    displayRange.value = value;
    // 如果是百分比，存储时需要除以100
    range.value = isPercentUnit.value ? String(divide(Number(value), 100)) : value;
  }
}

function handleInputRange() {
  if (!range.value) return;
  model.value = range.value;
  popRef.value.setShow(false);
}

// 重置功能
function handleReset() {
  displayRange.value = null;
  range.value = null;
  model.value = undefined;
  popRef.value.setShow(false);
}
</script>

<template>
  <NPopselect ref="popRef" v-bind="props" v-model:value="model">
    <template #default>
      <slot></slot>
    </template>
    <template #action>
      <div class="flex-col gap-2">
        <div class="flex-y-center gap-2">
          <span>More than</span>
          <NInput style="width: 150px" :value="displayRange" placeholder="" @update:value="handleFilterNumber">
            <template #suffix>{{ props?.unit }}</template>
          </NInput>
        </div>
        <div class="flex items-center justify-between">
          <NButton text @click="handleReset">Reset</NButton>
          <NButton type="primary" size="small" @click="handleInputRange">OK</NButton>
        </div>
      </div>
    </template>
  </NPopselect>
</template>

<style scoped></style>
