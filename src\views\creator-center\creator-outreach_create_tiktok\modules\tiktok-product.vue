<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { NAvatar, NEllipsis, NTag } from 'naive-ui';
import { isEmpty } from 'lodash-es';
import { fetchGetProductListByCard } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import ButtonCopy from '@/components/custom/button-copy.vue';
import Tip from '@/components/custom/tip.vue';
import { NumeralFormat } from '@/enum';
import TikTokProductItem from '../../creator-outreach_create_tiktok/modules/tiktok-product-item.vue';

interface Props {
  shopId: number | null;
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const [drawShow, toggleDrawerShow] = useToggle(false);

const selectedKeys = defineModel<string[]>('value', {
  required: true
});
// 用于保存打开抽屉前的选中状态，以便取消时还原
const previousSelectedKeys = ref<string[]>([]);

const statusAttrObj: {
  [prototype: string]: {
    label: string;
    type: NaiveUI.ThemeColor;
    desc?: string;
  };
} = {
  1: {
    label: 'Available',
    type: 'success'
  },
  2: {
    label: 'Unavailable',
    type: 'error',
    desc: 'This product was removed by seller.'
  },
  3: {
    label: 'Unavailable',
    type: 'error',
    desc: "Product can't be added because it was removed."
  },
  6: {
    label: 'Unavailable',
    type: 'error',
    desc: 'This product was removed by seller.'
  }
};

const { data, columns, pagination, loading, getData, updateSearchParams } = useTable({
  immediate: false,
  apiFn: fetchGetProductListByCard,
  apiParams: {
    current: 1,
    size: 20,
    searchParams: [{ searchKey: 1, searchValue: '' }]
  },
  columns() {
    return [
      {
        type: 'selection',
        width: 50,
        align: 'center',
        disabled(rowData) {
          const {
            affiliate_product: {
              base_product: { status, product_id }
            }
          } = rowData;

          // 状态不为1的产品不可选
          if (status !== 1) return true;

          // 如果已选择的产品数量达到5个，且当前行未被选中，则禁用
          if (selectedKeys.value.length >= 5 && !selectedKeys.value.includes(product_id)) {
            return true;
          }

          return false;
        }
      },
      {
        title: 'Product Information',
        key: 1,
        width: 250,
        render(rowData) {
          const {
            affiliate_product: { base_product }
          } = rowData;
          return (
            <div class="flex gap-2">
              <NAvatar size="large" class="flex-shrink-0" src={base_product?.image_url} />
              <div class="flex-col">
                <NEllipsis class="font-500" lineClamp={1} tooltip={{ contentStyle: 'width:400px' }}>
                  {base_product?.title}
                </NEllipsis>
                <div class="flex-y-center gap-2">
                  <span class="text-coolgray">ID:{base_product.product_id}</span>
                  <ButtonCopy copy={base_product.product_id} />
                </div>
              </div>
            </div>
          );
        }
      },
      {
        title: 'Price',
        key: 2,
        width: 100,
        align: 'center',
        render(rowData) {
          const {
            affiliate_product: {
              base_product: { price }
            }
          } = rowData;
          const str =
            price.max_price === price.min_price
              ? `${price.min_price_format}${price.max_price_format}`
              : `${price.min_price_format}~${price.max_price_format}`;
          return (
            <div class="flex-center gap-2">
              <span>{str}</span>
            </div>
          );
        }
      },
      {
        title: 'Total Units Sold',
        key: 3,
        width: 100,
        align: 'center',
        render(rowData) {
          const {
            affiliate_product: {
              base_product: { sales }
            }
          } = rowData;
          return (
            <div class="flex-center gap-2">
              <span>{sales}</span>
            </div>
          );
        }
      },
      {
        title: 'Stock',
        key: 4,
        width: 100,
        align: 'center',
        render(rowData) {
          const {
            affiliate_product: {
              base_product: { stock }
            }
          } = rowData;
          const isOutStock = !stock;
          return (
            <div
              class={{
                'flex-col justify-end': true,
                'text-error': isOutStock
              }}
            >
              <span>{stock || 0}</span>
              {isOutStock ? <span>Out of stock</span> : null}
            </div>
          );
        }
      },
      {
        title: 'Commission Rate',
        key: 5,
        width: 100,
        align: 'center',
        render(rowData) {
          const {
            affiliate_product: {
              current_commission_rate: { commission_rate }
            }
          } = rowData;
          return (
            <div class="flex-center gap-2">
              <span>{numberFormat(commission_rate / 10000, NumeralFormat.Percent)}</span>
            </div>
          );
        }
      },
      {
        title: 'Status',
        key: 6,
        width: 100,
        align: 'center',
        render(rowData) {
          const {
            affiliate_product: {
              base_product: { status }
            }
          } = rowData;
          const statusAttr = statusAttrObj[status];
          if (!statusAttr) return '-';
          return (
            <div class="flex-center gap-2">
              <NTag type={statusAttr.type} bordered={false}>
                <div class="flex-center gap-2">
                  <span class="font-500">{statusAttr.label}</span>
                  {statusAttr.desc ? <Tip iconClass="text-error" description={statusAttr.desc} /> : null}
                </div>
              </NTag>
            </div>
          );
        }
      }
    ];
  }
});

// 保存所有已选中的产品数据，避免分页后数据丢失
const selectedProductsMap = ref<Map<string, any>>(new Map());

// 最终展示的选中产品数据
const finalSelectedData = defineModel<any[]>('products', {
  required: true
});

// 临时计算属性，用于在抽屉中展示选中的产品
const selectedData = computed(() => {
  if (!selectedKeys.value.length) {
    return [];
  }

  // 更新当前页面中的选中产品到 Map 中
  data.value.forEach(item => {
    const productId = item.affiliate_product.base_product.product_id;
    if (selectedKeys.value.includes(productId)) {
      selectedProductsMap.value.set(productId, item);
    }
  });

  // 根据 selectedKeys 从 Map 中获取完整的选中产品数据
  return selectedKeys.value.map(key => selectedProductsMap.value.get(key)).filter(Boolean);
});

function handleSearchID(val: string) {
  updateSearchParams({
    searchParams: isEmpty(val) ? [{ searchKey: 1, searchValue: '' }] : [{ searchKey: 2, searchValue: val }]
  });
  getData();
}

function handleDelete(productId: string) {
  // 从 selectedKeys 中移除
  selectedKeys.value = selectedKeys.value.filter(key => key !== productId);

  // 从 selectedProductsMap 中移除
  selectedProductsMap.value.delete(productId);

  // 从 finalSelectedData 中移除
  finalSelectedData.value = finalSelectedData.value.filter(
    item => item.affiliate_product.base_product.product_id !== productId
  );
}

const addFlag = ref(false);

function handleAdd() {
  // 确认添加，保存当前选择
  addFlag.value = true;
  toggleDrawerShow();
}

function handleCancel() {
  // 取消时不设置 addFlag，直接关闭抽屉
  toggleDrawerShow();
}

function handleCloseDrawer() {
  // 抽屉关闭后的处理
  if (!addFlag.value) {
    // 如果不是通过 Add 按钮关闭的，还原选择状态
    selectedKeys.value = [...previousSelectedKeys.value];
  } else {
    // 如果是通过 Add 按钮关闭的，计算最终的选中数据
    finalSelectedData.value = selectedData.value;
  }
  // 重置标志位，为下次打开做准备
  addFlag.value = false;
}

function handleOpenDrawer() {
  // 打开抽屉前，保存当前选择状态
  previousSelectedKeys.value = [...selectedKeys.value];
  // 重置标志位
  addFlag.value = false;
  toggleDrawerShow();

  updateSearchParams({
    shopId: props.shopId
  });
  getData();
}

watch(
  () => selectedKeys.value,
  newVal => {
    if (newVal.length > 5) {
      selectedKeys.value = selectedKeys.value.slice(0, 5);
    }
  }
);
</script>

<template>
  <div class="h-full min-h-300px w-full flex-col">
    <NEmpty v-show="!finalSelectedData.length" class="m-auto">
      <template #icon>
        <icon-solar:inbox-in-bold-duotone class="" />
      </template>
      <template #default>Add up to 5 products to the card</template>
      <template #extra>
        <NButton strong secondary type="primary" @click="handleOpenDrawer">Add Product</NButton>
      </template>
    </NEmpty>
    <div v-show="finalSelectedData.length" class="flex-col gap-4">
      <NButton class="w-200px" type="primary" :disabled="finalSelectedData.length >= 5" @click="handleOpenDrawer">
        Add products ({{ finalSelectedData.length }} / 5)
      </NButton>
      <TikTokProductItem
        v-for="selected in finalSelectedData"
        :key="selected"
        :product="selected"
        :handle-delete="handleDelete"
      />
    </div>
    <NDrawer v-model:show="drawShow" width="1200px" @after-leave="handleCloseDrawer">
      <NDrawerContent title="Select products from open collaboration" closable>
        <div class="h-full flex-col gap-4">
          <SearchInput placeholder="Product ID/Name" @change="handleSearchID" />
          <NDataTable
            v-model:checked-row-keys="selectedKeys"
            class="h-full min-h-400px"
            flex-height
            size="small"
            :bordered="false"
            remote
            :loading="loading"
            :data="data"
            :row-key="row => row?.affiliate_product?.base_product?.product_id"
            :columns="columns"
            :pagination="pagination"
          ></NDataTable>
        </div>
        <template #footer>
          <div class="w-full flex items-center justify-between gap-4">
            <div>
              <span class="text-primary font-bold">{{ selectedKeys.length }}</span>
              /5 products to be added.
            </div>
            <div class="flex gap-4">
              <NButton @click="handleCancel">Cancel</NButton>
              <NButton type="primary" @click="handleAdd">Add</NButton>
            </div>
          </div>
        </template>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style scoped></style>
