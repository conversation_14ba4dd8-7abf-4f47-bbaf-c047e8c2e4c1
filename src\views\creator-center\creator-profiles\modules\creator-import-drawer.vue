<script setup lang="ts">
import UploadDetailCard from './upload-detail-card.vue';
import UploadHistoryCard from './upload-history-card.vue';

const show = defineModel<boolean>('show', {
  required: true,
  default: false
});
</script>

<template>
  <NDrawer v-model:show="show" width="1200px">
    <NDrawerContent class="bg-layout" body-content-class="flex-col gap-16px" title="Import & Update">
      <UploadDetailCard />
      <UploadHistoryCard />
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
