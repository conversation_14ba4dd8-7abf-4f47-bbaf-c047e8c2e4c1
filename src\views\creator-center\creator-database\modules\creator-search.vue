<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-20 13:59:42
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-22 14:59:14
 * @FilePath: \tiksage-frontend\src\views\creator-resources\modules\creator-search.vue
 * @Description: creator-search page
-->
<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue';
import type { SelectOption } from 'naive-ui';
import { debounce, isNil, max, min } from 'lodash-es';
import { fetchFuzzySearchCreator, fetchGetCategoryTree } from '@/service/api';
import {
  avgViewsMark,
  followerAgesOptions,
  followersGenderOptions,
  followersMark,
  formatData,
  gmvOptions,
  unitsSoldOptions
} from './data';
type SearchValue = CommonType.RecordNullable<{
  categoryArr: string[];
  followerAgeArr: string[];
  followersArr: number[];
  followerGender: string;
  gmv: string;
  unitsSold: string;
  avgViewsRange: number;
}>;

interface Emits {
  (e: 'search'): void;
  (e: 'reset'): void;
}
const emit = defineEmits<Emits>();

const isDefault = ref(true);

const model = defineModel<Api.CreatorResources.CreatorSearchParams>('model', {
  required: true
});

const idNameOptions = ref<SelectOption[]>([]);
const idNameLoading = ref(false);
const handleExactSearch = async (value: string) => {
  idNameLoading.value = true;
  const { data } = await fetchFuzzySearchCreator(value);
  idNameOptions.value =
    data?.creatorList.map(v => {
      return {
        label: v.id,
        value: v.id
      };
    }) || [];
  idNameLoading.value = false;
};
const debounceFuzzySearch = debounce(handleExactSearch, 1000, { leading: true });

const createFormValue = () => {
  return {
    categoryArr: null,
    followerAgeArr: null,
    followersArr: [0, 46],
    followerGender: null,
    gmv: null,
    unitsSold: null,
    avgViewsRange: 0
  };
};
const formValue = reactive<SearchValue>(createFormValue());

const categoryTreeOptions = ref<Api.CreatorResources.CategoryTree[]>([]);
const getCategoryTree = async () => {
  const { data, error } = await fetchGetCategoryTree();
  if (error) return;
  categoryTreeOptions.value = data;
};
onMounted(() => {
  getCategoryTree();
});

const handleSubmit = () => {
  emit('search');
  isDefault.value = false;
};
const handleReset = () => {
  Object.assign(formValue, createFormValue());
  emit('reset');
  isDefault.value = true;
};

// watch followers
watch(
  () => formValue.followersArr,
  newFollowerArr => {
    if (!newFollowerArr) return;
    if (newFollowerArr[0] === 0 && newFollowerArr[1] === 46) return;
    const arr = [
      formatData(newFollowerArr[0], 1000, false) as number,
      formatData(newFollowerArr[1], 1000, false) as number
    ];
    model.value.followersMin = min(arr);
    model.value.followersMax = max(arr);
  },
  { deep: true }
);
// watch gmv
watch(
  () => formValue.gmv,
  newGmv => {
    if (!newGmv) return;
    model.value.gmvMin = Number(newGmv.split(',')?.[0]);
    model.value.gmvMax = newGmv.split(',')?.[1] === '' ? null : Number(newGmv.split(',')?.[1]);
  },
  { deep: true }
);
// watch gender
watch(
  () => formValue.followerGender,
  newGender => {
    if (newGender === 'All') {
      model.value.followerGender = '';
    } else {
      model.value.followerGender = newGender;
    }
  },
  { deep: true }
);
// watch units sold
watch(
  () => formValue.unitsSold,
  newUnitsSold => {
    if (!newUnitsSold) return;

    model.value.unitsSoldMin = Number(newUnitsSold.split(',')?.[0]);
    model.value.unitsSoldMax = newUnitsSold.split(',')?.[1] === '' ? null : Number(newUnitsSold.split(',')?.[1]);
  },
  { deep: true }
);

// watch avg views
watch(
  () => formValue.avgViewsRange,
  newAvgViewsRange => {
    if (isNil(newAvgViewsRange)) return;

    model.value.avgViewsRangeArr = [{ avgViewsMin: formatData(newAvgViewsRange, 10, false) as number }];
  },
  { deep: true }
);
</script>

<template>
  <NCard :bordered="false">
    <template #header>
      <span>Find creators</span>
    </template>
    <NForm label-placement="top" :model="model" :show-label="false">
      <NGrid :x-gap="5" :y-gap="5" responsive="screen" item-responsive>
        <NFormItemGi span="24 s:8 m:8" label="TikTok ID Or Name" path="idName">
          <NSelect
            v-model:value="model.idName"
            :options="idNameOptions"
            :loading="idNameLoading"
            remote
            clearable
            placeholder="TikTok ID Or Name"
            @search="debounceFuzzySearch"
            @focus="handleExactSearch('')"
            @update:value="emit('search')"
          >
            <template #arrow>@</template>
          </NSelect>
        </NFormItemGi>
      </NGrid>
      <NCollapse>
        <NCollapseItem>
          <template #header>
            <NFlex>
              <NText>More select options</NText>
              <NText v-show="isDefault" class="text-sm" type="warning">
                * Currently displaying data for creators under all conditions.
              </NText>
            </NFlex>
          </template>
          <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
            <NFormItemGi span="12 s:12 m:6" label="Categories" path="categoryArr">
              <NTreeSelect
                v-model:value="model.categoryArr"
                :options="categoryTreeOptions"
                key-field="localName"
                label-field="localName"
                max-tag-count="responsive"
                multiple
                filterable
                clearable
                placeholder="Categories"
              ></NTreeSelect>
            </NFormItemGi>
            <NFormItemGi span="12 s:12 m:6" show-require-mark label="GMV">
              <NSelect v-model:value="formValue.gmv" :options="gmvOptions" placeholder="GMV" clearable></NSelect>
            </NFormItemGi>
            <NFormItemGi span="12 s:12 m:6" show-require-mark label="Units Sold">
              <NSelect
                v-model:value="formValue.unitsSold"
                :options="unitsSoldOptions"
                placeholder="Units Sold"
                clearable
              ></NSelect>
            </NFormItemGi>
            <NFormItemGi span="12 s:12 m:6" label="Followers">
              <NPopover trigger="hover">
                <template #trigger>
                  <NSlider
                    v-model:value="formValue.followersArr as number[]"
                    range
                    :marks="followersMark"
                    :step="1"
                    :max="46"
                    :format-tooltip="val => formatData(val, 1000, true)"
                  />
                </template>
                <span>Followers</span>
              </NPopover>
            </NFormItemGi>
          </NGrid>
          <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
            <NFormItemGi span="12 s:12 m:6" label="Follower Age">
              <NSelect
                v-model:value="model.followerAgeArr"
                multiple
                :options="followerAgesOptions"
                max-tag-count="responsive"
                placeholder="Follower Age"
                clearable
              ></NSelect>
            </NFormItemGi>
            <NFormItemGi span="12 s:12 m:6" label="Followers Gender">
              <NSelect
                v-model:value="model.followerGender"
                :options="followersGenderOptions"
                max-tag-count="responsive"
                placeholder="Followers Gender"
                clearable
              ></NSelect>
            </NFormItemGi>
            <NFormItemGi span="12 s:12 m:6" show-require-mark label="Avg. Views">
              <NPopover trigger="hover">
                <template #trigger>
                  <NSlider
                    v-model:value="formValue.avgViewsRange as number"
                    :marks="avgViewsMark"
                    :max="37"
                    :step="1"
                    :format-tooltip="val => formatData(val, 10, true)"
                  />
                </template>
                <span>Avg. Views</span>
              </NPopover>
            </NFormItemGi>
            <NFormItemGi span="12 s:12 m:6">
              <NSpace class="w-full" justify="end">
                <NButton type="primary" @click="handleSubmit">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  Search
                </NButton>
                <NButton @click="handleReset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  Reset
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NCollapseItem>
      </NCollapse>
    </NForm>
  </NCard>
</template>

<style scoped></style>
