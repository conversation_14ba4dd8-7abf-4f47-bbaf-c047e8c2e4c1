<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-08 21:32:19
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-08-13 13:42:17
 * @FilePath: \tiksage-frontend\src\components\custom\tag-popover.vue
 * @Description: tag-popover
-->
<script setup lang="ts">
import { computed } from 'vue';
import { head, tail } from 'lodash-es';

interface Props {
  prefix?: string;
  suffix?: string;
  showFirst?: boolean;
  tags: string[];
}
const props = withDefaults(defineProps<Props>(), {
  prefix: '+',
  suffix: '',
  showFirst: false
});

const firstTag = computed(() => {
  return head(props.tags);
});

const showTags = computed(() => {
  if (props.showFirst) {
    return tail(props.tags);
  }
  return props.tags;
});
</script>

<template>
  <div class="flex gap-1">
    <NTag v-if="showFirst" size="small" :bordered="false">{{ firstTag }}</NTag>
    <NPopover overlap placement="right">
      <template #trigger>
        <NFlex justify="center">
          <NTag v-if="showTags.length" size="small" :bordered="false">
            {{ props.prefix }} {{ showTags.length }} {{ props.suffix }}
          </NTag>
        </NFlex>
      </template>
      <NFlex class="w-250px">
        <NTag v-for="tag in showTags" :key="tag" size="small" :bordered="false">
          {{ tag }}
        </NTag>
      </NFlex>
    </NPopover>
  </div>
</template>

<style scoped></style>
