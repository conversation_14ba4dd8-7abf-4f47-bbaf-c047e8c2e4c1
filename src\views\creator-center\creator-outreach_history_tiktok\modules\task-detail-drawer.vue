<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { NImage, NTag } from 'naive-ui';
import dayjs from 'dayjs';
import { fetchGetTaskInfoByTikTok, fetchGetTaskInfoCreatorsByTikTok } from '@/service/api';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { useTable } from '@/hooks/common/table';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { getFallbackImage } from '@/utils/fake-image';
import { NumeralFormat, TimeFormat } from '@/enum';
import TagPopover from '@/components/custom/tag-popover.vue';
import TikTokProductItem from '../../creator-outreach_create_tiktok/modules/tiktok-product-item.vue';

const { VITE_CREATOR_AVATAR_URL } = import.meta.env;

interface Props {
  taskId: number | null;
}

const props = defineProps<Props>();

const taskDetail = ref<Api.CreatorNetwork.TaskDetailByTikTok | null>(null);

const { getDictionaryByCodeType } = useDictionaryStore();
const taskStatusOptions = ref<Api.Dictionary.DictionaryItem<number>[]>([]);
const taskSenderStatusOptions = ref<Api.Dictionary.DictionaryItem<number>[]>([]);
const messageModuleOptions = ref<Api.Dictionary.DictionaryItem<number>[]>([]);
const taskType = computed(() => {
  return messageModuleOptions.value.find(v => v.code === taskDetail.value?.messageModule)?.name;
});

const show = defineModel('show', {
  type: Boolean,
  default: false
});

const { numberFormat } = useNumberFormat();

const [taskLoading, toggleTaskLoading] = useToggle(false);

const taskIndicators = computed(() => {
  return [
    {
      title: 'Total Recipients',
      value: taskDetail.value?.totalCount || 0,
      color: '#000'
    },
    {
      title: 'Successful',
      value: taskDetail.value?.successCount || 0,
      color: '#52c41a'
    },
    {
      title: 'Failed',
      value: taskDetail.value?.failCount || 0,
      color: '#ff3d33'
    }
  ];
});

const statusObj = computed(() => {
  if (!taskStatusOptions.value.length || !taskDetail.value) return null;
  const attr = taskStatusOptions.value.find(item => item.code === taskDetail.value?.status);

  return {
    ...attr,
    description: JSON.parse(attr?.description)
  };
});

const { data, columns, pagination, loading, getData, searchParams, updateSearchParams } = useTable({
  immediate: false,
  apiFn: fetchGetTaskInfoCreatorsByTikTok,
  apiParams: {
    current: 1,
    size: 10,
    taskId: props.taskId
  },
  columns: () => [
    {
      key: 'index',
      title: 'Index',
      align: 'center',
      width: 70
    },
    {
      key: 'creatorName',
      title: 'Creator Information',
      width: 250,
      render(rowData) {
        const avatarUrl = `${VITE_CREATOR_AVATAR_URL}${rowData.creatorAvatar}`;
        const followerStr = `Followers ${numberFormat(rowData.followerNum || 0, NumeralFormat.Number) || '-'}`;
        const categorys = rowData.categoryJson ? JSON.parse(rowData.categoryJson) : [];
        return (
          <div class="flex items-center gap-4">
            <div class="h-40px w-40px flex-shrink-0">
              <NImage
                class="rounded-full"
                width="40px"
                height="40px"
                src={avatarUrl}
                fallback-src={getFallbackImage(50, 50)}
              />
            </div>
            <div class="flex flex-col gap-0">
              <span class="text-base font-bold">{rowData.creatorId}</span>
              {categorys.length > 0 ? <TagPopover showFirst tags={categorys} /> : <span>-</span>}
              <span class="text-sm text-coolgray">{followerStr}</span>
            </div>
          </div>
        );
      }
    },
    {
      key: 'updateTime',
      title: 'Sent time',
      align: 'center',
      render(rowData) {
        const time = dayjs(rowData.updateTime).format(TimeFormat.US_TIME_24);
        return time || '-';
      }
    },
    {
      title: 'Status',
      key: 'status',
      align: 'center',
      width: 200,
      render(rowData) {
        const taskSenderStatus = taskSenderStatusOptions.value.find(item => item.code === rowData.status);
        if (!taskSenderStatus) return '-';
        return (
          <NTag type={taskSenderStatus?.description || 'default'} bordered={false} size="small">
            {taskSenderStatus?.name}
          </NTag>
        );
      }
    },
    {
      title: 'Error Message',
      key: 'errorMessage',
      align: 'center',
      width: 200,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width:400px'
        }
      },
      render(rowData) {
        return rowData.errorMessage || '-';
      }
    }
  ]
});

async function initTask() {
  if (props.taskId) {
    taskLoading.value = true;
    const { data: taskInfoData, error } = await fetchGetTaskInfoByTikTok(props.taskId);
    if (!error) {
      taskDetail.value = taskInfoData;
      updateSearchParams({
        taskId: props.taskId
      });
      getData();
    }
    taskLoading.value = false;
  }
}

async function initStatusOptions() {
  const taskStatusOpts = await getDictionaryByCodeType<number>('email_task_status');
  if (!taskStatusOpts) return;
  taskStatusOptions.value = taskStatusOpts;
}

async function initTaskSenderStatusOptions() {
  const taskDetailStatusOpts = await getDictionaryByCodeType<number>('message_status');
  if (!taskDetailStatusOpts) return;
  taskSenderStatusOptions.value = taskDetailStatusOpts;
}

async function initMessageModuleOptions() {
  const messageModuleOpts = await getDictionaryByCodeType<number>('message_module');
  if (!messageModuleOpts) return;
  messageModuleOptions.value = messageModuleOpts;
}

async function init() {
  await initTask();
  await initStatusOptions();
  await initTaskSenderStatusOptions();
  await initMessageModuleOptions();
}

watch(
  () => searchParams.status,
  () => {
    updateSearchParams({
      current: 1,
      size: 10
    });
    getData();
  }
);

watch(
  () => show.value,
  async newVal => {
    if (newVal) {
      toggleTaskLoading(true);
      await init();
      toggleTaskLoading(false);
    }
  }
);
</script>

<template>
  <NDrawer v-model:show="show" :width="1200">
    <NDrawerContent class="bg-#F7FAFC" body-content-class="flex-col gap-16px" closable>
      <NSpin v-if="taskLoading" class="m-auto"></NSpin>
      <template v-else>
        <NCard class="card-wrapper" :bordered="false" title="Task Detail"></NCard>
        <NGrid item-responsive responsive="screen" :x-gap="16" :y-gap="16">
          <NGi span="12">
            <NCard class="card-wrapper" segmented :bordered="false">
              <NDescriptions label-class="text-coolgray" content-class="" :columns="2" label-placement="left">
                <NDescriptionsItem label="Task Name">
                  <span>{{ taskDetail?.taskName || '-' }}</span>
                </NDescriptionsItem>
                <NDescriptionsItem label="Task Type">
                  <span>{{ taskType || '-' }}</span>
                </NDescriptionsItem>
                <NDescriptionsItem label="Task Status">
                  <span v-if="!statusObj">-</span>
                  <NTag v-else :type="statusObj?.description.buttonType" :bordered="false" size="small">
                    {{ statusObj?.name }}
                  </NTag>
                </NDescriptionsItem>
              </NDescriptions>
            </NCard>
          </NGi>
          <NGi span="12">
            <NGrid class="" :cols="3" :x-gap="16" :y-gap="16">
              <NGi v-for="indicator in taskIndicators" :key="indicator.title" class="h-full">
                <div class="h-full flex-col card-wrapper rounded-8px bg-white px-16px py-24px">
                  <span class="text-coolgray">{{ indicator.title }}</span>
                  <span class="text-xl font-bold" :style="{ color: indicator.color }">{{ indicator.value }}</span>
                </div>
              </NGi>
            </NGrid>
          </NGi>
        </NGrid>
        <NCard class="card-wrapper" segmented :bordered="false" title="Message Content">
          <NDescriptions label-class="text-coolgray" content-class="" :columns="1" label-placement="left">
            <NDescriptionsItem label="Title">
              <span>{{ taskDetail?.title || '-' }}</span>
            </NDescriptionsItem>
            <NDescriptionsItem label="Text information">
              <span>{{ taskDetail?.content || '-' }}</span>
            </NDescriptionsItem>
            <NDescriptionsItem label="Others">
              <NGrid :cols="2" :x-gap="16" :y-gap="16" item-responsive>
                <NGi v-for="product in taskDetail?.products" :key="product.index">
                  <TikTokProductItem :product="product" />
                </NGi>
              </NGrid>
            </NDescriptionsItem>
          </NDescriptions>
        </NCard>
        <NCard class="card-wrapper" segmented :bordered="false" title="Recipient List">
          <div>
            <NGrid :cols="5" :x-gap="16" :y-gap="16" item-responsive>
              <NGi offset="4" span="1">
                <NSelect
                  v-model:value="searchParams.status"
                  placeholder="Open Status"
                  clearable
                  :options="taskSenderStatusOptions"
                  value-field="code"
                  label-field="name"
                />
              </NGi>
            </NGrid>
          </div>
          <NDataTable
            class="flex-1"
            remote
            :loading="loading"
            :bordered="false"
            :columns="columns"
            :data="data"
            :pagination="pagination"
          >
            <template #empty>
              <NEmpty description="Start by importing or selecting creators to populate the list." />
            </template>
          </NDataTable>
        </NCard>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
