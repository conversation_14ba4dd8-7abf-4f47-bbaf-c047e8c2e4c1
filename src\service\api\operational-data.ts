/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-09-04 14:38:46
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-09-12 13:50:44
 * @FilePath: \tiksage-frontend\src\service\api\operational-data.ts
 * @Description: OperationalData
 */
import { request } from '../request';

export function fetchOperationalDashboardData(data: Api.Common.CommonSearchParams) {
  return request<Api.OperationalData.DashboardResponse>({
    url: '/operational/groupPage',
    method: 'get',
    params: { ...data }
  });
}

export function fetchGetClientDataByDate(data: Api.OperationalData.ClientDataSearchParams) {
  return request<Api.OperationalData.DashboardResponse>({
    url: '/operational/list',
    method: 'get',
    params: { ...data }
  });
}

export function fetchAddOperationalDashboardData(data: Api.OperationalData.MonthlyDataCreateParams) {
  return request({
    url: '/operational/create',
    method: 'post',
    data
  });
}

export function fetchUpdateOperationalDashboardDataById(data: Api.OperationalData.MonthlyDataCreateParams) {
  return request({
    url: '/operational/update',
    method: 'put',
    data
  });
}

export function fetchDeleteOperationalDashboardDataById(id: number) {
  return request({
    url: '/operational/delete',
    method: 'delete',
    params: { id }
  });
}

export function fetchDeleteOperationalDashboardDataByIds(data: number[]) {
  return request({
    url: '/operational/deleteBatch',
    method: 'delete',
    data
  });
}
