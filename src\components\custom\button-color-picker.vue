<script setup lang="ts">
import { ref } from 'vue';
import { creatorTagColorOptions } from '@/constants/colors';

interface Props {
  /** Button label */
  label?: string;
}

withDefaults(defineProps<Props>(), {
  label: 'Select Color'
});

const color = defineModel<string>('color', {
  default: creatorTagColorOptions[0].value
});

const showColorPanel = ref(false);

const handleColorSelect = (newColor: string) => {
  color.value = newColor;
  showColorPanel.value = false;
};
</script>

<template>
  <NPopover v-model:show="showColorPanel" trigger="click" placement="bottom">
    <template #trigger>
      <NButton size="tiny" quaternary @click.stop>
        <template #icon>
          <icon-solar:pallete-2-linear />
        </template>
      </NButton>
    </template>
    <div class="max-w-200px">
      <div class="flex flex-wrap gap-2">
        <div
          v-for="option in creatorTagColorOptions"
          :key="option.value"
          class="color-item h-20px w-20px cursor-pointer rounded transition-transform hover:scale-110"
          :style="{ backgroundColor: option.value }"
          :title="option.label"
          @click="handleColorSelect(option.value)"
        />
      </div>
    </div>
  </NPopover>
</template>

<style scoped>
.n-button :deep(.n-button-icon) {
  margin-right: 6px;
}

.color-item {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.color-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
</style>
