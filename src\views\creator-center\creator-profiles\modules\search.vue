<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { useEventBus } from '@vueuse/core';
import type { SelectProps } from 'naive-ui';
import {
  fetchGetClientOptions,
  fetchGetCreatorCategoryOptions,
  fetchGetLanguageOptions,
  fetchGetProductCategoryOptions,
  fetchGetRaceOptions
} from '@/service/api';
import { useDictionaryStore } from '@/store/modules/dictonary';

type SelectOption = {
  label: string;
  value: string | number;
};

type SearchParams = CommonType.RecordNullable<{
  creatorId: string;
  creatorCategory: string;
  productCategory: string;
  creatorGender: string;
  language: string;
  race: string;
  followersCount: string;
  followersGender: string;
  followersAge: string[];
  gmv: string;
  client: string;
  views: string;
  evaluatedType: number;
}>;

type Select = {
  span: number;
  offset: number;
  key: keyof SearchParams;
  placeholder: string;
  options: SelectOption[] | Api.Dictionary.DictionaryItem<string | number>[];
  optionProps?: SelectProps;
};

interface Emits {
  (e: 'update:searchParams', params: SearchParams): void;
}

interface Props {
  evaluatedOptions: Api.Dictionary.DictionaryItem<number>[];
}

const props = defineProps<Props>();

const emits = defineEmits<Emits>();

const { getDictionaryByCodeType } = useDictionaryStore();

const bus = useEventBus<string>('refresh-creator-center-list');

const creatorCategoryOptions = ref<SelectOption[]>([]);
const raceOptions = ref<SelectOption[]>([]);
const languageOptions = ref<SelectOption[]>([]);
const productCategoryOptions = ref<SelectOption[]>([]);
const clientOptions = ref<SelectOption[]>([]);
const creatorGenderOptions = ref<Api.Dictionary.DictionaryItem<string>[]>([]);
const followerGenderOptions = ref<Api.Dictionary.DictionaryItem<string>[]>([]);
const followersCountOptions = ref<Api.Dictionary.DictionaryItem<string>[]>([]);
const followerAgesOptions = ref<Api.Dictionary.DictionaryItem<string>[]>([]);
const gmvOptions = ref<Api.Dictionary.DictionaryItem<string>[]>([]);
const viewsOptions = ref<Api.Dictionary.DictionaryItem<string>[]>([]);

function initOptions() {
  Promise.all([
    fetchGetCreatorCategoryOptions(),
    fetchGetRaceOptions(),
    fetchGetLanguageOptions(),
    fetchGetProductCategoryOptions(),
    fetchGetClientOptions()
  ]).then(([creatorCategoryOpts, raceOpts, languageOpts, productCategoryOpts, clientOpts]) => {
    if (creatorCategoryOpts.data)
      creatorCategoryOptions.value = creatorCategoryOpts.data.map(v => ({ label: v, value: v }));
    if (raceOpts.data) raceOptions.value = raceOpts.data.map(v => ({ label: v, value: v }));
    if (languageOpts.data) languageOptions.value = languageOpts.data.map(v => ({ label: v, value: v }));
    if (productCategoryOpts.data)
      productCategoryOptions.value = productCategoryOpts.data.map(v => ({ label: v, value: v }));
    if (clientOpts.data) clientOptions.value = clientOpts.data.map(v => ({ label: v, value: v }));
  });

  // select_option_creator_gender
  // select_option_followers_count
  // select_option_followers_gender
  // select_option_followers_age
  // select_option_gmv
  // select_option_video_views
  // select_option_evaluated
  Promise.all([
    getDictionaryByCodeType<string>('select_option_creator_gender'),
    getDictionaryByCodeType<string>('select_option_followers_count'),
    getDictionaryByCodeType<string>('select_option_followers_gender'),
    getDictionaryByCodeType<string>('select_option_followers_age'),
    getDictionaryByCodeType<string>('select_option_gmv'),
    getDictionaryByCodeType<string>('select_option_video_views')
  ]).then(([creatorGenderOpts, followersCountOpts, followerGenderOpts, followerAgesOpts, gmvOpts, viewsOpts]) => {
    if (creatorGenderOpts) creatorGenderOptions.value = creatorGenderOpts;
    if (followersCountOpts) followersCountOptions.value = followersCountOpts;
    if (followerGenderOpts) followerGenderOptions.value = followerGenderOpts;
    if (followerAgesOpts) followerAgesOptions.value = followerAgesOpts;
    if (gmvOpts) gmvOptions.value = gmvOpts;
    if (viewsOpts) viewsOptions.value = viewsOpts;
  });
}

const selects = computed<Select[]>(() => [
  {
    span: 1,
    offset: 0,
    key: 'creatorCategory',
    placeholder: 'Creator Category',
    options: creatorCategoryOptions.value
  },
  {
    span: 1,
    offset: 0,
    key: 'productCategory',
    placeholder: 'Product Category',
    options: productCategoryOptions.value
  },
  {
    span: 1,
    offset: 0,
    key: 'creatorGender',
    placeholder: 'Creator Gender',
    options: creatorGenderOptions.value,
    optionProps: {
      labelField: 'name',
      valueField: 'code'
    }
  },
  {
    span: 1,
    offset: 0,
    key: 'language',
    placeholder: 'Language',
    options: languageOptions.value
  },
  {
    span: 1,
    offset: 0,
    key: 'race',
    placeholder: 'Race',
    options: raceOptions.value
  },
  {
    span: 1,
    offset: 0,
    key: 'followersCount',
    placeholder: 'Followers Count',
    options: followersCountOptions.value,
    optionProps: {
      labelField: 'name',
      valueField: 'code'
    }
  },
  {
    span: 1,
    offset: 0,
    key: 'followersGender',
    placeholder: 'Followers Gender',
    options: followerGenderOptions.value,
    optionProps: {
      labelField: 'name',
      valueField: 'code'
    }
  },
  {
    span: 1,
    offset: 0,
    key: 'followersAge',
    placeholder: 'Followers Age',
    options: followerAgesOptions.value,
    optionProps: {
      labelField: 'name',
      valueField: 'code',
      multiple: true,
      maxTagCount: 'responsive'
    }
  },
  {
    span: 1,
    offset: 0,
    key: 'gmv',
    placeholder: 'GMV',
    options: gmvOptions.value,
    optionProps: {
      labelField: 'name',
      valueField: 'code'
    }
  },
  {
    span: 1,
    offset: 0,
    key: 'client',
    placeholder: 'Client',
    options: clientOptions.value
  },
  {
    span: 1,
    offset: 0,
    key: 'views',
    placeholder: 'Avg.Video Views',
    options: viewsOptions.value,
    optionProps: {
      labelField: 'name',
      valueField: 'code'
    }
  },
  {
    span: 1,
    offset: 0,
    key: 'evaluatedType',
    placeholder: 'Evaluated by Tiksage',
    options: props.evaluatedOptions,
    optionProps: {
      labelField: 'name',
      valueField: 'code'
    }
  }
]);

function initSearchParams(): SearchParams {
  return {
    creatorId: null,
    creatorCategory: null,
    productCategory: null,
    creatorGender: null,
    language: null,
    race: null,
    followersCount: null,
    followersGender: null,
    followersAge: null,
    gmv: null,
    client: null,
    views: null,
    evaluatedType: null
  };
}

function initRealParams(): Api.CreatorCenter.CreatorCenterSearchParams {
  return {
    current: 1,
    size: 10,
    avgViewsMax: null,
    avgViewsMin: null,
    client: null,
    creatorCategory: null,
    creatorId: null,
    evaluatedType: null,
    followerAgeArr: null,
    followerGender: null,
    followersMax: null,
    followersMin: null,
    gender: null,
    gmvMax: null,
    gmvMin: null,
    language: null,
    productCategory: null,
    race: null
  };
}

const creatorIdStr = ref<string>('');
const searchParams = ref<SearchParams>(initSearchParams());

function handleSearch(value: string) {
  searchParams.value.creatorId = value;
}

function transformSearchParams(params: SearchParams) {
  const result: Api.CreatorCenter.CreatorCenterSearchParams = initRealParams();

  const {
    creatorId,
    creatorCategory,
    productCategory,
    creatorGender,
    language,
    race,
    followersCount,
    followersGender,
    followersAge,
    gmv,
    client,
    views,
    evaluatedType
  } = params;

  if (creatorId) result.creatorId = creatorId;
  if (creatorGender) result.gender = creatorGender;
  if (creatorCategory) result.creatorCategory = creatorCategory;
  if (productCategory) result.productCategory = productCategory;
  if (followersGender) result.followerGender = followersGender;
  if (language) result.language = language;
  if (client) result.client = client;
  if (evaluatedType) result.evaluatedType = evaluatedType;
  if (race) result.race = race;

  if (views) {
    const [min, max] = views.split(',');
    if (min) result.avgViewsMin = Number(min);
    if (max) result.avgViewsMax = Number(max);
  }

  if (gmv) {
    const [min, max] = gmv.split(',');
    if (min) result.gmvMin = Number(min);
    if (max) result.gmvMax = Number(max);
  }

  if (followersCount) {
    const [min, max] = followersCount.split(',');
    if (min) result.followersMin = Number(min);
    if (max) result.followersMax = Number(max);
  }

  if (followersAge) {
    result.followerAgeArr = followersAge;
  }

  return result;
}

function handleReset() {
  searchParams.value = initSearchParams();
  creatorIdStr.value = '';
}

watch(
  () => searchParams.value,
  newVal => {
    emits('update:searchParams', transformSearchParams(newVal));
  },
  {
    deep: true
  }
);

const unsubscribe = bus.on(event => {
  if (event === 'refresh-creator-center-list') {
    initOptions();
  }
});

onMounted(() => {
  bus.on(unsubscribe);
});

onUnmounted(() => {
  bus.off(unsubscribe);
});

initOptions();
</script>

<template>
  <div class="flex-col gap-4">
    <NGrid :cols="2" x-gap="16" y-gap="16" responsive="self" item-responsive>
      <NGi :span="1">
        <NInput v-model:value="creatorIdStr" clearable placeholder="Search By Creator ID" @change="handleSearch">
          <template #prefix>
            <icon-tabler:search class="text-gray" />
          </template>
        </NInput>
      </NGi>
    </NGrid>
    <NGrid :cols="4" x-gap="16" y-gap="16" responsive="self" item-responsive>
      <NGi v-for="select in selects" :key="select.placeholder" :span="select.span" :offset="select.offset">
        <NSelect
          v-model:value="searchParams[select.key]"
          clearable
          :options="select.options"
          :placeholder="select.placeholder"
          v-bind="select?.optionProps"
        />
      </NGi>
    </NGrid>
    <NButton class="self-end" type="default" @click="handleReset">Reset</NButton>
  </div>
</template>

<style scoped></style>
