<script lang="ts"></script>

<script setup lang="ts">
import { ref } from 'vue';
import { onClickOutside, useToggle } from '@vueuse/core';
import { FilterType } from '@/enum';

export interface FilterAttr {
  key: string;
  title: string;
  type: FilterType;
  description?: string;
  unit?: string;
  options?: { label: string; value: string }[];
}

export interface FilterGroup {
  category: string;
  children: FilterAttr[];
}

export type FilterModel = {
  [key in string]: any;
};

interface Props {
  filterArr: FilterGroup[];
  showExportButton?: boolean;
  export?: (filterParams: FilterModel) => void;
}
const props = withDefaults(defineProps<Props>(), {
  showExportButton: false
});

// 定义 model
const model = defineModel<FilterModel>('value', {
  default: {}
});
const [panelShow, setPanelShow] = useToggle(false);

const [exportLoading, setExportLoading] = useToggle(false);

// 添加容器ref
const containerRef = ref<HTMLElement | null>(null);

onClickOutside(containerRef, (event: any) => {
  // 检查点击事件的目标元素是否是选项面板或其子元素
  const isPopoverElement = event?.target?.closest('.n-popselect-menu') !== null;

  // 只有当点击的不是选项面板时，才隐藏panel
  if (panelShow.value && !isPopoverElement) {
    setPanelShow(false);
  }
});

// 处理值更新
function handleValueUpdate(key: keyof FilterModel, value: any) {
  if (value === undefined || value === null || value === '') {
    // 使用对象解构，创建一个新对象，忽略要删除的key
    const { [key]: _, ...rest } = model.value;
    model.value = rest;
  } else {
    model.value = { ...model.value, [key]: value };
  }
}

// 重置过滤器
function resetFilter() {
  model.value = {};
  setPanelShow(false);
}

const panelAttr = ref<FilterAttr[]>();
// 记录上次打开的category
const lastOpenCategory = ref<string>('');

function handlePanelShow(children: any, category: string) {
  if (!children.length) return;

  if (lastOpenCategory.value === category) {
    // 如果点击的是同一个category，则切换panel的显示状态
    setPanelShow(!panelShow.value);
  } else {
    // 如果点击的是不同的category，则打开新的panel
    setPanelShow(true);
  }
  panelAttr.value = children;

  // 更新最后打开的category
  lastOpenCategory.value = category;
}

// eslint-disable-next-line complexity
function getDisplayValue(key: string, value: string | string[] | boolean) {
  const filterItem = props.filterArr.flatMap(g => g.children).find(item => item.key === key);

  // 处理RangeSelect类型
  if (filterItem?.type === FilterType.RangeSelect && typeof value === 'string') {
    const [min, max] = value.split(',');
    if (!min && max) {
      return `≤ ${max}`;
    } else if (min && !max) {
      return `> ${min}`;
    } else if (min && max) {
      return `${min} - ${max}`;
    }
  }

  if (filterItem?.type === FilterType.MoreThanSelect && typeof value === 'string') {
    if (filterItem.unit === '%') {
      return `More than ${Number(value) * 100}%`;
    }
    return `More than ${value}`;
  }

  // 处理MultipleSelect类型
  if (Array.isArray(value)) {
    return value.map(v => filterItem?.options?.find(opt => opt.value === v)?.label || v).join(', ');
  }

  // 处理Checkbox类型
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }
  // 处理Select类型
  return filterItem?.options?.find(opt => opt.value === value)?.label || value;
}

function getFilterTitle(key: string): string {
  return props.filterArr.flatMap(g => g.children).find(item => item.key === key)?.title || key;
}

async function handleExport() {
  if (!props.export) return;
  setExportLoading(true);
  await props.export(model.value);
  setExportLoading(false);
}
</script>

<template>
  <div ref="containerRef" class="relative z-999">
    <NCard class="card-wrapper" content-class=" flex-col gap-4" :bordered="false">
      <div class="flex-y-center justify-between gap-2">
        <div class="flex-y-center gap-2">
          <span>Filter by</span>
          <NButton
            v-for="button in props.filterArr"
            :key="button.category"
            class="px-8"
            strong
            secondary
            @click="handlePanelShow(button.children, button.category)"
          >
            {{ button.category }}
          </NButton>
        </div>
        <NButton v-if="showExportButton" :loading="exportLoading" strong secondary @click="handleExport">
          <template #icon>
            <icon-solar:export-linear />
          </template>
          Export
        </NButton>
      </div>

      <div v-if="Object.keys(model).length > 0" class="flex gap-2 border-t-1 pt-2">
        <div class="flex-shrink-0">Selected Condition</div>
        <div class="flex flex-1 flex-wrap gap-2">
          <template v-for="(value, key) in model" :key="key">
            <NTag
              v-if="value"
              class="font-bold"
              size="small"
              :bordered="false"
              closable
              @close="handleValueUpdate(key, undefined)"
            >
              <div class="flex-y-center">
                {{ getFilterTitle(key) }}:
                <NEllipsis style="max-width: 120px">
                  {{ getDisplayValue(key, value) }}
                </NEllipsis>
              </div>
            </NTag>
          </template>
        </div>
        <div class="flex-center flex-shrink-0 gap-2">
          <NButton text @click="resetFilter">Reset</NButton>
        </div>
      </div>
    </NCard>
    <div
      v-if="panelShow"
      class="absolute left-0px top-70px w-full border-t-1 rounded-[0px_0px_10px_10px] bg-white p-[20px_24px] shadow-2xl"
    >
      <NGrid :cols="5" :x-gap="16" :y-gap="16">
        <NGi v-for="attr in panelAttr" :key="attr.key">
          <NPopselect
            v-if="attr.type === FilterType.MultipleSelect || attr.type === FilterType.Select"
            trigger="click"
            width="trigger"
            :options="attr.options"
            :multiple="attr.type === FilterType.MultipleSelect"
            :value="model[attr.key]"
            scrollable
            @update:value="handleValueUpdate(attr.key, $event)"
            @click.prevent
          >
            <NButton block type="tertiary" icon-placement="right">
              <div class="w-full flex gap-2">
                <span>{{ attr.title }}</span>
                <div v-if="model[attr.key]" class="h-16px w-16px flex-center rounded-full bg-dark text-xs text-white">
                  {{ Array.isArray(model[attr.key]) ? model[attr.key]?.length : 1 }}
                </div>
              </div>
              <template #icon>
                <icon-solar:alt-arrow-down-linear class="text-4" />
              </template>
            </NButton>
          </NPopselect>
          <RangeSelect
            v-else-if="attr.type === FilterType.RangeSelect"
            trigger="click"
            :options="attr.options"
            :range="model[attr.key]"
            @update:range="handleValueUpdate(attr.key, $event)"
          >
            <NButton block type="tertiary" icon-placement="right">
              <div class="w-full flex gap-2">
                <span>{{ attr.title }}</span>
                <div v-if="model[attr.key]" class="h-16px w-16px flex-center rounded-full bg-dark text-xs text-white">
                  1
                </div>
              </div>
              <template #icon>
                <icon-solar:alt-arrow-down-linear class="text-4" />
              </template>
            </NButton>
          </RangeSelect>
          <MoreThanSelect
            v-else-if="attr.type === FilterType.MoreThanSelect"
            trigger="click"
            :unit="attr?.unit"
            :options="attr.options"
            :range="model[attr.key]"
            @update:range="handleValueUpdate(attr.key, $event)"
          >
            <NButton block type="tertiary" icon-placement="right">
              <div class="w-full flex gap-2">
                <span>{{ attr.title }}</span>
                <div v-if="model[attr.key]" class="h-16px w-16px flex-center rounded-full bg-dark text-xs text-white">
                  1
                </div>
              </div>
              <template #icon>
                <icon-solar:alt-arrow-down-linear class="text-4" />
              </template>
            </NButton>
          </MoreThanSelect>
          <div v-else-if="attr.type === FilterType.Checkbox" class="h-full flex-y-center">
            <NCheckbox :checked="model[attr.key]" @update:checked="handleValueUpdate(attr.key, $event)">
              {{ attr.title }}
            </NCheckbox>
          </div>
        </NGi>
      </NGrid>
    </div>
  </div>
</template>

<style scoped lang="scss">
:deep(.n-button .n-button__content) {
  width: 100% !important;
  transition: all 0.3s ease;
}
</style>
