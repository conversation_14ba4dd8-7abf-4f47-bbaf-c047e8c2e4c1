<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-05 14:18:14
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-25 16:16:21
 * @FilePath: \tiksage-frontend\src\views\home\modules\product-card.vue
 * @Description: product-card
-->
<script setup lang="tsx">
import { computed, ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { NButton, NEllipsis, NFlex, NImage } from 'naive-ui';
import { fetchDownoloadProductsTopDataByBrand, fetchGetProductTopListByBrand } from '@/service/api';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { downloadFile } from '@/utils/download';
import { LinkToProduct } from '@/utils/tiktok-link';
import { getFallbackImage } from '@/utils/fake-image';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { NumeralFormat } from '@/enum';
import Tip from '@/components/custom/tip.vue';
import ProductCreatorDrawer from '../../modules/product-creator-drawer.vue';
import GmvModal from '../../modules/gmv-modal.vue';

interface Props {
  params: Api.BrandBreakDown.DashboardSearchParamsByBrand;
}

const { numberFormat } = useNumberFormat();

const props = defineProps<Props>();

const tableData = ref<Api.Dashboard.ProductTop[]>([]);

const [loading, toggleLoading] = useToggle(false);

const selectedProductId = ref<string>('');
const [modalShow, toggleModalShow] = useToggle(false);

async function getTableData(params: Api.BrandBreakDown.DashboardSearchParamsByBrand) {
  toggleLoading(true);
  const query: Api.DashboardJazwares.DashboardTopSearchParams = {
    ...params,
    topNum: 10,
    shopIdsArr: [params.shopId as number],
    productBrands: params.brandList?.[0]
  };
  const { data: responseData, error } = await fetchGetProductTopListByBrand(query);

  if (!error) {
    tableData.value = responseData.productTopArr;
  }

  toggleLoading(false);
}

function handleShowTrend(videoId: string) {
  selectedProductId.value = videoId;
  toggleModalShow(true);
}

const columns = computed<NaiveUI.TableColumn<Api.Dashboard.ProductTop>[]>(() => [
  {
    key: 'productName',
    align: 'center',
    fixed: 'left',
    width: 50,
    render(_rowData, index) {
      if (index === 0) {
        return (
          <NFlex class="w-100%" justify="center">
            <SvgIcon style="width:25px;height:25px" icon="twemoji:1st-place-medal" />
          </NFlex>
        );
      } else if (index === 1) {
        return (
          <NFlex class="w-100%" justify="center">
            <SvgIcon style="width:25px;height:25px" icon="twemoji:2nd-place-medal" />
          </NFlex>
        );
      } else if (index === 2) {
        return (
          <NFlex class="w-100%" justify="center">
            <SvgIcon style="width:25px;height:25px" icon="twemoji:3rd-place-medal" />
          </NFlex>
        );
      }
      return <span>{index + 1}</span>;
    }
  },
  {
    key: 'productId',
    title: 'Product',
    fixed: 'left',
    width: 250,
    render(rowData) {
      return (
        <div
          class="flex flex-nowrap items-center gap-2 hover:(cursor-pointer text-primary)"
          onClick={() => handleLinkProduct(rowData.productId)}
        >
          <div class="h-50px w-50px overflow-hidden">
            <NImage
              width={50}
              height={50}
              src={`${import.meta.env.VITE_PRODUCT_AVATAR_URL}${rowData.productAvatar}`}
              fallbackSrc={getFallbackImage(50, 50)}
              previewDisabled
            ></NImage>
          </div>
          <NEllipsis style="max-width:180px" tooltip={{ contentClass: 'max-w-300px' }}>
            {rowData.productName}
          </NEllipsis>
        </div>
      );
    }
  },
  {
    key: 'gmv',
    title: 'GMV',
    align: 'center',
    width: 150,
    sorter: 'default',
    sortOrder: 'descend',
    render(rowData) {
      const val = numberFormat(rowData.gmv, NumeralFormat.Dollar);
      return (
        <div class="flex-center gap-2">
          <span>{val}</span>
          <NButton type="primary" size="small" text onClick={() => handleShowTrend(rowData.productId)}>
            {{
              icon: () => <SvgIcon icon="solar:diagram-up-linear" />
            }}
          </NButton>
        </div>
      );
    }
  },
  {
    key: 'totalImpressions',
    title: 'Total impressions',
    align: 'center',
    width: 150
  },
  {
    key: 'unitsSold',
    title: 'Total units sold',
    align: 'center',
    width: 150
  },
  {
    key: 'videoImpressions',
    title() {
      return (
        <NFlex align="center" wrap={false}>
          <span>Video impressions</span>
          <Tip description="Each time a product link appears in the shoppable video, it counts as 1 impression. Viewers can tap these links to visit your product listings." />
        </NFlex>
      );
    },
    align: 'center',
    width: 150
  },
  // {
  //   key: 'videoClickRate',
  //   title: 'Video click-through rate',
  //   align: 'center',
  //   render(rowData) {
  //     return `${rowData.videoClickRate}%`
  //   }
  // },
  {
    key: 'videoUnitsSold',
    title() {
      return (
        <NFlex align="center" wrap={false}>
          <span>Video units sold</span>
          <Tip description="The number of units sold for this product directly from shoppable videos." />
        </NFlex>
      );
    },
    align: 'center',
    width: 150
  },
  {
    key: 'liveImpressions',
    title() {
      return (
        <NFlex align="center" wrap={false}>
          <span>LIVE impressions</span>
          <Tip description="Each time a product link appears in a LIVE, it counts as 1 impression. Viewers can click these links to visit your product listings." />
        </NFlex>
      );
    },
    align: 'center',
    width: 150
  },
  // {
  //   key: 'liveClickRate',
  //   title: 'LIVE click-through rate',
  //   align: 'center',
  //   render(rowData) {
  //     return `${rowData.liveClickRate}%`
  //   }
  // },
  {
    key: 'liveUnitsSold',
    title() {
      return (
        <NFlex align="center" wrap={false}>
          <span>LIVE units sold</span>
          <Tip description="The number of units sold for this product directly from LIVEs." />
        </NFlex>
      );
    },
    align: 'center',
    width: 150
  },
  {
    key: 'productCardImpressions',
    title() {
      return (
        <NFlex align="center" wrap={false}>
          <span>Product card impressions</span>
          <Tip description="The number of impressions the product card received during the selected period." />
        </NFlex>
      );
    },
    align: 'center',
    width: 150
  },
  // {
  //   key: 'productCardClickRate',
  //   title: 'Product card click-through rate',
  //   align: 'center',
  // },
  {
    key: 'productCardUnitsSold',
    title() {
      return (
        <NFlex align="center" wrap={false}>
          <span>Product card units sold</span>
          <Tip description="The number of units sold for this product directly from its product card." />
        </NFlex>
      );
    },
    align: 'center',
    width: 150
  },
  {
    key: 'videoClickRate',
    title: 'Top Creators',
    width: 150,
    align: 'center',
    fixed: 'right',
    render(rowData) {
      return (
        <NButton type="primary" text onClick={() => handleShowTopCreator(rowData.productId)}>
          View
        </NButton>
      );
    }
  }
]);

function handleLinkProduct(id: any) {
  LinkToProduct(id);
}

type DollarKey = keyof Pick<Api.Dashboard.ProductTop, 'gmv'>;

const dollarKeys: DollarKey[] = ['gmv'];

const renderCell = (value: any, _rowData: any, column: any) => {
  const isDollar = dollarKeys.includes(column.key);
  return numberFormat(value, isDollar ? NumeralFormat.Dollar : NumeralFormat.Number);
};

const tableRef = ref();

async function downloadCsv() {
  const params: any = {
    ...props.params,
    topNum: 10,
    shopIdsArr: [props.params.shopId as number],
    productBrands: props.params.brandList?.[0]
  };

  const { data, error } = await fetchDownoloadProductsTopDataByBrand(params);
  if (error) return;

  downloadFile(data, 'xlsx', 'Top 10 Best-Performing Products');
}

const productCreatorSearchParams = ref<Api.Dashboard.ProductCreatorSearchParams>({
  startDate: props.params.startDateStr as string,
  endDate: props.params.endDateStr as string,
  productId: '',
  shopId: props.params.shopId as number
});
const [productCreatorShow, toggleProductCreatorShow] = useToggle(false);

function handleShowTopCreator(productId: string) {
  toggleProductCreatorShow(true);
  productCreatorSearchParams.value.productId = productId;
}

function initData() {
  getTableData(props.params);
}

initData();
</script>

<template>
  <NCard :bordered="false" title="Top 10 Best-Performing Products">
    <template #header-extra>
      <NFlex :size="16" :wrap="false" align="center">
        <ButtonIcon
          icon="solar:download-linear"
          tooltip-content="Export"
          tooltip-placement="top"
          @click="downloadCsv"
        />
      </NFlex>
    </template>
    <NDataTable
      ref="tableRef"
      :bordered="false"
      :loading="loading"
      size="small"
      :render-cell="renderCell"
      :columns="columns"
      :data="tableData"
      scroll-x="1800"
    ></NDataTable>
    <ProductCreatorDrawer v-model:show="productCreatorShow" :params="productCreatorSearchParams" />
    <GmvModal v-model:show="modalShow" :product-id="selectedProductId" :shop-id="props.params.shopId!" />
  </NCard>
</template>

<style scoped></style>
