import { request } from '../request';

export function fetchGetInviteProductsByPage(data: Api.CreatorOutreachInvite.InviteProductsSearchParams) {
  return request<Api.CreatorOutreachInvite.InviteProductsResponse>({
    url: '/contactCreators/invite/getShopProductsPage',
    method: 'post',
    data
  });
}

export function fetchValidateRepeatProduct(data: Api.CreatorOutreachInvite.ValidateRepeatProductParams) {
  return request<Api.CreatorOutreachInvite.ValidateRepeatProductResponse>({
    url: '/contactCreators/invite/checkCreatorProductConflict',
    method: 'post',
    data
  });
}

export function fetchCreateInviteTask(data: Api.CreatorOutreachInvite.CreateInviteTaskParams) {
  return request({
    url: '/contactCreators/invite/createTask',
    method: 'post',
    data
  });
}

export function fetchGetInviteHistory(data: Api.CreatorOutreachInvite.InviteHistorySearchParams) {
  return request<Api.CreatorOutreachInvite.InviteHistoryResponse>({
    url: '/contactCreators/invite/queryTaskList',
    method: 'post',
    data
  });
}

export function fetchStartInviteTask(id: number) {
  return request({
    url: '/contactCreators/invite/startTask',
    method: 'put',
    params: {
      id
    }
  });
}

export function fetchGetInviteTaskDetail(taskId: number) {
  return request<Api.CreatorOutreachInvite.InviteTaskDetail>({
    url: '/contactCreators/invite/queryTask',
    method: 'get',
    params: {
      taskId
    }
  });
}

export function fetchGetInviteTaskCreatorList(data: Api.CreatorOutreachInvite.InviteTaskCreatorListParams) {
  return request<Api.CreatorOutreachInvite.InviteTaskCreatorListResponse>({
    url: '/contactCreators/invite/queryTaskDetailList',
    method: 'post',
    data
  });
}

export function fetchGetInviteTaskCreatorListByAccepted(data: Api.CreatorOutreachInvite.InviteTaskCreatorListParams) {
  return request<Api.CreatorOutreachInvite.InviteTaskCreatorListResponse>({
    url: '/contactCreators/invite/queryAcceptedCreatorList',
    method: 'post',
    data
  });
}

export function fetchExportFailedCreators(taskId: number) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/contactCreators/invite/exportFailedCreatorId',
    method: 'get',
    params: { taskId }
  });
}

export function fetchSaveInviteTemplate(data: Api.CreatorOutreachInvite.SaveInviteTemplateParams) {
  return request({
    url: '/contactCreators/inviteTemplate/saveInviteTemplate',
    method: 'post',
    data
  });
}

export function fetchGetInviteTemplateList() {
  return request<Api.CreatorOutreachInvite.InviteTemplateOptions>({
    url: '/contactCreators/inviteTemplate/queryInviteTemplateList',
    method: 'get'
  });
}

export function fetchGetInviteTemplateDetail(templateId: number) {
  return request<Api.CreatorOutreachInvite.InviteTemplate>({
    url: '/contactCreators/inviteTemplate/queryInviteTemplateDetail',
    method: 'get',
    params: { templateId }
  });
}

export function fetchDeleteInviteTemplate(templateId: number) {
  return request({
    url: '/contactCreators/inviteTemplate/deleteInviteTemplate',
    method: 'get',
    params: { templateId }
  });
}
