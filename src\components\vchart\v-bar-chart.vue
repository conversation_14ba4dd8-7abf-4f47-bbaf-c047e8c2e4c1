<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-09-19 11:38:08
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-14 11:50:33
 * @FilePath: \tiksage-frontend\src\components\vchart\v-bar-chart.vue
 * @Description: visactor-bar-chart
-->
<script setup lang="ts">
import { onBeforeUnmount, onMounted, onUpdated, ref } from 'vue';
import { merge } from 'lodash-es';
import { VChart } from '@visactor/vchart';
import { theme } from '@/constants/visactor-vchart';
import { optionsHOC } from '@/utils/chart-options';

interface Props {
  chartOptions?: Visactor.VChart.IBarChartSpec;
}
const props = defineProps<Props>();

let chart: Visactor.VChart.IVChart;

const chartContainer = ref<HTMLDivElement>();

function parseSpec(chartProps: Props) {
  const { chartOptions } = chartProps;

  let baseOptions: Visactor.VChart.IBarChartSpec = {
    type: 'bar',
    data: {
      values: []
    },
    bar: {
      style: {
        cornerRadius: 5
      }
    },
    barMinWidth: 20,
    barMaxWidth: 50,
    legends: [{ visible: true, position: 'middle', orient: 'top' }],
    ...theme
  };

  if (!chartOptions) return baseOptions;

  baseOptions = optionsHOC<Visactor.VChart.IBarChartSpec>(baseOptions, chartOptions);

  return chartOptions ? merge(baseOptions, chartProps.chartOptions) : baseOptions;
}

function createOrUpdateChart(chartProps: Props) {
  if (chartContainer.value && !chart) {
    chart = new VChart(parseSpec(chartProps), {
      dom: chartContainer.value
    });

    chart.renderSync();
    return true;
  } else if (chart) {
    chart.updateSpec(parseSpec(chartProps));
    chart.renderSync();

    return true;
  }
  return false;
}

onMounted(() => {
  createOrUpdateChart(props);
});

onUpdated(() => {
  createOrUpdateChart(props);
});

onBeforeUnmount(() => {
  if (chart) {
    chart.release();
  }
});
</script>

<template>
  <div ref="chartContainer" class="h-400px w-full"></div>
</template>

<style scoped></style>
