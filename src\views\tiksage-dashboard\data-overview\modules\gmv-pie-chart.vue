<script setup lang="ts">
import { computed, watch } from 'vue';
import { useVChart } from '@/hooks/common/vchart';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';
import { pieDefaultSpec } from './chart';

interface Props {
  realData: Api.TikSageDashboard.OverviewDataResponse | undefined;
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const gmvByChannelSpec = computed<Visactor.VChart.IPieChartSpec>(() => {
  return {
    ...pieDefaultSpec,
    label: {
      visible: true,
      formatMethod(text: string | string[], datum?: any) {
        const val = numberFormat(datum?.value, NumeralFormat.Dollar) || '$0';
        /* eslint-disable-next-line no-underscore-dangle */
        return [`${text}`, `${val}(${datum?._percent_}%)`];
      }
    }
  };
});
const { updateSpec: setChannelSpec, domRef: gmvByChannelRef } = useVChart(() => gmvByChannelSpec.value);

const gmvByTypeRefSpec = computed<Visactor.VChart.IPieChartSpec>(() => {
  return {
    ...pieDefaultSpec,
    label: {
      visible: true,
      formatMethod(text: string | string[], datum?: any) {
        const val = numberFormat(datum?.value, NumeralFormat.Dollar) || '$0';
        /* eslint-disable-next-line no-underscore-dangle */
        return [`${text}`, `${val}(${datum?._percent_}%)`];
      }
    }
  };
});
const { updateSpec: setTypeSpec, domRef: gmvByTypeRef } = useVChart(() => gmvByTypeRefSpec.value);

function formatChannelData() {
  let values: any[] = [];
  if (props.realData) {
    const { gmvLive, gmvVideo, gmvProductCard } = props.realData.currentData;
    if (gmvLive || gmvVideo || gmvProductCard) {
      values = [
        { type: 'Live', value: gmvLive || 0 },
        { type: 'Video', value: gmvVideo || 0 },
        { type: 'Product Card', value: gmvProductCard || 0 }
      ];
    }
  }
  const cusSpec: Visactor.VChart.IPieChartSpec = {
    type: 'pie',
    data: {
      values
    },
    valueField: 'value',
    categoryField: 'type'
  };
  return cusSpec;
}

function formatTypeData() {
  let values: any[] = [];
  if (props.realData) {
    const { gmvAffiliate, gmvNoAffiliate } = props.realData.currentData;
    if (gmvAffiliate || gmvNoAffiliate) {
      values = [
        { type: 'Affiliate GMV', value: props.realData?.currentData.gmvAffiliate || 0 },
        { type: 'Non-Affiliate GMV', value: props.realData?.currentData.gmvNoAffiliate || 0 }
      ];
    }
  }

  const cusSpec: Visactor.VChart.IPieChartSpec = {
    type: 'pie',
    data: {
      values
    },
    valueField: 'value',
    categoryField: 'type'
  };
  return cusSpec;
}

watch(
  () => props.realData,
  async () => {
    if (!props.realData) return;
    setChannelSpec(opts => {
      return Object.assign(opts, formatChannelData());
    });
    setTypeSpec(opts => {
      return Object.assign(opts, formatTypeData());
    });
  },
  {
    immediate: true
  }
);
</script>

<template>
  <NGrid cols="2" :x-gap="16" :y-gap="16">
    <NGi>
      <NCard class="card-wrapper" :bordered="false" title="Total GMV by Channel">
        <div ref="gmvByChannelRef" class="h-300px"></div>
      </NCard>
    </NGi>
    <NGi>
      <NCard class="card-wrapper" :bordered="false" title="Total GMV by Affiliate">
        <div ref="gmvByTypeRef" class="h-300px"></div>
      </NCard>
    </NGi>
  </NGrid>
</template>

<style scoped></style>
