import { request } from '../request';

/** get role list */
export function fetchGetRoleList(params?: Api.SystemManage.RoleSearchParams) {
  return request<Api.SystemManage.RoleList>({
    url: '/role/list',
    method: 'get',
    params
  });
}

/** create role */
export function fetchCreateRole(data: Pick<Api.SystemManage.Role, 'roleName' | 'roleCode' | 'roleDesc' | 'roleSort'>) {
  return request({
    url: '/role/create',
    method: 'post',
    data
  });
}

/** update role */
export function fetchUpdateRole(
  data: Pick<Api.SystemManage.Role, 'id' | 'roleName' | 'roleDesc' | 'roleSort'> & { menuIds: number[] }
) {
  return request({
    url: '/role/update',
    method: 'put',
    data
  });
}

/** role delete by id */
export function fetchDeleteRoleById(id: string) {
  return request({
    url: '/role/delete',
    method: 'delete',
    params: { id }
  });
}
/** role delete batch */
export function fetchBatchDeleteRoleByIds(ids: string[]) {
  return request({
    url: '/role/deleteBatch',
    method: 'delete',
    data: ids
  });
}

export function fetchGetRoleUserIdList(id: number) {
  return request<number[]>({
    url: '/role/getRoleUserIds',
    method: 'get',
    params: { id }
  });
}

export function fetchBindRoleUser(roleId: number, userIdList: number[]) {
  return request({
    url: '/role/bindUser',
    method: 'put',
    data: { roleId, userIdList }
  });
}

/**
 * get all roles
 *
 * these roles are all enabled
 */
export function fetchGetAllRoles() {
  return request<Api.SystemManage.RoleList>({
    url: '/role/list',
    method: 'get',
    params: { current: 1, size: 999 }
  });
}

/** get role-menu by id */
export function fetchGetRoleMenuById(id: number) {
  return request<Api.SystemManage.RoleMenuResponse>({
    url: '/role/getRoleMenuIds',
    method: 'get',
    params: { id }
  });
}

/** update role-menu by id */
export function fetchUpdateRoleMenu(data: Api.SystemManage.RoleMenuParams) {
  return request({
    url: '/role/bindMenu',
    method: 'put',
    data
  });
}

/** get user list */
export function fetchGetUserList(params?: Api.SystemManage.UserSearchParams) {
  return request<Api.SystemManage.UserList>({
    url: '/user/list',
    method: 'get',
    params
  });
}

/** create user by admin */
export function fetchCreateUser(data: Api.SystemManage.UserUpdateParams) {
  return request({
    url: '/user/create',
    method: 'post',
    data
  });
}

/** update user by admin */
export function fetchUpdateUser(data: Api.SystemManage.UserUpdateParams) {
  return request({
    url: '/user/updateUser',
    method: 'put',
    data
  });
}

/** reset user password by admin */
export function fetchResetPasswordById(id: number) {
  return request<Api.SystemManage.ResetPasswordResponse>({
    url: '/user/resetPasswordById',
    method: 'put',
    params: { id }
  });
}

/** delete user by admin */
export function fetchDeleteUserById(id: number) {
  return request({
    url: '/user/delete',
    method: 'delete',
    params: { id }
  });
}

/** delete user by admin */
export function fetchBatchDeleteUserByIds(ids: number[]) {
  return request({
    url: '/user/deleteBatch',
    method: 'delete',
    data: ids
  });
}

/** get menu list */
export function fetchGetMenuList(params: Api.Common.CommonSearchParams) {
  return request<Api.SystemManage.MenuList>({
    url: '/menu/getMenuTree',
    method: 'get',
    params
  });
}

/** create menu */
export function fetchCreateMenu(data: Exclude<Api.SystemManage.Menu, 'id'>) {
  return request({
    url: '/menu/create',
    method: 'post',
    data
  });
}

/** update menu */
export function fetchUpdateMenu(data: Api.SystemManage.Menu) {
  return request({
    url: '/menu/update',
    method: 'put',
    data
  });
}

/** delete menu by id */
export function fetchDeleteMenuById(id: string) {
  return request({
    url: '/menu/delete',
    method: 'delete',
    params: { id }
  });
}

/** delete menu by ids */
export function fetchBatchDeleteMenuByIds(ids: string[]) {
  return request({
    url: '/menu/deleteBatch',
    method: 'delete',
    data: ids
  });
}

/** get all pages */
export function fetchGetRolePages(roleIdList: number[]) {
  return request<string[]>({
    url: '/user/getRoleMenuList',
    method: 'get',
    params: { roleIdList: roleIdList.join(',') }
  });
}

/** get all pages */
export function fetchGetAllPages(params: Api.Common.CommonSearchParams) {
  return request<Api.Common.PaginatingQueryRecord<Api.SystemManage.Menu>>({
    url: '/menu/list',
    method: 'get',
    params
  });
}

/** get menu tree */
export function fetchGetMenuTree() {
  return request<Api.Common.PaginatingQueryRecord<Api.SystemManage.MenuTree>>({
    url: '/menu/getMenuTree',
    method: 'get'
  });
}

export function fetchGetButtonTree() {
  return request<Api.SystemManage.ButtonTree[]>({
    url: '/user/getRouterButtons',
    method: 'get'
  });
}

export function fetchUpdateDashboardCookie(data: Api.SystemManage.DashboardCookieParams) {
  return request<Api.SystemManage.DashboardCookieResponse>({
    url: '/shop/updateCookie',
    method: 'post',
    data
  });
}

export function fetchUpdateDashboardData(data: Api.SystemManage.DashboardDataParams) {
  return request({
    url: '/sellerAnalytics/pullDataByShopId',
    method: 'post',
    data
  });
}

export function fetchGetShopUserList(shopId: number) {
  return request<number[]>({
    url: '/shop/getShopUser',
    method: 'get',
    params: { shopId }
  });
}

export function fetchBindShopUser(shopId: number, userIdList: number[]) {
  return request({
    url: '/shop/bindUser',
    method: 'put',
    data: { shopId, userIdList }
  });
}
