<script setup lang="ts">
import { computed } from 'vue';
import { useToggle } from '@vueuse/core';
import { isNil } from 'lodash-es';
import { fetchGetCreatorsList } from '@/service/api';
import { useVChart } from '@/hooks/common/vchart';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';

const { VITE_CREATOR_AVATAR_URL } = import.meta.env;

const { numberFormat } = useNumberFormat();

function convertToNumber(str: string) {
  let newStr = str;
  if (isNil(newStr)) return 1;
  // 去掉前面的 $ 符号
  newStr = newStr.replace('$', '');

  // 检查字符串的结尾字符
  const lastChar = newStr.slice(-1).toLowerCase();
  const number = Number.parseFloat(newStr.slice(0, -1));

  if (lastChar === 'k') {
    return number * 1000;
  } else if (lastChar === 'm') {
    return number * 1000000;
  }
  // 如果字符串不以 k 或 m 结尾，直接返回转换后的数字
  return Number.parseFloat(newStr);
}

function followerLevel(number: number) {
  if (number > 0 && number < 10000) {
    return 5;
  } else if (number > 10000 && number < 100000) {
    return 10;
  } else if (number > 100000 && number < 1000000) {
    return 15;
  } else if (number > 1000000 && number < 10000000) {
    return 30;
  } else if (number > 10000000 && number < 100000000) {
    return 70;
  }
  return 90;
}

const spec = computed<Visactor.VChart.ISCatterChartSpec>(() => {
  return {
    type: 'scatter',
    xField: 'x',
    yField: 'y',
    sizeField: 'r',
    size: [10, 100],
    color: ['#9B88FA'],
    point: {
      state: {
        hover: {
          scaleX: 1.2,
          scaleY: 1.2
        }
      },
      style: {
        innerBorder: {
          distance: 1,
          lineWidth: 1
        },
        fillOpacity: 0.5
      }
    },
    data: [
      {
        id: 'data',
        values: []
      }
    ],
    tooltip: {
      activeType: 'mark',
      enterable: true,
      mark: {
        content: [
          {
            key: 'Followers',
            value: v => numberFormat(Number(v?.follower) || 0, NumeralFormat.Number)
          },
          {
            key: 'GMV',
            value: v => `$${v?.gmv || 0}`
          },
          {
            key: 'Units Sold',
            value: v => v?.unitsSold
          },
          {
            key: 'AUR',
            value: v => `$${v?.aur || 0}`
          }
        ]
      },
      style: {
        keyLabel: {
          spacing: 100
        }
      },
      updateElement(tooltipElement, _actualTooltip, params) {
        const { datum } = params;
        const titleEl = tooltipElement.querySelector('.vchart-tooltip-title');
        titleEl?.remove();
        const titleWidth = (tooltipElement.querySelector('.vchart-tooltip-content-box') as HTMLElement)?.offsetWidth;
        const titleWrap = `<div style='display:flex;align-items:center;gap:8px;margin-bottom:16px;width:${titleWidth};' class="vchart-tooltip-title">
        <img style="width:40px;height:40px;border-radius: 50%" src="${VITE_CREATOR_AVATAR_URL + datum.avatar}"/>
        <div style="display:flex;flex-direction:column;gap:8px;font-size:14px;">
        <span style="">${datum.nickname}</span>
        <span>@${datum.id}</span>
        </div>
        </div>`;
        tooltipElement.innerHTML = titleWrap + tooltipElement.innerHTML;
        return tooltipElement;
      }
    },
    axes: [
      {
        type: 'linear',
        title: {
          visible: true,
          text: 'GMV'
        },
        orient: 'left',
        range: { min: 0 },
        label: {
          formatMethod: v => numberFormat(v, NumeralFormat.Dollar)
        }
      },
      {
        title: {
          visible: true,
          text: 'Units Sold'
        },
        orient: 'bottom',
        label: { visible: true, formatMethod: v => numberFormat(v, NumeralFormat.Number) },
        type: 'linear'
      }
    ]
  };
});

const { domRef: bubbleRef, updateSpec } = useVChart(() => spec.value);
const [loading, toggleLoading] = useToggle();
async function initData(searchParams: Partial<Api.CreatorResources.CreatorSearchParams>) {
  toggleLoading(true);
  const { data } = await fetchGetCreatorsList({ ...searchParams, current: 1, size: 100 });
  const result =
    data === null
      ? []
      : data.records.map(c => {
          return {
            ...c,
            x: convertToNumber(c.unitsSold),
            y: convertToNumber(c.gmv),
            r: followerLevel(convertToNumber(c.follower))
          };
        });

  updateSpec(opts => {
    opts.data = [
      {
        id: 'data',
        values: result
      }
    ];
    return opts;
  });
  toggleLoading(false);
}
defineExpose({
  initData
});

initData({});
</script>

<template>
  <NSpin class="h-400px w-full" :show="loading">
    <div ref="bubbleRef" class="h-400px w-full"></div>
  </NSpin>
</template>

<style scoped></style>
