@import './font.css';
@import './reset.css';
@import './nprogress.css';
@import './transition.css';
@import './vidstack.css';

html,
body,
#app {
  height: 100%;
  font-family: 'Nunito Sans', sans-serif;
}

html {
  overflow-x: hidden;
  color: rgb(var(--base-text-color));
}

#app {
  position: relative;
  overflow: hidden;
}

th {
  color: #9ca3af !important;
  font-weight: bold !important;
}

.html-reset {
  /* Basic reset */
  all: initial;
  font-family: 'Nunito Sans', sans-serif;
  font-size: var(--n-font-size);
  color: var(--n-title-text-color);
  line-height: var(--n-line-height);
}

/* Restore the default style of HTML elements */
.html-reset * {
  font-family: 'Nunito Sans', sans-serif;
  all: revert;
}

.html-reset table {
  border-collapse: collapse;
  border: 1px solid #e5e7eb;
}

.html-reset th,
.html-reset td {
  border: 1px solid #e5e7eb;
  padding: 4px 6px;
}
