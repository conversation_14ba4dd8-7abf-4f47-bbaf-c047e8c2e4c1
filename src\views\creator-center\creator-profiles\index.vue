<script setup lang="tsx">
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { useEventBus, useIntervalFn, useToggle } from '@vueuse/core';
import { NAvatar, NEllipsis, NFlex, NSelect, NTag, NText } from 'naive-ui';
import { toLower } from 'lodash-es';
import {
  fetchDownloadCreatorData,
  fetchGetCreatorCenterList,
  fetchGetRunningTask,
  fetchUpdateCreatorEvaluated
} from '@/service/api';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { useTable } from '@/hooks/common/table';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { downloadFile } from '@/utils/download';
import { getFallbackImage } from '@/utils/fake-image';
import { NumeralFormat } from '@/enum';
import SvgIcon from '@/components/custom/svg-icon.vue';
import CreatorImportDrawer from './modules/creator-import-drawer.vue';
import Search from './modules/search.vue';

const { getDictionaryByCodeType } = useDictionaryStore();

const bus = useEventBus('refresh-creator-center-list');

const [showCreatorImportDrawer, toggleShowCreatorImportDrawer] = useToggle(false);

const { numberFormat } = useNumberFormat();

const evaluatedOptions = ref<Api.Dictionary.DictionaryItem<number>[]>([]);

const { data, columns, loading, pagination, getData, updateSearchParams, searchParams } = useTable({
  apiFn: fetchGetCreatorCenterList,
  apiParams: {
    current: 1,
    size: 10
  },
  columns() {
    return [
      {
        key: 'creatorId',
        title: 'Creator ID/Name',
        width: 250,
        fixed: 'left',
        render(rowData) {
          return (
            <NFlex align="center" wrap={false}>
              <NAvatar
                src={`${import.meta.env.VITE_CREATOR_CENTER_AVATAR_URL}${rowData.avatarLocal}`}
                fallbackSrc={getFallbackImage(50, 50)}
              ></NAvatar>
              <NFlex class="flex-1" vertical size={4}>
                <NEllipsis lineClamp={1}>{rowData.nickname}</NEllipsis>
                <NEllipsis lineClamp={1}>@{rowData.creatorId || '-'}</NEllipsis>
                <div class="flex items-center gap-4">
                  <SvgIcon icon={toLower(rowData.gender) === 'male' ? 'twemoji:male-sign' : 'twemoji:female-sign'} />
                  <NTag bordered={false} type="default" size="small">
                    {rowData.category || '-'}
                  </NTag>
                </div>
              </NFlex>
            </NFlex>
          );
        }
      },
      // {
      //   key: 'category',
      //   title: 'Category',
      //   align: 'center',
      //   width: 100,
      //   render(rowData) {
      //     return (

      //     );
      //   }
      // },
      {
        key: 'email',
        title: 'Contact information',
        width: 200,
        render(rowData) {
          return (
            <NFlex vertical>
              <div class="flex items-center gap-1">
                <SvgIcon class="text-icon text-gray" icon="tabler:mail" />
                <NEllipsis>{rowData.email || '-'}</NEllipsis>
              </div>
              <div class="flex items-center gap-1">
                <SvgIcon class="text-icon text-gray" icon="tabler:phone" />
                <NEllipsis>{rowData.phone || '-'}</NEllipsis>
              </div>
            </NFlex>
          );
        }
      },
      {
        key: 'language',
        title: 'Language',
        align: 'center',
        width: 120
      },
      {
        key: 'race',
        title: 'Race',
        align: 'center',
        width: 120
      },
      {
        key: 'client',
        title: 'Client',
        align: 'center',
        width: 120
      },
      {
        key: 'slottingFee',
        title: 'Slotting Fee',
        align: 'center',
        width: 120
      },
      {
        key: 'commission',
        title: 'Commission',
        align: 'center',
        width: 120
      },
      {
        key: 'follower',
        title: 'Follower',
        width: 200,
        align: 'center',
        render(rowData) {
          const { followerFemaleRatio, followerMaleRatio } = rowData;
          let showGender;
          let showRatio;
          if (followerMaleRatio > followerFemaleRatio) {
            showGender = 'twemoji:male-sign';
            showRatio = numberFormat(followerMaleRatio, NumeralFormat.Percent);
          } else {
            showGender = 'twemoji:female-sign';
            showRatio = numberFormat(followerFemaleRatio, NumeralFormat.Percent);
          }
          return (
            <NFlex vertical>
              {/* <NEllipsis style="max-width:150px" lineClamp={1} class="font-bold"></NEllipsis> */}
              <NFlex align="center">
                <SvgIcon icon="tabler:users" />
                <NText>{rowData.follower}</NText>
                {<SvgIcon icon={showGender} />}
                {showRatio}
              </NFlex>
              <NFlex align="center">
                {JSON.parse(rowData.followerAgeJson).map((cgy: any) => {
                  return (
                    <NTag size="small" bordered={false}>
                      {cgy}
                    </NTag>
                  );
                })}
              </NFlex>
            </NFlex>
          );
        }
      },
      {
        key: 'evaluatedType',
        title: 'Evaluated',
        align: 'center',
        fixed: 'right',
        width: 130,
        render(rowData) {
          return (
            <NSelect
              size="small"
              placeholder="Evaluated"
              options={evaluatedOptions.value}
              value={rowData.evaluatedType}
              valueField="code"
              labelField="name"
              onUpdate:value={v => handleUpdateEvaluated(rowData.id, v)}
            ></NSelect>
          );
        }
      }
    ];
  }
});

async function handleUpdateEvaluated(id: number, value: number) {
  const { error } = await fetchUpdateCreatorEvaluated({
    id,
    evaluatedType: value
  });
  if (!error) {
    window.$message?.success('Update successfully.');
    getData();
  }
}

async function handleSearchParams(params: Api.CreatorCenter.CreatorCenterSearchParams) {
  updateSearchParams({ ...params });

  await getData();
}

async function handleDownloadCreatorData() {
  const { data: creatorXlsxData, error } = await fetchDownloadCreatorData(searchParams);
  if (!error) {
    downloadFile(creatorXlsxData, 'xlsx', 'Creator Center');
  }
}

async function initEvaluatedOptions() {
  const evaluatedOpts = await getDictionaryByCodeType<number>('select_option_evaluated');
  if (evaluatedOpts) {
    evaluatedOptions.value = evaluatedOpts;
  }
}

const isRunningTaskError = ref(false);

const { resume, pause } = useIntervalFn(async () => {
  const { data: runningTask, error } = await fetchGetRunningTask();
  if (!error) {
    if ([11, 12, 13].includes(runningTask.grabTaskStatus)) {
      isRunningTaskError.value = true;
    } else {
      isRunningTaskError.value = false;
    }
  }
}, 1000 * 30);

onMounted(() => {
  resume();
});

onUnmounted(() => {
  pause();
});

initEvaluatedOptions();

watch(
  () => showCreatorImportDrawer.value,
  newVal => {
    if (!newVal) {
      getData();
      bus.emit('refresh-creator-center-list');
    }
  }
);
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="card-wrapper" :bordered="false" title="Creator Profiles">
      <template #header-extra>
        <div class="relative flex-y-center justify-end gap-4">
          <span v-show="isRunningTaskError" class="relative size-3 flex">
            <span class="absolute h-full w-full inline-flex animate-ping rounded-full bg-warning-400 opacity-75"></span>
            <span class="relative size-3 inline-flex rounded-full bg-warning-500"></span>
          </span>
          <NButton strong secondary @click="toggleShowCreatorImportDrawer(true)">
            <template #icon>
              <icon-solar:add-circle-linear />
            </template>
            Import Creator
          </NButton>
        </div>
      </template>
      <Search class="w-full" :evaluated-options="evaluatedOptions" @update:search-params="handleSearchParams" />
    </NCard>
    <NCard class="h-full min-h-400px card-wrapper" :bordered="false" title="Creator List">
      <template #header-extra>
        <ButtonIcon icon="solar:download-linear" @click="handleDownloadCreatorData" />
      </template>
      <NDataTable
        class="h-full min-h-400px"
        remote
        flex-height
        :scroll-x="1580"
        :data="data"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        :bordered="false"
      />
    </NCard>
    <CreatorImportDrawer v-model:show="showCreatorImportDrawer" ImportDrawer />
  </NFlex>
</template>

<style scoped></style>
