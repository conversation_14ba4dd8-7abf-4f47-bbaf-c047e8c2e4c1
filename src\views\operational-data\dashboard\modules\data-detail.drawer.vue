<script setup lang="tsx">
import { computed, watch } from 'vue';
import dayjs from 'dayjs';
import { fetchDeleteOperationalDashboardDataById, fetchGetClientDataByDate } from '@/service/api/operational-data';
import { useAppStore } from '@/store/modules/app';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import { TimeFormat } from '@/enum';
import DataOperateDrawer from './data-operate-drawer.vue';

interface Props {
  rowData?: Api.OperationalData.MonthlyData | null;
}

const props = defineProps<Props>();

const appStore = useAppStore();

const width = computed(() => {
  return appStore.needHorizontalScreen ? '100%' : '80%';
});

const visible = defineModel<boolean>('visible', {
  default: false
});

const { columns, updateSearchParams, data, getData, loading, mobilePagination } = useTable({
  immediate: false,
  apiFn: fetchGetClientDataByDate,
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    year: props.rowData?.year,
    month: props.rowData?.month
  },
  columns: () => [
    {
      key: 'id',
      title: 'Date',
      width: 120,
      align: 'center',
      fixed: 'left',
      render(rowData) {
        return dayjs().year(rowData.year).month(rowData.month).format(TimeFormat.US_DATE_NO_DAY);
      }
    },
    { key: 'clientName', title: 'Client Name', width: 120, fixed: 'left', align: 'center' },
    { key: 'grossProfitFormatted', title: 'Gross Profit', width: 120, align: 'center' },
    { key: 'incomeFormatted', title: 'Income', width: 120, align: 'center' },
    { key: 'expenditureFormatted', title: 'Expenditure', width: 120, align: 'center' },
    { key: 'gmvFormatted', title: 'GMV', width: 120, align: 'center' },
    { key: 'orderNum', title: 'Order', width: 120, align: 'center' },
    { key: 'affiliates', title: 'Affiliates', width: 120, align: 'center' },
    { key: 'videos', title: 'Videos', width: 120, align: 'center' },
    { key: 'livestreams', title: 'Livestreams', width: 120, align: 'center' },
    { key: 'advertisingFormatted', title: 'Advertising', width: 120, align: 'center' },
    {
      key: 'operate',
      title: '',
      align: 'center',
      width: 100,
      fixed: 'right',
      render: row => {
        return (
          <div class="flex-center">
            <ButtonIcon
              icon="solar:settings-bold-duotone"
              tooltipContent="Detail"
              tooltipPlacement="top"
              // @ts-ignore
              onClick={() => edit(row.id)}
            />
            <ButtonIcon
              style={{
                color: 'red'
              }}
              icon="solar:trash-bin-2-linear"
              tooltipContent="Detail"
              tooltipPlacement="top"
              // @ts-ignore
              onClick={() => handleDelete(row.id)}
            />
          </div>
        );
      }
    }
  ]
});

const {
  drawerVisible,
  operateType,
  editingData,
  handleEdit,
  checkedRowKeys,
  onDeleted
  // closeDrawer
} = useTableOperate(data, getData);

function edit(id: number) {
  handleEdit(id);
}

async function handleDelete(id: number) {
  window.$dialog?.error({
    title: 'Delete',
    content() {
      return $t('common.confirmDelete');
    },
    closable: false,
    negativeText: 'Cancel',
    positiveText: 'Delete',
    async onPositiveClick() {
      // request
      const { error } = await fetchDeleteOperationalDashboardDataById(id);
      if (error) return;
      onDeleted();
    }
  });
}

watch(visible, () => {
  if (visible.value) {
    updateSearchParams({ year: props.rowData?.year, month: props.rowData?.month });
    getData();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" placement="right" display-directive="show" :width="width">
    <NDrawerContent title="Client Detail" :native-scrollbar="false" closable>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :bordered="false"
        :columns="columns"
        :data="data"
        size="small"
        :scroll-x="1420"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
    </NDrawerContent>
    <DataOperateDrawer
      v-model:visible="drawerVisible"
      :row-data="editingData"
      :operate-type="operateType"
      @submitted="getData"
    />
  </NDrawer>
</template>

<style scoped></style>
