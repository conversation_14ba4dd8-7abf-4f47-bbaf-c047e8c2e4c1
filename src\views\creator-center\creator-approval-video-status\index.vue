<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-12-02 11:47:35
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-12-02 15:57:07
 * @FilePath: \tiksage-frontend\src\views\creator-manage\video-status\index.vue
 * @Description: video-status
-->
<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useToggle } from '@vueuse/core';
import { fetchGetApprovalUserOptions } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useAuth } from '@/hooks/business/auth';
import TabContainer from './modules/tab-container.vue';

const { hasAuth } = useAuth();
const authStore = useAuthStore();

const needGoBack = computed(() => {
  const route = useRoute();
  return Boolean(route.query?.redirect);
});

const clientOptions = ref<Api.VideoManage.ApprovalUserOption[]>([]);

const [loading, toggleLoading] = useToggle(false);

async function initClientOptions() {
  toggleLoading(true);
  if (hasAuth('video-status:approver')) {
    clientOptions.value = [
      {
        userId: Number(authStore.userInfo.id),
        shopName: '',
        shopId: -1
      }
    ];
  }
  if (hasAuth('video-status:operator')) {
    const { data: optionsData, error: optionsError } = await fetchGetApprovalUserOptions();
    if (!optionsError) {
      clientOptions.value = optionsData;
    }
  }
  toggleLoading(false);
}

initClientOptions();
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="card-wrapper" :bordered="false" title="Creator Content">
      <template v-if="needGoBack" #header-extra>
        <ButtonBack />
      </template>
    </NCard>
    <NSpin v-if="loading"></NSpin>
    <TabContainer v-else :client-options="clientOptions" />
  </NFlex>
</template>

<style scoped></style>
