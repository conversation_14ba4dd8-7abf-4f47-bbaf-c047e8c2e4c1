<script setup lang="ts">
import { computed } from 'vue';

const copyright = computed(() => {
  const year = new Date().getFullYear();
  return `Copyright © 2024-${year} TikSage Corporation. All rights reserved.`;
});

defineOptions({
  name: '<PERSON>Footer'
});
</script>

<template>
  <DarkModeContainer class="h-full flex-center gap-4">
    <a href="https://tiksage.com/" target="_blank" rel="noopener noreferrer">
      {{ copyright }}
    </a>
    <a
      class="underline"
      href="https://www.tiksage.com/images/privacy/TikSage%20Privacy%20Policy.pdf"
      target="_blank"
      rel="noopener noreferrer"
    >
      PrivacyPolicy
    </a>
  </DarkModeContainer>
</template>

<style scoped></style>
