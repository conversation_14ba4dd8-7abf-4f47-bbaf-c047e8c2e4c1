<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-09-19 09:35:24
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-09-24 15:36:54
 * @FilePath: \tiksage-frontend\src\components\vchart\v-line-chart.vue
 * @Description: visactor-line-chart
-->
<script setup lang="ts">
import { onBeforeUnmount, onMounted, onUpdated, ref } from 'vue';
import { merge } from 'lodash-es';
import VChart from '@visactor/vchart';
import { theme } from '@/constants/visactor-vchart';
import { optionsHOC } from '@/utils/chart-options';

interface Props {
  chartOptions?: Visactor.VChart.ILineChartSpec;
}
const props = defineProps<Props>();

let chart: Visactor.VChart.IVChart;

const chartContainer = ref<HTMLDivElement>();

function parseSpec(chartProps: Props) {
  const { chartOptions } = chartProps;

  let baseOptions: Visactor.VChart.ILineChartSpec = {
    type: 'line',
    data: [],
    legends: [{ visible: true, position: 'middle', orient: 'top' }],
    series: [],
    axes: [
      {
        orient: 'bottom',
        label: {
          formatter: props.chartOptions?.xField === 'time' ? '%b %d' : undefined
        }
      }
    ],
    ...theme
  };

  if (!chartOptions) return baseOptions;

  baseOptions = optionsHOC(baseOptions, chartOptions);

  const res = chartOptions ? merge(baseOptions, chartProps.chartOptions) : baseOptions;
  return res;
}

function createOrUpdateChart(chartProps: Props) {
  if (chartContainer.value && !chart) {
    chart = new VChart(parseSpec(chartProps), {
      dom: chartContainer.value
    });

    chart.renderSync();
    return true;
  } else if (chart) {
    chart.updateSpec(parseSpec(chartProps));
    chart.renderSync();

    return true;
  }
  return false;
}

onMounted(() => {
  createOrUpdateChart(props);
});

onUpdated(() => {
  createOrUpdateChart(props);
});

onBeforeUnmount(() => {
  if (chart) {
    chart.release();
  }
});
</script>

<template>
  <div ref="chartContainer" class="h-400px w-full"></div>
</template>

<style scoped></style>
