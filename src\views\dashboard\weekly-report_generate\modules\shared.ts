import { isNil } from 'lodash-es';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';

const { numberFormat } = useNumberFormat();

export function renderCell(value: any, _rowData: object, column: any) {
  if (isNil(value)) return value;

  const { unit } = column;
  if (unit) {
    if (unit === NumeralFormat.Percent) {
      return numberFormat(value / 100, NumeralFormat.Percent);
    }
    return numberFormat(value, unit);
  } else if (typeof value === 'number') {
    return numberFormat(value, NumeralFormat.Real_Number);
  }
  return value;
}
