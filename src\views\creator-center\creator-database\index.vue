<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-19 16:48:21
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-11-07 15:15:06
 * @FilePath: \tiksage-frontend\src\views\creator-resources\index.vue
 * @Description: Creator Resources Page
-->
<script setup lang="tsx">
import { ref } from 'vue';
import { NAvatar, NEllipsis, NFlex, NTag, NText } from 'naive-ui';
import { fetchGetCreatorsList, fetchLastSyncTime } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { dateFormat } from '@/utils/date';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { SourceType } from '@/enum';
import CreatorSearch from './modules/creator-search.vue';
import BubbleChart from './modules/bubble-chart.vue';

const { columns, data, getData, loading, mobilePagination, resetSearchParams, searchParams } = useTable({
  showTotal: true,
  apiParams: {
    current: 1,
    size: 10,
    idName: null,
    categoryArr: null,
    followersRangeArr: null,
    followersMin: null,
    followersMax: null,
    followerAgeArr: null,
    followerGender: null,
    gmvMin: null,
    gmvMax: null,
    unitsSoldMin: null,
    unitsSoldMax: null,
    avgViewsRangeArr: null
  },
  apiFn: fetchGetCreatorsList,
  columns: () => [
    // {
    //   type: 'selection',
    //   align: 'center',
    //   fixed: 'left',
    //   width: 50
    // },
    {
      key: 'index',
      title: 'Index',
      fixed: 'left',
      align: 'center',
      width: 70
    },
    {
      key: 'creatorId',
      title: 'Creator',
      // align: 'center',
      fixed: 'left',
      width: 250,
      render: row => {
        return (
          <NFlex vertical>
            <NFlex justify="start" align="center" wrap={false}>
              <NAvatar
                style={`width:50px;height:50px`}
                round
                src={`${import.meta.env.VITE_CREATOR_AVATAR_URL}${row.avatar}`}
              >
                {{
                  fallback() {
                    return (
                      <div class="h-full w-full flex-center">
                        <SvgIcon icon="tabler:user-exclamation" class="h-32px w-32px" />
                      </div>
                    );
                  }
                }}
              </NAvatar>
              <NFlex vertical justify="center">
                <NEllipsis style="max-width:150px" lineClamp={1}>
                  <span class="font-bold">{row.nickname}</span>
                </NEllipsis>
                <NEllipsis style="max-width:150px" lineClamp={1}>
                  <span class="text-gray">{row.id}</span>
                </NEllipsis>
              </NFlex>
            </NFlex>
          </NFlex>
        );
      }
    },
    {
      key: 'follower',
      title: 'Followers',
      width: 200,
      render(rowData) {
        return (
          <NFlex vertical>
            {/* <NEllipsis style="max-width:150px" lineClamp={1} class="font-bold"></NEllipsis> */}
            <NFlex align="center">
              <SvgIcon icon="tabler:users" />
              <NText>{rowData.follower}</NText>
              {<SvgIcon icon={rowData.followerGender === 'Male' ? 'twemoji:male-sign' : 'twemoji:female-sign'} />}
              {`${rowData.followerGenderRatio}%`}
            </NFlex>
            <NFlex align="center">
              {JSON.parse(rowData.followerAgeJson).map((cgy: any) => {
                return (
                  <NTag size="small" bordered={false}>
                    {cgy}
                  </NTag>
                );
              })}
            </NFlex>
          </NFlex>
        );
      }
    },
    {
      key: 'categoryJson',
      title: 'Categories',
      width: 200,
      render(rowData) {
        return (
          <NFlex>
            {JSON.parse(rowData.categoryJson).map((cgy: any) => {
              return (
                <NTag size="small" bordered={false}>
                  {cgy}
                </NTag>
              );
            })}
          </NFlex>
        );
      }
    },
    {
      key: 'gmv',
      title: 'GMV',
      align: 'center',
      width: 110,
      render(rowData) {
        return `$${rowData.gmv || '-'}`;
      }
    },
    {
      key: 'unitsSold',
      title: 'Units Sold',
      align: 'center',
      width: 110
    },
    {
      key: 'avgViews',
      title: 'Avg. Views',
      align: 'center',
      width: 110
    },
    {
      key: 'sourceType',
      title: 'Source Channel',
      align: 'center',
      width: 110,
      render(rowData) {
        return <NTag>{rowData.sourceType === SourceType.TikTok ? 'Official' : 'Non-Official'}</NTag>;
      }
    }
  ]
});

const checkedRowKeys = ref<string[]>([]);

// send email to creator
// const onInvite = () => {
//   window.$dialog?.create({
//     title: 'Invite Creator',
//     content() {
//       return (
//         <NFlex vertical>
//           <NText class="color-coolgray">
//             Creators will be notified about your company information through TikTok Shop, includling your contact
//             information, company name, company address, product categories, and more. View details.
//           </NText>
//           <NForm>
//             <NFormItem label="Invitation purpose">
//               <NRadio>Inviting the creator for product distribution</NRadio>
//             </NFormItem>
//             <NFormItem label="Message to creator">
//               <NInput
//                 type="textarea"
//                 placeholder="Tell creators why and how you want to work with them and what's unique about yourcompany. Your message should be professional and respectful, otherwise the creatormay file a dispute and the platform may take action."
//                 maxlength="500"
//                 showCount
//                 autosize={{ minRows: 4 }}
//               />
//             </NFormItem>
//             <NText>Contact Information</NText>
//             <div class="bg-coolgray-1 p2">
//               <NFormItem label="Email Address">
//                 <NInput>{{ prefix: () => <SvgIcon icon="ic:outline-email" /> }}</NInput>
//               </NFormItem>
//               <NFormItem label="Email Address">
//                 <NInput placeholder="please enter a phone"></NInput>
//               </NFormItem>
//             </div>
//           </NForm>
//         </NFlex>
//       )
//     },
//     style: 'width:650px',
//     positiveText: 'Send Invite',
//     negativeText: 'Cancel'
//   })
// }

const bubbleChartRef = ref<InstanceType<typeof BubbleChart>>();

const handleSearch = () => {
  getData();
  bubbleChartRef.value && bubbleChartRef.value.initData(searchParams);
};

const handleReset = () => {
  resetSearchParams();
  getData();
  bubbleChartRef.value && bubbleChartRef.value.initData(searchParams);
};

// watch(
//   () => searchParams,
//   () => {
//     const params = omitBy(searchParams, v => v === null || undefined)
//     if (Object.keys(params).length > 2 && !has(params, 'idName')) {
//       toggleShowBubble(true)
//     } else {
//       toggleShowBubble(false)
//     }
//   },
//   {
//     deep: true
//   }
// )

const lastSyncTime = ref('');

async function getLastSyncTime() {
  const { data: responseData, error } = await fetchLastSyncTime();
  if (error) return;
  lastSyncTime.value = dateFormat(responseData.lastSyncDate);
}

getLastSyncTime();
</script>

<template>
  <NSpace vertical :size="16">
    <CreatorSearch v-model:model="searchParams" @reset="handleReset" @search="handleSearch" />
    <NCard :bordered="false" title="">
      <template #header>
        <NFlex vertical>
          <NText>Creator List</NText>
          <NText class="text-sm text-coolgray">* Update Interval: {{ lastSyncTime }}.</NText>
        </NFlex>
      </template>
      <template #header-extra>
        <!-- <NButton type="info" :disabled="checkedRowKeys.length < 1" @click="onInvite">Invite</NButton> -->
      </template>
      <div>
        <!-- <BubbleChartCard ref="bubbleChartRef" /> -->
        <BubbleChart ref="bubbleChartRef" />
      </div>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :bordered="false"
        remote
        :loading="loading"
        :columns="columns"
        size="small"
        :data="data"
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
        scroll-x="1160"
      />
    </NCard>
  </NSpace>
</template>

<style scoped></style>
