<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useToggle } from '@vueuse/core';
import type { FormItemRule } from 'naive-ui';
import dayjs from 'dayjs';
import {
  fetchCreateInvoice,
  fetchDownloadInvoicePDF,
  fetchGetContactList,
  fetchGetDefaultInvoiceInfo,
  fetchGetInvoiceDetail,
  fetchGetLastMonthEstCommission
} from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { useRouterPush } from '@/hooks/common/router';
import { NumeralFormat, TimeFormat } from '@/enum';
import ContactCreateDialog from './modules/contact-create-dialog.vue';
import PreviewCard from './modules/preview-card.vue';

const { VITE_INVOICE_URL } = import.meta.env;

const route = useRoute();
const invoiceId = route.query.invoiceId;

interface Props {
  id: string;
}

const props = defineProps<Props>();

const { routerPushByKey } = useRouterPush();
const { numberFormat } = useNumberFormat();

const [addContactShow, toggleAddContactShow] = useToggle(false);

const contactOptions = ref<Api.Invoice.Contactor[]>([]);

const lastEstCommission = ref<Api.TikSageDashboard.LastMonthEstCommissionResponse>();

const { formRef, validate } = useNaiveForm();

type Model = CommonType.RecordNullable<Api.Invoice.InvoiceCreator>;
const model = ref<Model>(createDefaultModel());

const rules = computed<Record<keyof Model, FormItemRule[]>>(() => {
  const { defaultRequiredRule } = useFormRules();
  return {
    clientName: [defaultRequiredRule],
    issueDate: [defaultRequiredRule],
    dueDate: [defaultRequiredRule],
    invoiceDetailList: [defaultRequiredRule],
    tax: [defaultRequiredRule],
    amountPaid: [defaultRequiredRule]
  };
});

function createDefaultModel(): Model {
  return {
    shopId: Number(props.id),
    clientName: null,
    invoiceNo: 0,
    invoiceNoStr: '', // not real model
    issueDate: null,
    dueDate: null,
    invoiceDetailList: [
      {
        itemName: '',
        itemDesc: '',
        rate: 0,
        qty: 0,
        total: 0,
        sort: 0
      }
    ],
    subtotal: 0, // not real model
    tax: 0,
    total: 0,
    amountPaid: 0
  };
}

function handleAddContact() {
  toggleAddContactShow();
}

function handleCreateInvoiceItem(): Api.Invoice.InvoiceItem {
  return {
    itemName: '',
    itemDesc: '',
    rate: 0,
    qty: 0,
    total: 0,
    sort: 0
  };
}

function isDueDisabledDate(ts: number) {
  if (model.value.issueDate) {
    return ts < dayjs(model.value.issueDate).valueOf();
  }
  return false;
}

function handleReset() {
  model.value = createDefaultModel();
}

const [saveLoading, toggleSaveLoading] = useToggle(false);
async function handleSave() {
  await validate();
  toggleSaveLoading(true);
  const { data: createInvoiceId, error: createInvoiceError } = await fetchCreateInvoice(model.value as any);
  if (!createInvoiceError) {
    window.$message?.success('Generated successfully');
    model.value = createDefaultModel();
    window.$dialog?.create({
      title: 'Invoice Generated Successfully',
      content: 'Your invoice has been generated. Would you like to:',
      positiveText: 'Download PDF',
      negativeText: 'View History',
      onPositiveClick: async () => {
        const { data: url, error: downloadPDFError } = await fetchDownloadInvoicePDF(createInvoiceId);
        if (!downloadPDFError) {
          window.open(VITE_INVOICE_URL + url?.fileName, '_blank');
        }
      },
      onNegativeClick: () => {
        routerPushByKey('tiksage-dashboard_shop-commission');
      }
    });
  }
  toggleSaveLoading(false);
}

async function initContactOptions() {
  const { data: contactOpts, error: contactOptionsError } = await fetchGetContactList(Number(props.id));
  if (!contactOptionsError) {
    contactOptions.value = contactOpts || [];
  }
}

async function initDefaultInfo() {
  const { data: defaultInfo, error: defaultInfoError } = await fetchGetDefaultInvoiceInfo(Number(props.id));
  if (!defaultInfoError) {
    model.value.invoiceNoStr = defaultInfo?.invoiceNoStr;
    model.value.invoiceNo = defaultInfo?.invoiceNo;
    model.value.clientName = defaultInfo?.defaultClientName;
  }
}

async function initEstCommission(date: string) {
  const { data: estCommissionInfo, error: estCommissionInfoError } = await fetchGetLastMonthEstCommission(
    Number(props.id),
    date
  );
  if (!estCommissionInfoError) {
    lastEstCommission.value = estCommissionInfo || undefined;
    model.value.dueDate = estCommissionInfo.dueDate;
  }
}

function initData() {
  initContactOptions();
  initDefaultInfo();
}

function handleContactChange() {
  initData();
}

initData();

watch(
  () => addContactShow.value,
  newVal => {
    if (!newVal) {
      initContactOptions();
    }
  }
);

function calculateLineTotal(item: Api.Invoice.InvoiceItem) {
  return item.rate * item.qty;
}

watch(
  () => invoiceId,
  async newVal => {
    if (newVal) {
      const { data: oddInvoiceData, error: oddInvoiceError } = await fetchGetInvoiceDetail(Number(newVal));
      if (!oddInvoiceError) {
        model.value = {
          ...oddInvoiceData,
          issueDate: dayjs(oddInvoiceData?.issueDate).format(TimeFormat.CN_DATE),
          dueDate: dayjs(oddInvoiceData?.dueDate).format(TimeFormat.CN_DATE),
          invoiceDetailList: oddInvoiceData?.invoiceDetailList || [handleCreateInvoiceItem()]
        };
      }
    }
  },
  { immediate: true }
);

watch(
  () => model.value.invoiceDetailList,
  newVal => {
    let total = 0;
    if (newVal?.length) {
      newVal.forEach(item => {
        item.total = calculateLineTotal(item);
        total += item.total || 0;
      });
    }

    if (total < 0) total = 0;

    model.value.subtotal = total;
  },
  {
    immediate: true,
    deep: true
  }
);

watch(
  () => model.value.issueDate,
  newVal => {
    if (newVal) {
      initEstCommission(newVal);
    } else {
      lastEstCommission.value = undefined;
    }
  }
);

watch(
  [() => model.value.subtotal, () => model.value.tax, () => model.value.amountPaid],
  ([subtotal, tax, amountPaid]) => {
    model.value.total = subtotal + tax + amountPaid;
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="card-wrapper" :bordered="false" title="Create Invoice">
      <template #header-extra>
        <ButtonBack />
      </template>
    </NCard>
    <NGrid :y-gap="16" :x-gap="16">
      <NGi span="12">
        <NCard class="transform-scale-80 card-wrapper" :bordered="false">
          <PreviewCard v-model:value="model" />
        </NCard>
      </NGi>
      <NGi span="12">
        <NCard class="card-wrapper" :bordered="false" title="Invoice Info">
          <NForm ref="formRef" :model="model" :rules="rules">
            <NFormItem label="Invoice Number">
              <div class="flex-col">
                <NInput disabled :value="model.invoiceNoStr"></NInput>
                <div class="mt-1 text-xs text-gray-400">
                  * This number is for reference only. The actual invoice number will be generated by the system.
                </div>
              </div>
            </NFormItem>
            <NFormItem label="Billed To" path="clientName">
              <div class="flex justify-between gap-2">
                <NSelect
                  v-model:value="model.clientName"
                  class="w-250px"
                  :options="contactOptions"
                  label-field="name"
                  value-field="name"
                ></NSelect>
                <NButton class="ml-4" text type="primary" @click="handleAddContact">Add Contact</NButton>
              </div>
            </NFormItem>
            <NGrid cols="2">
              <NGi>
                <NFormItem label="Date of Issue" path="issueDate">
                  <NDatePicker v-model:formatted-value="model.issueDate" type="date"></NDatePicker>
                </NFormItem>
                <NFormItem label="Due Date" path="dueDate">
                  <NDatePicker
                    v-model:formatted-value="model.dueDate"
                    type="date"
                    :is-date-disabled="isDueDisabledDate"
                  ></NDatePicker>
                </NFormItem>
              </NGi>
              <NGi>
                <NFormItem v-show="lastEstCommission" label="Est.commission">
                  <div class="w-full rounded bg-primary-100 p-4">
                    <NDescriptions :column="1" label-placement="left">
                      <NDescriptionsItem label="Date">
                        {{ dayjs(lastEstCommission?.month).format('MMM YYYY') }}
                      </NDescriptionsItem>
                      <NDescriptionsItem label="GMV">
                        {{
                          lastEstCommission?.gmv ? numberFormat(lastEstCommission?.gmv, NumeralFormat.Real_Dollar) : '-'
                        }}
                      </NDescriptionsItem>
                      <NDescriptionsItem label="Est. commission">
                        {{
                          lastEstCommission?.estCommission
                            ? numberFormat(lastEstCommission?.estCommission, NumeralFormat.Real_Dollar)
                            : '-'
                        }}
                      </NDescriptionsItem>
                    </NDescriptions>
                  </div>
                </NFormItem>
              </NGi>
            </NGrid>
            <NFormItem label="Description" path="invoiceDetailList">
              <NDynamicInput
                v-model:value="model.invoiceDetailList as any"
                item-class="flex gap-2"
                preset="pair"
                @create="handleCreateInvoiceItem"
              >
                <template #default="{ value }">
                  <div class="mb-4 flex-col gap-2">
                    <div class="flex-col gap-2">
                      <NInput v-model:value="value.itemName" placeholder="Item Name"></NInput>
                      <NInput v-model:value="value.itemDesc" placeholder="Item Description"></NInput>
                    </div>
                    <div class="flex gap-2">
                      <div>
                        <div>Rate</div>
                        <NInputNumber v-model:value="value.rate" :precision="0" :show-button="false" placeholder="">
                          <template #prefix>
                            <icon-tabler:currency-dollar />
                          </template>
                        </NInputNumber>
                      </div>
                      <div>
                        <div>Qty</div>
                        <NInputNumber
                          v-model:value="value.qty"
                          :min="0"
                          :precision="0"
                          :show-button="false"
                          placeholder=""
                        ></NInputNumber>
                      </div>
                      <div>
                        <div>Line Total</div>
                        <NInputNumber disabled :value="value.total" :precision="0" :show-button="false" placeholder="">
                          <template #prefix>
                            <icon-tabler:currency-dollar />
                          </template>
                        </NInputNumber>
                      </div>
                    </div>
                    <!-- <NDivider /> -->
                  </div>
                </template>
                <template #action="{ index, create, remove }">
                  <div class="flex-col">
                    <div class="mb-38px h-34px w-34px flex-center rounded-full bg-primary-100 text-primary">
                      {{ index + 1 }}
                    </div>
                    <NButtonGroup vertical>
                      <NButton class="text-primary" @click="() => create(index)">+</NButton>
                      <NButton class="text-warning" @click="() => remove(index)">-</NButton>
                    </NButtonGroup>
                  </div>
                </template>
              </NDynamicInput>
            </NFormItem>
            <NGrid cols="2">
              <NFormItemGi label="Tax" path="tax">
                <NInputNumber v-model:value="model.tax" :min="0" :precision="0" placeholder="">
                  <template #prefix>
                    <icon-tabler:currency-dollar />
                  </template>
                </NInputNumber>
              </NFormItemGi>
              <NFormItemGi label="Amount Paid" path="amountPaid">
                <NInputNumber v-model:value="model.amountPaid" :min="0" :precision="0" placeholder="">
                  <template #prefix>
                    <icon-tabler:currency-dollar />
                  </template>
                </NInputNumber>
              </NFormItemGi>
            </NGrid>
            <NFormItem label="Amount Due(USD)">
              <span class="text-8 text-primary">{{ numberFormat(model.total || 0, NumeralFormat.Real_Dollar) }}</span>
            </NFormItem>
          </NForm>
          <template #action>
            <div class="flex justify-end gap-4">
              <NButton @click="handleReset">Reset</NButton>
              <NButton type="primary" :loading="saveLoading" @click="handleSave">Save & Generate</NButton>
            </div>
          </template>
        </NCard>
      </NGi>
    </NGrid>
    <ContactCreateDialog v-model:show="addContactShow" :shop-id="Number(props.id)" @change="handleContactChange" />
  </NFlex>
</template>

<style scoped></style>
