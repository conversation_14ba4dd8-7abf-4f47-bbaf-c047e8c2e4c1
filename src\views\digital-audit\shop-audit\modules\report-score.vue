<script setup lang="ts">
import { computed, onUnmounted, ref } from 'vue';
import { useIntervalFn, useToggle } from '@vueuse/core';
import { groupBy, map } from 'lodash-es';
import { fetchGetShopAuditScoreDataByTaskId } from '@/service/api';
import RadarChart from './radar-chart.vue';
import GaugeChart from './gauge-chart.vue';

enum Type {
  Strengths = 1,
  Opportunities = 2,
  Improvement = 3
}

interface Props {
  taskId: number;
  isDownload: boolean;
}

const props = defineProps<Props>();

const [loading, toggleLoading] = useToggle(true);

const shopAuditScore = ref<Api.ShopAudit.ShopAuditScore>({
  totalScore: 0,
  scoreList: [] as any
});

const showSuggestionType = ref<Type>(Type.Strengths);

type Suggestion = {
  rating: Type;
  title: string;
  color: NaiveUI.ThemeColor;
  report: Api.ShopAudit.ScoreList[];
};

const suggestion = ref<Suggestion[]>([]);

const showReports = computed(() => {
  return suggestion.value.find(item => item.rating === showSuggestionType.value)?.report;
});

function getSuggestion() {
  const typeObj: Record<Type, { title: string; color: NaiveUI.ThemeColor }> = {
    [Type.Strengths]: {
      title: 'Strengths',
      color: 'success'
    },
    [Type.Opportunities]: {
      title: 'Opportunities',
      color: 'primary'
    },
    [Type.Improvement]: {
      title: 'Improvement',
      color: 'warning'
    }
  };

  const reportObj = groupBy(shopAuditScore.value.scoreList, 'rating');
  const res = map(reportObj, (value, key) => {
    return {
      rating: Number(key) as Type,
      title: typeObj[Number(key) as Type].title,
      color: typeObj[Number(key) as Type].color,
      report: value
    };
  });
  suggestion.value = res;
  showSuggestionType.value = res[0].rating;
}

function handleChangeReport(value: Type) {
  showSuggestionType.value = value;
}

function getTypeString(rating: Type) {
  let type = 'Excellent';
  switch (rating) {
    case Type.Strengths:
      type = 'Excellent';
      break;
    case Type.Opportunities:
      type = 'Average';
      break;
    case Type.Improvement:
      type = 'Needs Improvement';
      break;
    default:
  }
  return type;
}

const { pause } = useIntervalFn(
  async () => {
    if (!props.taskId) return;

    const { data } = await fetchGetShopAuditScoreDataByTaskId(props.taskId);
    if (data) {
      // if (data.grabTaskStatus >= 20) {
      shopAuditScore.value = data;
      getSuggestion();
      pause();
      toggleLoading(false);
      // }
    }
  },
  1000 * 10,
  {
    immediate: true,
    immediateCallback: true
  }
);

onUnmounted(() => {
  pause();
});
</script>

<template>
  <NFlex vertical :size="16">
    <NGrid x-gap="16px" y-gap="16px" responsive="screen" item-responsive>
      <NGi span="12">
        <NCard
          class="h-full card-wrapper"
          content-class="min-h-300px flex-center"
          :bordered="false"
          title="Overall Shop Score"
        >
          <Loading v-if="loading" />
          <GaugeChart v-else :score="shopAuditScore?.totalScore || 0" />
        </NCard>
      </NGi>
      <NGi span="12">
        <NCard
          class="h-full card-wrapper"
          :segmented="!loading"
          content-class="flex-col gap-16px"
          :bordered="false"
          title="Audit Report"
        >
          <Loading v-if="loading" class="self-center" />
          <template v-else-if="!loading && !isDownload">
            <NScrollbar style="height: 210px" content-class="flex-col gap-16px">
              <template v-if="showReports">
                <div v-for="content in showReports" :key="content.title" class="chat-message flex gap-8px">
                  <NAvatar color="#f2f2f2" round>
                    <icon-fluent-emoji:robot class="text-xl text-primary" />
                  </NAvatar>
                  <div
                    class="max-w-350px rounded-lg px-3 py-1.5 text-sm"
                    :class="{
                      'bg-success-50 text-success-500': showSuggestionType === Type.Strengths,
                      'bg-primary-50 text-primary-500': showSuggestionType === Type.Opportunities,
                      'bg-warning-50 text-warning-500': showSuggestionType === Type.Improvement
                    }"
                  >
                    {{ content.report }}
                  </div>
                </div>
              </template>
            </NScrollbar>
          </template>
          <!-- for download -->
          <template v-else>
            <div class="h-210px flex-col gap-16px overflow-hidden">
              <template v-if="showReports">
                <div v-for="content in showReports" :key="content.title" class="flex gap-8px">
                  <div class="h-34px w-34px flex-center rounded-full bg-#f2f2f2">
                    <icon-fluent-emoji:robot class="text-xl text-primary" />
                  </div>
                  <div
                    class="max-w-350px rounded-lg px-3 py-1.5 text-sm"
                    :class="{
                      'bg-success-50 text-success-500': showSuggestionType === Type.Strengths,
                      'bg-primary-50 text-primary-500': showSuggestionType === Type.Opportunities,
                      'bg-warning-50 text-warning-500': showSuggestionType === Type.Improvement
                    }"
                  >
                    {{ content.report }}
                  </div>
                </div>
              </template>
            </div>
          </template>
          <template v-if="!loading" #footer>
            <NFlex justify="flex-end" :wrap="false" :size="16">
              <NTag
                v-for="item in suggestion"
                :key="item.rating"
                class="hover:cursor-pointer"
                :bordered="false"
                :type="item.color"
                @click="handleChangeReport(item.rating)"
              >
                {{ item.title }}
                ({{ item.report.length }})
              </NTag>
            </NFlex>
          </template>
        </NCard>
      </NGi>
    </NGrid>
    <NCard
      class="h-full card-wrapper"
      content-class="flex justify-between gap-16px"
      :bordered="false"
      :wrap="false"
      title="Performance Breakdown by Key Metrics"
    >
      <Loading v-if="loading" class="m-auto" />

      <template v-else>
        <div class="flex-basis-50%">
          <RadarChart :data="shopAuditScore.scoreList" />
        </div>
        <div class="flex-basis-45%">
          <div v-for="score in shopAuditScore.scoreList" :key="score.title" class="py-8px">
            <div class="flex justify-between font-bold">
              <span>{{ score.title }}</span>
              <span
                :class="{
                  'text-success': score.rating === Type.Strengths,
                  'text-primary': score.rating === Type.Opportunities,
                  'text-warning': score.rating === Type.Improvement
                }"
              >
                {{ getTypeString(score.rating) }}
              </span>
            </div>
            <NFlex align="center" :wrap="false">
              <NProgress :percentage="score.score" color="#C680FF" :height="12" unit=""><span></span></NProgress>
              <div class="w-45px text-center text-xl font-bold">{{ score.score }}</div>
            </NFlex>
          </div>
        </div>
      </template>
    </NCard>
  </NFlex>
</template>

<style scoped lang="scss">
@keyframes chatAnimation {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-message {
  animation: chatAnimation 0.3s ease-in-out;
  animation-fill-mode: both;
  animation-delay: 0.1s;
  &:nth-child(even) {
    animation-delay: 0.3s;
  }
  &:nth-child(odd) {
    animation-delay: 0.2s;
  }
}
</style>
