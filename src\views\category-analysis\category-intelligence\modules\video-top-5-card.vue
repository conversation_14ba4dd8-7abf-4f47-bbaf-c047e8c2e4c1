<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-16 17:12:01
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-10-08 10:21:08
 * @FilePath: \tiksage-frontend\src\views\category-intelligence\modules\sku-top10-card.vue
 * @Description: sku-top10-card
-->
<script setup lang="tsx">
import { ref, watch } from 'vue';
import { NAvatar, NEllipsis, NFlex, NImage, NText } from 'naive-ui';
import { divide } from 'lodash-es';
import { fetchGetVideoLiveListByPage } from '@/service/api';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { useTable } from '@/hooks/common/table';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { dateFormat } from '@/utils/date';
import { getFallbackImage } from '@/utils/fake-image';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, LinkToVideo } from '@/utils/tiktok-link';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { NumeralFormat } from '@/enum';
import ButtonCopy from '@/components/custom/button-copy.vue';
import CategoryShopSelect from './category-shop-select.vue';

interface Props {
  shopType: number;
  month: string;
}

const props = defineProps<Props>();

const URL_PREFIX = `/images/el/avatar/`;
const { getDictionaryByCodeType } = useDictionaryStore();
const { numberFormat } = useNumberFormat();

const typeValue = ref<number>();

const typeOptions = ref<CommonType.Option<number>[]>([]);

const { data, columns, getData, mobilePagination, loading, searchParams, updateSearchParams, columnChecks } = useTable({
  apiFn: fetchGetVideoLiveListByPage,
  immediate: false,
  apiParams: {
    current: 1,
    size: 10,
    shopType: props.shopType,
    month: props.month
  },
  columns() {
    return [
      {
        key: 'index',
        align: 'center',
        fixed: 'left',
        width: 50,
        render(rowData) {
          if (rowData.index === 1) {
            return (
              <NFlex class="w-100%" justify="center">
                <SvgIcon style="width:25px;height:25px" icon="twemoji:1st-place-medal" />
              </NFlex>
            );
          } else if (rowData.index === 2) {
            return (
              <NFlex class="w-100%" justify="center">
                <SvgIcon style="width:25px;height:25px" icon="twemoji:2nd-place-medal" />
              </NFlex>
            );
          } else if (rowData.index === 3) {
            return (
              <NFlex class="w-100%" justify="center">
                <SvgIcon style="width:25px;height:25px" icon="twemoji:3rd-place-medal" />
              </NFlex>
            );
          }
          return <span>{rowData.index}</span>;
        }
      },
      {
        key: 'title',
        title: 'Content',
        fixed: 'left',
        width: 280,
        render: rowData => (
          <div
            class="flex flex-nowrap items-center gap-2 hover:(cursor-pointer text-primary)"
            onClick={() => handleLink('default', rowData.url)}
          >
            <div class="relative h-70px w-40px flex-shrink-0">
              <NImage
                class="h-full w-full rounded"
                objectFit="cover"
                src={`${URL_PREFIX}${rowData.avatarLocal}`}
                fallbackSrc={getFallbackImage(40, 71)}
              />
              <div class="absolute inset-0 flex-center rounded bg-black/20">
                <SvgIcon icon="solar:play-circle-bold" class="text-lg text-white" />
              </div>
              {rowData.adFlag ? (
                <div class="absolute right-[-6px] top-[-6px] rounded bg-#D31C67 p-0.5 text-xs text-white">Ad</div>
              ) : (
                ''
              )}
            </div>
            <div class="flex-col">
              <NEllipsis line-clamp={2} tooltip={{ contentClass: 'max-w-400px' }}>
                {rowData.title}
              </NEllipsis>

              <NText class="text-xs text-gray">{dateFormat(rowData.publishDate)}</NText>
            </div>
          </div>
        )
      },
      {
        key: 'creatorId',
        title: 'Creator',
        width: 250,
        render(rowData) {
          return (
            <div
              class="flex flex-nowrap items-center gap-2 hover:(cursor-pointer text-primary)"
              onClick={() => handleLink('creator', rowData.creatorId)}
            >
              <NAvatar
                class="flex-shrink-0"
                src={`${URL_PREFIX}${rowData.creatorAvatarLocal}`}
                fallbackSrc={getFallbackImage(50, 50)}
              ></NAvatar>
              <div class="flex-col">
                <NEllipsis line-clamp={1} tooltip={{ contentClass: 'max-w-400px' }}>
                  {rowData.creatorNickname}
                </NEllipsis>

                <div class="flex-y-center gap-2">
                  <span class="text-coolgray">ID:{rowData.creatorId}</span>
                  <ButtonCopy copy={rowData.creatorId} />
                </div>
                <span class="text-coolgray">Followers: {rowData.followers}</span>
              </div>
            </div>
          );
        }
      },
      {
        key: 'gmv',
        title: 'GMV',
        align: 'center',
        sorter: true,
        sortOrder: 'descend',
        width: 140
      },
      {
        key: 'views',
        title: 'Views',
        align: 'center'
      },
      {
        key: 'likes',
        title: 'Likes',
        align: 'center'
      },
      {
        key: 'sold',
        title: 'Units Sold',
        align: 'center'
      }
    ];
  }
});

const dollarKeys = ['commissionSpending'];
const percentKeys = ['directDiscountDepth'];

const renderCell = (value: any, _rowData: any, column: any) => {
  if (dollarKeys.includes(column.key)) {
    return numberFormat(value, NumeralFormat.Dollar);
  }
  if (percentKeys.includes(column.key)) {
    return numberFormat(divide(Number(value), 100), NumeralFormat.Percent);
  } else if (typeof value === 'number') {
    return numberFormat(value, NumeralFormat.Number);
  }
  return value;
};

function handleLink(type: 'creator' | 'video' | 'default', creatorId?: string, productId?: string) {
  switch (type) {
    case 'creator':
      LinkToCreator(creatorId);
      break;
    case 'video':
      LinkToVideo(creatorId, productId);
      break;
    default:
      window.open(creatorId, '_blank');
  }
}

const handleGetData = (model: { category: string; shopIds: number[] }) => {
  updateSearchParams({
    ...searchParams,
    current: 1,
    category: model.category,
    shopIds: model.shopIds[0] === -1 ? undefined : model.shopIds
  });
};

async function initTypeOptions() {
  const opt = await getDictionaryByCodeType<number>('metrics_video_live_type');

  if (!opt) return;
  typeOptions.value = opt.map(i => ({
    label: i.name,
    value: i.code
  }));
  typeValue.value = opt[0].code;
}

function init() {
  initTypeOptions();
}

init();

watch(
  () => typeValue.value,
  newVal => {
    updateSearchParams({
      ...searchParams,
      contentType: newVal
    });

    if (typeOptions.value.find(v => v.value === newVal)?.label === 'Live') {
      columnChecks.value.find(v => v.key === 'likes')!.checked = false;
    } else {
      columnChecks.value.find(v => v.key === 'likes')!.checked = true;
    }
  }
);

watch(
  () => searchParams,
  newVal => {
    if (newVal.contentType) {
      getData();
    }
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <NCard :bordered="false" class="card-wrapper" title="Top 50 Contents by Sales">
    <template #header-extra>
      <div class="flex-y-center gap-2">
        <NSelect v-model:value="typeValue" class="min-w-80px" :options="typeOptions"></NSelect>
        <CategoryShopSelect :month="month" :shop-type="shopType" show-shop @update:value="handleGetData" />
      </div>
    </template>
    <NDataTable
      :bordered="false"
      size="small"
      :loading="loading"
      :data="data"
      :columns="columns"
      :pagination="mobilePagination"
      :render-cell="renderCell"
      remote
      scroll-x="1140"
    ></NDataTable>
  </NCard>
</template>

<style scoped></style>
