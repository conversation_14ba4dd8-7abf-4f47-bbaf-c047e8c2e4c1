<script setup lang="tsx">
import { nextTick, ref, watch } from 'vue';
import { NFlex, NScrollbar, type UploadFileInfo } from 'naive-ui';
import { isNil } from 'lodash-es';
import { fetchUploadCreatorExcel } from '@/service/api';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';

interface Emits {
  (e: 'submit', clientId?: number | null): void;
}

const emit = defineEmits<Emits>();

interface Props {
  clientOptions: Api.VideoManage.ApprovalUserOption[];
}

const props = defineProps<Props>();

const show = defineModel('show', {
  required: true,
  default: false
});

type Model = Api.CreatorManage.UploadCreatorExcelSearchParams & {
  files: UploadFileInfo[];
};

const model = ref<Model>(createDefaultModel());

function createDefaultModel(): Model {
  return {
    files: [],
    file: null,
    client: null,
    tags: null,
    shopId: null
  };
}

const { formRef, validate } = useNaiveForm();

const { defaultRequiredRule } = useFormRules();

type RuleKey = Extract<keyof Model, 'files' | 'client'>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  files: defaultRequiredRule,
  client: defaultRequiredRule
};

function handleVideoLimit(data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) {
  if (!data.file.type?.includes('sheet')) {
    window.$message?.warning('Upload a Excel in xlsx format.');
    return false;
  }

  const fileSize = data.file.file?.size;
  if (fileSize && fileSize > 1024 * 1024 * 100) {
    window.$message?.warning("The file you're trying to upload is too large. Maximum allowed size is 100MB.");
    return false;
  }

  return true;
}

function handleDownloadTemplate() {
  window.location.href = 'https://product.tiksage.com/images/template/creator_management_template.xlsx';
}

// const duplicateCreators = ref<Api.CreatorManage.DuplicateCreatorList>([]);

async function handleSubmit() {
  validate();

  const { files, ...params } = model.value;
  const { data, error } = await fetchUploadCreatorExcel({ ...params, file: files[0].file });
  if (!error) {
    if (data.length) {
      nextTick(() => {
        window.$dialog?.create({
          title: 'Duplicate creator',
          content() {
            const creatorNames = data.map(v => v.creatorName);
            return (
              <NFlex vertical>
                <span>The following creators already exist in the system:</span>
                <NScrollbar style="max-height:300px">
                  {creatorNames.map(v => (
                    <div>{v}</div>
                  ))}
                </NScrollbar>
              </NFlex>
            );
          }
        });
      });
    }
    emit('submit', params.client);
  }
  show.value = false;
}

function handleCancel() {
  show.value = false;
}

watch(
  () => show.value,
  newVal => {
    if (!newVal) {
      // close the drawer
      // clear model
      model.value = createDefaultModel();
    }
  }
);

// get model.shopId
watch(
  () => model.value.client,
  newVal => {
    if (isNil(newVal)) return;

    model.value.shopId = null;
    const params = props.clientOptions?.find(v => v.userId === newVal);
    if (params) {
      model.value.shopId = params.shopId;
    }
  }
);
</script>

<template>
  <NDrawer v-model:show="show" width="400px">
    <NDrawerContent title="Add New Creators">
      <NFlex vertical>
        <NForm ref="formRef" :model="model" :rules="rules">
          <NFormItem label="Upload File" path="files">
            <NUpload
              ref="uploadRef"
              v-model:file-list="model.files"
              directory-dnd
              :max="1"
              @before-upload="handleVideoLimit"
            >
              <NUploadDragger>
                <NFlex class="h-200px" vertical justify="center" align="center" :size="16">
                  <SvgIcon class="text-4xl text-primary" icon="solar:cloud-upload-bold-duotone" />
                  <NText>Drag & drop your files here or choose files.</NText>
                  <NText>
                    *100 MB max file size. (
                    <NButton text type="primary" @click.stop="handleDownloadTemplate">Template</NButton>
                    )*
                  </NText>
                </NFlex>
              </NUploadDragger>
            </NUpload>
          </NFormItem>
          <NFormItem label="Client" path="client">
            <NSelect
              v-model:value="model.client"
              :options="clientOptions"
              value-field="userId"
              label-field="shopName"
              placeholder="Put in the client account name"
            />
          </NFormItem>
          <NFormItem label="Tags" path="tags">
            <NInput v-model:value="model.tags" placeholder="Put in the creator tags"></NInput>
          </NFormItem>
        </NForm>
      </NFlex>
      <template #footer>
        <NFlex :wrap="false" :size="16">
          <NButton @click="handleCancel">Cancel</NButton>
          <NButton type="primary" @click="handleSubmit">Submit</NButton>
        </NFlex>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
