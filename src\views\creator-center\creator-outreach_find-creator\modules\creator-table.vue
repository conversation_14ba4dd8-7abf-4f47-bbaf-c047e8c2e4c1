<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { computedAsync } from '@vueuse/core';
import { NAvatar, NEllipsis, NTag } from 'naive-ui';
import {
  fetchBatchSetTagByCreator,
  fetchDownloadCreatorDataByFilter,
  fetchGetCategoryTree,
  fetchGetFindCreatorIdList,
  fetchGetFindCreatorList,
  fetchSetTagByCreator
} from '@/service/api';
import { useTagStore } from '@/store/modules/tag';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { useAuth } from '@/hooks/business/auth';
import { localStg } from '@/utils/storage';
import { downloadFile } from '@/utils/download';
import { getFallbackImage } from '@/utils/fake-image';
import { LinkToCreator } from '@/utils/tiktok-link';
import type { FilterGroup } from '@/components/filter/creator-filter.vue';
import { FilterType, NumeralFormat } from '@/enum';
import SvgIcon from '@/components/custom/svg-icon.vue';
import TagPopover from '@/components/custom/tag-popover.vue';
import TagSelect from './tag-select.vue';
import CreatorSelect from './creator-select.vue';

const { VITE_CREATOR_AVATAR_URL } = import.meta.env;

const filterParams = ref({});

const checkedRowKeys = defineModel<number[]>('checkedRowKeys', {
  default: () => []
});
const { routerPushByKey } = useRouterPush();
const { getDictionaryByCodeType } = useDictionaryStore();
const categoryTreeOptions = ref<Api.CreatorResources.CategoryTree[]>([]);
const { numberFormat } = useNumberFormat();
const { hasAuth } = useAuth();

const canReBackTask = computed<boolean>(() => {
  const json = localStg.get('cachedTiktokMessageData');
  return Boolean(json);
});

const levelOpts = computedAsync(() => {
  const opts = getDictionaryByCodeType<number>('creator_gmv_level', false, true);
  return opts;
});

const filterArr = computedAsync<FilterGroup[]>(async () => {
  const { tagList } = useTagStore();
  const categoryOpts = categoryTreeOptions.value.map(item => ({
    label: item.localName,
    value: item.localName
  }));

  return [
    {
      category: 'Creators',
      children: [
        {
          key: 'creatorCategory',
          title: 'Creator category',
          type: FilterType.MultipleSelect,
          options: categoryOpts
        },
        {
          key: 'avgCommissionRateMax',
          title: 'Avg. commission rate',
          type: FilterType.Select,
          description: '',
          options: [
            {
              label: 'Less than 20%',
              value: '0.2'
            },
            {
              label: 'Less than 15%',
              value: '0.15'
            },
            {
              label: 'Less than 10%',
              value: '0.1'
            },
            {
              label: 'Less than 5%',
              value: '0.05'
            }
          ]
        },
        {
          key: 'contentType',
          title: 'Content type',
          type: FilterType.Select,
          description: '',
          options: [
            {
              label: 'Video',
              value: 'video'
            },
            {
              label: 'LIVE',
              value: 'live'
            },
            {
              label: 'Showcase',
              value: 'showcase'
            }
          ]
        },
        {
          key: 'fastGrowing',
          title: 'Fast growing',
          type: FilterType.Checkbox,
          description: ''
        }
      ]
    },
    {
      category: 'Followers',
      children: [
        {
          key: 'followers',
          title: 'Followers count',
          type: FilterType.RangeSelect,
          description: '',
          options: [
            {
              label: '0-10K',
              value: '0,10000'
            },
            {
              label: '10K-100K',
              value: '10000,100000'
            },
            {
              label: '100K-1M',
              value: '100000,1000000'
            },
            {
              label: '1M-10M',
              value: '1000000,10000000'
            },
            {
              label: '10M+',
              value: '10000000,'
            }
          ]
        },
        {
          key: 'followerAge',
          title: 'Follower age',
          type: FilterType.MultipleSelect,
          description: '',
          options: [
            {
              label: '18-24',
              value: '18-24'
            },
            {
              label: '25-34',
              value: '25-34'
            },
            {
              label: '35-44',
              value: '35-44'
            },
            {
              label: '45-54',
              value: '45-54'
            },
            {
              label: '55+',
              value: '55+'
            }
          ]
        },
        {
          key: 'followerGender',
          title: 'Follower gender',
          type: FilterType.Select,
          description: '',
          options: [
            {
              label: 'Majority Female',
              value: 'majorityFemale'
            },
            {
              label: 'Majority Male',
              value: 'majorityMale'
            }
          ]
        }
      ]
    },
    {
      category: 'Performance',
      children: [
        {
          key: 'gmv',
          title: 'GMV',
          type: FilterType.RangeSelect,
          description: '',
          options: [
            {
              label: '$0-$100',
              value: '0,100'
            },
            {
              label: '$100-$1K',
              value: '100,1000'
            },
            {
              label: '$1K-$10K',
              value: '1000,10000'
            },
            {
              label: '$10K+',
              value: '10000,'
            }
          ]
        },
        {
          key: 'unitsSold',
          title: 'Units sold',
          type: FilterType.RangeSelect,
          description: '',
          options: [
            {
              label: '0-10',
              value: '0,10'
            },
            {
              label: '10-100',
              value: '10,100'
            },
            {
              label: '100-1K',
              value: '100,1000'
            },
            {
              label: '1K+',
              value: '1000,'
            }
          ]
        },
        {
          key: 'averageViewsPerVideoMin',
          title: 'Avg. views per video',
          type: FilterType.MoreThanSelect,
          description: '',
          options: [
            {
              label: 'More than 100',
              value: '100'
            },
            {
              label: 'More than 1K',
              value: '1000'
            },
            {
              label: 'More than 10K',
              value: '10000'
            },
            {
              label: 'More than 100K',
              value: '100000'
            }
          ]
        },
        {
          key: 'averageViewsPerLiveMin',
          title: 'Avg. views per Live',
          type: FilterType.MoreThanSelect,
          description: '',
          options: [
            {
              label: 'More than 100',
              value: '100'
            },
            {
              label: 'More than 1K',
              value: '1000'
            },
            {
              label: 'More than 10K',
              value: '10000'
            },
            {
              label: 'More than 100K',
              value: '100000'
            }
          ]
        },
        {
          key: 'engagementRateMin',
          title: 'Engagement rate',
          type: FilterType.MoreThanSelect,
          description: '',
          unit: '%',
          options: [
            {
              label: 'More than 1%',
              value: '0.01'
            },
            {
              label: 'More than 5%',
              value: '0.05'
            },
            {
              label: 'More than 10%',
              value: '0.1'
            },
            {
              label: 'More than 15%',
              value: '0.15'
            },
            {
              label: 'More than 20%',
              value: '0.2'
            }
          ]
        },
        {
          key: 'estPostRate',
          title: 'Estimated post rate',
          type: FilterType.Select,
          description: '',
          options: [
            {
              label: 'OK',
              value: 'ok'
            },
            {
              label: 'Better',
              value: 'better'
            },
            {
              label: 'Good',
              value: 'good'
            }
          ]
        }
      ]
    },
    {
      category: 'Tags',
      children: [
        {
          key: 'selectedTag',
          title: 'Selected tags',
          type: FilterType.MultipleSelect,
          description: '',
          options: tagList.map(tag => ({
            label: tag.name,
            value: String(tag.id)
          }))
        }
      ]
    },
    {
      category: 'EC levels',
      children: [
        {
          key: 'gmvLevel',
          title: 'TikTok EC levels',
          type: FilterType.MultipleSelect,
          description: '',
          options: levelOpts.value?.map(l => ({
            label: `${l.name} : ${getRangeDisplay(l.description.range)}`,
            value: l.description.range
          }))
        }
      ]
    }
  ];
});

const { data, columns, getData, updateSearchParams, loading, pagination } = useTable({
  apiFn: fetchGetFindCreatorList,
  apiParams: {
    current: 1,
    size: 10,
    averageViewsPerLiveMin: null,
    averageViewsPerVideoMin: null,
    avgCommissionRateMax: null,
    contentType: null,
    creatorCategory: null,
    engagementRateMin: null,
    fastGrowing: null,
    followerAge: null,
    followerGender: null,
    followers: null,
    gmv: null,
    gmvLevel: null,
    selectedTag: null,
    unitsSold: null,
    idName: null
  },
  columns() {
    return [
      {
        type: 'selection',
        width: 50,
        fixed: 'left',
        align: 'center'
      },
      {
        key: 'id',
        title: 'Creator',
        fixed: 'left',
        width: 300,
        render(rowData) {
          const avatarUrl = `${VITE_CREATOR_AVATAR_URL}${rowData.avatarLocal}`;
          const categoryList = JSON.parse(rowData.categoryJson);

          return (
            <div
              class="flex-y-center gap-2 hover:(cursor-pointer text-primary)"
              onClick={() => LinkToCreator(rowData.id)}
            >
              <div class="flex-center flex-shrink-0">
                <NAvatar size="large" round src={avatarUrl} fallbackSrc={getFallbackImage(50, 50)} />
              </div>
              <div class="flex-col">
                <div class="flex flex-nowrap justify-between gap-2">
                  <NEllipsis class="font-bold" lineClamp={1}>
                    {rowData.nickname || '-'}
                  </NEllipsis>
                </div>
                <span class="text-coolgray">@{rowData.id || '-'}</span>
                <div>
                  {categoryList.length ? (
                    <TagPopover tags={categoryList} showFirst />
                  ) : (
                    <NTag size="small" bordered={false}>
                      Other
                    </NTag>
                  )}
                </div>
              </div>
            </div>
          );
        }
      },
      {
        key: 'tagArr',
        title: 'Tags',
        width: 160,
        align: 'center',
        render(rowData) {
          const tagList = rowData.tagArr || [];
          return (
            <TagSelect defaultTags={tagList} onSave={async tags => await handleSaveTags(rowData.creatorId, tags)} />
          );
        }
      },
      {
        key: 'follower',
        title: 'Followers',
        width: 150,
        align: 'center',
        render(rowData) {
          const ages = JSON.parse(rowData.followerAgeJson);
          return (
            <div class="flex-col gap-2">
              <div class="flex-y-center gap-2">
                <SvgIcon icon="solar:users-group-rounded-outline" />
                <span>{rowData.follower}</span>
                {<SvgIcon icon={rowData.followerGender === 'Male' ? 'twemoji:male-sign' : 'twemoji:female-sign'} />}
              </div>
              <div class="flex-y-center gap-2">{ages?.length ? <TagPopover tags={ages} showFirst /> : '-'}</div>
            </div>
          );
        }
      },
      {
        key: 'gmv',
        title: 'GMV',
        align: 'center',
        sorter: 'default',
        sortOrder: 'descend',
        width: 150,
        render(rowData) {
          const { name, textColor, color } = getGMVLevel(rowData.gmvNum || 0);
          return (
            <div class="flex gap-2">
              <NTag color={{ textColor, color }} size="small" bordered={false}>
                {name}
              </NTag>
              <span>{rowData.gmv}</span>
            </div>
          );
        }
      },
      {
        key: 'unitsSold',
        title: 'Units Sold',
        align: 'center',
        width: 120
      },
      {
        key: 'avgVideoViews',
        title: 'Avg. video views',
        align: 'center',
        width: 150
      },
      {
        key: 'engagementRate',
        title: 'Engagement rate',
        align: 'center',
        width: 150
      },
      {
        key: 'avgLiveViews',
        title: 'Avg. LIVE views',
        align: 'center',
        width: 150
      },
      {
        key: 'contentType',
        title: 'Content type',
        align: 'center',
        width: 150
      }
    ];
  }
});

const scrollx = computed(() => {
  return columns.value.reduce((p, c) => p + (Number(c.width) || 0), 0);
});

function getRangeDisplay(range: string): string {
  const [min, max] = range.split(',');
  const minStr = numberFormat(Number(min), NumeralFormat.Dollar);
  const maxStr = numberFormat(Number(max), NumeralFormat.Dollar);

  if (!min && max) {
    return `less than ${maxStr}`;
  } else if (min && !max) {
    return `${minStr}+`;
  } else if (min && max) {
    return `${minStr}-${maxStr}`;
  }

  // 添加默认返回值
  return '-';
}

function getGMVLevel(value: number): { name: string; textColor: string; color: string } {
  if (!levelOpts.value || !value) {
    // 默认返回L0的信息
    const defaultLevel = levelOpts.value?.find(level => level.name === 'L0');
    return {
      name: 'L0',
      textColor: defaultLevel?.description.textColor || '#1A4A57',
      color: defaultLevel?.description.color || '#B1EAF5'
    };
  }

  for (const level of levelOpts.value) {
    const range = level.description.range.split(',');
    const min = range[0] ? Number(range[0]) : 0;
    const max = range[1] ? Number(range[1]) : Infinity;

    if (value >= min && (max === Infinity || value < max)) {
      return {
        name: level.name,
        textColor: level.description.textColor,
        color: level.description.color
      };
    }
  }

  // 如果没有匹配的level，返回L0
  const defaultLevel = levelOpts.value.find(level => level.name === 'L0');
  return {
    name: 'L0',
    textColor: defaultLevel?.description.textColor || '#1A4A57',
    color: defaultLevel?.description.color || '#B1EAF5'
  };
}

function handleRenderCell(value: any) {
  if (value === null || value === undefined) {
    return '-';
  }
  return value;
}

async function handleSaveTags(creatorId: number, tags: Api.Tag.Tag[]) {
  const tagIds = tags.map(tag => tag.id);
  const { error: setTagErr } = await fetchSetTagByCreator({ creatorId, tagIds });
  if (!setTagErr) {
    window.$message?.success('Tags saved successfully');
    getData();
    return true;
  }
  return false;
}

async function handleBatchAddTags(tags: Api.Tag.Tag[]) {
  const tagIds = tags.map(tag => tag.id);
  const { error: setTagErr } = await fetchBatchSetTagByCreator({ creatorIds: checkedRowKeys.value, tagIds });
  if (!setTagErr) {
    window.$message?.success('Batch tags added successfully');
    getData();
    return true;
  }
  return false;
}

function handleSearchById(str: string) {
  updateSearchParams({
    idName: str
  });
  getData();
}

function createDefaultFilterParams() {
  return {
    averageViewsPerLiveMin: null,
    averageViewsPerVideoMin: null,
    avgCommissionRateMax: null,
    contentType: null,
    creatorCategory: null,
    engagementRateMin: null,
    fastGrowing: null,
    followerAge: null,
    followerGender: null,
    followers: null,
    gmv: null,
    gmvLevel: null,
    selectedTag: null,
    unitsSold: null
  };
}

async function handleSelectCreators(value: string[]) {
  // 检查value是否为数组且包含两个元素
  if (!Array.isArray(value) || value.length !== 2) {
    return;
  }

  // 检查两个值是否都是有效的数字
  if (!value[0] || !value[1] || !/^\d+$/.test(value[0]) || !/^\d+$/.test(value[1])) {
    return;
  }

  const params: any = {
    ...filterParams.value,
    limitStart: Number(value[0]) - 1,
    limitEnd: Number(value[1]) - 1
  };
  const { data: selectIds, error: selectIdsErr } = await fetchGetFindCreatorIdList(params);
  if (!selectIdsErr) {
    checkedRowKeys.value = selectIds;
  }
}

function handleReBackTask() {
  routerPushByKey('creator-center_creator-outreach_create_tiktok');
}

async function handleExport(params: any) {
  const { data: exportData, error: exportErr } = await fetchDownloadCreatorDataByFilter(params);
  if (!exportErr) {
    window.$message?.success('Export successfully');
    downloadFile(exportData, 'xlsx', 'Creator Data');
  }
}

async function initCategoryOptions() {
  const { data: ctgData, error: ctgErr } = await fetchGetCategoryTree();
  if (ctgErr) return;
  categoryTreeOptions.value = ctgData;
}

function init() {
  initCategoryOptions();
}

init();

watch(
  () => filterParams,
  newVal => {
    updateSearchParams({
      ...createDefaultFilterParams(),
      ...newVal.value,
      current: 1,
      size: 10
    });
    getData();
  },
  {
    deep: true
  }
);
</script>

<template>
  <div class="flex-col gap-4">
    <NCard class="card-wrapper" :bordered="false">
      <div class="flex justify-between gap-4">
        <NInput class="max-w-1/3" placeholder="Search by creator id" clearable @change="handleSearchById">
          <template #suffix>
            <icon-solar:magnifer-linear />
          </template>
        </NInput>
        <slot name="header-extra"></slot>
      </div>
    </NCard>
    <CreatorFilter
      v-model:value="filterParams"
      :filter-arr="filterArr || []"
      :show-export-button="hasAuth('creator-outreach:export-creator')"
      :export="handleExport"
    />
    <NCard class="h-full flex-1 card-wrapper" :bordered="false">
      <template #header>
        <div class="flex-y-center gap-4">
          <span>Creator List</span>
          <CreatorSelect @update:value="handleSelectCreators" @reset="checkedRowKeys = []" />
        </div>
      </template>
      <template #header-extra>
        <div class="flex justify-end gap-4">
          <NButton v-if="canReBackTask" type="default" @click="handleReBackTask">
            <template #icon>
              <icon-solar:restart-linear />
            </template>
            Resume Draft Task
          </NButton>
          <TagSelect :default-tags="[]" @save="handleBatchAddTags">
            <template #trigger>
              <NButton>
                Batch add tags
                <template #icon>
                  <icon-solar:bookmark-linear />
                </template>
              </NButton>
            </template>
          </TagSelect>
        </div>
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        class="h-full"
        :scroll-x="scrollx"
        remote
        :row-key="row => row.creatorId"
        :loading="loading"
        :pagination="pagination"
        :bordered="false"
        :columns="columns"
        :data="data"
        :render-cell="handleRenderCell"
      ></NDataTable>
    </NCard>
  </div>
</template>

<style scoped></style>
