import { request } from '../request';

export function fetchCreateTag(data: Api.Tag.CreateTagParams) {
  return request<Api.Tag.Tag>({
    url: '/creatorTag/create',
    method: 'post',
    data
  });
}

export function fetchGetTagList() {
  return request<Api.Tag.Tag[]>({
    url: '/creatorTag/list',
    method: 'get'
  });
}

export function fetchUpdateTag(data: Api.Tag.Tag) {
  return request<Api.Tag.Tag>({
    url: '/creatorTag/update',
    method: 'put',
    data
  });
}

export function fetchDeleteTag(id: number) {
  return request({
    url: '/creatorTag/delete',
    method: 'delete',
    params: {
      id
    }
  });
}

// Will overwrite the original tag of the creator
export function fetchSetTagByCreator(data: Api.Tag.SetTagByCreatorParams) {
  return request({
    url: '/creatorTag/setTag',
    method: 'put',
    data
  });
}

// Only add new tags to creators
export function fetchBatchSetTagByCreator(data: Api.Tag.BatchSetTagByCreatorParams) {
  return request({
    url: '/creatorTag/setTagBatch',
    method: 'put',
    data
  });
}
