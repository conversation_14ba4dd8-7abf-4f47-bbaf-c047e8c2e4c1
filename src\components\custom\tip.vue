<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-09-18 14:55:35
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-08 15:15:56
 * @FilePath: \tiksage-frontend\src\components\custom\tip.vue
 * @Description: tip
-->
<script setup lang="ts">
interface Props {
  description?: string;
  contentClass?: string;
  iconClass?: string;
}
const props = defineProps<Props>();
</script>

<template>
  <NTooltip style="max-width: 400px; overflow-wrap: break-word" trigger="hover" :content-class="contentClass">
    <template #trigger>
      <NButton text color="#999">
        <template #icon>
          <SvgIcon icon="solar:question-circle-linear" :class="iconClass" />
        </template>
      </NButton>
    </template>
    <span v-if="description" class="whitespace-pre-wrap">
      {{ props.description }}
    </span>
    <slot v-else></slot>
  </NTooltip>
</template>

<style scoped></style>
