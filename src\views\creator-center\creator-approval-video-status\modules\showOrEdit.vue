<script setup lang="ts">
import { nextTick, ref, watch } from 'vue';

interface Emits {
  (e: 'update', value: string): void;
}

const emit = defineEmits<Emits>();

interface Props {
  value: any;
}

const props = defineProps<Props>();

const isEdit = ref<boolean>(false);
const inputRef = ref<HTMLInputElement | null>(null);
const inputValue = ref(props.value);

function handleClick() {
  isEdit.value = true;
  nextTick(() => {
    inputRef.value?.focus();
  });
}

function validValue() {
  if (inputValue.value?.trim() !== props.value?.trim()) {
    emit('update', inputValue.value);
  }

  isEdit.value = false;
}

const isEnterPressed = ref<boolean>(false);
function handleBlur() {
  if (!isEnterPressed.value) {
    validValue();
  }

  isEnterPressed.value = false;
}
function handleEnter() {
  validValue();

  isEnterPressed.value = true;
}

watch(
  () => isEdit.value,
  newVal => {
    if (newVal) inputValue.value = props.value;
  }
);
</script>

<template>
  <div @click="handleClick">
    <div v-if="!isEdit">
      <ButtonIcon v-if="!props.value" text :quaternary="false" icon="solar:pen-2-linear" />
      <NEllipsis v-else :line-clamp="2">{{ props.value }}</NEllipsis>
    </div>
    <NInput
      v-else
      ref="inputRef"
      v-model:value="inputValue"
      placeholder="Input"
      @keyup.enter="handleEnter"
      @blur="handleBlur"
    ></NInput>
  </div>
</template>

<style scoped></style>
