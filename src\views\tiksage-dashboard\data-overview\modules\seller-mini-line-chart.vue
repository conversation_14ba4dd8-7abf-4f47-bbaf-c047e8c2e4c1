<script setup lang="ts">
import { onMounted, watch } from 'vue';
import dayjs from 'dayjs';
import { useVChart } from '@/hooks/common/vchart';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat, TimeFormat } from '@/enum';
import { transformData } from './chart';

interface Props {
  data: Api.TikSageDashboard.ShopOverviewDailyData[];
  isYear: boolean;
}

const props = defineProps<Props>();
const { numberFormat } = useNumberFormat();

const opts = [{ label: 'GMV', value: 'gmvTotal' }];

const baseSpec: Visactor.VChart.IAreaChartSpec = {
  type: 'area',
  padding: [0, 0, 2, 0],
  background: 'transparent',
  data: [],
  xField: 'x',
  yField: 'y',
  line: {
    style: {
      curveType: 'monotone'
    }
  },
  point: {
    visible: false
  },
  area: {
    style: {
      fill: {
        gradient: 'linear',
        x0: 0.5,
        y0: 0,
        x1: 0.5,
        y1: 1,
        stops: [
          {
            offset: 0,
            opacity: 1
          },
          {
            offset: 1,
            opacity: 0
          }
        ]
      }
    }
  },
  axes: [
    {
      orient: 'left',
      visible: false
    },
    {
      orient: 'bottom',
      visible: false
    }
  ]
};

function formatSpec(data: Api.TikSageDashboard.ShopOverviewDailyData[]) {
  const cusSpec: Visactor.VChart.IAreaChartSpec = {
    type: 'area',
    data: [
      {
        id: '1',
        values: transformData<Api.TikSageDashboard.ShopOverviewDailyData>({
          data,
          selected: ['gmvTotal'],
          options: opts,
          xField: 'belongDate'
        })
      }
    ],
    tooltip: {
      activeType: 'dimension',
      dimension: {
        title: {
          value: v => {
            if (v) {
              if (props.isYear) {
                return dayjs(v?.x).format(TimeFormat.US_DATE_NO_DAY);
              }
              return dayjs(v?.x).format(TimeFormat.US_DATE);
            }
            return v;
          }
        },
        content: {
          key: v => {
            return v ? (v?.type as string) : '';
          },
          value: v => {
            return v ? numberFormat(v[cusSpec.yField as string], NumeralFormat.Dollar) : v;
          }
        }
      }
    },
    xField: 'x',
    yField: 'value'
  };
  return cusSpec;
}

const { domRef, updateSpec } = useVChart(() => baseSpec);

function updateChart() {
  updateSpec(oldOpts => {
    return { ...oldOpts, ...formatSpec(props.data) };
  });
}

watch(
  () => props.data,
  () => {
    if (props.data?.length) {
      updateChart();
    }
  }
);

onMounted(() => {
  setTimeout(() => {
    if (props.data?.length) {
      updateChart();
    }
  });
});
</script>

<template>
  <div class="h-45px w-150px">
    <div v-if="!props.data?.length" class="h-full w-full flex-center">-</div>
    <div v-else ref="domRef" class="h-full w-full"></div>
  </div>
</template>

<style scoped></style>
