<script setup lang="tsx">
import { onMounted, onUnmounted, ref } from 'vue';
import { useIntervalFn, useToggle } from '@vueuse/core';
import { NPopover, NTag } from 'naive-ui';
import { fetchGetTaskHistoryByTikTok, fetchPauseTikTokTask, fetchStartTikTokTask } from '@/service/api';
import { useUserStore } from '@/store/modules/user';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import { dateFormat } from '@/utils/date';
import ButtonConfirm from '@/components/custom/button-confirm.vue';
import SvgIcon from '@/components/custom/svg-icon.vue';
import ButtonIcon from '@/components/custom/button-icon.vue';
import TaskSearch from './modules/task-search.vue';
import TaskDetailDrawer from './modules/task-detail-drawer.vue';

const userstore = useUserStore();

const { getDictionaryByCodeType } = useDictionaryStore();

const statusOptions = ref<Api.Dictionary.DictionaryItem<number>[]>([]);
const taskSenderStatusOptions = ref<Api.Dictionary.DictionaryItem[]>([]);
const messageModuleOptions = ref<Api.Dictionary.DictionaryItem[]>([]);

const { data, columns, pagination, loading, getData, searchParams } = useTable({
  apiFn: fetchGetTaskHistoryByTikTok,
  apiParams: {
    current: 1,
    size: 10
  },
  columns() {
    return [
      {
        key: 'taskName',
        title: 'Task name',
        width: 200,
        ellipsis: {
          tooltip: {
            contentStyle: 'max-width:400px'
          }
        }
      },
      {
        key: 'shopId',
        title: 'Shop',
        align: 'center',
        width: 200,
        render(rowData) {
          const shopName = userstore.userShops.find(v => v.shopId === rowData.shopId)?.shopName;
          if (!shopName) return '-';
          return (
            <NTag size="small" bordered={false} type="default">
              {shopName}
            </NTag>
          );
        }
      },
      {
        key: 'createTime',
        title: 'Created time',
        align: 'center',
        width: 300,
        render(rowData) {
          return dateFormat(rowData.createTime);
        }
      },
      {
        key: 'messageModule',
        title: 'Connection method',
        align: 'center',
        render(rowData) {
          const dicObj = messageModuleOptions.value.find(v => v.code === rowData.messageModule);
          if (!dicObj) return '-';
          return (
            <NTag size="small" bordered={false}>
              {dicObj.name}
            </NTag>
          );
        }
      },
      {
        key: 'status',
        title: 'Status',
        align: 'center',
        width: 150,
        render(rowData) {
          const attrs = statusOptions.value.find(v => v.code === rowData.status);
          if (!attrs) return '-';
          const { name, description } = attrs;
          const desc = JSON.parse(description);

          return (
            <NPopover>
              {{
                trigger: () => (
                  <NTag size="small" bordered={false} type={desc.buttonType as NaiveUI.ThemeColor}>
                    <div class="flex-center gap-2">
                      <span>{name}</span>
                      <SvgIcon icon="solar:question-circle-linear" class="text-base" />
                    </div>
                  </NTag>
                ),
                default: () => (
                  <div class="flex-col gap-2">
                    <span>Total:{rowData.totalCount}</span>
                    <span class="text-success-700">Success:{rowData.successCount}</span>
                    <span class="text-error">Fail:{rowData.failCount}</span>
                  </div>
                )
              }}
            </NPopover>
          );
        }
      },
      {
        key: 'operate',
        width: 100,
        render(rowData) {
          let attrs = statusOptions.value.find(v => v.code === rowData.status)?.description;
          if (!attrs) return '-';
          attrs &&= JSON.parse(attrs);
          const canView = attrs.buttonType !== 'primary' || false;
          return (
            <div class="flex items-center justify-end gap-8px">
              <ButtonIcon
                text
                quaternary={false}
                icon="solar:eye-linear"
                disabled={!canView}
                onClick={() => handleOpenDetail(rowData.id)}
              />
              {attrs ? (
                <ButtonConfirm
                  icon={attrs.taskButtonProps.icon}
                  confirmText={attrs.taskButtonProps.confirmText}
                  buttonProps={attrs.taskButtonProps.buttonProps}
                  onPositiveClick={() => handlePlayOrPause(rowData.id, attrs.taskButtonProps.icon)}
                />
              ) : (
                <div class="w-20px" />
              )}
            </div>
          );
        }
      }
    ];
  }
});

const [visible, toggleVisible] = useToggle(false);
const taskId = ref<number | null>(null);
function handleOpenDetail(id: number) {
  taskId.value = null;
  taskId.value = id;
  toggleVisible(true);
}

async function handlePlayOrPause(id: number, icon: string) {
  if (icon === 'tabler:player-play-filled') {
    const { error } = await fetchStartTikTokTask(id);
    if (error) return;
  } else if (icon === 'tabler:player-pause-filled') {
    const { error } = await fetchPauseTikTokTask(id);
    if (error) return;
  }
  getData();
}

function handleSearch() {
  searchParams.current = 1;
  searchParams.size = 10;
  getData();
}

async function initStatusOptions() {
  const taskStatusOpts = await getDictionaryByCodeType<number>('email_task_status');
  if (!taskStatusOpts) return;
  statusOptions.value = taskStatusOpts;
}

async function initTaskSenderStatusOptions() {
  const taskDetailStatusOpts = await getDictionaryByCodeType<number>('message_status');
  if (!taskDetailStatusOpts) return;
  taskSenderStatusOptions.value = taskDetailStatusOpts;
}

async function initMessageModuleOptions() {
  const messageModuleOpts = await getDictionaryByCodeType<number>('message_module');
  if (!messageModuleOpts) return;
  messageModuleOptions.value = messageModuleOpts;
}

const { routerPushByKey } = useRouterPush();
function handleBack() {
  routerPushByKey('creator-center_creator-outreach_find-creator');
}

const { pause, resume } = useIntervalFn(
  () => {
    getData();
  },
  1000 * 60 * 5
);

onMounted(() => {
  resume();
});

onUnmounted(() => {
  pause();
});

function init() {
  initStatusOptions();
  initTaskSenderStatusOptions();
  initMessageModuleOptions();
}

init();
</script>

<template>
  <div class="flex-col gap-4">
    <NCard class="card-wrapper" :bordered="false" title="Creator Outreach">
      <template #header-extra>
        <div class="flex justify-end gap-4">
          <ButtonBack :back-callback="handleBack" />
        </div>
      </template>
      <TaskSearch v-model:value="searchParams" :status-options="statusOptions" @change="handleSearch" />
    </NCard>
    <NCard class="h-full card-wrapper" :bordered="false" title="Task List">
      <template #header-extra>
        <div class="flex justify-end gap-4">
          <ButtonRefresh :callback="getData" />
        </div>
      </template>
      <NDataTable
        class="h-full"
        remote
        flex-height
        :loading="loading"
        :bordered="false"
        :columns="columns"
        :data="data"
        :pagination="pagination"
      >
        <template #empty>
          <NEmpty description="Start by importing or selecting creators to populate the list." />
        </template>
      </NDataTable>
      <TaskDetailDrawer
        v-model:show="visible"
        :task-id="taskId"
        :task-status-options="statusOptions"
        :task-sender-status-options="taskSenderStatusOptions"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
