<script setup lang="tsx">
import { computed, ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { NAvatar, NEllipsis, NImage } from 'naive-ui';
import { delay } from 'lodash-es';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { getFallbackImage } from '@/utils/fake-image';
import { NumeralFormat } from '@/enum';

interface Props {
  creators: any;
  removeRepeatCreators: (creatorsOecIds: string[]) => boolean;
  removeRepeatProducts: (productsIds: string[]) => boolean;
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const show = defineModel('show', {
  type: Boolean,
  required: true
});

const creatorOecIds = computed<string[]>(() => {
  const res = props.creators.map((item: any) => item.creator_id_list[0].base_info.creator_oec_id);
  return [...new Set(res)] as string[];
});

const productIds = computed<string[]>(() => {
  const res = props.creators.map((item: any) => item.product_list[0].product_id);
  return [...new Set(res)] as string[];
});

const columns = ref<NaiveUI.DataTableColumns<{ [key: string]: any }>>([
  {
    key: '0',
    title: 'Invitation name',
    width: 100,
    render(rowData) {
      return <NEllipsis lineClamp={2}>{rowData.name}</NEllipsis>;
    }
  },
  {
    key: '1',
    title: 'Creator',
    width: 200,
    render(rowData) {
      const creatorInfo = rowData.creator_id_list[0].base_info;
      return (
        <div class="flex items-center gap-2">
          <NAvatar
            class="flex-shrink-0"
            size="large"
            round
            src={creatorInfo.image.thumb_url_list[0]}
            fallbackSrc={getFallbackImage(50, 50)}
          />
          <NEllipsis lineClamp={1}>@{creatorInfo.user_name}</NEllipsis>
        </div>
      );
    }
  },
  {
    key: '2',
    title: 'Products',
    width: 300,
    render(rowData) {
      const product = rowData.product_list[0];
      return (
        <div class="flex items-center gap-2">
          <div class="h-80px w-80px flex-shrink-0">
            <NImage
              class="h-full w-full"
              src={product.image.thumb_url_list[0]}
              fallback-src={getFallbackImage(80, 80)}
            />
          </div>
          <div class="flex-col gap-2">
            <NEllipsis lineClamp={2} tooltip={{ contentStyle: 'max-width:400px;' }}>
              {product.title}
            </NEllipsis>
            <span class="text-coolgray">ID: {product.product_id}</span>
          </div>
        </div>
      );
    }
  },
  {
    key: '3',
    title: 'Previous commission',
    align: 'center',
    render(rowData) {
      const product = rowData.product_list[0];
      const rate = numberFormat(product.target_commission / 10000, NumeralFormat.Percent);
      return rate;
    }
  }
]);

const [removeCreatorsLoading, toggleRemoveCreatorsLoading] = useToggle(false);
const [removeProductsLoading, toggleRemoveProductsLoading] = useToggle(false);

function handleDeleteCreators() {
  toggleRemoveCreatorsLoading(true);
  const res = props.removeRepeatCreators(creatorOecIds.value);

  delay(() => {
    if (res) {
      window.$message?.success('Duplicate creators removed successfully');
      toggleRemoveCreatorsLoading(false);
      show.value = false;
    }
  }, 500);
}

function handleDeleteProducts() {
  toggleRemoveProductsLoading(true);
  const res = props.removeRepeatProducts(productIds.value);

  delay(() => {
    if (res) {
      window.$message?.success('Duplicate products removed successfully');
      toggleRemoveProductsLoading(false);
      show.value = false;
    }
  }, 500);
}
</script>

<template>
  <NModal
    v-model:show="show"
    class="w-800px"
    content-class="flex-col items-center gap-4"
    :closable="false"
    :close-on-esc="false"
    :mask-closable="false"
    preset="card"
    title="Duplicate invitations"
  >
    <div class="w-600px flex-y-center gap-4">
      <div>
        <icon-solar:shield-warning-bold-duotone class="text-20 text-warning" />
      </div>
      <div class="flex-col">
        <span class="text-2xl font-bold">The creators below are alreadypromoting products in this invitation.</span>
        <span class="text-base text-coolgray">
          To add these creators and products, please cancel theother invitations below.
        </span>
      </div>
    </div>
    <NDataTable
      class="min-h-300px"
      flex-height
      max-height="400px"
      :data="props.creators"
      :columns="columns"
    ></NDataTable>
    <template #footer>
      <div class="flex items-center justify-end gap-4">
        <NButton type="warning" :loading="removeCreatorsLoading" @click="handleDeleteCreators">
          Remove duplicate creators ({{ creatorOecIds.length }})
        </NButton>
        <NButton type="warning" :loading="removeProductsLoading" @click="handleDeleteProducts">
          Remove duplicate products ({{ productIds.length }})
        </NButton>
      </div>
    </template>
  </NModal>
</template>

<style scoped></style>
