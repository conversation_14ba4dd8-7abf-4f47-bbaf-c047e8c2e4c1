<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { objectPick, useToggle } from '@vueuse/core';
import dayjs from 'dayjs';
import { isNil, isNumber } from 'lodash-es';
import type { RouteKey } from '@elegant-router/types';
import { fetchCreateInviteTask, fetchGetFindCreatorListByIds } from '@/service/api';
import { useUserStore } from '@/store/modules/user';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useRouterPush } from '@/hooks/common/router';
import { localStg } from '@/utils/storage';
import CreatorSelect from '../creator-outreach_create_tiktok/modules/creator-select.vue';
import TiktokProductCusCommission from './modules/tiktok-product-cus-commission.vue';
import InviteCreatorSelectDrawer from './modules/invite-creator-select-drawer.vue';
import TemplateSelect from './modules/template-select.vue';

type Model = Api.CreatorOutreachInvite.CreateInviteTaskParams & {
  productIds: string[];
};

const userStore = useUserStore();
const { routerPushByKey } = useRouterPush();
const { getDictionaryByCodeType } = useDictionaryStore();
const needCached = ref(true);
const stepCurrent = ref(1);
const steps = [
  {
    title: 'Choose shop & creators'
  },
  {
    title: 'Create invitation'
  },
  {
    title: 'Choose products & Set up free samples'
  },
  {
    title: 'finish'
  }
];

const contentTypeOptions = ref<CommonType.Option<number>[]>([]);

const freeSampleOptions = ref<CommonType.Option<number>[]>([]);

const shopOptions = ref<CommonType.Option<number>[]>([]);

const [taskLoading, toggleTaskLoading] = useToggle(false);

const selectedCreatorData = ref<Api.CreatorNetwork.FindCreator[]>([]);

const [showAddCreatorDrawer, toggleAddCreatorDrawer] = useToggle(false);

const model = ref<Model>(createDefaultModel());

function createDefaultModel(): Model {
  return {
    shopId: null,
    creatorIdList: [],
    productIdCommissionList: [],
    invitationName: '',
    validUntil: null,
    email: '',
    countryCode: 'US#1',
    phone: '',
    content: '',
    contentType: null,
    productList: [],
    productIds: [],
    freeSampleAutoReview: 1,
    freeSamples: 1
  };
}

const isValidate = ref<boolean>(false);

const { formRef, validate } = useNaiveForm();

function createProductCommissionRequireRule() {
  const res: App.Global.FormRule[] = [
    { required: true, message: 'Please select products' },
    {
      asyncValidator: (rule, value) => {
        const hasEmptyCommission = value.some((item: any) => isNil(item.commission));
        const hasEmptyAdsCommission = value.some((item: any) => item.isAdsCommission && isNil(item.adsCommission));
        if (hasEmptyCommission || hasEmptyAdsCommission) {
          return Promise.reject(rule.message);
        }
        return Promise.resolve();
      },
      message: 'Please set standard commission rate for all products'
    }
  ];
  return res;
}

const { defaultRequiredRule, formRules } = useFormRules();
const allRules = computed(() => {
  return {
    shopId: [defaultRequiredRule],
    creatorIdList: [defaultRequiredRule],
    invitationName: [defaultRequiredRule],
    email: [defaultRequiredRule, ...formRules.email],
    contentType: [defaultRequiredRule],
    productIdCommissionList: [...createProductCommissionRequireRule()]
  };
});

const rules = ref({});

async function initShopOptions() {
  const userShops = await userStore.getUserShop();
  shopOptions.value =
    userShops?.map(item => {
      return {
        label: item.shopName,
        value: item.shopId
      };
    }) || [];
}

async function initFreeSampleOptions() {
  const freeSampleOpts = await getDictionaryByCodeType<number>('invite_free_sample', false, false);
  if (!freeSampleOpts) return;
  freeSampleOptions.value = freeSampleOpts.map(item => ({
    label: item.name,
    value: item.code,
    description: item.description
  }));
  model.value.freeSampleAutoReview = freeSampleOptions.value[0].value;
}

function handleBack() {
  window.$dialog?.warning({
    title: 'Warning',
    content: 'Going back will clear all task data you have entered. Are you sure you want to leave?',
    positiveText: 'Leave',
    negativeText: 'Stay',
    onPositiveClick: () => {
      needCached.value = false;
      routerPushByKey('creator-center_creator-outreach_find-creator');
    }
  });
}

async function handleNext() {
  await validate(err => {
    err?.flat().forEach((item: any) => {
      if (item.field === 'creatorIdList') {
        stepCurrent.value = 1;
      }
    });
  });

  switch (stepCurrent.value) {
    case 3: {
      if (!isValidate.value) {
        window.$message?.warning('Please wait for the verification to be completed');
        return;
      }
      toggleTaskLoading(true);
      const { error } = await fetchCreateInviteTask(model.value);
      toggleTaskLoading(false);
      if (error) {
        return;
      }
      break;
    }
    case 1: {
      const shopName = shopOptions.value.find(item => item.value === model.value.shopId)?.label;
      const timeStr = dayjs().format('MMDD');
      model.value.invitationName = `${shopName}-${model.value.creatorIdList.length}-${timeStr}`;
      model.value.validUntil = dayjs().add(1, 'year').format('YYYY-MM-DD');
      break;
    }
    default:
      break;
  }
  stepCurrent.value += 1;
}

function handleLinkTo(route: RouteKey) {
  needCached.value = false;
  routerPushByKey(route);
}

function handleAddCreators() {
  toggleAddCreatorDrawer(true);
}

function handleUpdateCheckedRowKeys(ids: number[]) {
  localStg.set('findCreatorSelectedIds', ids);
  initSelectedCreatorData();
}

async function initSelectedCreatorData() {
  const ids = localStg.get('findCreatorSelectedIds') || [];
  const { data: selectedCreators, error: selectedCreatorErr } = await fetchGetFindCreatorListByIds(ids || []);
  if (!selectedCreatorErr) {
    selectedCreatorData.value = selectedCreators;
  }
}

async function initContentTypeOptions() {
  const opts = await getDictionaryByCodeType<number>('invite_content_type', false, false);
  contentTypeOptions.value =
    opts?.map(item => ({
      label: item.name,
      value: item.code
    })) || [];

  model.value.contentType = contentTypeOptions.value[0].value;
}

function initData() {
  initShopOptions();
  initSelectedCreatorData();
  initContentTypeOptions();
  initFreeSampleOptions();
}

initData();

watch(
  () => stepCurrent.value,
  newVal => {
    let res = {};
    switch (newVal) {
      case 1:
        res = objectPick(allRules.value, ['shopId', 'creatorIdList']);
        break;
      case 2:
        res = objectPick(allRules.value, ['invitationName', 'email', 'contentType']);
        break;
      case 3:
        res = allRules.value;
        break;
      default:
        break;
    }
    rules.value = res;
  },
  {
    immediate: true
  }
);

watch(
  () => selectedCreatorData.value,
  newVal => {
    model.value.creatorIdList = newVal.map(item => item.creatorId) as any;
  }
);

watch(
  () => model.value.productList,
  newVal => {
    if (!newVal.length) {
      model.value.productIdCommissionList = [];
      return;
    }

    model.value.productIdCommissionList = newVal.map((item: any) => {
      return {
        productId: item.product_id,
        commission: isNumber(item.standardCommissionRate)
          ? item.standardCommissionRate * 100
          : item.standardCommissionRate,
        isAdsCommission: item.isAdsCommission,
        adsCommission: isNumber(item.shopAdsCommissionRate)
          ? item.shopAdsCommissionRate * 100
          : item.shopAdsCommissionRate
      };
    });
  },
  {
    deep: true
  }
);
</script>

<template>
  <div class="flex-col gap-4">
    <NCard class="h-full min-h-400px card-wrapper" segmented :bordered="false" title="Invite creators to collaborate">
      <template #header-extra>
        <ButtonBack :back-callback="handleBack" />
      </template>
      <NSteps :current="stepCurrent">
        <NStep v-for="s in steps" :key="s.title" :title="s.title"></NStep>
      </NSteps>
      <NForm ref="formRef" class="mt-8" :model="model" :rules="rules">
        <div v-show="stepCurrent === 1">
          <NFormItem label="Shop" path="shopId">
            <NSelect v-model:value="model.shopId" class="max-w-400px" :options="shopOptions"></NSelect>
          </NFormItem>
          <NFormItem label="Creator Data" path="creatorIdList">
            <div class="w-full flex-col gap-2">
              <NButton class="max-w-200px" type="primary" @click="handleAddCreators">Add Creators</NButton>
              <CreatorSelect :creator-data="selectedCreatorData" :show-manual="false" />
            </div>
            <InviteCreatorSelectDrawer
              v-model:show="showAddCreatorDrawer"
              :checked-row-keys="model.creatorIdList"
              @update:checked-row-keys="handleUpdateCheckedRowKeys"
            />
          </NFormItem>
        </div>
        <div v-show="stepCurrent === 2">
          <!--
 <NFormItem label="Select Outreach Method" path="">
            <NRadioGroup v-model:value="selectOutreachMethod">
              <NRadio v-for="item in selectOutreachMethodOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </NRadio>
            </NRadioGroup>
          </NFormItem>
-->
          <NFormItem label="Invitation name" path="invitationName">
            <NInput v-model:value="model.invitationName" class="max-w-400px" />
            <template #feedback>
              <span class="form-tip">Default format: Shop Name-Creator Count-Date (MMDD)</span>
            </template>
          </NFormItem>
          <NFormItem label="Valid until" path="validUntil">
            <NDatePicker v-model:formatted-value="model.validUntil" type="date" format="yyyy-MM-dd" />
            <template #feedback>
              <span class="form-tip">
                Once expired, creators will no longer be able to accept or be eligible for the commission offered in
                this invitation.
              </span>
            </template>
          </NFormItem>
          <NGrid :cols="4" :x-gap="16">
            <NFormItemGi label="Email address" path="email">
              <NInput v-model:value="model.email" />
            </NFormItemGi>
            <NFormItemGi label="Phone" path="phone">
              <InputPhone v-model:value="model.phone" v-model:country="model.countryCode" />
            </NFormItemGi>
            <NGi class="flex-center">
              <TemplateSelect v-model:data="model" />
            </NGi>
          </NGrid>
          <NFormItem label="Message" path="content">
            <NInput
              v-model:value="model.content"
              class="h-300px max-w-600px"
              type="textarea"
              show-count
              :maxlength="500"
            />
            <template #feedback>
              <span class="form-tip">
                Send a message to introduce yourself and share a bit about why you’re excited to collaborate.
              </span>
            </template>
          </NFormItem>
          <NFormItem label="Preferred content type" path="contentType">
            <NSelect v-model:value="model.contentType" class="max-w-200px" :options="contentTypeOptions"></NSelect>
            <template #feedback>
              <span class="form-tip">
                Let creators know if you have a preference between shoppable videos or LIVE sessions. Creators can post
                any content type to earn target plan commission.
              </span>
            </template>
          </NFormItem>
        </div>
        <div v-show="stepCurrent === 3">
          <NFormItem label="Products" path="productIdCommissionList">
            <TiktokProductCusCommission
              v-model:value="model.productIds"
              v-model:products="model.productList"
              v-model:creators="selectedCreatorData"
              v-model:validate="isValidate"
              :shop-id="model.shopId"
              class="h-full w-full"
            />
          </NFormItem>
          <NFormItem path="freeSampleAutoReview">
            <template #label>
              <div class="flex-y-center gap2">
                <span>Offer free samples</span>
                <NSwitch v-model:value="model.freeSamples" :checked-value="1" :unchecked-value="0"></NSwitch>
              </div>
            </template>
            <NGrid v-show="model.freeSamples" class="my-4" :cols="2" :x-gap="16">
              <NGi
                v-for="opt in freeSampleOptions"
                :key="opt.value"
                class="h-130px flex-col gap-4 rounded bg-#F8F8F8 p-4 hover:cursor-pointer"
                @click="model.freeSampleAutoReview = opt.value"
              >
                <div class="flex gap-4">
                  <NRadio :checked="model.freeSampleAutoReview === opt.value"></NRadio>
                  <span>{{ opt.label }}</span>
                </div>
                <span>{{ opt.description }}</span>
              </NGi>
            </NGrid>
          </NFormItem>
        </div>
        <div v-show="stepCurrent === 4" class="h-full flex-center">
          <div class="flex-col-center gap-4">
            <icon-solar:check-circle-bold-duotone class="text-5xl text-green-5" />
            <span class="text-base">
              Successfully Created an Outreach Task for {{ model.creatorIdList.length }} Creator(s).
            </span>
            <div class="flex gap-4">
              <!-- <NButton strong secondary> Create Another Outreach Task with the Same Creator(s) </NButton> -->
              <NButton
                strong
                secondary
                type="primary"
                @click="handleLinkTo('creator-center_creator-outreach_history_target-invitation')"
              >
                Back to target invitation List
              </NButton>
            </div>
          </div>
        </div>
      </NForm>
      <template #action>
        <div v-if="stepCurrent < 4" class="flex-y-center justify-end gap-4">
          <!--
 <span v-if="stepCurrent === 1" class="text-coolgray">
            Total {{ model.creatorList.length }} creator{{ model.creatorList.length !== 1 ? 's' : '' }} selected
          </span>
-->
          <NButton v-if="stepCurrent > 1" @click="stepCurrent--">Previous</NButton>
          <NButton type="primary" :loading="taskLoading" @click="handleNext">
            {{ stepCurrent === 3 ? 'Save & Run Task' : 'Next' }}
          </NButton>
        </div>
      </template>
    </NCard>
  </div>
</template>

<style scoped lang="scss">
.form-tip {
  @apply text-xs text-coolgray;
}
</style>
