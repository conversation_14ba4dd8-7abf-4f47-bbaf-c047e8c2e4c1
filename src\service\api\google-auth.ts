import { request } from '../request';

export function fetchGoogleOauth2Url(url: string) {
  return request<{ url: string }>({
    url: '/google/oauth2Url',
    method: 'get',
    params: { url }
  });
}

export function fetchGoogleOauth2Callback(url: string, code: string, scopes: string) {
  return request({
    url: '/google/oauth2Code',
    method: 'post',
    params: { url, code, scopes }
  });
}
