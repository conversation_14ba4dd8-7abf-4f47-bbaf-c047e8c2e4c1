<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-04 16:25:08
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-08 15:10:20
 * @FilePath: \tiksage-frontend\src\views\home\modules\contribution-card.vue
 * @Description: contribution-card
-->
<script setup lang="tsx">
import { ref, watch } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useDashboardStore } from '@/store/modules/dashboard';
import { useIndicator } from '@/hooks/custom/indicator';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import eventBus from '@/utils/event-bus';
import { NumeralFormat } from '@/enum';

interface Props {
  model: {
    title: string;
    allIndicatorKey: Api.Auth.AllIndicatorKey[];
    userIndicatorKey: Api.Auth.userIndicatorKey;
    description?: string;
    [key: string]: any;
  };
}

const appStore = useAppStore();

const { numberFormat } = useNumberFormat();
const props = defineProps<Props>();

const chartOptions = ref<Visactor.VChart.IPieChartSpec>();

// data
const { displayValue, checkedValue, options, updateCheckedValue } = useIndicator<string>(
  props.model.allIndicatorKey,
  props.model.userIndicatorKey
);

const handleValueChange = (value: string) => {
  checkedValue.value = value;
  updateCheckedValue();
};

const dashboardStore = useDashboardStore();

const loading = ref(false);

const initData = () => {
  const values: any[] = [];
  const chartData = dashboardStore.getUsefulData(displayValue.value, options.value);
  const { fieldsArr, unit } = chartData[0]!.option;
  if (fieldsArr) {
    for (const field of fieldsArr) {
      const item = {
        type: field.title,
        value: dashboardStore.dashboardData.currentTotalData[field.key],
        unit
      };
      values.push(item);
    }
  }
  const total = values.reduce((pre, cur) => {
    return pre + cur.value;
  }, 0);
  chartOptions.value = {
    type: 'pie',
    data: [
      {
        id: 'dollarY',
        values
      }
    ],
    categoryField: 'type',
    valueField: 'value',
    label: {
      visible: false
    },
    legends: {
      visible: true,
      orient: 'right',
      data: legendData => {
        return legendData.map(d => {
          let value = values.find(v => v.type === d.label).value;
          const percent = numberFormat(value / total, NumeralFormat.Percent);
          value = numberFormat(value, unit === '$' ? NumeralFormat.Dollar : NumeralFormat.Number);
          return {
            ...d,
            value: `${value}(${percent})`
          };
        });
      },
      item: {
        width: '50%',
        spaceRow: 8,
        autoEllipsisStrategy: 'valueFirst',
        shape: {
          style: {
            symbolType: 'circle'
          }
        },
        label: {
          style: {
            fontSize: 14,
            fill: '#9ca3af'
          }
        },
        value: {
          alignRight: true,
          style: {
            textAlign: 'right',
            fontSize: 14,
            fill: '#000',
            fontWeight: 'bold',
            ellipsis: false
          }
        }
      }
    }
  };
};

function handleShowMore() {
  eventBus.emit('monthly-data-drawer', {
    type: 'show',
    data: true
  });
}

watch(
  () => displayValue.value,
  () => {
    loading.value = true;

    initData();
    setTimeout(() => {
      loading.value = false;
    }, 500);
  }
);

initData();
</script>

<template>
  <NCard :bordered="false">
    <template #header>
      <NFlex align="center">
        <span>{{ model.title }}</span>
        <Tip v-if="model.description" :description="model.description" />
      </NFlex>
    </template>
    <template #header-extra>
      <div class="flex justify-end gap-2">
        <NSelect
          :value="checkedValue"
          :on-update:value="handleValueChange"
          :options="(options[0] as any[])"
          :consistent-menu-width="false"
        />

        <ButtonIcon v-if="model.title === 'Breakdown By Channel'" icon="solar:menu-dots-bold" @click="handleShowMore" />
      </div>
    </template>
    <NFlex v-if="loading" justify="space-around" align="center" :wrap="false" class="h-300px">
      <NSkeleton height="180px" width="180px" circle />
      <div class="w-40%">
        <NSkeleton text :repeat="6" />
      </div>
    </NFlex>
    <NFlex v-else justify="space-between" :vertical="appStore.isMobile" :wrap="false">
      <div class="h-full w-full">
        <VDoughnutChart style="height: 300px" :chart-options="chartOptions" />
      </div>
      <div :id="props.model.userIndicatorKey" class="flex flex-1 items-center"></div>
    </NFlex>
  </NCard>
</template>

<style scoped></style>
