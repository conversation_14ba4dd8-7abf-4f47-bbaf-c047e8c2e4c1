<script setup lang="ts">
import { computed, ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { useTagManagement } from '@/hooks/custom/tagManagement';
import { focus as vFocus } from '@/directives/focus';

type Tag = Api.Tag.Tag;

interface Props {
  defaultTags?: number[];
  onSave?: (tags: Tag[]) => Promise<boolean>; // 添加保存回调函数
}

const props = defineProps<Props>();
const model = defineModel<number[]>('value', { default: [] });
const [saveLoading, toggleSaveLoading] = useToggle(false);

const {
  tagList,
  selectedTags,
  editingTag,
  searchValue,
  filteredTags,
  hasExistingTag,
  selectedTagIds,
  handleSearch,
  createTag,
  updateTag,
  toggleTag,
  removeTag,
  deleteTag
} = useTagManagement();

// 添加 popover ref
const popoverRef = ref();

// 处理标签选择
async function handleSelectTag(tag: Tag) {
  toggleTag(tag);
  model.value = selectedTagIds.value;
}

// 处理标签移除
function handleRemoveTag(tagId: number) {
  removeTag(tagId);
  model.value = selectedTagIds.value;
}

// 处理标签删除
async function handleDeleteTag(tagId: number) {
  const success = await deleteTag(tagId);
  if (success) {
    window.$message?.success('Tag deleted successfully');
  } else {
    window.$message?.error('Failed to delete tag');
  }
}

// 创建新标签
async function handleCreateTag() {
  if (!searchValue.value) return;

  const result = await createTag(searchValue.value);
  if (!result) {
    window.$message?.error('Failed to create tag');
    return;
  }

  if (result.exists) {
    window.$message?.warning('Tag with this name already exists');
    if (!selectedTags.value.some(t => t.id === result.tag?.id)) {
      result.tag && handleSelectTag(result.tag);
    }
  } else if (result.tag) {
    handleSelectTag(result.tag);
    window.$message?.success('Tag created successfully');
  }

  searchValue.value = '';
}

// 进入编辑模式
function handleEdit(tag: Tag) {
  editingTag.value = { ...tag };
}

// 保存修改
async function handleSave(needExit = true) {
  if (!editingTag.value) return;

  const success = await updateTag(editingTag.value.id, {
    name: editingTag.value.name,
    color: editingTag.value.color
  });
  if (success) {
    if (needExit) {
      exitEdit();
    }
  } else {
    window.$message?.error('Failed to update tag');
  }
}

// 退出编辑模式
function exitEdit() {
  editingTag.value = null;
}

// 处理回车事件
function handleKeydown(e: KeyboardEvent) {
  if (e.key === 'Enter') {
    handleSave();
  } else if (e.key === 'Escape') {
    exitEdit();
  }
}

function handleCancel() {
  exitEdit();
  // 使用 defaultTags 重置选中状态
  popoverRef.value?.setShow(false);
}

// 重置选中状态
function handleClear() {
  exitEdit();

  selectedTags.value = [];
  model.value = [];
}

// 保存所有更改
async function handleSaveTags() {
  if (saveLoading.value) return;
  toggleSaveLoading(true);
  try {
    if (props.onSave) {
      const success = await props.onSave(selectedTags.value);
      if (success) {
        popoverRef.value?.setShow(false);
      }
    }
  } finally {
    toggleSaveLoading(false);
  }
}

const displayTags = computed(() => {
  // 从 tagList 中获取 defaultTags 对应的标签
  return props.defaultTags
    ? props.defaultTags
        .map(tagId => tagList.value.find(t => t.id === tagId))
        .filter((tag): tag is Tag => tag !== undefined)
    : [];
});

// 为每个标签设置样式
const getTagStyle = (backgroundColor: string) => {
  return {
    color: {
      color: backgroundColor,
      textColor: '#ffffff'
    },
    themeOverrides: {
      closeIconColor: '#ffffff',
      closeIconColorHover: '#ffffffCC', // 80% 透明度
      closeIconColorPressed: '#ffffffE6' // 90% 透明度
    }
  };
};

function handlePanelVisible(show: boolean) {
  if (show) {
    // 处理面板显示
    initSelectTags();
  }
}

function initSelectTags() {
  // 初始化选中的标签
  selectedTags.value = displayTags.value;
}
</script>

<template>
  <NPopover
    ref="popoverRef"
    :width="300"
    trigger="click"
    :show-arrow="false"
    @clickoutside="handleCancel"
    @update:show="handlePanelVisible"
  >
    <template #trigger>
      <slot name="trigger">
        <div class="w-full flex-y-center gap2 hover:cursor-pointer">
          <template v-if="displayTags.length > 0">
            <NTag
              class="hover:cursor-pointer"
              :bordered="false"
              size="small"
              v-bind="getTagStyle(displayTags[0].color)"
            >
              <div class="max-w-60px truncate">
                {{ displayTags[0].name }}
              </div>
            </NTag>

            <NTag v-if="displayTags.length > 1" class="hover:cursor-pointer" :bordered="false" size="small">
              + {{ displayTags.length - 1 }}
            </NTag>
            <icon-solar:alt-arrow-down-bold class="flex-shrink-0" />
          </template>
          <template v-else>
            <span class="text-gray-400">No tags</span>
            <icon-solar:pen-2-linear />
          </template>
        </div>
      </slot>
    </template>
    <template #header>
      <div class="mb-2 flex-y-center flex-wrap gap-2">
        <span class="text-gray-400">Tags</span>
        <NTag
          v-for="tag in selectedTags"
          :key="tag.id"
          class=""
          strong
          :bordered="false"
          size="small"
          closable
          v-bind="getTagStyle(tag.color)"
          @close="handleRemoveTag(tag.id)"
        >
          <div class="max-w-60px truncate">
            {{ tag.name }}
          </div>
        </NTag>
      </div>
      <div class="flex-y-center gap-2">
        <NInput v-model:value="searchValue" placeholder="Search or create tag" clearable @input="handleSearch">
          <template #suffix>
            <icon-solar:magnifer-linear />
          </template>
        </NInput>
      </div>
    </template>
    <NScrollbar style="height: 250px">
      <NList hoverable clickable>
        <NListItem v-for="tag in filteredTags" :key="tag.id">
          <div class="flex-y-center justify-between gap-2" @click.stop="handleSelectTag(tag)">
            <template v-if="editingTag?.id === tag.id">
              <NInput v-model:value="editingTag.name" v-focus size="medium" @keydown="handleKeydown" @click.stop>
                <template #prefix>
                  <div class="h-14px w-14px rounded" :style="{ backgroundColor: editingTag.color }"></div>
                </template>
                <template #suffix>
                  <ButtonColorPicker v-model:color="editingTag.color" @click="handleSave(false)" />
                  <NPopconfirm positive-text="Delete" @positive-click="handleDeleteTag(tag.id)">
                    <template #trigger>
                      <NButton size="tiny" type="error" quaternary>
                        <template #icon>
                          <icon-solar:trash-bin-2-linear />
                        </template>
                      </NButton>
                    </template>
                    <template #default>
                      <div class="text-sm">This will permanently delete the tag "{{ tag.name }}" from all items.</div>
                    </template>
                  </NPopconfirm>
                </template>
              </NInput>
            </template>
            <template v-else>
              <div class="flex-y-center gap-2">
                <NCheckbox class="flex-shrink-0" :checked="selectedTags.some(t => t.id === tag.id)" />
                <div class="flex-1">
                  <div
                    class="flex-center rounded-md px-6px py-3px text-xs"
                    :style="{ backgroundColor: tag.color, color: '#ffffff' }"
                  >
                    <NEllipsis line-clamp="1">
                      {{ tag.name }}
                    </NEllipsis>
                  </div>
                </div>
              </div>
              <NButton class="flex-shrink-0" size="tiny" quaternary @click.stop="handleEdit(tag)">
                <template #icon>
                  <icon-solar:pen-2-linear />
                </template>
              </NButton>
            </template>
          </div>
        </NListItem>

        <!-- 修改创建标签的条件显示逻辑 -->
        <NListItem v-if="searchValue && !hasExistingTag">
          <div class="flex-col-center gap-2">
            <span class="text-gray-400">Create new tag "{{ searchValue }}"</span>
            <NButton size="small" type="primary" @click="handleCreateTag">
              <template #icon>
                <icon-solar:add-circle-bold />
              </template>
              Create
            </NButton>
          </div>
        </NListItem>

        <!-- 添加提示信息，当存在同名标签时显示 -->
        <NListItem v-else-if="searchValue && hasExistingTag">
          <div class="flex-col-center text-gray-400">Tag with this name already exists</div>
        </NListItem>
      </NList>
    </NScrollbar>
    <template #footer>
      <div class="flex-y-center justify-between gap-2">
        <NButton size="small" @click="handleClear">Clear</NButton>
        <div class="flex-y-center gap-2">
          <NButton size="small" @click="handleCancel">Cancel</NButton>
          <NButton size="small" type="primary" :loading="saveLoading" @click="handleSaveTags">Save</NButton>
        </div>
      </div>
    </template>
  </NPopover>
</template>

<style scoped lang="scss"></style>
