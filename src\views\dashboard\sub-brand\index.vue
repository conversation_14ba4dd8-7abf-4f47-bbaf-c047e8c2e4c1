<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-05 11:03:34
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-12-05 11:07:55
 * @FilePath: \tiksage-frontend\src\views\home\index.vue
 * @Description: Dashboard Page
-->
<script setup lang="ts">
import { reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useToggle } from '@vueuse/core';
import {
  fetchDownloadTrendPerformance,
  fetchGetSubBrandShopMetricData,
  fetchGetUserShopListByDashboard
} from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useDashboardStore } from '@/store/modules/dashboard';
import { downloadFile } from '@/utils/download';
import MonthlyDataDrawer from '@/views/dashboard/modules/monthly-data-drawer.vue';
import ComparisonCard from '../modules/comparison-card.vue';
import ContributionCard from '../modules/contribution-card.vue';
import CreatorSalesCard from '../modules/creator-sales-card.vue';
import QuotaCard from '../modules/quota-card.vue';
import TrendLineCard from '../modules/trend-line-card.vue';
import ShopInfoCard from '../modules/shop-info-card.vue';
import PopularContent from '../modules/popular-content.vue';
import CreatorPerformanceTab from '../modules/creator-performance/creator-performance-tab.vue';
import ConversionAnalysis from '../modules/conversion-analysis.vue';
import ProductCard from './modules/product-card.vue';
import ShopList from './modules/shop-list.vue';
import SubBrandProductCard from './modules/sub-brand-product-card.vue';
import SubBrandShopCard from './modules/sub-brand-shop-card.vue';

interface MetricCardInfo {
  title: string;
  allIndicatorKey: Api.Auth.AllIndicatorKey[];
  userIndicatorKey: Api.Auth.userIndicatorKey;
  description?: string;
}
const route = useRoute();
const routeparams = route.query;
const [shopReady, toggleShopReady] = useToggle(false);

const dashboardStore = useDashboardStore();
const authStore = useAuthStore();
const indicatorData: Record<'quota' | 'trendLine' | 'contribution' | 'comparison', MetricCardInfo[]> = {
  quota: [
    {
      title: 'Sales',
      allIndicatorKey: ['salesArr'],
      userIndicatorKey: 'salesArr',
      description:
        "The sales metric is elucidated by presenting the shop's sales figures and compared with previous periods."
    },
    {
      title: 'Marketing',
      allIndicatorKey: ['trafficArr'],
      userIndicatorKey: 'trafficArr',
      description:
        'The customer conversion data from viewing to purchasing is succinctly presented and compared with previous periods.'
    },
    {
      title: 'Content',
      allIndicatorKey: ['contentArr'],
      userIndicatorKey: 'contentArr',
      description:
        'Displaying content exposure and interaction data for relevant videos and live sessions, with comparisons to previous periods.'
    },
    {
      title: 'Advertising',
      allIndicatorKey: ['adsArr'],
      userIndicatorKey: 'adsArr',
      description: 'Shop advertising data and performance.'
    }
  ],
  trendLine: [
    {
      title: 'Breakdown By Affiliate ',
      allIndicatorKey: ['performanceSalesArr', 'performanceTrafficArr', 'performancecontentArr'],
      userIndicatorKey: 'performanceArr'
    }
  ],
  contribution: [
    {
      title: 'Breakdown By Channel',
      allIndicatorKey: ['breakdownChannelArr'],
      userIndicatorKey: 'breakdownChannel',
      description: `According to the data source channels for differentiated statistics, such as "GMV":
Live:
The total amount paid for orders placed directly from all LIVEs, including returns and refunds.
Video:
The total amount of paid orders from all shoppable videos, including returns and refunds.
Product card:
The transaction amount generated through Product Card transactions, including cancellations and refunds. This is the transaction amount generated by users who clicked on the Product Card (excluding video and LIVE), entered the product details page, and conducted a transaction during the selected period.`
    },
    {
      title: 'Breakdown By Affiliate ',
      allIndicatorKey: ['breakdownAffiliateArr'],
      userIndicatorKey: 'breakdownAffiliate',
      description: `Affiliate: The affiliate is available to all TikTok Shop creators. You may add affiliate products to your TikTok Shop showcase. After a customer purchases one of your affiliate items, you will earn commissions on qualifying purchases. Your commission is calculated using this formula: Commission = (Revenue - Refunds) * Commission rate.`
    }
  ],
  comparison: [
    {
      title: 'Content Performance Between Affiliate & Non-affiliate',
      allIndicatorKey: ['contentAffiliateOwnArr'],
      userIndicatorKey: 'contentAffiliateOwn'
    }
  ]
};

const tab = ref<'dashboard' | 'creator-performance'>('dashboard');

function createSearchParams() {
  let arr: number[];
  if (routeparams.shopId) {
    toggleShopReady(true);
    arr = [Number(routeparams.shopId)];
  } else {
    arr = [];
  }

  return {
    brand: null,
    market: null,
    // shopIdsArr: authStore.userInfo.userShopIds || [],
    shopIdsArr: arr,
    startDateStr: '',
    endDateStr: ''
  };
}

const searchParams = reactive<Api.Dashboard.DashboardTopSearchParams>(createSearchParams());

const subBrandMetricData = ref<Api.DashboardJazwares.SubBrandMetricResponse>();

async function getSubBrandMetricData(params: Api.DashboardJazwares.BaseSearchParams) {
  const { data, error } = await fetchGetSubBrandShopMetricData(params);
  if (error) return;
  subBrandMetricData.value = data;
}

function handleChangeDateRange(value: [string, string] | null) {
  if (value) {
    searchParams.startDateStr = value[0];
    searchParams.endDateStr = value[1];
  }
}

async function handleDownload() {
  const { data, error } = await fetchDownloadTrendPerformance(searchParams);
  if (error) return;
  downloadFile(data, 'xlsx', 'Performance Trend');
}

const shopOptions = ref<CommonType.Option<number>[]>([]);
async function initShopOptions() {
  const { data: shopOpts, error: ShopOptsError } = await fetchGetUserShopListByDashboard('dashboard_sub-brand');
  if (!ShopOptsError) {
    shopOptions.value = shopOpts.map(item => ({ label: item.shopName, value: item.shopId }));
    if (!routeparams.shopId) {
      searchParams.shopIdsArr = [shopOpts[0].shopId];
      toggleShopReady(true);
    }
  }
}

function handleShopUpdate(value: number) {
  searchParams.shopIdsArr = [value];
}

function initData() {
  initShopOptions();
}

initData();

watch(
  () => searchParams,
  async () => {
    if (shopReady.value && tab.value === 'dashboard') {
      getSubBrandMetricData(searchParams);
      dashboardStore.getDashboardData(searchParams);
    }
  },
  {
    deep: true
  }
);
</script>

<template>
  <NFlex vertical :size="16" class="h-full">
    <NCard :bordered="false" class="card-wrapper">
      <template #header>
        <NTabs v-model:value="tab" tab-style="font-size: 18px; font-weight: 500;" animated>
          <NTab name="dashboard" tab="Dashboard" />
          <NTab name="creator-performance" tab="Creator Performance" />
        </NTabs>
      </template>
      <template #header-extra>
        <NPopselect
          :disabled="authStore.userInfo.userShopIds.length <= 1"
          :value="searchParams.shopIdsArr?.[0]"
          :options="shopOptions"
          @update-value="handleShopUpdate"
        >
          <NButton quaternary>
            <div class="flex items-center justify-end">
              <ShopInfoCard v-if="searchParams.shopIdsArr?.length === 1" :id="searchParams.shopIdsArr[0]" />
              <icon-solar:alt-arrow-down-line-duotone v-if="authStore.userInfo.userShopIds.length > 1" />
            </div>
          </NButton>
        </NPopselect>
        <!-- <ShopInfoCard v-if="searchParams.shopIdsArr?.length === 1" :id="searchParams.shopIdsArr[0]" /> -->
        <!-- <ShopButton v-model:value="searchParams" /> -->
      </template>
    </NCard>
    <NFlex v-if="tab === 'dashboard'" vertical :size="16">
      <!-- <TodayPerformance v-if="searchParams.shopIdsArr?.length === 1" :shop-id="searchParams.shopIdsArr[0]" /> -->
      <NSpin class="h-full min-h-100vh" :show="dashboardStore.loading">
        <NSpace class="pb-16px" vertical :size="16">
          <NCard class="card-wrapper" :bordered="false" title=" ">
            <template #header-extra>
              <ButtonDate
                :default-value="-1"
                :start-time="authStore.userInfo.selectStartDateStr"
                :end-time="authStore.userInfo.selectEndDateStr"
                @update:value="handleChangeDateRange"
              />
            </template>
          </NCard>
          <NFlex v-if="!dashboardStore.loading" vertical :size="16">
            <NGrid :x-gap="16" :y-gap="16" :cols="4">
              <NGi v-for="item in indicatorData.quota" :key="item.title">
                <QuotaCard :model="item" />
              </NGi>
            </NGrid>
            <ConversionAnalysis :params="searchParams" />
            <TrendLineCard :model="indicatorData.trendLine[0]" @download="handleDownload" />
            <NGrid v-if="subBrandMetricData" :x-gap="16" y-gap="16" responsive="screen" item-responsive>
              <NGi span="16">
                <SubBrandShopCard :data="subBrandMetricData" />
              </NGi>
              <NGi span="8">
                <SubBrandProductCard :data="subBrandMetricData" />
              </NGi>
            </NGrid>
            <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
              <NGi v-for="item in indicatorData.contribution" :key="item.title" span="12">
                <ContributionCard :model="item" />
              </NGi>
            </NGrid>
            <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
              <NGi span="24">
                <ComparisonCard :model="indicatorData.comparison[0]" />
              </NGi>
            </NGrid>
            <ProductCard :params="searchParams" />
            <PopularContent :params="searchParams" />
            <CreatorSalesCard :params="searchParams" />
            <ShopList :params="searchParams" />
            <MonthlyDataDrawer :shop-id="searchParams.shopIdsArr?.[0]" />
          </NFlex>
        </NSpace>
      </NSpin>
    </NFlex>
    <CreatorPerformanceTab
      v-if="tab === 'creator-performance'"
      sub-brand
      :shop-ids-arr="searchParams.shopIdsArr"
      :shop-options="shopOptions"
    />
  </NFlex>
</template>

<style scoped></style>
