/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppProvider: typeof import('./../components/common/app-provider.vue')['default']
    AverageCountTo: typeof import('./../components/custom/average-count-to.vue')['default']
    BetterScroll: typeof import('./../components/custom/better-scroll.vue')['default']
    ButtonBack: typeof import('./../components/custom/button-back.vue')['default']
    ButtonColorPicker: typeof import('./../components/custom/button-color-picker.vue')['default']
    ButtonConfirm: typeof import('./../components/custom/button-confirm.vue')['default']
    ButtonCopy: typeof import('./../components/custom/button-copy.vue')['default']
    ButtonDate: typeof import('./../components/custom/button-date.vue')['default']
    ButtonIcon: typeof import('./../components/custom/button-icon.vue')['default']
    ButtonRefresh: typeof import('./../components/custom/button-refresh.vue')['default']
    CardTitle: typeof import('./../components/custom/card-title.vue')['default']
    CountTo: typeof import('./../components/custom/count-to.vue')['default']
    CreatorFilter: typeof import('./../components/filter/creator-filter.vue')['default']
    CycleRatio: typeof import('./../components/custom/cycle-ratio.vue')['default']
    DarkModeContainer: typeof import('./../components/common/dark-mode-container.vue')['default']
    DateSingleButton: typeof import('./../components/custom/date-single-button.vue')['default']
    Empty: typeof import('./../components/custom/empty.vue')['default']
    ExceptionBase: typeof import('./../components/common/exception-base.vue')['default']
    FullScreen: typeof import('./../components/common/full-screen.vue')['default']
    HorizontalScreenTip: typeof import('./../components/custom/horizontal-screen-tip.vue')['default']
    IconAntDesignReloadOutlined: typeof import('~icons/ant-design/reload-outlined')['default']
    IconAntDesignSettingOutlined: typeof import('~icons/ant-design/setting-outlined')['default']
    'IconEosIcons:hourglass': typeof import('~icons/eos-icons/hourglass')['default']
    'IconFluent:mailTemplate16Regular': typeof import('~icons/fluent/mail-template16-regular')['default']
    'IconFluentEmoji:robot': typeof import('~icons/fluent-emoji/robot')['default']
    IconGridiconsFullscreen: typeof import('~icons/gridicons/fullscreen')['default']
    IconGridiconsFullscreenExit: typeof import('~icons/gridicons/fullscreen-exit')['default']
    'IconIc:roundPlus': typeof import('~icons/ic/round-plus')['default']
    IconIcRoundDelete: typeof import('~icons/ic/round-delete')['default']
    IconIcRoundPlus: typeof import('~icons/ic/round-plus')['default']
    IconIcRoundRefresh: typeof import('~icons/ic/round-refresh')['default']
    IconIcRoundRemove: typeof import('~icons/ic/round-remove')['default']
    IconIcRoundSearch: typeof import('~icons/ic/round-search')['default']
    'IconLineMd:alert': typeof import('~icons/line-md/alert')['default']
    'IconLineMd:circleToConfirmCircleTransition': typeof import('~icons/line-md/circle-to-confirm-circle-transition')['default']
    IconLocalLogo: typeof import('~icons/local/logo')['default']
    IconLocalLogoTitle: typeof import('~icons/local/logo-title')['default']
    IconLocalLogoTitleDark: typeof import('~icons/local/logo-title-dark')['default']
    'IconLogos:tiktokIcon': typeof import('~icons/logos/tiktok-icon')['default']
    'IconMaterialSymbols:add2Rounded': typeof import('~icons/material-symbols/add2-rounded')['default']
    'IconMaterialSymbols:equalRounded': typeof import('~icons/material-symbols/equal-rounded')['default']
    'IconMaterialSymbols:screenRotationRounded': typeof import('~icons/material-symbols/screen-rotation-rounded')['default']
    IconMdiDrag: typeof import('~icons/mdi/drag')['default']
    IconMdiRefresh: typeof import('~icons/mdi/refresh')['default']
    'IconMingcute:inviteLine': typeof import('~icons/mingcute/invite-line')['default']
    'IconSolar:addCircleBold': typeof import('~icons/solar/add-circle-bold')['default']
    'IconSolar:addCircleLinear': typeof import('~icons/solar/add-circle-linear')['default']
    'IconSolar:altArrowDownBold': typeof import('~icons/solar/alt-arrow-down-bold')['default']
    'IconSolar:altArrowDownLinear': typeof import('~icons/solar/alt-arrow-down-linear')['default']
    'IconSolar:altArrowDownLineDuotone': typeof import('~icons/solar/alt-arrow-down-line-duotone')['default']
    'IconSolar:archiveDownLinear': typeof import('~icons/solar/archive-down-linear')['default']
    'IconSolar:bookmarkLinear': typeof import('~icons/solar/bookmark-linear')['default']
    'IconSolar:checkCircleBoldDuotone': typeof import('~icons/solar/check-circle-bold-duotone')['default']
    'IconSolar:clipboardListLinear': typeof import('~icons/solar/clipboard-list-linear')['default']
    'IconSolar:crownLineBoldDuotone': typeof import('~icons/solar/crown-line-bold-duotone')['default']
    'IconSolar:disketteLinear': typeof import('~icons/solar/diskette-linear')['default']
    'IconSolar:documentsLinear': typeof import('~icons/solar/documents-linear')['default']
    'IconSolar:downloadLinear': typeof import('~icons/solar/download-linear')['default']
    'IconSolar:exportLinear': typeof import('~icons/solar/export-linear')['default']
    'IconSolar:eyeLinear': typeof import('~icons/solar/eye-linear')['default']
    'IconSolar:historyBoldDuotone': typeof import('~icons/solar/history-bold-duotone')['default']
    'IconSolar:inboxInBoldDuotone': typeof import('~icons/solar/inbox-in-bold-duotone')['default']
    'IconSolar:magniferLinear': typeof import('~icons/solar/magnifer-linear')['default']
    'IconSolar:pallete2Linear': typeof import('~icons/solar/pallete2-linear')['default']
    'IconSolar:pen2Linear': typeof import('~icons/solar/pen2-linear')['default']
    'IconSolar:plainLinear': typeof import('~icons/solar/plain-linear')['default']
    'IconSolar:refreshLinear': typeof import('~icons/solar/refresh-linear')['default']
    'IconSolar:restartCircleLinear': typeof import('~icons/solar/restart-circle-linear')['default']
    'IconSolar:restartLinear': typeof import('~icons/solar/restart-linear')['default']
    'IconSolar:rocketLinear': typeof import('~icons/solar/rocket-linear')['default']
    'IconSolar:settingsLinear': typeof import('~icons/solar/settings-linear')['default']
    'IconSolar:shieldWarningBoldDuotone': typeof import('~icons/solar/shield-warning-bold-duotone')['default']
    'IconSolar:trashBin2Linear': typeof import('~icons/solar/trash-bin2-linear')['default']
    'IconSolar:trashBinMinimalisticLinear': typeof import('~icons/solar/trash-bin-minimalistic-linear')['default']
    'IconSolar:undoLeftRoundLineDuotone': typeof import('~icons/solar/undo-left-round-line-duotone')['default']
    'IconSolar:undoRightLinear': typeof import('~icons/solar/undo-right-linear')['default']
    'IconSolar:videocameraRecordBoldDuotone': typeof import('~icons/solar/videocamera-record-bold-duotone')['default']
    'IconSolar:videocameraRecordLinear': typeof import('~icons/solar/videocamera-record-linear')['default']
    'IconSvgSpinners:3DotsFade': typeof import('~icons/svg-spinners/3-dots-fade')['default']
    'IconSvgSpinners:pulseMultiple': typeof import('~icons/svg-spinners/pulse-multiple')['default']
    'IconSvgSpinners:ringResize': typeof import('~icons/svg-spinners/ring-resize')['default']
    'IconTabler:alertCircleFilled': typeof import('~icons/tabler/alert-circle-filled')['default']
    'IconTabler:arrowRightToArc': typeof import('~icons/tabler/arrow-right-to-arc')['default']
    'IconTabler:chevronDown': typeof import('~icons/tabler/chevron-down')['default']
    'IconTabler:circleCheckFilled': typeof import('~icons/tabler/circle-check-filled')['default']
    'IconTabler:circleXFilled': typeof import('~icons/tabler/circle-x-filled')['default']
    'IconTabler:currencyDollar': typeof import('~icons/tabler/currency-dollar')['default']
    'IconTabler:faceIdError': typeof import('~icons/tabler/face-id-error')['default']
    'IconTabler:loader2': typeof import('~icons/tabler/loader2')['default']
    'IconTabler:minus': typeof import('~icons/tabler/minus')['default']
    'IconTabler:number': typeof import('~icons/tabler/number')['default']
    'IconTabler:playerPlayFilled': typeof import('~icons/tabler/player-play-filled')['default']
    'IconTabler:progressCheck': typeof import('~icons/tabler/progress-check')['default']
    'IconTabler:refreshAlert': typeof import('~icons/tabler/refresh-alert')['default']
    'IconTabler:search': typeof import('~icons/tabler/search')['default']
    'IconTabler:tags': typeof import('~icons/tabler/tags')['default']
    'IconTabler:trendingDown': typeof import('~icons/tabler/trending-down')['default']
    'IconTabler:trendingUp': typeof import('~icons/tabler/trending-up')['default']
    'IconTabler:upload': typeof import('~icons/tabler/upload')['default']
    'IconTwemoji:flagUnitedStates': typeof import('~icons/twemoji/flag-united-states')['default']
    'IconVscodeIcons:fileTypeExcel2': typeof import('~icons/vscode-icons/file-type-excel2')['default']
    IndicatorCard: typeof import('./../components/custom/indicator-card.vue')['default']
    IndicatorCardVertical: typeof import('./../components/custom/indicator-card-vertical.vue')['default']
    InputPhone: typeof import('./../components/custom/input-phone.vue')['default']
    LangSwitch: typeof import('./../components/common/lang-switch.vue')['default']
    Loading: typeof import('./../components/custom/loading.vue')['default']
    LogoTimeBar: typeof import('./../components/report/logo-time-bar.vue')['default']
    LookForward: typeof import('./../components/custom/look-forward.vue')['default']
    MenuToggler: typeof import('./../components/common/menu-toggler.vue')['default']
    MoreThanSelect: typeof import('./../components/filter/more-than-select.vue')['default']
    NAlert: typeof import('naive-ui')['NAlert']
    NAvatar: typeof import('naive-ui')['NAvatar']
    NBreadcrumb: typeof import('naive-ui')['NBreadcrumb']
    NBreadcrumbItem: typeof import('naive-ui')['NBreadcrumbItem']
    NButton: typeof import('naive-ui')['NButton']
    NButtonGroup: typeof import('naive-ui')['NButtonGroup']
    NCard: typeof import('naive-ui')['NCard']
    NCheckbox: typeof import('naive-ui')['NCheckbox']
    NCheckboxGroup: typeof import('naive-ui')['NCheckboxGroup']
    NCollapse: typeof import('naive-ui')['NCollapse']
    NCollapseItem: typeof import('naive-ui')['NCollapseItem']
    NColorPicker: typeof import('naive-ui')['NColorPicker']
    NDataTable: typeof import('naive-ui')['NDataTable']
    NDatePicker: typeof import('naive-ui')['NDatePicker']
    NDescriptions: typeof import('naive-ui')['NDescriptions']
    NDescriptionsItem: typeof import('naive-ui')['NDescriptionsItem']
    NDialog: typeof import('naive-ui')['NDialog']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NDivider: typeof import('naive-ui')['NDivider']
    NDrawer: typeof import('naive-ui')['NDrawer']
    NDrawerContent: typeof import('naive-ui')['NDrawerContent']
    NDropdown: typeof import('naive-ui')['NDropdown']
    NDynamicInput: typeof import('naive-ui')['NDynamicInput']
    NEllipsis: typeof import('naive-ui')['NEllipsis']
    NEmpty: typeof import('naive-ui')['NEmpty']
    NFlex: typeof import('naive-ui')['NFlex']
    NForm: typeof import('naive-ui')['NForm']
    NFormItem: typeof import('naive-ui')['NFormItem']
    NFormItemGi: typeof import('naive-ui')['NFormItemGi']
    NGi: typeof import('naive-ui')['NGi']
    NGradientText: typeof import('naive-ui')['NGradientText']
    NGrid: typeof import('naive-ui')['NGrid']
    NH1: typeof import('naive-ui')['NH1']
    NH3: typeof import('naive-ui')['NH3']
    NIconWrapper: typeof import('naive-ui')['NIconWrapper']
    NImage: typeof import('naive-ui')['NImage']
    NInput: typeof import('naive-ui')['NInput']
    NInputGroup: typeof import('naive-ui')['NInputGroup']
    NInputGroupLabel: typeof import('naive-ui')['NInputGroupLabel']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NList: typeof import('naive-ui')['NList']
    NListItem: typeof import('naive-ui')['NListItem']
    NLoadingBarProvider: typeof import('naive-ui')['NLoadingBarProvider']
    NMenu: typeof import('naive-ui')['NMenu']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NModal: typeof import('naive-ui')['NModal']
    NNotificationProvider: typeof import('naive-ui')['NNotificationProvider']
    NPagination: typeof import('naive-ui')['NPagination']
    NPopconfirm: typeof import('naive-ui')['NPopconfirm']
    NPopover: typeof import('naive-ui')['NPopover']
    NPopselect: typeof import('naive-ui')['NPopselect']
    NProgress: typeof import('naive-ui')['NProgress']
    NRadio: typeof import('naive-ui')['NRadio']
    NRadioButton: typeof import('naive-ui')['NRadioButton']
    NRadioGroup: typeof import('naive-ui')['NRadioGroup']
    NRate: typeof import('naive-ui')['NRate']
    NScrollbar: typeof import('naive-ui')['NScrollbar']
    NSelect: typeof import('naive-ui')['NSelect']
    NSkeleton: typeof import('naive-ui')['NSkeleton']
    NSlider: typeof import('naive-ui')['NSlider']
    NSpace: typeof import('naive-ui')['NSpace']
    NSpin: typeof import('naive-ui')['NSpin']
    NStatistic: typeof import('naive-ui')['NStatistic']
    NStep: typeof import('naive-ui')['NStep']
    NSteps: typeof import('naive-ui')['NSteps']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NTab: typeof import('naive-ui')['NTab']
    NTable: typeof import('naive-ui')['NTable']
    NTabPane: typeof import('naive-ui')['NTabPane']
    NTabs: typeof import('naive-ui')['NTabs']
    NTag: typeof import('naive-ui')['NTag']
    NText: typeof import('naive-ui')['NText']
    NThing: typeof import('naive-ui')['NThing']
    NTimePicker: typeof import('naive-ui')['NTimePicker']
    NTooltip: typeof import('naive-ui')['NTooltip']
    NTree: typeof import('naive-ui')['NTree']
    NTreeSelect: typeof import('naive-ui')['NTreeSelect']
    NUpload: typeof import('naive-ui')['NUpload']
    NUploadDragger: typeof import('naive-ui')['NUploadDragger']
    NWatermark: typeof import('naive-ui')['NWatermark']
    PinToggler: typeof import('./../components/common/pin-toggler.vue')['default']
    PopoverImg: typeof import('./../components/custom/popover-img.vue')['default']
    RangeSelect: typeof import('./../components/filter/range-select.vue')['default']
    ReloadButton: typeof import('./../components/common/reload-button.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchInput: typeof import('./../components/custom/search-input.vue')['default']
    SelectDesc: typeof import('./../components/custom/select-desc.vue')['default']
    SoybeanAvatar: typeof import('./../components/custom/soybean-avatar.vue')['default']
    SvgIcon: typeof import('./../components/custom/svg-icon.vue')['default']
    SystemLogo: typeof import('./../components/common/system-logo.vue')['default']
    TableColumnSetting: typeof import('./../components/advanced/table-column-setting.vue')['default']
    TableHeaderOperation: typeof import('./../components/advanced/table-header-operation.vue')['default']
    TablerTitleTip: typeof import('./../components/custom/tabler-title-tip.vue')['default']
    TagPopover: typeof import('./../components/custom/tag-popover.vue')['default']
    ThemeSchemaSwitch: typeof import('./../components/common/theme-schema-switch.vue')['default']
    TimeTool: typeof import('./../components/common/time-tool.vue')['default']
    Tip: typeof import('./../components/custom/tip.vue')['default']
    VBarChart: typeof import('./../components/vchart/v-bar-chart.vue')['default']
    VBubbleChart: typeof import('./../components/vchart/v-bubble-chart.vue')['default']
    VDoughnutChart: typeof import('./../components/vchart/v-doughnut-chart.vue')['default']
    Vidstack: typeof import('./../components/video/vidstack.vue')['default']
    VLineAreaChart: typeof import('./../components/vchart/v-line-area-chart.vue')['default']
    VLineChart: typeof import('./../components/vchart/v-line-chart.vue')['default']
    VSparklineChart: typeof import('./../components/vchart/v-sparkline-chart.vue')['default']
    VTreemapChart: typeof import('./../components/vchart/v-treemap-chart.vue')['default']
    WangEditor: typeof import('./../components/common/wang-editor.vue')['default']
    WaveBg: typeof import('./../components/custom/wave-bg.vue')['default']
  }
}
