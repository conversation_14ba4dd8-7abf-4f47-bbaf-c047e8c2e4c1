<script setup lang="ts">
import dayjs from 'dayjs';
import { TimeFormat } from '@/enum';

interface Props {
  months: string[];
}
defineProps<Props>();

const model = defineModel('value', {
  required: true
});
</script>

<template>
  <NButtonGroup>
    <NButton
      v-for="month in months"
      :key="month"
      secondary
      :type="month === model ? 'primary' : 'default'"
      @click="model = month"
    >
      {{ dayjs(month).format(TimeFormat.US_DATE_NO_DAY) }}
    </NButton>
  </NButtonGroup>
</template>

<style scoped></style>
