<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-24 15:01:32
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-10-31 13:10:07
 * @FilePath: \tiksage-frontend\src\views\home\modules\shop-list.vue
 * @Description: shop list
-->
<script setup lang="tsx">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import type { DataTableBaseColumn } from 'naive-ui';
import { NAvatar, NEllipsis, NFlex, NImage, NText } from 'naive-ui';
import { fetchDownoloadSubBrandShopsData, fetchGetShopList } from '@/service/api';
import { useRouterPush } from '@/hooks/common/router';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { downloadFile } from '@/utils/download';
import { LinkToProduct } from '@/utils/tiktok-link';
import { getFallbackImage } from '@/utils/fake-image';
import Tip from '@/components/custom/tip.vue';
import { NumeralFormat } from '@/enum';

interface Props {
  params: Pick<Api.Dashboard.DashboardSearchParams, 'shopIdsArr' | 'startDateStr' | 'endDateStr'>;
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const { routerPushByKey } = useRouterPush();
const onShopSet = () => {
  routerPushByKey('account_center');
};

const [loading, toggleLoading] = useToggle(false);

const columns: DataTableBaseColumn<Api.DashboardJazwares.DashboardShop>[] = [
  // {
  //   key: 'index',
  //   title: '',
  //   fixed: 'left',
  //   width: 50,
  //   render(rowData, rowIndex) {
  //     if (rowData.shopId === 6) {
  //       return <SvgIcon class='text-25px' icon="tabler:chevron-down" />;
  //     } else {
  //       return '';
  //     }
  //   }
  // },
  {
    key: 'shopName',
    title: 'Shop',
    fixed: 'left',
    width: 250,
    render: rowData => (
      <NFlex align="center">
        <NAvatar
          src={`${import.meta.env.VITE_SHOP_AVATAR_URL}${rowData.avatar}`}
          fallbackSrc={getFallbackImage(50, 50)}
        ></NAvatar>
        <NFlex vertical>
          <NEllipsis style={'max-width:180px'}>{rowData.shopName}</NEllipsis>
          <NEllipsis style={'max-width:180px'}>@{rowData.shopCode || '-'}</NEllipsis>
        </NFlex>
      </NFlex>
    )
  },
  {
    key: 'topSellingProductImgList',
    title: 'Top Selling Products',
    fixed: 'left',
    align: 'center',
    width: 200,
    render: rowData => {
      // let topProducts = rowData.topSellingProductImgList.length ? rowData.topSellingProductImgList:
      return (
        <NFlex class="w-100%" justify="center">
          {rowData.topSellingProductImgList.length
            ? rowData.topSellingProductImgList.map(tsp => {
                return (
                  <div
                    class="flex flex-nowrap items-center gap-2 hover:(cursor-pointer text-primary)"
                    onClick={() => handleLinkProduct(tsp.productId)}
                  >
                    <NImage
                      previewDisabled
                      width={50}
                      height={50}
                      src={`${import.meta.env.VITE_PRODUCT_AVATAR_URL}${tsp.productAvatarLocal}`}
                      fallbackSrc={getFallbackImage(50, 50)}
                    ></NImage>
                  </div>
                );
              })
            : '-'}
        </NFlex>
      );
    }
  },
  {
    key: 'gmv',
    title: 'GMV',
    align: 'center',
    width: 110
    // sorter: 'default',
    // defaultSortOrder:'descend'
    // sortOrder: sortKeyMapOrderRef.value.gmv || false
  },
  {
    key: 'liveGmv',
    title: () => (
      <NFlex justify="center">
        <span>LIVE GMV</span>
        <Tip
          contentClass="max-w-400px"
          description="The total amount paid for orders of this product directly from LIVEs, including returns and refunds."
        />
      </NFlex>
    ),
    align: 'center',
    width: 140
    // sorter: 'default',
    // sortOrder: sortKeyMapOrderRef.value.followers || false
  },
  {
    key: 'videoGmv',
    title: () => (
      <NFlex justify="center">
        <span>Video GMV</span>
        <Tip
          contentClass="max-w-400px"
          description="The total amount paid for orders of this product directly from shoppable videos, including returns and refunds."
        />
      </NFlex>
    ),
    align: 'center',
    width: 140
    // sorter: 'default',
    // sortOrder: sortKeyMapOrderRef.value.visitors || false
  },
  // {
  //   key: 'buyers',
  //   title: 'Buyers',
  //   align: 'center',
  //   width: 110
  //   // sorter: 'default',
  //   // sortOrder: sortKeyMapOrderRef.value.buyers || false
  // },
  {
    key: 'productCardGmv',
    title: () => (
      <NFlex justify="center">
        <span>Product Card GMV</span>
        <Tip
          contentClass="max-w-400px"
          description="The total amount paid for orders of this product directly from its product card, including returns and refunds."
        />
      </NFlex>
    ),
    align: 'center',
    width: 180
    // sorter: 'default',
    // sortOrder: sortKeyMapOrderRef.value.videos || false
  },
  {
    key: 'shopTabGmv',
    title: () => (
      <NFlex justify="center">
        <span>Shop Tab GMV</span>
        <Tip contentClass="max-w-400px" description="The number of units sold directly via Shop Tab." />
      </NFlex>
    ),
    align: 'center',
    width: 160
    // sorter: 'default',
    // sortOrder: sortKeyMapOrderRef.value.videos || false
  },
  {
    key: 'unitsSold',
    title: 'Units Sold',
    align: 'center',
    width: 110
    // sorter: 'default',
    // sortOrder: sortKeyMapOrderRef.value.lives || false
  },
  {
    key: 'orderCnt',
    title: 'Orders',
    align: 'center',
    width: 110
    // sorter: 'default',
    // sortOrder: sortKeyMapOrderRef.value.lives || false
  }
];

function handleLinkProduct(id: any) {
  LinkToProduct(id);
}

const tableData = ref<Api.DashboardJazwares.DashboardShop[]>([]);

async function getTableData(params: Api.DashboardJazwares.BaseSearchParams) {
  toggleLoading(true);
  const { data, error } = await fetchGetShopList(params);
  if (!error) {
    tableData.value = data;
  }
  toggleLoading(false);
}

type DollarKey = keyof Pick<
  Api.DashboardJazwares.DashboardShop,
  'gmv' | 'liveGmv' | 'videoGmv' | 'productCardGmv' | 'shopTabGmv'
>;

const dollarKeys: DollarKey[] = ['gmv', 'liveGmv', 'videoGmv', 'productCardGmv', 'shopTabGmv'];

const renderCell = (value: any, _rowData: any, column: any) => {
  const isDollar = dollarKeys.includes(column.key);
  return numberFormat(value, isDollar ? NumeralFormat.Dollar : NumeralFormat.Number);
};

function rowClassName(rowData: Api.DashboardJazwares.DashboardShop) {
  if (rowData.shopId === props.params.shopIdsArr?.[0]) {
    return 'highlight';
  }
  return '';
}

const tableRef = ref();

async function downloadCsv() {
  const { data, error } = await fetchDownoloadSubBrandShopsData(props.params);
  if (error) return;

  downloadFile(data, 'xlsx', 'Shop Performance');
}

getTableData(props.params);
</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <template #header>
      <NText>Shop Performance</NText>
    </template>
    <template #header-extra>
      <NButtonGroup>
        <ButtonIcon icon="solar:settings-linear" tooltip-content="Set" tooltip-placement="top" @click="onShopSet" />
        <ButtonIcon
          icon="solar:download-linear"
          tooltip-content="Export"
          tooltip-placement="top"
          @click="downloadCsv"
        />
      </NButtonGroup>
    </template>
    <NDataTable
      ref="tableRef"
      :bordered="false"
      size="small"
      :row-class-name="rowClassName"
      :loading="loading"
      remote
      :render-cell="renderCell"
      :columns="columns"
      :data="tableData"
      :row-key="row => row.id"
      scroll-x="1400"
    />
  </NCard>
</template>

<style scoped>
:deep(.highlight td) {
  background-color: #faf4fe !important;
  font-weight: bold !important;
  color: rgb(var(--nprogress-color)) !important;
}
</style>
