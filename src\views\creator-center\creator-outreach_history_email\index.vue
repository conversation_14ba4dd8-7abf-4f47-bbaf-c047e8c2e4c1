<script setup lang="tsx">
import { onMounted, onUnmounted, ref } from 'vue';
import { useIntervalFn, useToggle } from '@vueuse/core';
import { NTag } from 'naive-ui';
import type { RouteKey } from '@elegant-router/types';
import { fetchDeleteEmailTask, fetchGetTaskHistoryList, fetchPauseEmailTask, fetchStartEmailTask } from '@/service/api';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { useRouterPush } from '@/hooks/common/router';
import { useTable } from '@/hooks/common/table';
import { dateFormat } from '@/utils/date';
import ButtonIcon from '@/components/custom/button-icon.vue';
import ButtonConfirm from '@/components/custom/button-confirm.vue';
import TaskDetailDrawer from './modules/task-detail-drawer.vue';
import TaskSearch from './modules/task-search.vue';

const { getDictionaryByCodeType } = useDictionaryStore();

const { routerPushByKey } = useRouterPush();

function handleLinkTo(key: RouteKey) {
  routerPushByKey(key);
}

const statusOptions = ref<Api.Dictionary.DictionaryItem<number>[]>([]);

const { data, columns, pagination, getData, loading, searchParams } = useTable({
  immediate: false,
  apiFn: fetchGetTaskHistoryList,
  apiParams: {
    current: 1,
    size: 10
  },
  columns: () => [
    {
      key: 'taskName',
      title: 'TaskName',
      width: 200,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width:400px'
        }
      }
    },
    {
      key: 'senderEmail',
      title: 'Sender Email',
      align: 'center',
      width: 200,
      ellipsis: {
        tooltip: true
      }
    },
    {
      key: 'createTime',
      title: 'Start Time',
      align: 'center',
      width: 300,
      render(rowData) {
        return dateFormat(rowData.createTime);
      }
    },
    {
      key: 'contactType',
      title: 'Contact Type',
      align: 'center',
      render() {
        return (
          <NTag size="small" bordered={false}>
            Email
          </NTag>
        );
      }
    },
    {
      key: 'status',
      title: 'Status',
      align: 'center',
      width: 150,
      render(rowData) {
        const attrs = statusOptions.value.find(v => v.code === rowData.status);
        if (!attrs) return '-';
        const { name, description } = attrs;
        const desc = JSON.parse(description);

        return (
          <NTag size="small" bordered={false} type={desc.buttonType as NaiveUI.ThemeColor}>
            {name}
          </NTag>
        );
      }
    },
    {
      key: 'totalCount',
      title: 'Recipients',
      align: 'center'
    },
    {
      key: 'operate',
      width: 100,
      render(rowData) {
        let attrs = statusOptions.value.find(v => v.code === rowData.status)?.description;
        attrs &&= JSON.parse(attrs);
        const canView = attrs.buttonType !== 'primary' || false;

        return (
          <div class="flex items-center justify-end gap-8px">
            <ButtonIcon
              text
              quaternary={false}
              icon="solar:eye-linear"
              disabled={!canView}
              onClick={() => handleOpenDetail(rowData.id)}
            />
            {attrs ? (
              <ButtonConfirm
                icon={attrs.taskButtonProps.icon}
                confirmText={attrs.taskButtonProps.confirmText}
                buttonProps={attrs.taskButtonProps.buttonProps}
                onPositiveClick={() => handlePlayOrPause(rowData.id, attrs.taskButtonProps.icon)}
              />
            ) : (
              <div class="w-20px" />
            )}
            <ButtonConfirm
              icon="solar:trash-bin-2-linear"
              confirmText="Are you sure you want to delete?"
              buttonProps={{ text: true, size: 'large', type: 'error' }}
              onPositiveClick={() => handleDelete(rowData.id)}
            />
          </div>
        );
      }
    }
  ]
});

async function handlePlayOrPause(id: number, icon: string) {
  if (icon === 'tabler:player-play-filled') {
    const { error } = await fetchStartEmailTask(id);
    if (error) return;
  } else if (icon === 'tabler:player-pause-filled') {
    const { error } = await fetchPauseEmailTask(id);
    if (error) return;
  }
  getData();
}

async function handleDelete(id: number) {
  const { error } = await fetchDeleteEmailTask(id);
  if (error) return;
  window.$message?.success('Delete Successfully.');
  getData();
}

const [visible, toggleVisible] = useToggle(false);
const taskId = ref<number | null>(null);

function handleOpenDetail(id: number) {
  taskId.value = null;
  taskId.value = id;
  toggleVisible(true);
}

function handleSearch() {
  searchParams.current = 1;
  searchParams.size = 10;
  getData();
}

async function initStatusOptions() {
  const taskStatusOpts = await getDictionaryByCodeType<number>('email_task_status');
  if (!taskStatusOpts) return;
  statusOptions.value = taskStatusOpts;
}

const taskSenderStatusOptions = ref<Api.Dictionary.DictionaryItem[]>([]);

async function initTaskSenderStatusOptions() {
  const taskDetailStatusOpts = await getDictionaryByCodeType<number>('email_task_detail_status');
  if (!taskDetailStatusOpts) return;
  taskSenderStatusOptions.value = taskDetailStatusOpts;
}

function handleRefresh() {
  getData();
}

const { pause, resume } = useIntervalFn(
  () => {
    getData();
  },
  1000 * 60 * 5
);

onMounted(() => {
  resume();
});

onUnmounted(() => {
  pause();
});

function init() {
  initStatusOptions();
  initTaskSenderStatusOptions();
}

init();
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="card-wrapper" :bordered="false" title="Creator Outreach">
      <template #header-extra>
        <div class="flex justify-end gap-4">
          <NButton strong secondary @click="handleLinkTo('creator-center_creator-outreach_create_email')">
            <template #icon>
              <icon-solar:add-circle-linear />
            </template>
            New Task
          </NButton>
          <NButton strong secondary @click="handleLinkTo('creator-center_creator-outreach_settings_email')">
            <template #icon>
              <icon-solar:settings-linear />
            </template>
            Settings
          </NButton>
          <ButtonBack back-key="creator-center_creator-outreach" />
        </div>
      </template>
      <TaskSearch v-model:value="searchParams" :status-options="statusOptions" @change="handleSearch" />
    </NCard>
    <NCard class="min-h-400px flex-1 card-wrapper" :bordered="false" title="Task List">
      <template #header-extra>
        <div class="flex justify-end gap-4">
          <ButtonRefresh :callback="handleRefresh" />
        </div>
      </template>
      <NDataTable
        class="h-full"
        remote
        flex-height
        :loading="loading"
        :bordered="false"
        :columns="columns"
        :data="data"
        :pagination="pagination"
      >
        <template #empty>
          <NEmpty description="Start by importing or selecting creators to populate the list." />
        </template>
      </NDataTable>
      <TaskDetailDrawer
        v-model:visible="visible"
        :task-id="taskId"
        :task-status-options="statusOptions"
        :task-sender-status-options="taskSenderStatusOptions"
      />
    </NCard>
  </NFlex>
</template>

<style scoped></style>
