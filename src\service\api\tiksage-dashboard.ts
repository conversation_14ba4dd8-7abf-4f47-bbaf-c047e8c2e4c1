import { request } from '../request';

export function fetchGetShopCommissionList(params: Api.TikSageDashboard.ShopCommissionSearchParams) {
  return request<Api.TikSageDashboard.ShopCommissionListResponse>({
    url: '/partner/getShopList',
    method: 'get',
    params
  });
}

export function fetchUpdateCommission(data: Api.TikSageDashboard.ShopCommissionUpdateParams) {
  return request({
    url: '/partner/setCommission',
    method: 'post',
    data
  });
}

export function fetchGetProductTopListOnTikSageDashboard(params: Api.TikSageDashboard.TopListParams) {
  return request<Api.TikSageDashboard.ProductTopListResponse>({
    url: '/partner/getProductsTopData',
    method: 'get',
    params
  });
}

export function fetchGetSellerTopListOnTikSageDashboard(params: Api.TikSageDashboard.TopListParams) {
  return request<Api.TikSageDashboard.SellerListResponse>({
    url: '/partner/getShopsTopData',
    method: 'get',
    params
  });
}

export function fetchGetOverviewDataOnTikSageDashboard(params: Api.TikSageDashboard.DateParams) {
  return request<Api.TikSageDashboard.OverviewDataResponse>({
    url: '/partner/getOverviewData',
    method: 'get',
    params
  });
}

export function fetchGetMonthlyGmvAndEstOnTikSageDashboard() {
  return request<Api.TikSageDashboard.MonthlyDataResponse>({
    url: '/partner/getOverviewMonthlyData',
    method: 'get'
  });
}

export function fetchGetGmvTrendOnTikSageDashboard(params: Api.TikSageDashboard.DateParams) {
  return request<Api.TikSageDashboard.GmvTrendResponse>({
    url: '/partner/getOverviewDailyData',
    method: 'get',
    params
  });
}

export function fetchGetDateRangeOnTikSageDashboard() {
  return request<Api.TikSageDashboard.DateRangeResponse>({
    url: '/partner/getRangeDate',
    method: 'get'
  });
}

export function fetchGetLastMonthEstCommission(shopId: number, date: string) {
  return request<Api.TikSageDashboard.LastMonthEstCommissionResponse>({
    url: '/partner/getLastMonthEstCommission',
    method: 'get',
    params: { shopId, date }
  });
}

export function fetchUpdatePartnershipStatus(shopId: number, isEnabled: number) {
  return request({
    url: '/partner/updateIsEnabled',
    method: 'put',
    params: { shopId, isEnabled }
  });
}

export function fetchGetSellersContribution(params: Api.TikSageDashboard.DateParams) {
  return request<Api.TikSageDashboard.SellersContributionResponse>({
    url: '/partner/getOverviewPieData',
    method: 'get',
    params
  });
}
