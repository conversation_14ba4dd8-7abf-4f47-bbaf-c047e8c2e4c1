<script setup lang="ts">
import { computed, nextTick, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import dayjs from 'dayjs';
import { delay, last } from 'lodash-es';
import { fetchGetCategoryList, fetchGetMonthByCategory, fetchGetTopGmvShopList } from '@/service/api/category-leaders';
import { useAuthStore } from '@/store/modules/auth';
import { downloadByUrl, downloadReport } from '@/utils/download';
import { TimeFormat } from '@/enum';
import TopGmvCard from './modules/top-gmv-card.vue';
const downloadOptions = [
  {
    label: 'Export as PNG',
    key: 'png'
  },
  {
    label: 'Export as PDF',
    key: 'pdf'
  },
  {
    label: 'Export the complete report as PDF',
    key: 'all'
  }
];

const authStore = useAuthStore();

const [isExport, toggleIsExport] = useToggle(false);

const [loading, toggleLoading] = useToggle(false);

const [isInit, toggleIsInit] = useToggle(false);

const monthOptions = ref<CommonType.Option<string>[]>([]);

const currentMonth = ref<string>('');

const selectedValue = ref<string>('');

const options = ref<CommonType.Option<string>[]>([]);

const topGmvShopList = ref<Api.CategoryLeaders.TopGmvShopListResponse[]>([]);

const updateTime = computed(() => {
  if (topGmvShopList.value.length > 0) {
    return dayjs(topGmvShopList.value[0].updateTime).tz('Etc/Gmt+8').format(TimeFormat.US_TIME_24);
  }
  return '';
});

async function getData(category: string, month: string) {
  const { data: topGmvShopListData, error: topGmvShopListError } = await fetchGetTopGmvShopList(category, month);
  if (!topGmvShopListError) {
    topGmvShopList.value = topGmvShopListData;
  }
}

async function getMonths(category: string) {
  const { data: monthData, error: monthError } = await fetchGetMonthByCategory(category);
  if (!monthError) {
    monthOptions.value = monthData.map(item => ({
      label: item,
      value: item
    }));

    currentMonth.value = '';

    await nextTick();
    currentMonth.value = last(monthData) || '';
  } else {
    monthOptions.value = [];
    currentMonth.value = '';
  }
}

async function getCategoryOptions() {
  toggleLoading(true);
  const { data, error } = await fetchGetCategoryList();
  if (!error) {
    options.value = data.map(item => ({
      label: item,
      value: item
    }));

    const defaultCategory = authStore.userInfo.categoryLeadersDefault;
    if (defaultCategory) {
      const defaultCategoryIndex = data.findIndex(item => item === defaultCategory);
      if (defaultCategoryIndex !== -1) {
        selectedValue.value = defaultCategory;
      } else {
        selectedValue.value = data[0];
      }
    } else {
      selectedValue.value = data[0];
    }
    await getMonths(selectedValue.value);
    await getData(selectedValue.value, currentMonth.value);
    toggleIsInit(true);
    toggleLoading(false);
  }
}

async function handleDownload(type: string) {
  toggleIsExport(true);
  if (type === 'all') {
    const url =
      `${import.meta.env.VITE_SHOP_LEADER_REPORT_URL}category-leaders-report_${selectedValue.value}_${currentMonth.value}.pdf`.replace(
        /\s/g,
        ''
      );
    downloadByUrl(url);
  } else {
    await downloadReport(type as 'pdf' | 'png', 'report-container');
  }
  toggleIsExport(false);
}

getCategoryOptions();

watch(
  () => selectedValue.value,
  newVal => {
    if (isInit.value) {
      getMonths(newVal);
    }
  }
);

watch(
  () => currentMonth.value,
  async newVal => {
    if (isInit.value) {
      if (newVal) {
        toggleLoading(true);
        await getData(selectedValue.value, newVal);
        delay(() => {
          toggleLoading(false);
        }, 500);
      }
    }
  }
);
</script>

<template>
  <NFlex id="report-container" vertical :size="16">
    <NCard class="card-wrapper" :bordered="false" title="Category Leaders Report">
      <template #header-extra>
        <div class="flex gap-16px nowrap-hidden">
          <div class="flex-y-center gap-2">
            <NButton type="primary" secondary>Month</NButton>
            <NSelect v-model:value="currentMonth" :options="monthOptions" :consistent-menu-width="false"></NSelect>
          </div>
          <NSelect
            v-if="options.length > 0"
            v-model:value="selectedValue"
            filterable
            placeholder="Category"
            :options="options"
          >
            <template #arrow>
              <icon-tabler:search />
            </template>
          </NSelect>
          <NDropdown trigger="hover" placement="bottom-end" :options="downloadOptions" @select="handleDownload">
            <NButton data-html2canvas-ignore quaternary>
              <template #icon>
                <SvgIcon icon="solar:download-linear" />
              </template>
            </NButton>
          </NDropdown>
        </div>
      </template>
    </NCard>
    <Loading v-if="loading" class="m-auto" />
    <NEmpty v-else-if="topGmvShopList.length === 0" class="m-auto" description="No data available at the moment" />
    <template v-else>
      <TopGmvCard :data="topGmvShopList" :is-export="isExport" />
      <LogoTimeBar label="Updated at" :content="updateTime" />
    </template>
  </NFlex>
</template>

<style scoped></style>
