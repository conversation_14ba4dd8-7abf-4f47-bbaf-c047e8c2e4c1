<script setup lang="tsx">
import dayjs from 'dayjs';
import { fetchUserPageViewLog } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { TimeFormat } from '@/enum';

interface Props {
  dateRange: string[] | null;
}
const props = defineProps<Props>();

const { data, loading, columns, pagination } = useTable({
  apiFn: fetchUserPageViewLog,
  apiParams: {
    current: 1,
    size: 10,
    id: undefined,
    startTime: props.dateRange?.[0],
    endTime: props.dateRange?.[1]
  },
  columns() {
    return [
      {
        key: 'userName',
        title: 'Account Name'
      },
      {
        key: 'pageName',
        title: 'Page Name'
      },
      {
        key: 'timestamp',
        title: 'Login Timestamps',
        render(rowData) {
          const date = dayjs(rowData.timestamp);
          return (
            <div class="flex-col gap-16px text-xs">
              <span>{date.tz('Etc/GMT+8').format(TimeFormat.US_TIME_24)}</span>
              <span class="text-coolgray">{date.tz('Etc/GMT-8').format(TimeFormat.US_TIME_24)}</span>
            </div>
          );
        }
      },
      {
        key: 'ip',
        title: 'IP'
      },
      {
        key: 'address',
        title: 'Address'
      }
    ];
  }
});
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" title="User Page Location">
    <NDataTable
      :bordered="false"
      remote
      :columns="columns"
      :data="data"
      :pagination="pagination"
      :loading="loading"
    ></NDataTable>
  </NCard>
</template>

<style scoped></style>
