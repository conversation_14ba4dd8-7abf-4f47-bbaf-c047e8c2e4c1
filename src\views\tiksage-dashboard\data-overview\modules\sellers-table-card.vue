<script setup lang="tsx">
import { computed, watch } from 'vue';
import type { DataTableCreateSummary } from 'naive-ui';
import { NAvatar, NEllipsis } from 'naive-ui';
import dayjs from 'dayjs';
import { fetchGetSellerTopListOnTikSageDashboard } from '@/service/api';
import { useTable } from '@/hooks/common/table';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { useRouterPush } from '@/hooks/common/router';
import { getFallbackImage } from '@/utils/fake-image';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { NumeralFormat } from '@/enum';
import CycleRatio from '@/components/custom/cycle-ratio.vue';
import { useOverviewData } from './shared';
import SellerMiniLineChart from './seller-mini-line-chart.vue';

const { VITE_SHOP_AVATAR_URL } = import.meta.env;

interface Props {
  dateRange: string[] | undefined;
}
const props = defineProps<Props>();

const isYear = computed(() => {
  if (!props.dateRange) return false;
  const [startDate, endDate] = props.dateRange;
  const start = dayjs(startDate);
  const end = dayjs(endDate);

  return end.diff(start, 'month', true) >= 1;
});

const { numberFormat } = useNumberFormat();

function getMoneyNumber(num: number) {
  return numberFormat(num, NumeralFormat.Real_Dollar);
}

function getPercent(value: number, prevValue: number) {
  return numberFormat((value - prevValue) / prevValue, NumeralFormat.PlusPercent);
}

const { data, columns, pagination, loading, getData, updateSearchParams } = useTable({
  immediate: false,
  apiFn: fetchGetSellerTopListOnTikSageDashboard,
  apiParams: {
    current: 1,
    size: 10
  },
  columns() {
    return [
      {
        key: 'index',
        title: 'Rank',
        align: 'center',
        width: 60,
        fixed: 'left',
        render(rowData) {
          const iconClass = {
            'text-3xl': true,
            'text-yellow-500': rowData.index === 1,
            'text-gray-400': rowData.index === 2,
            'text-amber-700': rowData.index === 3
          };

          return (
            <div class="flex-center">
              {rowData.index <= 3 ? (
                <SvgIcon icon="solar:crown-line-bold-duotone" class={iconClass} />
              ) : (
                <span class="text-base">{rowData.index}</span>
              )}
            </div>
          );
        }
      },
      {
        key: 'shopName',
        title: 'Shop',
        width: 200,
        fixed: 'left',
        render(rowData) {
          const avatarUrl = VITE_SHOP_AVATAR_URL + rowData.shopAvatarLocal;
          return (
            <div
              class={{
                'flex items-center gap-4': true,
                'hover:(text-primary cursor-pointer underline)': Boolean(rowData.dashboardRoute)
              }}
              onClick={() => handleLinkDashboard(rowData.dashboardRoute, rowData.shopId)}
            >
              <NAvatar
                class="flex-shrink-0"
                size="large"
                round
                src={avatarUrl}
                fallbackSrc={getFallbackImage(60, 60)}
              />
              <NEllipsis lineClamp={2}>{rowData.shopName}</NEllipsis>
            </div>
          );
        }
      },
      {
        key: 'gmvTotal',
        title: 'GMV',
        align: 'center',
        width: 180,
        defaultSortOrder: 'descend',
        sorter: true,
        render(rowData) {
          const percent = getPercent(rowData.gmvTotal, rowData.perviousGmvTotal);
          return (
            <div class="flex-col-center">
              <span>{getMoneyNumber(rowData.gmvTotal)}</span>
              <CycleRatio text percent={percent} size="small" />
            </div>
          );
        }
      },
      {
        key: 'shopOverviewDailyData',
        title: 'GMV Trend',
        align: 'center',
        width: 150,
        render(rowData) {
          return <SellerMiniLineChart data={rowData.shopOverviewDailyData} isYear={isYear.value} />;
        }
      },
      {
        key: 'gmvRate',
        title: 'GMV Distribution',
        align: 'center',
        width: 150,
        render(rowData) {
          const str = rowData.gmvRate ? numberFormat(rowData.gmvRate, NumeralFormat.Percent) : '-';
          return str;
        }
      },
      {
        key: 'monthlyEstCommission',
        title: 'Monthly Est. Commission',
        align: 'center',
        width: 220,
        sorter: true,
        render(rowData) {
          const str = rowData.monthlyEstCommission ? getMoneyNumber(rowData.monthlyEstCommission) : '-';
          return <span class="text-primary font-bold">{str}</span>;
        }
      },
      {
        key: 'estCommission',
        title: 'Current Est. Commission',
        align: 'center',
        width: 220,
        sorter: true,
        render(rowData) {
          const str = rowData.estCommission ? getMoneyNumber(rowData.estCommission) : '-';
          return <span class="text-primary font-bold">{str}</span>;
        }
      },
      {
        key: 'unitSales',
        title: 'Units Sold',
        align: 'center',
        width: 180,
        render(rowData) {
          // const percent = getPercent(rowData.unitSales, rowData.perviousUnitSales);
          return (
            <div class="flex-col-center">
              <span>{numberFormat(rowData.unitSales, NumeralFormat.Real_Number)}</span>
              {/* <CycleRatio text percent={percent} size="small" /> */}
            </div>
          );
        }
      },
      {
        key: 'gmvVideo',
        title: 'Video GMV',
        align: 'center',
        width: 180,
        render(rowData) {
          // const percent = getPercent(rowData.gmvVideo, rowData.perviousGmvVideo);
          return (
            <div class="flex-col-center">
              <span>{getMoneyNumber(rowData.gmvVideo)}</span>
              {/* <CycleRatio text percent={percent} size="small" /> */}
            </div>
          );
        }
      },
      {
        key: 'gmvLive',
        title: 'LIVE GMV',
        align: 'center',
        width: 180,
        render(rowData) {
          // const percent = getPercent(rowData.gmvLive, rowData.perviousGmvLive);
          return (
            <div class="flex-col-center">
              <span>{getMoneyNumber(rowData.gmvLive)}</span>
              {/* <CycleRatio text percent={percent} size="small" /> */}
            </div>
          );
        }
      },
      {
        key: 'gmvProductCard',
        title: 'Product Card GMV',
        align: 'center',
        width: 180,
        render(rowData) {
          // const percent = getPercent(rowData.gmvProductCard, rowData.perviousGmvProductCard);
          return (
            <div class="flex-col-center">
              <span>{getMoneyNumber(rowData.gmvProductCard)}</span>
              {/* <CycleRatio text percent={percent} size="small" /> */}
            </div>
          );
        }
      },
      {
        key: 'gmvAffiliate',
        title: 'Affiliate GMV',
        align: 'center',
        width: 180,
        render(rowData) {
          // const percent = getPercent(rowData.gmvAffiliate, rowData.perviousGmvAffiliate);
          return (
            <div class="flex-col-center">
              <span>{getMoneyNumber(rowData.gmvAffiliate)}</span>
              {/* <CycleRatio text percent={percent} size="small" /> */}
            </div>
          );
        }
      }
    ];
  }
});

const { realData } = useOverviewData();

const summary: DataTableCreateSummary = () => {
  return {
    shopName: {
      value: <div class="font-bold">Total {realData.value?.sellerCount || 0} Shops</div>
    },
    gmvTotal: {
      value: <div class="font-bold">{getMoneyNumber(realData.value?.currentData.gmvTotal || 0)}</div>
    },
    gmvRate: {
      value: <div class="font-bold">-</div>
    },
    monthlyEstCommission: {
      value: <div class="font-bold">{getMoneyNumber(realData.value?.currentData.monthlyEstCommission || 0)}</div>
    },
    estCommission: {
      value: <div class="font-bold">{getMoneyNumber(realData.value?.currentData.estCommission || 0)}</div>
    },
    unitSales: {
      value: (
        <div class="font-bold">
          {numberFormat(realData.value?.currentData.unitSales || 0, NumeralFormat.Real_Number)}
        </div>
      )
    },
    gmvVideo: {
      value: <div class="font-bold">{getMoneyNumber(realData.value?.currentData.gmvVideo || 0)}</div>
    },
    gmvLive: {
      value: <div class="font-bold">{getMoneyNumber(realData.value?.currentData.gmvLive || 0)}</div>
    },
    gmvProductCard: {
      value: <div class="font-bold">{getMoneyNumber(realData.value?.currentData.gmvProductCard || 0)}</div>
    },
    gmvAffiliate: {
      value: <div class="font-bold">{getMoneyNumber(realData.value?.currentData.gmvAffiliate || 0)}</div>
    }
  };
};

// function getPercent(value: number, prevValue: number) {
//   return numberFormat(divide(value - prevValue, prevValue), NumeralFormat.PlusPercent);
// }

function handleSearchShopName(value: string) {
  updateSearchParams({ shopName: value, size: 10, current: 1 });
  getData();
}

function handleSorter(order: { columnKey: string | number | null; order: 'ascend' | 'descend' | false }) {
  const { columnKey, order: orderType } = order;
  if (!columnKey) return;
  if (orderType === false) {
    updateSearchParams({
      sortField: undefined,
      reverseFlag: undefined
    });
  } else {
    updateSearchParams({
      sortField: columnKey as string,
      reverseFlag: orderType !== 'ascend'
    });
  }
  getData();
}

const { routerPushByKey } = useRouterPush();
function handleLinkDashboard(routeKey: string, shopId: number) {
  routerPushByKey(routeKey as any, {
    query: {
      shopId: `${shopId}`
    }
  });
}

watch(
  () => props.dateRange,
  newVal => {
    if (!newVal) return;
    const [startDateStr, endDateStr, perviousStartDateStr, perviousEndDateStr] = newVal;
    updateSearchParams({
      startDateStr,
      endDateStr,
      perviousStartDateStr,
      perviousEndDateStr
    });
    getData();
  },
  {
    immediate: true
  }
);
</script>

<template>
  <NCard class="card-wrapper" :bordered="false" title="Your Sellers">
    <SearchInput class="mb-4" placeholder="Search Shop" @change="handleSearchShopName" />
    <NDataTable
      remote
      :scroll-x="2080"
      :loading="loading"
      :bordered="false"
      :columns="columns"
      :data="data"
      :pagination="pagination"
      :summary="summary"
      @update:sorter="handleSorter"
    />
  </NCard>
</template>

<style scoped lang="scss">
.total {
  @apply font-bold;
}
</style>
