<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-06-24 16:47:13
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-22 14:56:45
 * @FilePath: \soybean-admin\src\views\sales-analytics\[shopId].vue
 * @Description: sales-analytics page
-->
<script setup lang="ts">
import { reactive, ref } from 'vue';
import CreatorTable from './modules/creator-table.vue';
import ProductTable from './modules/product-table.vue';
import TopCard from './modules/top-card.vue';

const selectValue = ref('gmv');
const selectOptions = reactive([{ label: 'GMV', value: 'gmv' }]);
const pieOptions = reactive({
  chart: {
    type: 'donut'
  },
  responsive: [
    {
      breakpoint: 480,
      options: {
        chart: {
          width: 200
        },
        dataLabels: {
          enable: true
        },
        legend: {
          position: 'bottom'
        }
      }
    }
  ],
  labels: ['Video', 'Live', 'Product', 'Multi-Content']
});

const series = reactive([49.48, 4.77, 45.75, 0.0]);

const tabValue = ref('Video');
const handleChartClick = (event: any) => {
  const targetText = event.target?.textContent;
  if (['Video', 'Live', 'Product'].includes(targetText)) {
    tabValue.value = targetText;
  }
};
</script>

<template>
  <NSpace vertical :size="16">
    <!-- <HeaderBanner title="Sales Analytics" /> -->
    <NGrid :x-gap="16" :y-gap="16" responsive="screen" item-responsive>
      <NGi span="24 s:24 m:12 l:12">
        <NCard title="Breakdown By Channel" :bordered="false" class="card-wrapper">
          <template #header-extra>
            <NSelect v-model:value="selectValue" :options="selectOptions" />
          </template>
          <Apexchart
            type="donut"
            :options="pieOptions"
            height="330"
            :series="series"
            @click="handleChartClick"
          ></Apexchart>
        </NCard>
      </NGi>
      <NGi span="24 s:24 m:12 l:12">
        <TopCard v-model:tab-value="tabValue" />
      </NGi>
      <NGi span="24">
        <ProductTable />
      </NGi>
      <NGi span="24">
        <CreatorTable />
      </NGi>
    </NGrid>
  </NSpace>
</template>

<style scoped></style>
