import { request } from '../request';

export function fetchGetWeeklyReportDataByOperator(params: Api.WeeklyReport.WeeklyReportDataParams) {
  return request<Api.WeeklyReport.WeeklyReportData>({
    url: '/weeklyReport/getShopWeeklyReport',
    method: 'get',
    params
  });
}

export function fetchGetWeeklyReportDataByViewer(id: number) {
  return request<Api.WeeklyReport.WeeklyReportData>({
    url: '/weeklyReport/queryWeeklyReport',
    method: 'get',
    params: { id }
  });
}

export function fetchGetWeeklyReportDateList(shopId: number) {
  return request<Api.WeeklyReport.WeeklyReportDate[]>({
    url: '/weeklyReport/queryWeeklyReportList',
    method: 'get',
    params: { shopId }
  });
}

export function fetchReleaseWeeklyReport(id: number) {
  return request({
    url: '/weeklyReport/releaseWeeklyReport',
    method: 'post',
    params: { id }
  });
}

export function fetchRetractWeeklyReport(id: number) {
  return request({
    url: '/weeklyReport/withdrawWeeklyReport',
    method: 'post',
    params: { id }
  });
}

export function fetchExportWeeklyReport(id: number) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/weeklyReport/exportWeeklyReportPdf',
    method: 'get',
    params: { id }
  });
}

export function fetchRebuildWeeklyReport(params: Api.WeeklyReport.WeeklyReportDataParams) {
  return request<Api.WeeklyReport.WeeklyReportData>({
    url: '/weeklyReport/generateWeeklyReports',
    method: 'get',
    params
  });
}

export function fetchUpdatePerformanceByOperator(data: Api.WeeklyReport.WeeklyReportData) {
  return request({
    url: '/weeklyReport/createOrUpdatePerformanceWeeklyReport',
    method: 'post',
    data
  });
}

export function fetchUpdateAffiliateByOperator(data: Api.WeeklyReport.WeeklyReportData) {
  return request({
    url: '/weeklyReport/createOrUpdateAffiliateWeeklyReport',
    method: 'post',
    data
  });
}

export function fetchUpdateAdsByOperator(data: Api.WeeklyReport.WeeklyReportData) {
  return request({
    url: '/weeklyReport/createOrUpdateAdsWeeklyReport',
    method: 'post',
    data
  });
}

export function fetchUpdateCampaignsByOperator(data: Api.WeeklyReport.WeeklyReportData) {
  return request({
    url: '/weeklyReport/createOrUpdateCampaignsWeeklyReport',
    method: 'post',
    data
  });
}

export function fetchGetCampaignsOptionsByWeeklyReport(shopId: number) {
  return request<Api.WeeklyReport.CampaignsOptions>({
    url: '/ads/listShopAdsCampaigns',
    method: 'get',
    params: { shopId }
  });
}

export function fetchGetWeeklyReportByCampaignId(data: Api.WeeklyReport.WeeklyReportByCampaignIdParams) {
  return request<Api.WeeklyReport.WeeklyReportByCampaignIdResponse>({
    url: '/ads/getShopAdsTotalDataByCampaigns',
    method: 'post',
    data
  });
}

export function fetchSaveCampaignTemplate(data: Api.WeeklyReport.CampaignTemplateParams) {
  return request({
    url: '/weeklyReport/saveCampaignTemplate',
    method: 'post',
    data
  });
}

export function fetchGetCampaignTemplateList() {
  return request<Api.WeeklyReport.CampaignList>({
    url: '/weeklyReport/queryCampaignTemplateList',
    method: 'get'
  });
}

export function fetchGetCampaignTemplateDetail(templateId: number) {
  return request<Api.WeeklyReport.CampaignTemplate[]>({
    url: '/weeklyReport/queryCampaignTemplateDetail',
    method: 'get',
    params: { templateId }
  });
}

export function fetchDeleteCampaignTemplate(templateId: number) {
  return request({
    url: '/weeklyReport/deleteCampaignTemplate',
    method: 'get',
    params: { templateId }
  });
}
