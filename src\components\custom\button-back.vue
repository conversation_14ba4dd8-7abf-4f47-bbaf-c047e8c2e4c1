<script setup lang="ts">
import type { RouteKey } from '@elegant-router/types';
import { useRouterPush } from '@/hooks/common/router';

interface Props {
  backKey?: RouteKey;
  backCallback?: () => void;
}

const props = defineProps<Props>();

const { routerPushByKey, routerBack } = useRouterPush();
function handleBack() {
  if (props.backCallback) {
    props.backCallback();
    return;
  }

  if (props.backKey) {
    routerPushByKey(props.backKey);
  } else {
    routerBack();
  }
}
</script>

<template>
  <NButton strong secondary @click="handleBack">
    <template #icon>
      <icon-solar:undo-left-round-line-duotone class="text-icon" />
    </template>
    Back
  </NButton>
</template>

<style scoped></style>
