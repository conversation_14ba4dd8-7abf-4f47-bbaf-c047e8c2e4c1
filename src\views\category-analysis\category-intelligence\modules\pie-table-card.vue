<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-07-29 09:25:40
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-10 09:43:20
 * @FilePath: \tiksage-frontend\src\views\category-intelligence\modules\pie-table-card.vue
 * @Description: category sales distribution
-->
<script setup lang="tsx">
import { computed, ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { NEllipsis, NFlex } from 'naive-ui';
import { camelCase, delay, divide, orderBy } from 'lodash-es';
import { fetchGetCoreData } from '@/service/api';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';
import Tip from '@/components/custom/tip.vue';
import CycleRatio from '@/components/custom/cycle-ratio.vue';
import CategoryShopSelect from './category-shop-select.vue';

interface Props {
  shopType: number;
  month: string;
}
const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const data = ref<Api.CategoryIntelligence.ShopMetrics[]>();

const chartSpec = ref<Visactor.VChart.IPieChartSpec>();

type MetricKey = Extract<
  keyof Api.CategoryIntelligence.ShopMetrics,
  'activeSkus' | 'contentVolume' | 'salesMob' | 'unitsSold' | 'followersGained'
>;

const metricValue = ref<MetricKey>('salesMob');

const metricOptions = computed<CommonType.Option<MetricKey>[]>(() => {
  return [
    { label: 'Gross Sales', value: 'salesMob', unit: '$' },
    { label: 'Units Sold', value: 'unitsSold' },
    { label: 'No.of Active SPUs', value: 'activeSkus' },
    { label: 'Content Volume', value: 'contentVolume' }
  ];
});

function initChartData(metricKey: MetricKey) {
  const values: any[] = [];
  if (data.value) {
    const sortData = orderBy(
      data.value,
      o => {
        return o[metricKey];
      },
      'desc'
    );
    sortData.forEach(shop => {
      const { category } = shop;
      values.push({
        type: category,
        value: shop[metricKey],
        unit: metricOptions.value.find(o => o.value === metricKey)?.unit
      });
    });
  }
  const result: Visactor.VChart.IPieChartSpec = {
    type: 'pie',
    data: {
      values
    },
    categoryField: 'type',
    valueField: 'value',
    legends: {
      visible: false
    }
  };
  chartSpec.value = result;
}

const [chartLoading, toggleChartLoading] = useToggle(false);
const [tableLoading, toggleTableLoading] = useToggle(false);

const tableData = computed(() => {
  if (!data.value) return [];
  return data.value;
});

const columns: NaiveUI.DataTableBaseColumn<Api.CategoryIntelligence.ShopMetrics>[] = [
  {
    key: 'category',
    title: `Category`,
    width: 250,
    fixed: 'left',
    render(rowData) {
      return <NEllipsis>{rowData.category}</NEllipsis>;
    }
  },
  {
    key: 'salesMob',
    title: 'Gross Sales',
    align: 'center',
    width: 160
  },
  {
    key: 'previousSalesMob',
    title() {
      return (
        <NFlex justify="center">
          <span>Sales MoM</span>
          <Tip description="The percentage change in sales revenue from the previous month." />
        </NFlex>
      );
    },
    align: 'center',
    width: 160
  },
  {
    key: 'unitsSold',
    title: 'Units Sold',
    align: 'center',
    width: 160
  },
  {
    key: 'previousUnitsSold',
    title: 'Units Sold MoM',
    align: 'center',
    width: 160
  },
  {
    key: 'followersGained',
    title: 'Followers Gained',
    align: 'center',
    width: 160
  },
  {
    key: 'previousFollowersGained',
    title: 'Followers Gained MoM ',
    align: 'center',
    width: 180
  },
  {
    key: 'activeSkus',
    title: 'No.of Active SPUs',
    align: 'center',
    width: 160
  },
  {
    key: 'previousActiveSkus',
    title: 'SPU Change MoM',
    align: 'center',
    width: 160
  },
  {
    key: 'contentVolume',
    title: 'Content Volume',
    align: 'center',
    width: 160
  },
  {
    key: 'previousContentVolume',
    title: 'Content Volume Gained MoM',
    align: 'center',
    width: 220
  }
];

const dollarKeys = ['salesMob'];

const renderCell = (value: any, rowData: any, column: any) => {
  if (dollarKeys.includes(column.key)) {
    return numberFormat(value, NumeralFormat.Dollar);
  }
  if (column.key.includes('previous')) {
    const curMetricKey = camelCase(column.key.replace('previous', ''));
    const percentValue = divide(rowData[curMetricKey] - value, value);
    const percent = numberFormat(percentValue, NumeralFormat.PlusPercent);
    return <CycleRatio percent={percent} />;
  }
  return numberFormat(value, NumeralFormat.Number);
};

async function handleSelectChange(model: { category: string; thirdCategory: string; shopIds: number[] }) {
  toggleChartLoading(true);
  const query = {
    month: props.month,
    shopType: props.shopType,
    subCategory: model.category
  };
  //  api
  await initCoreData(query);

  initChartData(metricValue.value);
  delay(() => {
    toggleChartLoading(false);
  }, 500);
}

async function initCoreData(params: Api.CategoryIntelligence.CoreDataParams) {
  toggleTableLoading(true);
  const { data: coreData, error: coreDataErr } = await fetchGetCoreData(params);

  if (!coreDataErr) {
    data.value = coreData?.categoryDataList;
  }

  toggleTableLoading(false);
}

watch(
  () => metricValue.value,
  newMetricValue => {
    toggleChartLoading(true);
    initChartData(newMetricValue);
    delay(() => {
      toggleChartLoading(false);
    }, 500);
  },
  { immediate: true }
);
</script>

<template>
  <NGrid x-gap="16" y-gap="16" item-responsive responsive="screen">
    <NGi span="8" class="h-full">
      <NCard
        :bordered="false"
        class="card-wrapper"
        content-class="w-full min-h-320px flex-center"
        title="Categories Sales Distribution"
      >
        <template #header-extra>
          <NSelect
            v-model:value="metricValue"
            class="max-w-110px"
            :consistent-menu-width="false"
            :options="metricOptions"
          />
        </template>
        <NSpin v-if="chartLoading"></NSpin>
        <VDoughnutChart v-else style="height: 300px" :chart-options="chartSpec" />
        <!-- <DoughnutChart :chart-options="chartOptions" :chart-data="chartData" /> -->
      </NCard>
    </NGi>
    <NGi span="16" class="h-full">
      <NCard :bordered="false" class="h-full card-wrapper" content-class="min-h-300px" title="Core Data">
        <template #header-extra>
          <CategoryShopSelect :month="month" :shop-type="shopType" @update:value="handleSelectChange" />
        </template>
        <NDataTable
          class="h-full w-full"
          style="min-height: 280px"
          size="small"
          flex-height
          :loading="tableLoading"
          :bordered="false"
          :data="tableData"
          :columns="columns"
          scroll-x="1930"
          :render-cell="renderCell"
        />
      </NCard>
    </NGi>
  </NGrid>
</template>

<style scoped></style>
