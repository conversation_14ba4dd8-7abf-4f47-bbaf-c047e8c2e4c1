<script setup lang="ts">
import { ref, watch } from 'vue';

interface Props extends /* @vue-ignore */ NaiveUI.PopselectProps {
  unit?: string;
}

const props = defineProps<Props>();
const model = defineModel('range', {
  type: String
});

const popRef = ref<any>(null);
const range = ref<[string, string] | null>(null);

// 监听model值的变化，如果值不在options中，则设置到range中
watch(
  () => model.value,
  newVal => {
    if (!newVal) {
      range.value = null;
      return;
    }

    // 检查是否在预定义选项中
    const isPresetOption = props.options?.some(opt => opt.value === newVal);
    if (!isPresetOption) {
      // 如果不在预定义选项中，将值设置到range中
      const [min, max] = newVal.split(',');
      range.value = [min || '', max || ''];
    } else {
      range.value = null;
    }
  },
  { immediate: true }
);

// 处理输入过滤
function handleFilterNumber(value: [string, string] | null) {
  if (!value) {
    range.value = null;
    return;
  }

  // 只允许输入数字
  const reg = /^\d*$/;
  const newValue: [string, string] = [
    reg.test(value[0]) ? value[0] : range.value?.[0] || '',
    reg.test(value[1]) ? value[1] : range.value?.[1] || ''
  ];

  range.value = newValue;
}

function handleInputRange() {
  if (!range.value) return;
  model.value = range.value.join(',');
  popRef.value.setShow(false);
}

// 重置功能
function handleReset() {
  range.value = null;
  model.value = undefined;
  popRef.value.setShow(false);
}
</script>

<template>
  <NPopselect ref="popRef" v-bind="props" v-model:value="model">
    <template #default>
      <slot></slot>
    </template>
    <template #action>
      <div class="flex-col gap-2">
        <NInput
          style="width: 220px"
          pair
          separator="-"
          :value="range"
          :placeholder="['min', 'max']"
          @update:value="handleFilterNumber"
        />
        <div class="flex items-center justify-between">
          <NButton text @click="handleReset">Reset</NButton>
          <NButton type="primary" size="small" @click="handleInputRange">OK</NButton>
        </div>
      </div>
    </template>
  </NPopselect>
</template>

<style scoped></style>
