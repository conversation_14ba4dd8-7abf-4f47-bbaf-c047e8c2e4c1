<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-11-04 16:29:41
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-14 13:30:39
 * @FilePath: \tiksage-frontend\src\views\return-refund\modules\orders-proportion-card.vue
 * @Description: orders-proportion-card
-->
<script setup lang="ts">
import { computed } from 'vue';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';

type Data = Pick<Api.ReturnRefund.OverviewResponse, 'affiliateTotalOrderNum' | 'returnTotalNum'>;

interface Props {
  data: Data;
}

const props = defineProps<Props>();

const { numberFormat } = useNumberFormat();

const description = `R&R Orders:
Reflects the total count of all orders that have been returned or exchanged during the specified timeframe, capturing both full refunds and partial credit returns.
Orders:
This indicator refers to the status of "normal orders," representing the orders that have been successfully placed and are proceeding without any issues or requests for refunds. It excludes "return orders," which are orders that have been canceled or requested for refunds by customers.
`;

const chartOptions = computed<Visactor.VChart.IPieChartSpec>(() => {
  const values: any[] = [
    { type: 'Orders', value: props.data.affiliateTotalOrderNum || 0 },
    { type: 'R&R Orders', value: props.data.returnTotalNum || 0 }
  ];

  const total = values.reduce((acc, cur) => acc + cur.value, 0);
  const res: Visactor.VChart.IPieChartSpec = {
    type: 'pie',
    data: [
      {
        id: 'numberY',
        values
      }
    ],
    categoryField: 'type',
    valueField: 'value',
    label: {
      visible: false
    },
    legends: {
      visible: true,
      orient: 'right',
      data: legendData => {
        return legendData.map(d => {
          let value = values.find(v => v.type === d.label).value;
          value = `${numberFormat(value, NumeralFormat.Number)}(${numberFormat(value / total, NumeralFormat.Percent)})`;
          return {
            ...d,
            value
          };
        });
      },
      item: {
        width: '50%',
        spaceRow: 8,
        autoEllipsisStrategy: 'valueFirst',
        shape: {
          style: {
            symbolType: 'circle'
          }
        },
        label: {
          style: {
            fontSize: 14,
            fill: '#9ca3af'
          }
        },
        value: {
          alignRight: true,
          style: {
            textAlign: 'right',
            fontSize: 14,
            fill: '#000',
            fontWeight: 'bold',
            ellipsis: false
          }
        }
      }
    }
  };
  return res;
});
</script>

<template>
  <NCard class="card-wrapper" :bordered="false">
    <template #header>
      <NFlex align="center" :wrap="false" :size="16">
        <span>Returns/Refunds Orders Proportion</span>
        <Tip :description="description" />
      </NFlex>
    </template>
    <VDoughnutChart style="height: 300px" :chart-options="chartOptions" />
  </NCard>
</template>

<style scoped></style>
