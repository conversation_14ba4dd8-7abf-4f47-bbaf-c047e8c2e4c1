/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-29 09:38:45
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-21 14:18:01
 * @FilePath: \tiksage-frontend\src\router\guard\monitor.ts
 * @Description: Path jump buried point monitoring
 */
import type { Router } from 'vue-router';
import { fetchPageView } from '@/service/api';

export function createMonitorGuard(router: Router) {
  router.beforeEach((_to, _from, next) => {
    next();
  });
  router.afterEach(to => {
    // fetch API
    fetchPageView(to.name as string);
  });
}
