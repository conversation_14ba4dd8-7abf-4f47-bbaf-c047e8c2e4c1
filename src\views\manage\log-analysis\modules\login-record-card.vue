<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-29 15:46:17
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-22 16:07:59
 * @FilePath: \tiksage-frontend\src\views\manage\log-analysis\modules\user-rank-card.vue
 * @Description: user-rank-card
-->
<script setup lang="tsx">
import { reactive } from 'vue';
import type { PaginationProps } from 'naive-ui';
import dayjs from 'dayjs';
import { TimeFormat } from '@/enum';

interface Props {
  data: Api.SystemManage.SysUserLoginLog[];
}
defineProps<Props>();

const columns: NaiveUI.DataTableBaseColumn<Api.SystemManage.SysUserLoginLog>[] = [
  {
    key: 'userName',
    title: 'Account Name'
  },
  {
    key: 'accountId',
    title: 'Account ID'
  },
  {
    key: 'timestamp',
    title: 'Login Timestamps',
    render(rowData) {
      const date = dayjs(rowData.timestamp);
      return (
        <div class="flex-col gap-16px text-xs">
          <span>{date.tz('Etc/GMT+8').format(TimeFormat.US_TIME_24)}</span>
          <span class="text-coolgray">{date.tz('Etc/GMT-8').format(TimeFormat.US_TIME_24)}</span>
        </div>
      );
    }
  },
  {
    key: 'loginIp',
    title: 'IP'
  },
  {
    key: 'loginAddress',
    title: 'Login Address'
  }
];

const pagination = reactive<PaginationProps>({
  showSizePicker: true,
  pageSizes: [10, 15, 20, 25, 30]
});
</script>

<template>
  <NCard class="card-wrapper" :border="false" title="Detailed Login Records">
    <NDataTable :columns="columns" :data="data" :pagination="pagination"></NDataTable>
  </NCard>
</template>

<style scoped></style>
