<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-12-08 19:24:49
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-12-10 16:10:52
 * @FilePath: \tiksage-frontend\src\views\shop-audit\create\modules\report.vue
 * @Description: report
-->
<script setup lang="ts">
import { computed, nextTick, onUnmounted, ref } from 'vue';
import { useIntervalFn, useToggle } from '@vueuse/core';
import { NDropdown } from 'naive-ui';
import dayjs from 'dayjs';
import html2canvas from 'html2canvas-pro';
import jsPDF from 'jspdf';
import { fetchGetShopAuditBaseDataByTaskId } from '@/service/api';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { getFallbackImage } from '@/utils/fake-image';
import { NumeralFormat, TimeFormat } from '@/enum';
import TopShopCard from './top-shop-card.vue';
import ReportScore from './report-score.vue';
interface Props {
  taskId: number;
}

const { VITE_SHOP_AUDIT_AVATAR_URL } = import.meta.env;

const props = defineProps<Props>();

const [loading, toggleLoading] = useToggle(true);
const shopAuditInfo = ref<Api.ShopAudit.ShopAuditInfo>();

const updateTime = computed(() => {
  if (!shopAuditInfo.value?.updateTime) return '';
  return dayjs.tz(shopAuditInfo.value.updateTime, 'Etc/Gmt+8').format(TimeFormat.US_TIME_24);
});

const shopAvatarUrl = computed(() => {
  if (!shopAuditInfo.value) return '';
  const { avatar, avatarLocal } = shopAuditInfo.value;
  if (avatarLocal) {
    return `${VITE_SHOP_AUDIT_AVATAR_URL}${avatarLocal}`;
  }
  return avatar;
});

const accountAvatarUrl = computed(() => {
  if (!shopAuditInfo.value) return '';
  const { accountAvatar, accountAvatarLocal } = shopAuditInfo.value;
  if (accountAvatarLocal) {
    return `${VITE_SHOP_AUDIT_AVATAR_URL}${accountAvatarLocal}`;
  }
  return accountAvatar;
});

const { numberFormat } = useNumberFormat();

const shopIndicators = computed(() => {
  return [
    {
      title: 'Total GMV',
      prefix: '$',
      value: numberFormat(shopAuditInfo.value?.totalGmv || 0, NumeralFormat.Dollar),
      description: '',
      average: true,
      decimals: 2,
      suffix: ''
    }
  ];
});
const accountIndicators = computed(() => {
  return [
    {
      title: 'Followers',
      prefix: '',
      value: shopAuditInfo.value?.accountFollowers || 0,
      description: '',
      average: true,
      decimals: 2,
      suffix: ''
    }
  ];
});

const downloadOptions = [
  {
    label: 'Export as PNG',
    key: 'png'
  },
  {
    label: 'Export as PDF',
    key: 'pdf'
  }
];

const [downloadLoading, toggleDownloadLoading] = useToggle(false);

async function downloadReport(type: 'pdf' | 'png') {
  toggleDownloadLoading(true);
  window.$loadingBar?.start();
  // Get the element to screenshots
  await nextTick();
  const element = document.getElementById('report-container');

  if (!element) return;
  // Call html2canvas
  try {
    const canvas = await html2canvas(element as HTMLElement, {
      // Set screenshot ratio and cross -domain
      scale: 2, // The larger the value, the clearer the more

      // Whether to use external pictures
      allowTaint: true,

      // Whether to use cross -domain pictures
      useCORS: true,
      logging: true,
      // Background color
      backgroundColor: '#F7FAFC',
      ignoreElements(ele) {
        return ele.id === 'download-button';
      },

      // The width of the screenshot
      width: element.scrollWidth + 50,
      height: element.scrollHeight + 75,

      // The location of the screenshot
      x: -25,
      y: -25
    });

    // Canvas is the conversion Canvas element

    // Waiting for Canvas rendering to be converted into pictures
    // Make sure canvas rendering is completed
    // eslint-disable-next-line
    await new Promise(resolve => setTimeout(resolve));

    const imgUrl = canvas.toDataURL('image/png');

    if (type === 'png') {
      // or trigger download
      const link = document.createElement('a');
      link.download = `report-${dayjs().format(TimeFormat.US_TIME_24)}.png`;
      link.href = imgUrl;
      link.click();
    } else {
      // eslint-disable-next-line
      const pdf = new jsPDF();
      const imgProps = pdf.getImageProperties(imgUrl);
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
      // Calculate the X and Y coordinates of the center position
      const x = (pdf.internal.pageSize.getWidth() - pdfWidth) / 2;
      const y = (pdf.internal.pageSize.getHeight() - pdfHeight) / 2;
      pdf.addImage(imgUrl, 'PNG', x, y, pdfWidth, pdfHeight);
      pdf.save(`report-${dayjs().format(TimeFormat.US_TIME_24)}.pdf`);
    }
    window.$loadingBar?.finish();
    toggleDownloadLoading(false);
  } catch (error) {
    window.$loadingBar?.error();
    toggleDownloadLoading(false);
    window.$message?.error('Failed to generate report');
    console.error('Failed to generate report:', error);
  }
}

const { pause } = useIntervalFn(
  async () => {
    const { data } = await fetchGetShopAuditBaseDataByTaskId(props.taskId);
    if (data) {
      if (data.grabTaskStatus === 23) {
        // ThisCategoryIsNotCurrentlySupported
        window.$dialog?.warning({
          content:
            'Currently, Shop Audit is only available for Home Supplies, Toys & Hobbies, Health, and Pet Supplies categories. The shop you entered belongs to a category that is not yet supported. Stay tuned as we plan to expand support to additional categories in the near future.'
        });

        // StopInterval
        pause();
        return;
      }

      toggleLoading(false);
      shopAuditInfo.value = data;
      if (data.grabTaskStatus >= 20) {
        pause();
      }
    }
  },
  1000 * 10,
  {
    immediate: true,
    immediateCallback: true
  }
);

onUnmounted(() => {
  pause();
});
</script>

<template>
  <NFlex id="report-container" vertical :size="16">
    <div v-if="loading" class="flex-col-center self-center">
      <span class="text-gray">Report generation usually takes 3-5 minutes. Please be patient.</span>
      <Loading class="self-center" />
    </div>
    <template v-else>
      <NCard class="card-wrapper" content-class="" :bordered="false">
        <template #header>
          <NFlex class="my-16px" :size="16" :wrap="false">
            <NText class="text-size-18px font-500">Shop Audit Report</NText>
          </NFlex>
        </template>
        <template #header-extra>
          <NDropdown trigger="hover" placement="bottom-end" :options="downloadOptions" @select="downloadReport">
            <NButton id="download-button" quaternary>
              <template #icon>
                <SvgIcon icon="solar:download-linear" />
              </template>
            </NButton>
          </NDropdown>
        </template>
      </NCard>
      <NCard class="card-wrapper" content-class="" :bordered="false" :wrap="false">
        <NGrid x-gap="16" y-gap="16" item-responsive responsive="screen">
          <NGi span="11">
            <div class="relative flex items-center gap-16px pt-20px">
              <NTag :bordered="false" class="absolute left-0 top--20px" type="primary">TikTok Shop</NTag>
              <div class="h-60px w-60px flex-shrink-0 border rounded-full">
                <NImage
                  class="rounded-full"
                  :width="60"
                  :height="60"
                  :src="shopAvatarUrl"
                  :fallback-src="getFallbackImage(60, 60)"
                />
              </div>
              <NFlex class="flex-1" vertical :wrap="false">
                <NFlex align="center" :wrap="false">
                  <NText class="text-xl">{{ shopAuditInfo?.shopName }}</NText>
                  <icon-twemoji:flag-united-states class="text-xl" />
                </NFlex>
                <div>
                  <NDescriptions
                    class=""
                    :columns="1"
                    label-placement="left"
                    label-class="text-gray mr-8px"
                    label-style="vertical-align:middle"
                  >
                    <NDescriptionsItem label="Category">
                      <NTag :bordered="false" type="primary">{{ shopAuditInfo?.category }}</NTag>
                    </NDescriptionsItem>
                  </NDescriptions>
                </div>
              </NFlex>
              <NGrid class="max-w-100px divide-x" y-gap="16" :cols="1">
                <NGi v-for="indicator in shopIndicators" :key="indicator.title">
                  <NFlex vertical justify="center" align="center">
                    <span class="font-bold">{{ indicator.value }}</span>
                    <NFlex>
                      <NText class="text-gray">{{ indicator.title }}</NText>
                      <Tip v-if="indicator.description !== ''" :description="indicator.description" />
                    </NFlex>
                  </NFlex>
                </NGi>
              </NGrid>
            </div>
          </NGi>
          <template v-if="shopAuditInfo?.accountId">
            <NGi class="flex-center" span="2">
              <NDivider class="m-auto" style="height: 100px" vertical />
            </NGi>
            <NGi span="11">
              <div class="relative flex items-center gap-16px pt-20px">
                <NTag :bordered="false" class="absolute top--20px" type="primary">TikTok Account</NTag>
                <NFlex class="flex-1" align="center" :wrap="false">
                  <div class="h-60px w-60px flex-shrink-0 border rounded-full">
                    <NImage class="rounded-full" :src="accountAvatarUrl" :fallback-src="getFallbackImage(60, 60)" />
                  </div>
                  <NFlex vertical :wrap="false">
                    <NFlex align="center" :wrap="false">
                      <NText class="text-xl">{{ shopAuditInfo?.accountName }}</NText>
                      <icon-twemoji:flag-united-states class="text-xl" />
                    </NFlex>
                    <NDescriptions
                      label-class="text-gray mr-8px"
                      label-style="vertical-align:middle"
                      :columns="1"
                      label-placement="left"
                    >
                      <NDescriptionsItem label="ID">{{ shopAuditInfo?.accountId }}</NDescriptionsItem>
                    </NDescriptions>
                  </NFlex>
                </NFlex>
                <NGrid class="max-w-100px divide-x" y-gap="16" :cols="1">
                  <NGi v-for="indicator in accountIndicators" :key="indicator.title">
                    <NFlex vertical justify="center" align="center">
                      <span class="font-bold">{{ indicator.value }}</span>
                      <NFlex>
                        <NText class="text-gray">{{ indicator.title }}</NText>
                        <Tip v-if="indicator.description !== ''" :description="indicator.description" />
                      </NFlex>
                    </NFlex>
                  </NGi>
                </NGrid>
              </div>
            </NGi>
          </template>
        </NGrid>
      </NCard>
      <ReportScore :task-id="taskId" :is-download="downloadLoading" />
      <NCard
        v-if="shopAuditInfo?.saShopTotalPOList && shopAuditInfo?.saShopTotalPOList.length > 0"
        class="card-wrapper"
        content-class="flex items-center gap-16px text-nowrap"
        :border="false"
        title="Top 3 Shops in This Category"
      >
        <TopShopCard
          v-for="(item, index) in shopAuditInfo?.saShopTotalPOList"
          :key="item.id"
          class="flex-1"
          :index="index"
          :data="item"
        />
      </NCard>
      <LogoTimeBar :content="updateTime" />
    </template>
  </NFlex>
</template>

<style scoped></style>
