<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-12 10:10:59
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-10-22 16:09:24
 * @FilePath: \tiksage-frontend\src\views\analysis\list\index.vue
 * @Description: report list page
-->
<script setup lang="tsx">
import { onMounted, onUnmounted } from 'vue';
import { useIntervalFn } from '@vueuse/core';
import { NAvatar, NButton, NFlex, NPopconfirm, NText } from 'naive-ui';
import { fetchAnalysisReportHistoryList, fetchDeleteById, fetchDownloadExcel } from '@/service/api';
import { useRouterPush } from '@/hooks/common/router';
import { useTable } from '@/hooks/common/table';
import { downloadFile } from '@/utils/download';
import { dateFormat } from '@/utils/date';
import SvgIcon from '@/components/custom/svg-icon.vue';
import TagPopover from '@/components/custom/tag-popover.vue';
import { TaskStatus } from '@/enum';
import TaskProgress from './modules/task-progress.vue';

const { routerPushByKey } = useRouterPush();

const handleViewReport = (id: number, status: Api.Analysis.TaskStatus) => {
  routerPushByKey('analysis_report', {
    params: { id: String(id) },
    query: { status: String(status) }
  });
};

const handleDownloadExcel = async (taskId: number, shopName: string) => {
  const { data, error } = await fetchDownloadExcel(taskId);
  if (error) return;
  downloadFile(data, 'xlsx', shopName);
};

const handleDelete = async (taskId: number) => {
  const { error } = await fetchDeleteById(taskId);
  if (error) return;
  window.$message?.success('Delete success.');
};

const { columns, data, getData, loading, mobilePagination } = useTable({
  immediate: true,
  apiFn: fetchAnalysisReportHistoryList,
  apiParams: {
    current: 1,
    size: 10
  },
  columns: () => {
    return [
      {
        key: 'index',
        title: '',
        width: '80'
      },
      {
        key: 'shopInfo',
        title: 'Shop Info',
        width: '300',
        render(rowData) {
          const { shopInfo } = rowData;
          return (
            <>
              {shopInfo ? (
                <NFlex align="center">
                  <NAvatar src={shopInfo.avatar} />
                  <NText>{shopInfo.shopName}</NText>
                </NFlex>
              ) : (
                '-'
              )}
            </>
          );
        }
      },
      {
        key: 'requestParameters',
        title: 'Target',
        align: 'center',
        children: [
          {
            key: 'requestParameters',
            title: 'Category',
            align: 'center',
            width: '200',
            render(rowData) {
              const { requestParameters: targetInfo } = rowData;
              return (
                <NFlex justify="center">
                  <TagPopover showFirst prefix="+" tags={targetInfo.categoryArr} />
                </NFlex>
              );
            }
          },
          {
            key: 'requestParameters',
            title: 'Follower Ages',
            align: 'center',
            width: '200',
            render(rowData) {
              const { requestParameters: targetInfo } = rowData;
              return (
                <NFlex justify="center">
                  <TagPopover showFirst prefix="+" tags={targetInfo.followerAgeArr} />
                </NFlex>
              );
            }
          },
          {
            key: 'requestParameters',
            title: 'Follower Gender',
            align: 'center',
            width: '200',
            render(rowData) {
              const { requestParameters: targetInfo } = rowData;
              return (
                <NFlex justify="center">
                  {targetInfo.followerGender !== 'Female' ? <SvgIcon icon="twemoji:male-sign" /> : ''}
                  {targetInfo.followerGender !== 'Male' ? <SvgIcon icon="twemoji:female-sign" /> : ''}
                </NFlex>
              );
            }
          }
        ]
      },
      {
        key: 'createTime',
        title: 'Create Time',
        align: 'center',
        width: '200',
        render(rowData) {
          return dateFormat(rowData.createTime);
        }
      },
      {
        key: 'grabTaskStatus',
        title: 'Status',
        align: 'center',
        fixed: 'right',
        width: '150',
        render(rowData) {
          return <TaskProgress taskStatus={rowData.grabTaskStatus} />;
        }
      },
      {
        key: 'operate',
        title: 'Operation',
        align: 'center',
        fixed: 'right',
        width: '250',
        render(rowData, index) {
          const idDisabled = [TaskStatus.SHOP_NOT_FIND, TaskStatus.CATEGORY_NOT_FIND, TaskStatus.NEW].includes(
            rowData.grabTaskStatus
          );
          return (
            <NFlex justify="center" wrap={false}>
              <NButton
                size="small"
                ghost
                type="primary"
                disabled={idDisabled}
                onClick={() => {
                  handleViewReport(rowData.id, rowData.grabTaskStatus);
                }}
              >
                View
              </NButton>
              <NButton
                size="small"
                ghost
                loading={rowData.loading}
                disabled={idDisabled}
                onClick={async () => {
                  data.value[index].loading = true;
                  await handleDownloadExcel(rowData.id, rowData.shopInfo.shopName);
                  data.value[index].loading = false;
                }}
              >
                Export
              </NButton>
              <NPopconfirm
                onPositiveClick={async () => {
                  await handleDelete(rowData.id);
                  getData();
                }}
              >
                {{
                  trigger: () => (
                    <NButton size="small" ghost type="error">
                      Delete
                    </NButton>
                  ),
                  default: () => <>Remove this target shop? This step is final</>
                }}
              </NPopconfirm>
            </NFlex>
          );
        }
      }
    ];
  }
});

const refresh = () => {
  getData();
};

const { pause, resume } = useIntervalFn(() => {
  getData();
}, 1000 * 30);

onMounted(() => {
  resume();
});

onUnmounted(() => {
  pause();
});
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="flex-1 card-wrapper" :bordered="false" content-class="flex-col">
      <template #header>
        <NFlex vertical>
          <span>Report List</span>
          <span class="text-sm text-coolgray">*Analysis for target shops runs automatically every two weeks.</span>
        </NFlex>
      </template>
      <template #header-extra>
        <NButton @click="refresh">
          <template #icon>
            <icon-mdi-refresh class="text-icon" :class="{ 'animate-spin': loading }" />
          </template>
          {{ $t('common.refresh') }}
        </NButton>
      </template>
      <NDataTable
        :bordered="false"
        class="h-full min-h-385px flex-1"
        remote
        flex-height
        :loading="loading"
        :columns="columns"
        :data="data"
        :pagination="mobilePagination"
        scroll-x="1630"
      ></NDataTable>
    </NCard>
  </NFlex>
</template>

<style scoped></style>
