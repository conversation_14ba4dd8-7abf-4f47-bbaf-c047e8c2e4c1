<script setup lang="ts">
import { ref, watch } from 'vue';
import CreatorTable from '../../creator-outreach_find-creator/modules/creator-table.vue';

interface Emits {
  (e: 'update:checked-row-keys', value: number[]): void;
}

const emit = defineEmits<Emits>();

const show = defineModel('show', {
  type: Boolean,
  required: true
});

const checkedRowKeys = ref<number[]>([]);

// 接收外部传入的初始值
const modelValue = defineModel<number[]>('checkedRowKeys', {
  default: () => []
});

function handleAdd() {
  show.value = false;
  emit('update:checked-row-keys', checkedRowKeys.value);
}

watch(
  () => modelValue.value,
  newVal => {
    checkedRowKeys.value = [...newVal];
  },
  {
    deep: true,
    immediate: true
  }
);
</script>

<template>
  <NDrawer v-model:show="show" width="1200px">
    <NDrawerContent closable>
      <CreatorTable v-model:checked-row-keys="checkedRowKeys">
        <template #header-extra>
          <NButton :disabled="!checkedRowKeys.length" type="primary" @click="handleAdd">
            ({{ checkedRowKeys.length }})Add Creators
          </NButton>
        </template>
      </CreatorTable>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
