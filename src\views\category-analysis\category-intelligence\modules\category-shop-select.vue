<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-09-02 10:30:34
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-09-05 10:45:30
 * @FilePath: \tiksage-frontend\src\views\category-intelligence\estee-lauder\modules\category-shop-select.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { isNil } from 'lodash-es';
import { fetchGetSuCategoryOptions, fetchGetThirdCategoryOptions, fetchShopsByCategory } from '@/service/api';

interface Props {
  month: string;
  shopType: number;
  showThirdCategory?: boolean;
  showShop?: boolean;
}

interface Model {
  category: string;
  thirdCategory: string;
  shopIds: number[];
}

interface Emits {
  (e: 'update:value', value: Model): void;
}

const emit = defineEmits<Emits>();

const props = withDefaults(defineProps<Props>(), {
  showThirdCategory: false,
  showShop: false
});

const searchParams = ref({
  subCategory: '',
  thirdCategory: '',
  shopId: -1
});

const [shopLoading, toggleShopLoading] = useToggle(false);

const [thirdLoading, toggleThirdLoading] = useToggle(false);

const subCategoryOptions = ref<CommonType.Option<string>[]>([{ label: 'All Sub-category', value: '' }]);

const thirdCategoryOptions = ref<CommonType.Option<string>[]>([{ label: 'All Third-category', value: '' }]);

const shopOptions = ref<CommonType.Option<number>[]>([{ label: 'All Shop', value: -1 }]);

async function initSubCategoryOptions(shopType: number) {
  const { data: subOpts, error: subOptsErr } = await fetchGetSuCategoryOptions(shopType);
  if (!subOptsErr) {
    subCategoryOptions.value = [{ label: 'All Sub-category', value: '' }].concat(
      subOpts.map(item => ({
        label: item,
        value: item
      }))
    );
  }
}

async function initThirdCategoryOptions(shopType: number, subCategory: string) {
  toggleThirdLoading(true);
  const { data: thirdOpts, error: thirdOptsErr } = await fetchGetThirdCategoryOptions(shopType, subCategory);
  if (!thirdOptsErr) {
    thirdCategoryOptions.value = [{ label: 'All Third-category', value: '' }].concat(
      thirdOpts.map(item => ({
        label: item,
        value: item
      }))
    );
  }

  toggleThirdLoading(false);
  searchParams.value = { ...searchParams.value, thirdCategory: '' };
}

async function initShopOptions(shopType: number, subCategory: string, thirdCategory?: string) {
  toggleShopLoading(true);
  const { data: shopOpts, error: shopOptsErr } = await fetchShopsByCategory({
    shopType,
    category: subCategory,
    month: props.month,
    thirdCategory
  });
  if (!shopOptsErr) {
    shopOptions.value = [{ label: 'All Shop', value: -1 }].concat(
      shopOpts.map(item => ({
        label: item.shopName,
        value: item.shopId
      }))
    );
  }
  toggleShopLoading(false);

  searchParams.value = { ...searchParams.value, shopId: -1 };
}

watch(
  () => props.shopType,
  newVal => {
    if (isNil(newVal)) return;

    initSubCategoryOptions(newVal);
  },
  {
    immediate: true
  }
);

watch(
  () => searchParams.value.subCategory,
  async newSubCategory => {
    if (props.showThirdCategory) {
      await initThirdCategoryOptions(props.shopType, newSubCategory);
    }

    if (props.showShop && !props.showThirdCategory) {
      initShopOptions(props.shopType, newSubCategory);
    }
  }
);

watch(
  () => searchParams.value.thirdCategory,
  newVal => {
    if (props.showThirdCategory && props.showShop) {
      initShopOptions(props.shopType, searchParams.value.subCategory, newVal);
    }
  }
);

watch(
  () => searchParams.value,
  newVal => {
    if (!newVal) return;
    if (shopLoading.value || thirdLoading.value) return;

    emit('update:value', {
      category: newVal.subCategory,
      thirdCategory: newVal.thirdCategory,
      shopIds: [newVal.shopId]
    });
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <NFlex :size="8" :wrap="false">
    <NSelect
      v-model:value="searchParams.subCategory"
      :options="subCategoryOptions"
      :consistent-menu-width="false"
    ></NSelect>
    <NSelect
      v-if="props.showThirdCategory"
      v-model:value="searchParams.thirdCategory"
      :loading="thirdLoading"
      :options="thirdCategoryOptions"
      :consistent-menu-width="false"
    ></NSelect>
    <NSelect
      v-if="props.showShop"
      v-model:value="searchParams.shopId"
      :loading="shopLoading"
      :options="shopOptions"
      :consistent-menu-width="false"
    ></NSelect>
  </NFlex>
</template>

<style scoped></style>
