<script setup lang="ts">
import { computed, ref } from 'vue';
import { useToggle } from '@vueuse/core';
import dayjs from 'dayjs';
import { delay } from 'lodash-es';
import { fetchGetDateRangeOnTikSageDashboard } from '@/service/api';
import { TimeFormat } from '@/enum';
import SellersTableCard from './modules/sellers-table-card.vue';
import ProductRankingCard from './modules/product-ranking-card.vue';
import MonthlyGmvAndEstCard from './modules/monthly-gmv-and-est-card.vue';
import GmvTrendCard from './modules/gmv-trend-card.vue';
import SingleDatePick from './modules/single-date-pick.vue';
import OverviewCard from './modules/overview-card.vue';
import SellersContributionCard from './modules/sellers-contribution-card.vue';

const dateRange = ref<string[]>();
const initDateInfo = ref<Api.TikSageDashboard.DateRangeResponse>();
const updateTime = computed(() => {
  if (!initDateInfo.value) {
    return '';
  }
  return `Last Updated: ${initDateInfo.value?.updateTime}`;
});

function handleDateRangeChange(value: string[]) {
  dateRange.value = value;
}

const [dateLoading, toggleDateLoading] = useToggle(false);

async function initDate() {
  toggleDateLoading(true);
  const { data, error } = await fetchGetDateRangeOnTikSageDashboard();
  if (!error) {
    initDateInfo.value = {
      ...data,
      updateTime: dayjs(data.updateTime).tz('Etc/GMT+8').format(TimeFormat.US_TIME_24)
    };

    delay(() => {
      toggleDateLoading(false);
    }, 300);
  }
}

initDate();
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="card-wrapper" :bordered="false" title="Data Overview"></NCard>
    <NSpin v-if="dateLoading" class="h-full"></NSpin>
    <template v-else>
      <NCard class="card-wrapper" :bordered="false">
        <template #header>
          <div class="flex-y-center gap-4">
            <span>Core Data</span>
            <span class="text-base text-gray">{{ updateTime }}</span>
          </div>
        </template>
        <template #header-extra>
          <SingleDatePick
            :undisable-date-range="initDateInfo ? [initDateInfo.startDateStr, initDateInfo.endDateStr] : undefined"
            @change="handleDateRangeChange"
          />
        </template>
      </NCard>
      <OverviewCard :date-range="dateRange" />
      <MonthlyGmvAndEstCard />
      <GmvTrendCard :date-range="dateRange" />
      <SellersContributionCard :date-range="dateRange" />
      <SellersTableCard :date-range="dateRange" />
      <ProductRankingCard :date-range="dateRange" />
    </template>
  </NFlex>
</template>

<style scoped></style>
