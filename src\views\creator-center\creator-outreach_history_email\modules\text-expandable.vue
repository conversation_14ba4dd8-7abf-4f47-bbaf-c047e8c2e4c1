<!--
功能描述：默认显示几行文字，点击后显示全部文字
功能要点：
1. 默认显示几行文字
2. 点击后显示全部文字
3. 文字过长时，显示省略号
4. 文字过短时，不显示省略号
5. 友好的交互
6. 文字内容，支持v-html
-->

<script setup lang="ts">
import { onMounted, ref } from 'vue';

interface Props {
  content: string; // 文本内容
  maxLines?: number; // 最大显示行数,默认3行
}

const props = withDefaults(defineProps<Props>(), {
  maxLines: 3
});

const textRef = ref<HTMLElement | null>(null);
const isExpanded = ref(false);
const showToggle = ref(false);

// 检查是否需要显示展开/收起按钮
const checkNeedToggle = () => {
  if (!textRef.value) return;

  const element = textRef.value;
  const lineHeight = Number.parseInt(window.getComputedStyle(element).lineHeight, 10);
  const height = element.scrollHeight;

  // 如果内容高度大于最大行数对应的高度,则显示toggle按钮
  showToggle.value = height > props.maxLines * lineHeight;
};

// 监听图片加载完成后重新检查
const handleImagesLoaded = () => {
  const images = textRef.value?.getElementsByTagName('img') || [];
  let loadedImages = 0;
  const totalImages = images.length;

  if (totalImages === 0) {
    checkNeedToggle();
    return;
  }

  Array.from(images).forEach(img => {
    if (img.complete) {
      loadedImages += 1;
      if (loadedImages === totalImages) {
        checkNeedToggle();
      }
    } else {
      img.onload = () => {
        loadedImages += 1;
        if (loadedImages === totalImages) {
          checkNeedToggle();
        }
      };
    }
  });
};

// 展开/收起切换
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value;
};

onMounted(() => {
  checkNeedToggle();
  handleImagesLoaded();
});
</script>

<template>
  <div class="text-expandable">
    <!-- 文本内容容器 -->
    <div
      ref="textRef"
      class="text-content transition-all duration-1000"
      :class="{ 'line-clamp-3': !isExpanded }"
      v-html="props.content"
    />

    <!-- 展开/收起按钮 -->
    <div
      v-if="showToggle"
      class="toggle-btn mt-1 cursor-pointer text-blue-500 hover:text-blue-600"
      @click="toggleExpand"
    >
      {{ isExpanded ? '收起' : '展开' }}
    </div>
  </div>
</template>

<style scoped>
.text-content {
  word-break: break-word;
  overflow: hidden;
}
</style>
