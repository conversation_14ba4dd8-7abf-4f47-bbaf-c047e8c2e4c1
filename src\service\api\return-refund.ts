import { request } from '../request';

export function fetchGetRRByDate(data: Api.ReturnRefund.OverviewSearchParams) {
  return request<Api.ReturnRefund.OverviewResponse>({
    url: '/reverse/getOrderReturnByDate',
    method: 'post',
    data
  });
}

export function fetchGetReturnOrderDetailByPage(data: Api.ReturnRefund.ReturnOrderDetailSearchParams) {
  return request<Api.ReturnRefund.ReturnOrderDetailResponse>({
    url: '/reverse/getReturnOrderPage',
    method: 'post',
    data
  });
}

export function fetchExportRRDetailExcel(data: Api.ReturnRefund.OverviewSearchParams) {
  return request({
    timeout: 1000 * 60,
    responseType: 'blob',
    url: '/reverse/exportReturnOrderData',
    method: 'post',
    data
  });
}
