<script setup lang="tsx">
import { ref, watch } from 'vue';
import { useToggle } from '@vueuse/core';
import { NAvatar, NButton, NEllipsis, NSwitch } from 'naive-ui';
import { cloneDeep } from 'lodash-es';
import { fetchGetShopCommissionList, fetchUpdatePartnershipStatus } from '@/service/api';
import { useDictionaryStore } from '@/store/modules/dictonary';
import { useTable } from '@/hooks/common/table';
import { useRouterPush } from '@/hooks/common/router';
import { getFallbackImage } from '@/utils/fake-image';
import ButtonIcon from '@/components/custom/button-icon.vue';
import UpdateDrawer from './modules/update-drawer.vue';
import InvoiceHistoryDrawer from './modules/invoice-history-drawer.vue';
import CommissionDisplay from './modules/commission-display.vue';

const { VITE_SHOP_AVATAR_URL } = import.meta.env;

const { routerPushByKey } = useRouterPush();

const { getDictionaryByCodeType } = useDictionaryStore();

const [showEditCommission, toggleShowEditCommission] = useToggle(false);
const [showInvoiceHistory, toggleShowInvoiceHistory] = useToggle(false);
const selectedShopId = ref<number>();

const selectedShopCommission = ref<Api.TikSageDashboard.ShopCommission>();

const commissionOptions = ref<Api.Dictionary.DictionaryItem<number>[]>();

const { data, columns, pagination, loading, getData, updateSearchParams } = useTable({
  apiFn: fetchGetShopCommissionList,
  apiParams: {
    current: 1,
    size: 10,
    shopName: ''
  },
  columns() {
    return [
      {
        title: 'Shop Name',
        key: 'shopId',
        width: 260,
        render(rowData) {
          const avatarUrl = VITE_SHOP_AVATAR_URL + rowData.avatarLocal;
          return (
            <div class="flex flex-nowrap items-center gap-4">
              <NAvatar size="large" round src={avatarUrl} fallbackSrc={getFallbackImage(60, 60)} />
              <div class="flex-col flex-1">
                <NEllipsis lineClamp={1}>
                  <span class="text-base font-bold">{rowData.shopName}</span>
                </NEllipsis>
                <NEllipsis lineClamp={1}>ID:{rowData.oecSellerId || '-'}</NEllipsis>
              </div>
            </div>
          );
        }
      },
      {
        title: 'Commission Structure',
        key: 'commissions',
        align: 'center',
        render(rowData) {
          if (!rowData.commissions) return '-';
          return <CommissionDisplay commissions={rowData.commissions} />;
        }
      },
      {
        title: 'Payment Terms',
        key: 'dueDateOffset',
        align: 'center'
      },
      {
        title: 'Invoice History',
        key: 'invoiceCount',
        align: 'center',
        render(rowData) {
          if (!rowData.invoiceCount) return '-';
          return (
            <NButton type="primary" text onClick={() => hanleOpenInvoiceHistory(rowData.shopId)}>
              {rowData.invoiceCount} records
            </NButton>
          );
        }
      },
      {
        title: 'Partnership Status',
        key: 'isEnabled',
        align: 'center',
        render(rowData) {
          return (
            <NSwitch
              value={Boolean(rowData.isEnabled)}
              onUpdateValue={() => handleUpdatePartnership(rowData.shopId, Number(!rowData.isEnabled))}
            >
              {{
                checked: () => 'Show',
                unchecked: () => 'Hide'
              }}
            </NSwitch>
          );
        }
      },
      {
        title: '',
        key: 'operate',
        textAlign: 'center',
        align: 'right',
        width: 50,
        render(rowData) {
          return (
            <div class="flex justify-end gap-2">
              <ButtonIcon
                text
                quaternary={false}
                tooltipPlacement="top"
                tooltipContent="Set Commission"
                icon="solar:pen-2-linear"
                onClick={() => handleEdit(rowData)}
              />
              <ButtonIcon
                text
                quaternary={false}
                tooltipPlacement="top-end"
                tooltipContent="Create Invoice"
                icon="solar:bill-list-linear"
                onClick={() => handleCreateInvoice(rowData.shopId)}
              />
            </div>
          );
        }
      }
    ];
  }
});

async function handleUpdatePartnership(shopId: number, isEnabled: number) {
  const { error: updateError } = await fetchUpdatePartnershipStatus(shopId, isEnabled);
  if (!updateError) {
    window.$message?.success('Update shop partnership successfully.');
    getData();
  }
}

function hanleOpenInvoiceHistory(shopId: number) {
  selectedShopId.value = shopId;
  toggleShowInvoiceHistory(true);
}

function handleSearchShopName(value: string) {
  updateSearchParams({ shopName: value, size: 10, current: 1 });
  getData();
}

function handleEdit(rowData: Api.TikSageDashboard.ShopCommission) {
  selectedShopCommission.value = cloneDeep(rowData);
  toggleShowEditCommission();
}

function handleCreateInvoice(shopId: number) {
  routerPushByKey('tiksage-dashboard_invoice-create', {
    params: {
      id: `${shopId}`
    }
  });
}

async function initCommissionOptions() {
  const opts = await getDictionaryByCodeType('commission_type');
  commissionOptions.value = opts;
}

function init() {
  initCommissionOptions();
}

init();

watch(
  () => showEditCommission.value,
  newVal => {
    if (!newVal) getData();
  }
);
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="card-wrapper" :bordered="false" title="Shop Commission Setup" />
    <NCard class="h-full min-h-400px card-wrapper" content-class="flex-col gap-4" :bordered="false" title="Seller list">
      <SearchInput placeholder="Search Shops" @change="handleSearchShopName"></SearchInput>
      <NDataTable
        class="h-full"
        remote
        :loading="loading"
        :bordered="false"
        :data="data"
        :columns="columns"
        :pagination="pagination"
      ></NDataTable>
    </NCard>
    <InvoiceHistoryDrawer v-model:show="showInvoiceHistory" :shop-id="selectedShopId" />
    <UpdateDrawer
      v-model:show="showEditCommission"
      :selected-data="selectedShopCommission"
      :commission-options="commissionOptions"
    />
  </NFlex>
</template>

<style scoped></style>
