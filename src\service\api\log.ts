/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-22 13:31:23
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-10-22 14:01:45
 * @FilePath: \tiksage-frontend\src\service\api\log.ts
 * @Description: log api
 */
import { request } from '../request';

export function fetchPageView(pageName: string) {
  return request({
    url: '/sysLog/pageView',
    method: 'post',
    params: { pageName }
  });
}

export function fetchUserLog(data: Api.SystemManage.UserLogSearchParams) {
  return request<Api.SystemManage.UserLogResponse>({
    url: '/sysLog/queryUserLog',
    method: 'post',
    data
  });
}

export function fetchUserPageViewLog(params: Api.SystemManage.UserPageViewSearchParams) {
  return request<Api.SystemManage.UserPageViewLogResponse>({
    url: '/sysLog/queryUserPageViewLog',
    method: 'get',
    params
  });
}
