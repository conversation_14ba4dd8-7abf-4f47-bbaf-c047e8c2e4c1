<script setup lang="tsx">
import { ref } from 'vue';
import { useToggle } from '@vueuse/core';
import { type DataTableColumn, NTag } from 'naive-ui';
import { fetchDeleteEmailAccount, fetchGetEmailAccountList, fetchGoogleOauth2Url } from '@/service/api';
import { dateFormat } from '@/utils/date';
import ButtonIcon from '@/components/custom/button-icon.vue';
import ButtonConfirm from '@/components/custom/button-confirm.vue';

const columns: DataTableColumn<Api.CreatorNetwork.EmailAccount>[] = [
  {
    title: 'emailType',
    key: 'type',
    align: 'center',
    width: 100,
    render() {
      return <ButtonIcon size="small" text quaternary={false} icon="logos:google-icon" />;
    }
  },
  {
    title: 'Email Address',
    key: 'email',
    align: 'center'
  },
  {
    title: 'Add Time',
    key: 'createTime',
    align: 'center',
    width: 300,
    render(rowData) {
      return dateFormat(rowData.createTime);
    }
  },
  {
    title: 'Status',
    key: 'invalidGrant',
    align: 'center',
    width: 100,
    render(rowData) {
      if (rowData.invalidGrant) {
        return (
          <NTag size="small" bordered={false} type="error">
            Authorization expires
          </NTag>
        );
      }
      return (
        <NTag size="small" bordered={false} type="success">
          Active
        </NTag>
      );
    }
  },
  {
    key: 'operate',
    width: 120,
    render(rowData) {
      return (
        <div class="flex items-center justify-end gap-8px">
          <ButtonConfirm
            icon="solar:trash-bin-2-linear"
            confirmText="Are you sure you want to delete?"
            buttonProps={{ text: true, size: 'large', type: 'error' }}
            onPositiveClick={() => handleDeleteEmailAccount(rowData.id)}
          />
        </div>
      );
    }
  }
];

const [loading, toggleLoading] = useToggle(false);
const emailAccountList = ref<Api.CreatorNetwork.EmailAccount[]>([]);

async function handleDeleteEmailAccount(id: number) {
  const { error } = await fetchDeleteEmailAccount(id);
  if (!error) {
    window.$message?.success('Delete successfully');
    initData();
  }
}

async function initData() {
  toggleLoading(true);
  const { data, error } = await fetchGetEmailAccountList();
  if (!error) {
    emailAccountList.value = data;
  }
  toggleLoading(false);
}

async function handleGoogleAuth() {
  const { origin } = window.location;
  const { data, error } = await fetchGoogleOauth2Url(origin);
  // for test
  // const { data, error } = await fetchGoogleOauth2Url('http://127.0.0.1:9527');

  if (!error) {
    window.location.href = data.url;
  }
}

initData();
</script>

<template>
  <NCard class="card-wrapper" segmented :bordered="false" title="Account Management">
    <template #header-extra>
      <div class="flex items-center gap-8px text-nowrap">
        <span>Click icon to authorize email:</span>
        <ButtonIcon size="small" icon="logos:google-icon" @click="handleGoogleAuth" />
        <!-- <ButtonIcon size="small" icon="logos:microsoft-windows-icon" /> -->
      </div>
    </template>
    <NDataTable :columns="columns" :data="emailAccountList" :bordered="false" :loading="loading">
      <template #empty>
        <NEmpty description="There is no authorized mailbox, click on the upper right button to authorize"></NEmpty>
      </template>
    </NDataTable>
  </NCard>
</template>

<style scoped></style>
