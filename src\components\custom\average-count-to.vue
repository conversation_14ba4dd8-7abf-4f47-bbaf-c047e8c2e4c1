<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-10-21 11:43:22
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-11-26 14:03:01
 * @FilePath: \tiksage-frontend\src\components\custom\average-count-to.vue
 * @Description: average-count-to
-->
<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import type { TransitionPresets } from '@vueuse/core';
import { toNumber } from 'lodash-es';
import { useNumberFormat } from '@/hooks/custom/numberFormat';
import { NumeralFormat } from '@/enum';

interface Props {
  startValue?: number;
  endValue?: number;
  duration?: number;
  autoplay?: boolean;
  decimals?: number;
  prefix?: string;
  suffix?: string;
  separator?: string;
  decimal?: string;
  useEasing?: boolean;
  transition?: keyof typeof TransitionPresets;
  // Handle the unit suffixes in numbers, such as "k", "m", "b".
  average?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  startValue: 0,
  endValue: 0,
  duration: 1500,
  autoplay: true,
  decimals: 0,
  prefix: '',
  suffix: '',
  separator: ',',
  decimal: '.',
  useEasing: true,
  transition: 'linear',
  average: false
});

const { numberFormat, extractParts } = useNumberFormat();

const isInteger = ref<boolean>(false);

const endVal = computed(() => {
  let res = 0;

  if (props.average) {
    const num = numberFormat(props.endValue, NumeralFormat.Number).match(/\d+(\.\d+)?/g);
    res = num ? toNumber(num[0]) : 0;
  } else {
    res = props.endValue;
  }

  return res;
});

const realSuffix = computed(() => {
  if (props.average) {
    const suf = extractParts(numberFormat(props.endValue, NumeralFormat.Number));

    return suf.suffix || '';
  }
  return props.suffix;
});

watch(
  () => endVal.value,
  () => {
    // Judge whether it is an integer or not.
    if (endVal.value % 1 === 0) {
      isInteger.value = true;
    }
  },
  {
    immediate: true
  }
);
</script>

<template>
  <CountTo v-bind="{ ...props, endValue: endVal, suffix: realSuffix, decimals: isInteger ? 0 : props.decimals }" />
</template>

<style scoped></style>
