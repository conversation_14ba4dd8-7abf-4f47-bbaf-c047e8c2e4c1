<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-11-18 14:32:42
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-12-06 11:08:05
 * @FilePath: \tiksage-frontend\src\views\creator-manage\video-status\modules\tab-container.vue
 * @Description:
-->
<script setup lang="ts">
import { reactive, ref } from 'vue';
import { useToggle } from '@vueuse/core';
import type { PaginationProps } from 'naive-ui';
import { delay } from 'lodash-es';
import {
  fetchDownloadCreatorProductVideosByApprover,
  fetchDownloadCreatorProductVideosByOperator,
  fetchGetCreatorProductVideoListByApprover,
  fetchGetCreatorProductVideoListByOperator,
  fetchPullCreatorProductVideos
} from '@/service/api';
import { useAuth } from '@/hooks/business/auth';
import { downloadFile } from '@/utils/download';
import CreatorVideoItem from './creator-video-item.vue';
import { VideoStatus } from './share';

const tabs = [
  { label: 'All Status', name: VideoStatus.ALL_STATUS },
  { label: 'Invite for Sample', name: VideoStatus.INVITE_FOR_SAMPLE },
  { label: 'Shipping samples', name: VideoStatus.SHIPPING_SAMPLES },
  { label: 'Video Pending', name: VideoStatus.VIDEO_PENDING },
  { label: 'Video Post ', name: VideoStatus.VIDEO_POST },
  { label: 'Canceled-Unfulfilled', name: VideoStatus.CANCELED_UNFULFILLED },
  { label: 'Canceled-Expired', name: VideoStatus.CANCELED_EXPIRED }
];

const adCodeOptions = [
  { label: 'Yes', value: 1 },
  { label: 'No', value: 0 }
];

interface Props {
  clientOptions: Api.VideoManage.ApprovalUserOption[];
}

const props = defineProps<Props>();

const { hasAuth } = useAuth();

const currentTab = ref<VideoStatus>(VideoStatus.ALL_STATUS);

type SearchParams = Api.CreatorManage.CreatorProductVideoListSearchParams;

function createDefaultSearchParams(): SearchParams {
  return {
    current: 1,
    size: 10,
    client: props.clientOptions[0].userId
  };
}

const searchParams = ref<SearchParams>(createDefaultSearchParams());

const pagination: PaginationProps = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: false,
  onUpdatePage: async (page: number) => {
    pagination.page = page;

    searchParams.value.current = page;
    searchParams.value.size = pagination.pageSize!;

    await getData(searchParams.value);
  },
  onUpdatePageSize: async (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;

    searchParams.value.current = pagination.page;
    searchParams.value.size = pageSize;

    await getData(searchParams.value);
  }
});

function updatePagination(update: Partial<PaginationProps>) {
  Object.assign(pagination, update);
}

const creatorProductVideos = ref<Api.CreatorManage.CreatorProductVideo[]>([]);

const [loading, toggleLoading] = useToggle(false);

async function handleDownloadExcel() {
  if (hasAuth('video-status:operator')) {
    const { data, error } = await fetchDownloadCreatorProductVideosByOperator(searchParams.value);
    if (!error) {
      downloadFile(data, 'xlsx', 'Creator Content');
    }
  } else if (hasAuth('video-status:approver')) {
    const { data, error } = await fetchDownloadCreatorProductVideosByApprover(searchParams.value);
    if (!error) {
      downloadFile(data, 'xlsx', 'Creator Content');
    }
  } else {
    window.$message?.error('No permission.');
  }
}

const refreshButtonDisable = ref(false);
async function handleRefresh() {
  window.$message?.info(
    'Data synchronization is in progress. It may take 3-5 minutes to complete. You can continue with other operations and check back later for updated data.'
  );
  fetchPullCreatorProductVideos(searchParams.value.client);
  refreshButtonDisable.value = true;
}

// Tab Switch
function handleTabChange(newVal: VideoStatus) {
  if (loading.value) return;

  currentTab.value = newVal;
  const status = newVal === -1 ? null : newVal;

  const newParams = {
    ...searchParams.value,
    status,
    current: 1,
    size: 10
  };
  searchParams.value = newParams;

  getData(newParams);
}

// Search input
function handleSearch(type: 'creatorName' | 'product', value: string) {
  const newParams = {
    ...searchParams.value,
    [type]: value,
    current: 1,
    size: 10
  };
  searchParams.value = newParams;

  getData(newParams);
}

// Client selection
function handleClientChange(value: number) {
  const newParams = {
    ...searchParams.value,
    client: value,
    current: 1,
    size: 10
  };
  searchParams.value = newParams;

  getData(newParams);
}

// Ad Code
function handleAdCodeChange(value: number | null) {
  const newParams = {
    ...searchParams.value,
    adCodeFlag: value,
    current: 1,
    size: 10
  };
  searchParams.value = newParams;

  getData(newParams);
}

// Date change
function handleDateChange(time: [number, number] | null) {
  const newParams = {
    ...searchParams.value,
    releaseTimeStart: time ? time[0] : undefined,
    releaseTimeEnd: time ? time[1] : undefined,
    current: 1,
    size: 10
  };

  searchParams.value = newParams;

  getData(newParams);
}

async function getData(params: SearchParams) {
  toggleLoading(true);
  // creatorProductVideos.value = [];

  if (hasAuth('video-status:operator')) {
    const { data, error } = await fetchGetCreatorProductVideoListByOperator(params);

    if (!error) {
      updatePagination({
        page: data.current,
        pageSize: data.size,
        itemCount: data.total
      });
      creatorProductVideos.value = data.records;
    }
  } else if (hasAuth('video-status:approver')) {
    const { data, error } = await fetchGetCreatorProductVideoListByApprover(params);

    if (!error) {
      updatePagination({
        page: data.current,
        pageSize: data.size,
        itemCount: data.total
      });
      creatorProductVideos.value = data.records;
    }
  } else {
    creatorProductVideos.value = [];
  }

  delay(() => {
    toggleLoading(false);
  }, 500);
}

async function handleUpdate() {
  const { data, error } = await fetchGetCreatorProductVideoListByOperator(searchParams.value);
  if (!error) {
    creatorProductVideos.value = data.records;
  }
}

function init() {
  // operator not have dateButton
  if (hasAuth('video-status:approver')) {
    getData(searchParams.value);
  }
}

init();
</script>

<template>
  <NCard class="h-full card-wrapper" content-class="flex-col h-full min-h-400px" :bordered="false">
    <template v-if="hasAuth('video-status:operator')">
      <NTabs :value="currentTab" @update:value="handleTabChange">
        <NTab v-for="item in tabs" :key="item.label" :label="item.label" :name="item.name" />
        <template #suffix>
          <NFlex :size="16" :wrap="false">
            <ButtonRefresh :disabled="refreshButtonDisable" :callback="handleRefresh" />
            <ButtonIcon
              icon="solar:download-linear"
              :quaternary="false"
              text
              tooltip-content="Export"
              tooltip-placement="top"
              @click="handleDownloadExcel"
            />
          </NFlex>
        </template>
      </NTabs>
      <NFlex class="m-t-16px" align="center" :size="16" :wrap="false">
        <SearchInput clearable placeholder="Creator Name" @change="(v: string) => handleSearch('creatorName', v)" />
        <SearchInput clearable placeholder="Product Name/ID" @change="(v: string) => handleSearch('product', v)" />
        <NSelect
          :value="searchParams.client"
          class="w-200px"
          :options="clientOptions"
          value-field="userId"
          label-field="shopName"
          placeholder="Client"
          @update:value="handleClientChange"
        />
        <NSelect
          :value="searchParams.adCodeFlag"
          class="w-200px"
          clearable
          :options="adCodeOptions"
          placeholder="Ad Code"
          @update:value="handleAdCodeChange"
        />
        <ButtonDate :default-value="-2" :show-quick-button="false" @update:timestamp="handleDateChange" />
      </NFlex>
    </template>
    <template v-else>
      <NFlex class="" justify="space-between" align="center" :size="16" :wrap="false">
        <NFlex :size="16" :wrap="false">
          <SearchInput clearable placeholder="Creator Name" @change="(v: string) => handleSearch('creatorName', v)" />
          <SearchInput clearable placeholder="Product Name/ID" @change="(v: string) => handleSearch('product', v)" />
        </NFlex>
        <NFlex :size="16" :wrap="false">
          <ButtonRefresh :disabled="refreshButtonDisable" :callback="handleRefresh" />
          <ButtonIcon
            icon="solar:download-linear"
            text
            :quaternary="false"
            tooltip-content="Export"
            tooltip-placement="top"
            @click="handleDownloadExcel"
          />
        </NFlex>
      </NFlex>
    </template>

    <NSpin v-if="loading" class="flex-1" />
    <template v-else>
      <div v-show="creatorProductVideos.length" class="flex-col flex-1 justify-between">
        <NList :show-divider="false">
          <NListItem v-for="creatorInfo in creatorProductVideos" :key="creatorInfo.creatorName">
            <CreatorVideoItem :data="creatorInfo" @update="handleUpdate" />
          </NListItem>
        </NList>
        <NFlex justify="flex-end">
          <NPagination v-bind="pagination" />
        </NFlex>
      </div>
      <NEmpty v-show="!creatorProductVideos.length" class="m-auto"></NEmpty>
    </template>
  </NCard>
</template>

<style scoped></style>
