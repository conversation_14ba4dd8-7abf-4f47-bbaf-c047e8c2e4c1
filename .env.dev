###
 # @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 # @Date: 2024-06-05 11:03:34
 # @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 # @LastEditTime: 2024-11-20 14:24:46
 # @FilePath: \soybean-admin\.env.test
 # @Description: config in develop environment
###
# backend service base url, develop environment
VITE_SERVICE_BASE_URL=http://*************:8086/tiktop-api/
# VITE_SERVICE_BASE_URL=http://**************:8092/tiksage-core-api/
# VITE_SERVICE_BASE_URL=http://***********:8092/tiktok-core-api/

# other backend service base url, test environment
VITE_OTHER_SERVICE_BASE_URL= `{
  "demo": "http://localhost:9528",
  "adhoc": "https://product.tiksage.com/adhoc-api/"
}`

# backend shop avatar prefix url
VITE_SHOP_AVATAR_URL=https://product.tiksage.com/images/shop/avatar/

# backend product avatar prefix url
VITE_PRODUCT_AVATAR_URL=http://*************:8086/images/product/avatar/

# backend creator avatar prefix url
VITE_CREATOR_AVATAR_URL=https://product.tiksage.com/images/creator/avatar/

VITE_TODAY_VIDEO_AVATAR_URL=http://*************:8086/images/today/

VITE_VIDEO_URL=http://*************:8086/images/video/

VITE_SOURCE_MAP=Y

VITE_SHOP_AUDIT_AVATAR_URL=http://*************:8086/images/shop-audit/avatar/

VITE_SHOP_LEADER_AVATAR_URL=http://*************:8086/images/leader-shop/avatar/

VITE_SHOP_LEADER_REPORT_URL=http://*************:8086/images/leader-shop/report/

VITE_CREATOR_CENTER_AVATAR_URL=http://*************:8086/images/aic-creator/avatar/
