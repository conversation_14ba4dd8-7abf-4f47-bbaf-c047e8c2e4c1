<script setup lang="tsx">
import { ref } from 'vue';
import type { RouteKey } from '@elegant-router/types';
import { useRouterPush } from '@/hooks/common/router';
import { localStg } from '@/utils/storage';
import CreatorTable from './modules/creator-table.vue';

const checkedRowKeys = ref<number[]>([]);
const { routerPushByKey } = useRouterPush();

function handleBatchConnect() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.error('Please select at least one creator');
    return;
  }
  localStg.remove('findCreatorSelectedIds');
  localStg.remove('cachedTiktokMessageData');
  localStg.set('findCreatorSelectedIds', checkedRowKeys.value);
  routerPushByKey('creator-center_creator-outreach_create_tiktok', {});
}

function handleTargetInvitation() {
  if (checkedRowKeys.value.length === 0) {
    window.$message?.error('Please select at least one creator');
    return;
  }
  localStg.remove('findCreatorSelectedIds');
  localStg.remove('cachedTiktokMessageData');
  localStg.set('findCreatorSelectedIds', checkedRowKeys.value);
  routerPushByKey('creator-center_creator-outreach_create_target-invitation', {});
}

function handleLinkTo(key: RouteKey) {
  routerPushByKey(key);
}
</script>

<template>
  <div class="flex-col gap-4">
    <NCard class="card-wrapper" :bordered="false" title="Find Creators">
      <template #header-extra>
        <div class="flex-y-center justify-end gap-2">
          <NButton strong secondary @click="handleLinkTo('creator-center_creator-outreach_history_target-invitation')">
            <template #icon>
              <icon-solar:clipboard-list-linear />
            </template>
            Invite History
          </NButton>
          <NButton strong secondary @click="handleLinkTo('creator-center_creator-outreach_history_tiktok')">
            <template #icon>
              <icon-solar:clipboard-list-linear />
            </template>
            Message History
          </NButton>
          <ButtonBack back-key="creator-center_creator-outreach" />
        </div>
      </template>
    </NCard>
    <CreatorTable v-model:checked-row-keys="checkedRowKeys">
      <template #header-extra>
        <div class="flex gap-4">
          <NButton type="primary" :disabled="checkedRowKeys.length === 0" @click="handleTargetInvitation">
            ({{ checkedRowKeys.length }})Batch Invite
            <template #icon>
              <icon-solar:plain-linear />
            </template>
          </NButton>
          <NButton type="primary" :disabled="checkedRowKeys.length === 0" @click="handleBatchConnect">
            ({{ checkedRowKeys.length }})Batch Message
            <template #icon>
              <icon-solar:plain-linear />
            </template>
          </NButton>
        </div>
      </template>
    </CreatorTable>
  </div>
</template>

<style scoped></style>
