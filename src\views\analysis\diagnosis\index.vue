<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@axwuxi.com
 * @Date: 2024-08-06 10:15:16
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jaron<PERSON>@axwuxi.com
 * @LastEditTime: 2024-09-29 16:41:14
 * @FilePath: \tiksage-frontend\src\views\analysis\index.vue
 * @Description: analysis
-->
<script setup lang="ts">
import { ref } from 'vue';
import { fetchDiagnosis } from '@/service/api';
import { useRouterPush } from '@/hooks/common/router';
import IdealForm from './modules/ideal-form.vue';
type Model = CommonType.RecordNullable<Api.Analysis.DiagnosisParam>;

const { routerPushByKey } = useRouterPush();

function createModel(): Model {
  return {
    shopName: null,
    categoryArr: [],
    followerAgeArr: [],
    followerGender: 'All',
    shopAvatar: null
  };
}
const model = ref<Model>(createModel());
const handleValidate = async (validate: boolean) => {
  if (!validate) return;
  // fetch api
  const { error } = await fetchDiagnosis([model.value]);
  if (error) return;

  routerPushByKey('analysis_list');
};
</script>

<template>
  <NFlex vertical :size="16">
    <NCard class="h-full card-wrapper" content-class="flex-center" :bordered="false" title="Shop Creator Analyzer">
      <div class="w-50%">
        <IdealForm v-model:model="model" @validate="handleValidate" />
      </div>
    </NCard>
  </NFlex>
</template>

<style scoped></style>
