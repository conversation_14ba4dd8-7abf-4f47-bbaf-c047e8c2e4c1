<script setup lang="ts">
interface Props {
  title?: string;
  justify: 'left' | 'center' | 'right';
  description: string;
}

defineProps<Props>();
</script>

<template>
  <NFlex class="text-align-center" :justify="justify" align="center" :wrap="false" :size="8">
    <slot>
      <span>{{ title }}</span>
    </slot>
    <Tip :description="description" />
  </NFlex>
</template>

<style scoped></style>
